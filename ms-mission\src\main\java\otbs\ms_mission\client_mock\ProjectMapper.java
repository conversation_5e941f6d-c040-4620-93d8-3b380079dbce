package otbs.ms_mission.client_mock;

import org.mapstruct.*;

import java.util.List;

/**
 * Mapper pour convertir entre l'entité Project et son DTO.
 */
@Mapper(componentModel = "spring")
public interface ProjectMapper {
    /**
     * Convertit une entité Project en DTO simplifié.
     * Seuls les champs ID et nom sont mappés.
     *
     * @param project L'entité à convertir
     * @return Le DTO correspondant
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    ProjectDTO toDto(Project project);

    /**
     * Convertit un DTO Project en entité.
     * Seuls les champs ID et nom sont mappés, les autres sont ignorés.
     *
     * @param projectDTO Le DTO à convertir
     * @return L'entité correspondante
     */
    @Named("dtoToEntity")
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "identifier", ignore = true)
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "createdOn", ignore = true)
    @Mapping(target = "updatedOn", ignore = true)
    @Mapping(target = "authorId", ignore = true)
    @Mapping(target = "isPublic", ignore = true)
    Project toEntity(ProjectDTO projectDTO);

    /**
     * Met à jour une entité Project existante avec les données d'un DTO.
     * Seuls les champs ID et nom sont mis à jour.
     *
     * @param projectDTO Le DTO contenant les nouvelles données
     * @param project L'entité à mettre à jour
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "name", source = "name")
    @Mapping(target = "identifier", ignore = true)
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "createdOn", ignore = true)
    @Mapping(target = "updatedOn", ignore = true)
    @Mapping(target = "authorId", ignore = true)
    @Mapping(target = "isPublic", ignore = true)
    void updateEntityFromDto(ProjectDTO projectDTO, @MappingTarget Project project);

    /**
     * Convertit une liste d'entités Project en liste de DTOs.
     *
     * @param projects La liste d'entités à convertir
     * @return La liste de DTOs correspondante
     */
    List<ProjectDTO> toDtoList(List<Project> projects);
}
