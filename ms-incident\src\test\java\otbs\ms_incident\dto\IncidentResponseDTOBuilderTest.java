package otbs.ms_incident.dto;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test for IncidentResponseDTO builder
 */
class IncidentResponseDTOBuilderTest {

    @Test
    void testBuilder_withAllFields() {
        // Given
        Long id = 1L;
        Long vehiculeId = 100L;
        String immatriculation = "AB-123-CD";
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        StatusIncident status = StatusIncident.A_TRAITER;
        NiveauPrioriteIncident priorite = NiveauPrioriteIncident.MOYEN;
        String lieu = "Paris";
        String description = "Description de l'incident";
        String constat = "constat.pdf";
        List<String> photos = Arrays.asList("photo1.jpg", "photo2.jpg");
        List<ReparationResponseDTO> reparations = Collections.singletonList(new ReparationResponseDTO());
        LocalDateTime createdAt = LocalDateTime.of(2023, 5, 15, 10, 30);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 5, 16, 11, 45);

        // When
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
                .id(id)
                .vehiculeId(vehiculeId)
                .immatriculation(immatriculation)
                .date(date)
                .type(type)
                .status(status)
                .priorite(priorite)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        // Then
        assertEquals(id, dto.getId());
        assertEquals(vehiculeId, dto.getVehiculeId());
        assertEquals(immatriculation, dto.getImmatriculation());
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertEquals(status, dto.getStatus());
        assertEquals(priorite, dto.getPriorite());
        assertEquals(lieu, dto.getLieu());
        assertEquals(description, dto.getDescription());
        assertEquals(constat, dto.getConstat());
        assertEquals(photos, dto.getPhotos());
        assertEquals(reparations, dto.getReparations());
        assertEquals(createdAt, dto.getCreatedAt());
        assertEquals(updatedAt, dto.getUpdatedAt());
    }

    @Test
    void testBuilder_withMinimalFields() {
        // Given
        Long id = 1L;
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";

        // When
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .build();

        // Then
        assertEquals(id, dto.getId());
        assertNull(dto.getVehiculeId());
        assertNull(dto.getImmatriculation());
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertNull(dto.getStatus());
        assertNull(dto.getPriorite());
        assertEquals(lieu, dto.getLieu());
        assertNull(dto.getDescription());
        assertNull(dto.getConstat());
        assertNotNull(dto.getPhotos());
        assertTrue(dto.getPhotos().isEmpty());
        assertNotNull(dto.getReparations());
        assertTrue(dto.getReparations().isEmpty());
        assertNull(dto.getCreatedAt());
        assertNull(dto.getUpdatedAt());
    }

    @ParameterizedTest
    @EnumSource(TypeIncident.class)
    void testBuilder_withDifferentTypeValues(TypeIncident type) {
        // Given
        Long id = 1L;
        LocalDate date = LocalDate.of(2023, 5, 15);
        String lieu = "Paris";

        // When
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .build();

        // Then
        assertEquals(type, dto.getType());
    }

    @ParameterizedTest
    @EnumSource(StatusIncident.class)
    void testBuilder_withDifferentStatusValues(StatusIncident status) {
        // Given
        Long id = 1L;
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";

        // When
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .status(status)
                .build();

        // Then
        assertEquals(status, dto.getStatus());
    }

    @ParameterizedTest
    @EnumSource(NiveauPrioriteIncident.class)
    void testBuilder_withDifferentPrioriteValues(NiveauPrioriteIncident priorite) {
        // Given
        Long id = 1L;
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";

        // When
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .priorite(priorite)
                .build();

        // Then
        assertEquals(priorite, dto.getPriorite());
    }

    @Test
    void testBuilder_withEmptyCollections() {
        // Given
        Long id = 1L;
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";
        List<String> photos = new ArrayList<>();
        List<ReparationResponseDTO> reparations = new ArrayList<>();

        // When
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .photos(photos)
                .reparations(reparations)
                .build();

        // Then
        assertEquals(photos, dto.getPhotos());
        assertTrue(dto.getPhotos().isEmpty());
        assertEquals(reparations, dto.getReparations());
        assertTrue(dto.getReparations().isEmpty());
    }

    @Test
    void testToString_shouldNotThrowException() {
        // Given
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
                .id(1L)
                .vehiculeId(100L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        // When & Then
        assertDoesNotThrow(() -> {
            String result = dto.toString();
            assertNotNull(result);
            assertFalse(result.isEmpty());
        });
    }

    @Test
    void testEquals_withSameObject() {
        // Given
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
                .id(1L)
                .vehiculeId(100L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        // When & Then
        assertEquals(dto, dto);
    }

    @Test
    void testEquals_withEqualObject() {
        // Given
        IncidentResponseDTO dto1 = IncidentResponseDTO.builder()
                .id(1L)
                .vehiculeId(100L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        IncidentResponseDTO dto2 = IncidentResponseDTO.builder()
                .id(1L)
                .vehiculeId(100L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        // When & Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEquals_withDifferentObject() {
        // Given
        IncidentResponseDTO dto1 = IncidentResponseDTO.builder()
                .id(1L)
                .vehiculeId(100L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        IncidentResponseDTO dto2 = IncidentResponseDTO.builder()
                .id(2L)
                .vehiculeId(100L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        // When & Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }
}
