package otbs.ms_incident.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import otbs.ms_incident.client.VehiculeClient;
import otbs.ms_incident.client.VehiculeDto;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.service.IncidentService;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IncidentControllerVehiculeTest {

    @Mock
    private IncidentService incidentService;

    @Mock
    private VehiculeClient vehiculeClient;

    @InjectMocks
    private IncidentController controller;

    private VehiculeDto vehiculeDto;
    private List<IncidentResponseDTO> incidents;

    @BeforeEach
    void setUp() {
        vehiculeDto = new VehiculeDto();
        vehiculeDto.setIdVehicule(1L);
        vehiculeDto.setImmatriculation("AB-123-CD");
        vehiculeDto.setMarque("Toyota");
        vehiculeDto.setModele("Corolla");

        IncidentResponseDTO incident1 = new IncidentResponseDTO();
        incident1.setId(1L);
        incident1.setVehiculeId(1L);

        IncidentResponseDTO incident2 = new IncidentResponseDTO();
        incident2.setId(2L);
        incident2.setVehiculeId(1L);

        incidents = Arrays.asList(incident1, incident2);
    }

    @Test
    void getVehiculeDetails_shouldReturnVehiculeDetails_whenVehiculeExists() {
        // Given
        when(vehiculeClient.getVehiculeById(1L)).thenReturn(vehiculeDto);

        // When
        ResponseEntity<VehiculeDto> response = controller.getVehiculeDetails(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(vehiculeDto, response.getBody());
    }

    @Test
    void getVehiculeDetails_shouldReturnNotFound_whenVehiculeDoesNotExist() {
        // Given
        when(vehiculeClient.getVehiculeById(1L)).thenReturn(null);

        // When
        ResponseEntity<VehiculeDto> response = controller.getVehiculeDetails(1L);

        // Then
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    void getVehiculeDetailsWithIncidents_shouldReturnVehiculeAndIncidents_whenVehiculeExists() {
        // Given
        when(vehiculeClient.getVehiculeById(1L)).thenReturn(vehiculeDto);
        when(incidentService.getIncidentsByVehiculeId(1L)).thenReturn(incidents);
        when(incidentService.countIncidentsByVehiculeId(1L)).thenReturn(2L);

        // When
        ResponseEntity<Map<String, Object>> response = controller.getVehiculeDetailsWithIncidents(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(vehiculeDto, response.getBody().get("vehicule"));
        assertEquals(incidents, response.getBody().get("incidents"));
    }

    @Test
    void getVehiculeDetailsWithIncidents_shouldReturnNotFound_whenVehiculeDoesNotExist() {
        // Given
        when(vehiculeClient.getVehiculeById(1L)).thenReturn(null);

        // When
        ResponseEntity<Map<String, Object>> response = controller.getVehiculeDetailsWithIncidents(1L);

        // Then
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    void getVehiculeCompleteDetails_shouldReturnCompleteInfo_whenVehiculeExists() {
        // Given
        when(vehiculeClient.getVehiculeById(1L)).thenReturn(vehiculeDto);
        when(incidentService.getIncidentsByVehiculeId(1L)).thenReturn(incidents);
        when(incidentService.countIncidentsByVehiculeId(1L)).thenReturn(2L);

        // When
        ResponseEntity<Map<String, Object>> response = controller.getVehiculeCompleteDetails(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(vehiculeDto, response.getBody().get("vehicule"));
        assertEquals(incidents, response.getBody().get("incidents"));
        assertEquals(2L, response.getBody().get("incidentCount"));
    }

    @Test
    void getVehiculeCompleteDetails_shouldReturnNotFound_whenVehiculeDoesNotExist() {
        // Given
        when(vehiculeClient.getVehiculeById(1L)).thenReturn(null);

        // When
        ResponseEntity<Map<String, Object>> response = controller.getVehiculeCompleteDetails(1L);

        // Then
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
    }

    // Tests supprimés car les endpoints getIncidentsByVehiculeIdPaginated et getIncidentsByVehiculeIdPaginatedFallback ont été supprimés
}
