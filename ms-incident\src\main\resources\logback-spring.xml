<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="LOGS" value="./logs" />

    <appender name="Console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOGS}/ms-incident.log</file>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGS}/archived/ms-incident-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>7</maxHistory>
            <totalSizeCap>100MB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- Loggers -->
    <logger name="otbs.ms_incident.controller.FileStorageController" level="DEBUG" additivity="false">
        <appender-ref ref="Console" />
        <appender-ref ref="RollingFile" />
    </logger>
    
    <logger name="otbs.ms_incident.service.FileStorageService" level="DEBUG" additivity="false">
        <appender-ref ref="Console" />
        <appender-ref ref="RollingFile" />
    </logger>
    
    <logger name="otbs.ms_incident.exception" level="DEBUG" additivity="false">
        <appender-ref ref="Console" />
        <appender-ref ref="RollingFile" />
    </logger>
    
    <!-- Root Logger -->
    <root level="INFO">
        <appender-ref ref="Console" />
        <appender-ref ref="RollingFile" />
    </root>
</configuration> 