package otbs.ms_incident.dto.dashboard;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

class DashboardSummaryDTOTest {

    @Test
    void testNoArgsConstructor() {
        // When
        DashboardSummaryDTO dto = new DashboardSummaryDTO();

        // Then
        assertEquals(0, dto.getTotalIncidents());
        assertEquals(0, dto.getOpenIncidents());
        assertEquals(0, dto.getInProgressIncidents());
        assertEquals(0, dto.getResolvedIncidents());
        assertEquals(0, dto.getHighPriorityIncidents());
        assertEquals(0, dto.getTotalReparations());
        assertEquals(0, dto.getInProgressReparations());
        assertEquals(0, dto.getCompletedReparations());
        assertEquals(BigDecimal.ZERO, dto.getTotalCost());
        assertEquals(BigDecimal.ZERO, dto.getAverageCost());
        assertEquals(0.0, dto.getTotalIncidentsChange());
        assertEquals(0.0, dto.getOpenIncidentsChange());
        assertEquals(0.0, dto.getTotalReparationsChange());
        assertEquals(0.0, dto.getInProgressReparationsChange());
        assertEquals(0.0, dto.getCompletedReparationsChange());
        assertEquals(0.0, dto.getTotalCostChange());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        long totalIncidents = 10;
        long openIncidents = 5;
        long inProgressIncidents = 3;
        long resolvedIncidents = 2;
        long highPriorityIncidents = 2;
        double totalIncidentsChange = 25.0;
        double openIncidentsChange = 10.0;
        long totalReparations = 8;
        long inProgressReparations = 5;
        long completedReparations = 3;
        BigDecimal totalCost = BigDecimal.valueOf(1000);
        BigDecimal averageCost = BigDecimal.valueOf(125);
        double totalReparationsChange = 15.0;
        double inProgressReparationsChange = 20.0;
        double completedReparationsChange = 5.0;
        double totalCostChange = 30.0;

        // When
        DashboardSummaryDTO dto = new DashboardSummaryDTO(
                totalIncidents, openIncidents, inProgressIncidents, resolvedIncidents, highPriorityIncidents,
                totalIncidentsChange, openIncidentsChange,
                totalReparations, inProgressReparations, completedReparations,
                totalCost, averageCost,
                totalReparationsChange, inProgressReparationsChange, completedReparationsChange, totalCostChange
        );

        // Then
        assertEquals(totalIncidents, dto.getTotalIncidents());
        assertEquals(openIncidents, dto.getOpenIncidents());
        assertEquals(inProgressIncidents, dto.getInProgressIncidents());
        assertEquals(resolvedIncidents, dto.getResolvedIncidents());
        assertEquals(highPriorityIncidents, dto.getHighPriorityIncidents());
        assertEquals(totalIncidentsChange, dto.getTotalIncidentsChange());
        assertEquals(openIncidentsChange, dto.getOpenIncidentsChange());
        assertEquals(totalReparations, dto.getTotalReparations());
        assertEquals(inProgressReparations, dto.getInProgressReparations());
        assertEquals(completedReparations, dto.getCompletedReparations());
        assertEquals(totalCost, dto.getTotalCost());
        assertEquals(averageCost, dto.getAverageCost());
        assertEquals(totalReparationsChange, dto.getTotalReparationsChange());
        assertEquals(inProgressReparationsChange, dto.getInProgressReparationsChange());
        assertEquals(completedReparationsChange, dto.getCompletedReparationsChange());
        assertEquals(totalCostChange, dto.getTotalCostChange());
    }

    @Test
    void testJPQLConstructor() {
        // Given
        long totalIncidents = 10;
        long openIncidents = 5;
        long inProgressIncidents = 3;
        long resolvedIncidents = 2;
        long highPriorityIncidents = 2;
        long totalReparations = 8;
        long inProgressReparations = 5;
        long completedReparations = 3;
        BigDecimal totalCost = BigDecimal.valueOf(1000);
        BigDecimal averageCost = BigDecimal.valueOf(125);
        double totalIncidentsChange = 25.0;
        double openIncidentsChange = 10.0;
        double totalReparationsChange = 15.0;
        double inProgressReparationsChange = 20.0;
        double completedReparationsChange = 5.0;
        double totalCostChange = 30.0;

        // When
        DashboardSummaryDTO dto = new DashboardSummaryDTO(
                totalIncidents, openIncidents, inProgressIncidents, resolvedIncidents, highPriorityIncidents,
                totalReparations, inProgressReparations, completedReparations,
                totalCost, averageCost,
                totalIncidentsChange, openIncidentsChange,
                totalReparationsChange, inProgressReparationsChange, completedReparationsChange, totalCostChange
        );

        // Then
        assertEquals(totalIncidents, dto.getTotalIncidents());
        assertEquals(openIncidents, dto.getOpenIncidents());
        assertEquals(inProgressIncidents, dto.getInProgressIncidents());
        assertEquals(resolvedIncidents, dto.getResolvedIncidents());
        assertEquals(highPriorityIncidents, dto.getHighPriorityIncidents());
        assertEquals(totalReparations, dto.getTotalReparations());
        assertEquals(inProgressReparations, dto.getInProgressReparations());
        assertEquals(completedReparations, dto.getCompletedReparations());
        assertEquals(totalCost, dto.getTotalCost());
        assertEquals(averageCost, dto.getAverageCost());
        assertEquals(totalIncidentsChange, dto.getTotalIncidentsChange());
        assertEquals(openIncidentsChange, dto.getOpenIncidentsChange());
        assertEquals(totalReparationsChange, dto.getTotalReparationsChange());
        assertEquals(inProgressReparationsChange, dto.getInProgressReparationsChange());
        assertEquals(completedReparationsChange, dto.getCompletedReparationsChange());
        assertEquals(totalCostChange, dto.getTotalCostChange());
    }

    @Test
    void testJPQLConstructorWithNullCosts() {
        // Given
        long totalIncidents = 10;
        long openIncidents = 5;
        long inProgressIncidents = 3;
        long resolvedIncidents = 2;
        long highPriorityIncidents = 2;
        long totalReparations = 8;
        long inProgressReparations = 5;
        long completedReparations = 3;
        BigDecimal totalCost = null;
        BigDecimal averageCost = null;
        double totalIncidentsChange = 25.0;
        double openIncidentsChange = 10.0;
        double totalReparationsChange = 15.0;
        double inProgressReparationsChange = 20.0;
        double completedReparationsChange = 5.0;
        double totalCostChange = 30.0;

        // When
        DashboardSummaryDTO dto = new DashboardSummaryDTO(
                totalIncidents, openIncidents, inProgressIncidents, resolvedIncidents, highPriorityIncidents,
                totalReparations, inProgressReparations, completedReparations,
                totalCost, averageCost,
                totalIncidentsChange, openIncidentsChange,
                totalReparationsChange, inProgressReparationsChange, completedReparationsChange, totalCostChange
        );

        // Then
        assertEquals(totalIncidents, dto.getTotalIncidents());
        assertEquals(openIncidents, dto.getOpenIncidents());
        assertEquals(inProgressIncidents, dto.getInProgressIncidents());
        assertEquals(resolvedIncidents, dto.getResolvedIncidents());
        assertEquals(highPriorityIncidents, dto.getHighPriorityIncidents());
        assertEquals(totalReparations, dto.getTotalReparations());
        assertEquals(inProgressReparations, dto.getInProgressReparations());
        assertEquals(completedReparations, dto.getCompletedReparations());
        assertEquals(BigDecimal.ZERO, dto.getTotalCost());
        assertEquals(BigDecimal.ZERO, dto.getAverageCost());
        assertEquals(totalIncidentsChange, dto.getTotalIncidentsChange());
        assertEquals(openIncidentsChange, dto.getOpenIncidentsChange());
        assertEquals(totalReparationsChange, dto.getTotalReparationsChange());
        assertEquals(inProgressReparationsChange, dto.getInProgressReparationsChange());
        assertEquals(completedReparationsChange, dto.getCompletedReparationsChange());
        assertEquals(totalCostChange, dto.getTotalCostChange());
    }

    @Test
    void testCreateMethod() {
        // Given
        long totalIncidents = 10;
        long openIncidents = 5;
        long inProgressIncidents = 3;
        long resolvedIncidents = 2;
        long highPriorityIncidents = 2;
        double totalIncidentsChange = 25.0;
        double openIncidentsChange = 10.0;
        long totalReparations = 8;
        long inProgressReparations = 5;
        long completedReparations = 3;
        BigDecimal totalCost = BigDecimal.valueOf(1000);
        BigDecimal averageCost = BigDecimal.valueOf(125);
        double totalReparationsChange = 15.0;
        double inProgressReparationsChange = 20.0;
        double completedReparationsChange = 5.0;
        double totalCostChange = 30.0;

        // When
        DashboardSummaryDTO dto = DashboardSummaryDTO.create(
                totalIncidents, openIncidents, inProgressIncidents, resolvedIncidents, highPriorityIncidents,
                totalIncidentsChange, openIncidentsChange,
                totalReparations, inProgressReparations, completedReparations,
                totalCost, averageCost,
                totalReparationsChange, inProgressReparationsChange, completedReparationsChange, totalCostChange
        );

        // Then
        assertEquals(totalIncidents, dto.getTotalIncidents());
        assertEquals(openIncidents, dto.getOpenIncidents());
        assertEquals(inProgressIncidents, dto.getInProgressIncidents());
        assertEquals(resolvedIncidents, dto.getResolvedIncidents());
        assertEquals(highPriorityIncidents, dto.getHighPriorityIncidents());
        assertEquals(totalIncidentsChange, dto.getTotalIncidentsChange());
        assertEquals(openIncidentsChange, dto.getOpenIncidentsChange());
        assertEquals(totalReparations, dto.getTotalReparations());
        assertEquals(inProgressReparations, dto.getInProgressReparations());
        assertEquals(completedReparations, dto.getCompletedReparations());
        assertEquals(totalCost, dto.getTotalCost());
        assertEquals(averageCost, dto.getAverageCost());
        assertEquals(totalReparationsChange, dto.getTotalReparationsChange());
        assertEquals(inProgressReparationsChange, dto.getInProgressReparationsChange());
        assertEquals(completedReparationsChange, dto.getCompletedReparationsChange());
        assertEquals(totalCostChange, dto.getTotalCostChange());
    }

    @Test
    void testCreateMethodWithNullCosts() {
        // Given
        long totalIncidents = 10;
        long openIncidents = 5;
        long inProgressIncidents = 3;
        long resolvedIncidents = 2;
        long highPriorityIncidents = 2;
        double totalIncidentsChange = 25.0;
        double openIncidentsChange = 10.0;
        long totalReparations = 8;
        long inProgressReparations = 5;
        long completedReparations = 3;
        BigDecimal totalCost = null;
        BigDecimal averageCost = null;
        double totalReparationsChange = 15.0;
        double inProgressReparationsChange = 20.0;
        double completedReparationsChange = 5.0;
        double totalCostChange = 30.0;

        // When
        DashboardSummaryDTO dto = DashboardSummaryDTO.create(
                totalIncidents, openIncidents, inProgressIncidents, resolvedIncidents, highPriorityIncidents,
                totalIncidentsChange, openIncidentsChange,
                totalReparations, inProgressReparations, completedReparations,
                totalCost, averageCost,
                totalReparationsChange, inProgressReparationsChange, completedReparationsChange, totalCostChange
        );

        // Then
        assertEquals(totalIncidents, dto.getTotalIncidents());
        assertEquals(openIncidents, dto.getOpenIncidents());
        assertEquals(inProgressIncidents, dto.getInProgressIncidents());
        assertEquals(resolvedIncidents, dto.getResolvedIncidents());
        assertEquals(highPriorityIncidents, dto.getHighPriorityIncidents());
        assertEquals(totalIncidentsChange, dto.getTotalIncidentsChange());
        assertEquals(openIncidentsChange, dto.getOpenIncidentsChange());
        assertEquals(totalReparations, dto.getTotalReparations());
        assertEquals(inProgressReparations, dto.getInProgressReparations());
        assertEquals(completedReparations, dto.getCompletedReparations());
        assertEquals(BigDecimal.ZERO, dto.getTotalCost());
        assertEquals(BigDecimal.ZERO, dto.getAverageCost());
        assertEquals(totalReparationsChange, dto.getTotalReparationsChange());
        assertEquals(inProgressReparationsChange, dto.getInProgressReparationsChange());
        assertEquals(completedReparationsChange, dto.getCompletedReparationsChange());
        assertEquals(totalCostChange, dto.getTotalCostChange());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        DashboardSummaryDTO dto = new DashboardSummaryDTO();

        // When
        dto.setTotalIncidents(10);
        dto.setOpenIncidents(5);
        dto.setInProgressIncidents(3);
        dto.setResolvedIncidents(2);
        dto.setHighPriorityIncidents(2);
        dto.setTotalIncidentsChange(25.0);
        dto.setOpenIncidentsChange(10.0);
        dto.setTotalReparations(8);
        dto.setInProgressReparations(5);
        dto.setCompletedReparations(3);
        dto.setTotalCost(BigDecimal.valueOf(1000));
        dto.setAverageCost(BigDecimal.valueOf(125));
        dto.setTotalReparationsChange(15.0);
        dto.setInProgressReparationsChange(20.0);
        dto.setCompletedReparationsChange(5.0);
        dto.setTotalCostChange(30.0);

        // Then
        assertEquals(10, dto.getTotalIncidents());
        assertEquals(5, dto.getOpenIncidents());
        assertEquals(3, dto.getInProgressIncidents());
        assertEquals(2, dto.getResolvedIncidents());
        assertEquals(2, dto.getHighPriorityIncidents());
        assertEquals(25.0, dto.getTotalIncidentsChange());
        assertEquals(10.0, dto.getOpenIncidentsChange());
        assertEquals(8, dto.getTotalReparations());
        assertEquals(5, dto.getInProgressReparations());
        assertEquals(3, dto.getCompletedReparations());
        assertEquals(BigDecimal.valueOf(1000), dto.getTotalCost());
        assertEquals(BigDecimal.valueOf(125), dto.getAverageCost());
        assertEquals(15.0, dto.getTotalReparationsChange());
        assertEquals(20.0, dto.getInProgressReparationsChange());
        assertEquals(5.0, dto.getCompletedReparationsChange());
        assertEquals(30.0, dto.getTotalCostChange());
    }

    @Test
    void testEnsureNonNullValues() {
        // Given
        DashboardSummaryDTO dto = new DashboardSummaryDTO();
        dto.setTotalCost(null);
        dto.setAverageCost(null);

        // When
        dto.ensureNonNullValues();

        // Then
        assertEquals(BigDecimal.ZERO, dto.getTotalCost());
        assertEquals(BigDecimal.ZERO, dto.getAverageCost());
    }

    @Test
    void testCreateGrouped() {
        // Given
        DashboardSummaryDTO.IncidentStats incidentStats = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(10)
                .openIncidents(5)
                .inProgressIncidents(3)
                .resolvedIncidents(2)
                .highPriorityIncidents(2)
                .build();

        DashboardSummaryDTO.ReparationStats reparationStats = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(8)
                .inProgressReparations(5)
                .completedReparations(3)
                .totalCost(BigDecimal.valueOf(1000))
                .averageCost(BigDecimal.valueOf(125))
                .build();

        DashboardSummaryDTO.VariationStats variationStats = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .openIncidentsChange(10.0)
                .totalReparationsChange(15.0)
                .inProgressReparationsChange(20.0)
                .completedReparationsChange(5.0)
                .totalCostChange(30.0)
                .build();

        // When
        DashboardSummaryDTO dto = DashboardSummaryDTO.createGrouped(incidentStats, reparationStats, variationStats);

        // Then
        assertEquals(10, dto.getTotalIncidents());
        assertEquals(5, dto.getOpenIncidents());
        assertEquals(3, dto.getInProgressIncidents());
        assertEquals(2, dto.getResolvedIncidents());
        assertEquals(2, dto.getHighPriorityIncidents());
        assertEquals(25.0, dto.getTotalIncidentsChange());
        assertEquals(10.0, dto.getOpenIncidentsChange());
        assertEquals(8, dto.getTotalReparations());
        assertEquals(5, dto.getInProgressReparations());
        assertEquals(3, dto.getCompletedReparations());
        assertEquals(BigDecimal.valueOf(1000), dto.getTotalCost());
        assertEquals(BigDecimal.valueOf(125), dto.getAverageCost());
        assertEquals(15.0, dto.getTotalReparationsChange());
        assertEquals(20.0, dto.getInProgressReparationsChange());
        assertEquals(5.0, dto.getCompletedReparationsChange());
        assertEquals(30.0, dto.getTotalCostChange());
    }

    @Test
    void testIncidentStatsBuilder() {
        // Given & When
        DashboardSummaryDTO.IncidentStats stats = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(10)
                .openIncidents(5)
                .inProgressIncidents(3)
                .resolvedIncidents(2)
                .highPriorityIncidents(2)
                .build();

        // Then
        assertEquals(10, stats.getTotalIncidents());
        assertEquals(5, stats.getOpenIncidents());
        assertEquals(3, stats.getInProgressIncidents());
        assertEquals(2, stats.getResolvedIncidents());
        assertEquals(2, stats.getHighPriorityIncidents());
    }

    @Test
    void testReparationStatsBuilder() {
        // Given & When
        DashboardSummaryDTO.ReparationStats stats = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(8)
                .inProgressReparations(5)
                .completedReparations(3)
                .totalCost(BigDecimal.valueOf(1000))
                .averageCost(BigDecimal.valueOf(125))
                .build();

        // Then
        assertEquals(8, stats.getTotalReparations());
        assertEquals(5, stats.getInProgressReparations());
        assertEquals(3, stats.getCompletedReparations());
        assertEquals(BigDecimal.valueOf(1000), stats.getTotalCost());
        assertEquals(BigDecimal.valueOf(125), stats.getAverageCost());
    }

    @Test
    void testVariationStatsBuilder() {
        // Given & When
        DashboardSummaryDTO.VariationStats stats = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .openIncidentsChange(10.0)
                .totalReparationsChange(15.0)
                .inProgressReparationsChange(20.0)
                .completedReparationsChange(5.0)
                .totalCostChange(30.0)
                .build();

        // Then
        assertEquals(25.0, stats.getTotalIncidentsChange());
        assertEquals(10.0, stats.getOpenIncidentsChange());
        assertEquals(15.0, stats.getTotalReparationsChange());
        assertEquals(20.0, stats.getInProgressReparationsChange());
        assertEquals(5.0, stats.getCompletedReparationsChange());
        assertEquals(30.0, stats.getTotalCostChange());
    }

    @Test
    void testIncidentStatsToString() {
        // Given
        DashboardSummaryDTO.IncidentStats stats = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(10)
                .openIncidents(5)
                .inProgressIncidents(3)
                .resolvedIncidents(2)
                .highPriorityIncidents(2)
                .build();

        // When
        String toString = stats.toString();

        // Then
        assertTrue(toString.contains("totalIncidents=10"));
        assertTrue(toString.contains("openIncidents=5"));
        assertTrue(toString.contains("inProgressIncidents=3"));
        assertTrue(toString.contains("resolvedIncidents=2"));
        assertTrue(toString.contains("highPriorityIncidents=2"));
    }

    @Test
    void testIncidentStatsEqualsAndHashCode() {
        // Given
        DashboardSummaryDTO.IncidentStats stats1 = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(10)
                .openIncidents(5)
                .inProgressIncidents(3)
                .resolvedIncidents(2)
                .highPriorityIncidents(2)
                .build();

        DashboardSummaryDTO.IncidentStats stats2 = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(10)
                .openIncidents(5)
                .inProgressIncidents(3)
                .resolvedIncidents(2)
                .highPriorityIncidents(2)
                .build();

        DashboardSummaryDTO.IncidentStats stats3 = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(20)
                .openIncidents(10)
                .inProgressIncidents(6)
                .resolvedIncidents(4)
                .highPriorityIncidents(4)
                .build();

        // Then
        assertEquals(stats1, stats1); // Same object
        assertEquals(stats1, stats2); // Equal objects
        assertEquals(stats1.hashCode(), stats2.hashCode());
        assertNotEquals(stats1, stats3); // Different objects
        assertNotEquals(stats1.hashCode(), stats3.hashCode());
        assertNotEquals(null, stats1); // Null comparison
        assertNotEquals("Not a stats object", stats1); // Different class
    }

    @Test
    void testIncidentStatsWithDifferentTotalIncidents() {
        // Given
        DashboardSummaryDTO.IncidentStats stats1 = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(10)
                .build();

        DashboardSummaryDTO.IncidentStats stats2 = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(20)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testIncidentStatsWithDifferentOpenIncidents() {
        // Given
        DashboardSummaryDTO.IncidentStats stats1 = DashboardSummaryDTO.IncidentStats.builder()
                .openIncidents(5)
                .build();

        DashboardSummaryDTO.IncidentStats stats2 = DashboardSummaryDTO.IncidentStats.builder()
                .openIncidents(10)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testIncidentStatsWithDifferentInProgressIncidents() {
        // Given
        DashboardSummaryDTO.IncidentStats stats1 = DashboardSummaryDTO.IncidentStats.builder()
                .inProgressIncidents(3)
                .build();

        DashboardSummaryDTO.IncidentStats stats2 = DashboardSummaryDTO.IncidentStats.builder()
                .inProgressIncidents(6)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testIncidentStatsWithDifferentResolvedIncidents() {
        // Given
        DashboardSummaryDTO.IncidentStats stats1 = DashboardSummaryDTO.IncidentStats.builder()
                .resolvedIncidents(2)
                .build();

        DashboardSummaryDTO.IncidentStats stats2 = DashboardSummaryDTO.IncidentStats.builder()
                .resolvedIncidents(4)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testIncidentStatsWithDifferentHighPriorityIncidents() {
        // Given
        DashboardSummaryDTO.IncidentStats stats1 = DashboardSummaryDTO.IncidentStats.builder()
                .highPriorityIncidents(2)
                .build();

        DashboardSummaryDTO.IncidentStats stats2 = DashboardSummaryDTO.IncidentStats.builder()
                .highPriorityIncidents(4)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testReparationStatsToString() {
        // Given
        DashboardSummaryDTO.ReparationStats stats = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(8)
                .inProgressReparations(5)
                .completedReparations(3)
                .totalCost(BigDecimal.valueOf(1000))
                .averageCost(BigDecimal.valueOf(125))
                .build();

        // When
        String toString = stats.toString();

        // Then
        assertTrue(toString.contains("totalReparations=8"));
        assertTrue(toString.contains("inProgressReparations=5"));
        assertTrue(toString.contains("completedReparations=3"));
        assertTrue(toString.contains("totalCost=1000"));
        assertTrue(toString.contains("averageCost=125"));
    }

    @Test
    void testReparationStatsEqualsAndHashCode() {
        // Given
        DashboardSummaryDTO.ReparationStats stats1 = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(8)
                .inProgressReparations(5)
                .completedReparations(3)
                .totalCost(BigDecimal.valueOf(1000))
                .averageCost(BigDecimal.valueOf(125))
                .build();

        DashboardSummaryDTO.ReparationStats stats2 = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(8)
                .inProgressReparations(5)
                .completedReparations(3)
                .totalCost(BigDecimal.valueOf(1000))
                .averageCost(BigDecimal.valueOf(125))
                .build();

        DashboardSummaryDTO.ReparationStats stats3 = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(16)
                .inProgressReparations(10)
                .completedReparations(6)
                .totalCost(BigDecimal.valueOf(2000))
                .averageCost(BigDecimal.valueOf(250))
                .build();

        // Then
        assertEquals(stats1, stats1); // Same object
        assertEquals(stats1, stats2); // Equal objects
        assertEquals(stats1.hashCode(), stats2.hashCode());
        assertNotEquals(stats1, stats3); // Different objects
        assertNotEquals(stats1.hashCode(), stats3.hashCode());
        assertNotEquals(null, stats1); // Null comparison
        assertNotEquals("Not a stats object", stats1); // Different class
    }

    @Test
    void testReparationStatsWithDifferentTotalReparations() {
        // Given
        DashboardSummaryDTO.ReparationStats stats1 = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(8)
                .build();

        DashboardSummaryDTO.ReparationStats stats2 = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(16)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testReparationStatsWithDifferentInProgressReparations() {
        // Given
        DashboardSummaryDTO.ReparationStats stats1 = DashboardSummaryDTO.ReparationStats.builder()
                .inProgressReparations(5)
                .build();

        DashboardSummaryDTO.ReparationStats stats2 = DashboardSummaryDTO.ReparationStats.builder()
                .inProgressReparations(10)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testReparationStatsWithDifferentCompletedReparations() {
        // Given
        DashboardSummaryDTO.ReparationStats stats1 = DashboardSummaryDTO.ReparationStats.builder()
                .completedReparations(3)
                .build();

        DashboardSummaryDTO.ReparationStats stats2 = DashboardSummaryDTO.ReparationStats.builder()
                .completedReparations(6)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testReparationStatsWithDifferentTotalCost() {
        // Given
        DashboardSummaryDTO.ReparationStats stats1 = DashboardSummaryDTO.ReparationStats.builder()
                .totalCost(BigDecimal.valueOf(1000))
                .build();

        DashboardSummaryDTO.ReparationStats stats2 = DashboardSummaryDTO.ReparationStats.builder()
                .totalCost(BigDecimal.valueOf(2000))
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testReparationStatsWithDifferentAverageCost() {
        // Given
        DashboardSummaryDTO.ReparationStats stats1 = DashboardSummaryDTO.ReparationStats.builder()
                .averageCost(BigDecimal.valueOf(125))
                .build();

        DashboardSummaryDTO.ReparationStats stats2 = DashboardSummaryDTO.ReparationStats.builder()
                .averageCost(BigDecimal.valueOf(250))
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testReparationStatsWithNullCosts() {
        // Given
        DashboardSummaryDTO.ReparationStats stats = DashboardSummaryDTO.ReparationStats.builder()
                .totalCost(null)
                .averageCost(null)
                .build();

        // When & Then
        assertDoesNotThrow(stats::hashCode);
    }

    @Test
    void testVariationStatsToString() {
        // Given
        DashboardSummaryDTO.VariationStats stats = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .openIncidentsChange(10.0)
                .totalReparationsChange(15.0)
                .inProgressReparationsChange(20.0)
                .completedReparationsChange(5.0)
                .totalCostChange(30.0)
                .build();

        // When
        String toString = stats.toString();

        // Then
        assertTrue(toString.contains("totalIncidentsChange=25.0"));
        assertTrue(toString.contains("openIncidentsChange=10.0"));
        assertTrue(toString.contains("totalReparationsChange=15.0"));
        assertTrue(toString.contains("inProgressReparationsChange=20.0"));
        assertTrue(toString.contains("completedReparationsChange=5.0"));
        assertTrue(toString.contains("totalCostChange=30.0"));
    }

    @Test
    void testVariationStatsEqualsAndHashCode() {
        // Given
        DashboardSummaryDTO.VariationStats stats1 = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .openIncidentsChange(10.0)
                .totalReparationsChange(15.0)
                .inProgressReparationsChange(20.0)
                .completedReparationsChange(5.0)
                .totalCostChange(30.0)
                .build();

        DashboardSummaryDTO.VariationStats stats2 = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .openIncidentsChange(10.0)
                .totalReparationsChange(15.0)
                .inProgressReparationsChange(20.0)
                .completedReparationsChange(5.0)
                .totalCostChange(30.0)
                .build();

        DashboardSummaryDTO.VariationStats stats3 = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(50.0)
                .openIncidentsChange(20.0)
                .totalReparationsChange(30.0)
                .inProgressReparationsChange(40.0)
                .completedReparationsChange(10.0)
                .totalCostChange(60.0)
                .build();

        // Then
        assertEquals(stats1, stats1); // Same object
        assertEquals(stats1, stats2); // Equal objects
        assertEquals(stats1.hashCode(), stats2.hashCode());
        assertNotEquals(stats1, stats3); // Different objects
        assertNotEquals(stats1.hashCode(), stats3.hashCode());
        assertNotEquals(null, stats1); // Null comparison
        assertNotEquals("Not a stats object", stats1); // Different class
    }

    @Test
    void testVariationStatsWithDifferentTotalIncidentsChange() {
        // Given
        DashboardSummaryDTO.VariationStats stats1 = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .build();

        DashboardSummaryDTO.VariationStats stats2 = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(50.0)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testVariationStatsWithDifferentOpenIncidentsChange() {
        // Given
        DashboardSummaryDTO.VariationStats stats1 = DashboardSummaryDTO.VariationStats.builder()
                .openIncidentsChange(10.0)
                .build();

        DashboardSummaryDTO.VariationStats stats2 = DashboardSummaryDTO.VariationStats.builder()
                .openIncidentsChange(20.0)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testVariationStatsWithDifferentTotalReparationsChange() {
        // Given
        DashboardSummaryDTO.VariationStats stats1 = DashboardSummaryDTO.VariationStats.builder()
                .totalReparationsChange(15.0)
                .build();

        DashboardSummaryDTO.VariationStats stats2 = DashboardSummaryDTO.VariationStats.builder()
                .totalReparationsChange(30.0)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testVariationStatsWithDifferentInProgressReparationsChange() {
        // Given
        DashboardSummaryDTO.VariationStats stats1 = DashboardSummaryDTO.VariationStats.builder()
                .inProgressReparationsChange(20.0)
                .build();

        DashboardSummaryDTO.VariationStats stats2 = DashboardSummaryDTO.VariationStats.builder()
                .inProgressReparationsChange(40.0)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testVariationStatsWithDifferentCompletedReparationsChange() {
        // Given
        DashboardSummaryDTO.VariationStats stats1 = DashboardSummaryDTO.VariationStats.builder()
                .completedReparationsChange(5.0)
                .build();

        DashboardSummaryDTO.VariationStats stats2 = DashboardSummaryDTO.VariationStats.builder()
                .completedReparationsChange(10.0)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testVariationStatsWithDifferentTotalCostChange() {
        // Given
        DashboardSummaryDTO.VariationStats stats1 = DashboardSummaryDTO.VariationStats.builder()
                .totalCostChange(30.0)
                .build();

        DashboardSummaryDTO.VariationStats stats2 = DashboardSummaryDTO.VariationStats.builder()
                .totalCostChange(60.0)
                .build();

        // Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setTotalIncidents(10);
        dto1.setOpenIncidents(5);
        dto1.setTotalCost(BigDecimal.valueOf(1000));

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setTotalIncidents(10);
        dto2.setOpenIncidents(5);
        dto2.setTotalCost(BigDecimal.valueOf(1000));

        DashboardSummaryDTO dto3 = new DashboardSummaryDTO();
        dto3.setTotalIncidents(20);
        dto3.setOpenIncidents(10);
        dto3.setTotalCost(BigDecimal.valueOf(2000));

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testEqualsWithDifferentTotalIncidents() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setTotalIncidents(10);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setTotalIncidents(20);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentOpenIncidents() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setOpenIncidents(5);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setOpenIncidents(10);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentInProgressIncidents() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setInProgressIncidents(3);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setInProgressIncidents(6);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentResolvedIncidents() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setResolvedIncidents(2);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setResolvedIncidents(4);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentHighPriorityIncidents() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setHighPriorityIncidents(2);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setHighPriorityIncidents(4);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentTotalIncidentsChange() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setTotalIncidentsChange(25.0);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setTotalIncidentsChange(50.0);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentOpenIncidentsChange() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setOpenIncidentsChange(10.0);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setOpenIncidentsChange(20.0);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentTotalReparations() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setTotalReparations(8);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setTotalReparations(16);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentInProgressReparations() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setInProgressReparations(5);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setInProgressReparations(10);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentCompletedReparations() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setCompletedReparations(3);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setCompletedReparations(6);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentTotalCost() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setTotalCost(BigDecimal.valueOf(1000));

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setTotalCost(BigDecimal.valueOf(2000));

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentAverageCost() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setAverageCost(BigDecimal.valueOf(125));

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setAverageCost(BigDecimal.valueOf(250));

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentTotalReparationsChange() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setTotalReparationsChange(15.0);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setTotalReparationsChange(30.0);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentInProgressReparationsChange() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setInProgressReparationsChange(20.0);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setInProgressReparationsChange(40.0);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentCompletedReparationsChange() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setCompletedReparationsChange(5.0);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setCompletedReparationsChange(10.0);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentTotalCostChange() {
        // Given
        DashboardSummaryDTO dto1 = new DashboardSummaryDTO();
        dto1.setTotalCostChange(30.0);

        DashboardSummaryDTO dto2 = new DashboardSummaryDTO();
        dto2.setTotalCostChange(60.0);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testHashCodeWithNullCosts() {
        // Given
        DashboardSummaryDTO dto = new DashboardSummaryDTO();
        dto.setTotalCost(null);
        dto.setAverageCost(null);

        // When & Then
        assertDoesNotThrow(dto::hashCode);
    }

    @Test
    void testToString() {
        // Given
        DashboardSummaryDTO dto = new DashboardSummaryDTO();
        dto.setTotalIncidents(10);
        dto.setOpenIncidents(5);
        dto.setTotalCost(BigDecimal.valueOf(1000));

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("totalIncidents=10"));
        assertTrue(result.contains("openIncidents=5"));
        assertTrue(result.contains("totalCost=1000"));
    }

    @Test
    void testEqualsWithSameObject() {
        // Given
        DashboardSummaryDTO dto = new DashboardSummaryDTO();
        dto.setTotalIncidents(10);

        // Then
        assertEquals(dto, dto);
    }

    @Test
    void testEqualsWithNull() {
        // Given
        DashboardSummaryDTO dto = new DashboardSummaryDTO();
        dto.setTotalIncidents(10);

        // Then
        assertNotEquals(null, dto);
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Given
        DashboardSummaryDTO dto = new DashboardSummaryDTO();
        dto.setTotalIncidents(10);

        // Then
        assertNotEquals("Not a DTO", dto);
    }
}
