image: maven:3-eclipse-temurin-17 

variables:
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Définit l'emplacement du cache de la tâche d'analyse
  GIT_DEPTH: "0"  # Dit à git de récupérer toutes les branches du projet, nécessaire pour la tâche d'analyse

stages:
  - sonarqube-check
  - sonarqube-vulnerability-report
  - package
  - build_deploy

# Etape d'analyse SonarQube
sonarqube-check:
  stage: sonarqube-check
  tags:
    - sonarqube-runner
  script:
    - mvn clean test
    - mvn org.sonarsource.scanner.maven:sonar-maven-plugin:sonar   # Exécute l'analyse SonarQube
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == 'master'
    - if: $CI_COMMIT_BRANCH == 'main'
    - if: $CI_COMMIT_BRANCH == 'develop'

# Etape pour générer le rapport de vulnérabilité SonarQube
sonarqube-vulnerability-report:
  stage: sonarqube-vulnerability-report
  script:
    - 'curl -u "${SONAR_TOKEN}:" "${SONAR_HOST_URL}/api/issues/gitlab_sast_export?projectKey=ms_incident&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" -o gl-sast-sonar-report.json'
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == 'master'
    - if: $CI_COMMIT_BRANCH == 'main'
    - if: $CI_COMMIT_BRANCH == 'develop'
  artifacts:
    expire_in: 1 day 
    reports:
      sast: gl-sast-sonar-report.json   # Génère un rapport SAST

# Etape de packaging (construction du JAR avec Maven)
package:
  stage: package
  tags:
    - sonarqube-runner
  script:
    - mvn package 
  artifacts:
    paths:
      - target/*.jar   
    expire_in: 1 week  

# Etape de construction et déploiement du conteneur
build_deploy:
  stage: build_deploy
  tags:
    - sonarqube-runner
  script:
    - sudo podman build -t ms-incident .
    - sudo podman run -d --replace --name ms-incident -p 9002:9002  -v /mnt/nfs_storage:/app/storage:z localhost/ms-incident:latest
    - sudo podman generate systemd --name ms-incident --new --files
    - sudo mkdir -p /etc/systemd/system/   
    - sudo mv container-ms-incident.service /etc/systemd/system/ms-incident.service  
    - sudo systemctl daemon-reload 
    - sudo systemctl enable ms-incident.service  
    - sudo systemctl start ms-incident.service  
    - sudo systemctl status ms-incident.service  
