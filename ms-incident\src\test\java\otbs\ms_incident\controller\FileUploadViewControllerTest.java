package otbs.ms_incident.controller;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.servlet.view.InternalResourceViewResolver;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.view;

@ExtendWith(MockitoExtension.class)
class FileUploadViewControllerTest {

    @InjectMocks
    private FileUploadViewController fileUploadViewController;

    @Test
    void uploadTestPage_shouldReturnUploadTestView() throws Exception {
        // Given
        InternalResourceViewResolver viewResolver = new InternalResourceViewResolver();
        viewResolver.setPrefix("classpath:/templates/");
        viewResolver.setSuffix(".html");
        
        MockMvc mockMvc = MockMvcBuilders
            .standaloneSetup(fileUploadViewController)
            .setViewResolvers(viewResolver)
            .build();
        
        // When/Then
        mockMvc.perform(get("/upload-test"))
                .andExpect(status().isOk())
                .andExpect(view().name("upload-test"));
    }
} 