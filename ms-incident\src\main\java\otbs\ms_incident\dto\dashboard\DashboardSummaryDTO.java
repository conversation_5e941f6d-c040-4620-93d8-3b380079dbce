package otbs.ms_incident.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * DTO pour les statistiques générales du dashboard
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DashboardSummaryDTO {
    // Statistiques d'incidents
    private long totalIncidents;
    private long openIncidents;
    private long inProgressIncidents;
    private long resolvedIncidents;
    private long highPriorityIncidents;

    // Variations (pour les tendances)
    private double totalIncidentsChange;
    private double openIncidentsChange;

    // Statistiques de réparations
    private long totalReparations;
    private long inProgressReparations;
    private long completedReparations;
    @Builder.Default
    private BigDecimal totalCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal averageCost = BigDecimal.ZERO;

    // Variations des réparations
    private double totalReparationsChange;
    private double inProgressReparationsChange;
    private double completedReparationsChange;
    private double totalCostChange;

    /**
     * Constructeur utilisé par les requêtes JPQL dans DashboardRepository
     */
    public DashboardSummaryDTO(
            long totalIncidents,
            long openIncidents,
            long inProgressIncidents,
            long resolvedIncidents,
            long highPriorityIncidents,
            long totalReparations,
            long inProgressReparations,
            long completedReparations,
            BigDecimal totalCost,
            BigDecimal averageCost,
            double totalIncidentsChange,
            double openIncidentsChange,
            double totalReparationsChange,
            double inProgressReparationsChange,
            double completedReparationsChange,
            double totalCostChange
    ) {
        this.totalIncidents = totalIncidents;
        this.openIncidents = openIncidents;
        this.inProgressIncidents = inProgressIncidents;
        this.resolvedIncidents = resolvedIncidents;
        this.highPriorityIncidents = highPriorityIncidents;
        this.totalReparations = totalReparations;
        this.inProgressReparations = inProgressReparations;
        this.completedReparations = completedReparations;
        this.totalCost = totalCost != null ? totalCost : BigDecimal.ZERO;
        this.averageCost = averageCost != null ? averageCost : BigDecimal.ZERO;
        this.totalIncidentsChange = totalIncidentsChange;
        this.openIncidentsChange = openIncidentsChange;
        this.totalReparationsChange = totalReparationsChange;
        this.inProgressReparationsChange = inProgressReparationsChange;
        this.completedReparationsChange = completedReparationsChange;
        this.totalCostChange = totalCostChange;
    }



    /**
     * Crée une instance de DashboardSummaryDTO avec les valeurs spécifiées
     * Méthode de fabrique pour remplacer le constructeur avec trop de paramètres
     */
    public static DashboardSummaryDTO create(
            long totalIncidents,
            long openIncidents,
            long inProgressIncidents,
            long resolvedIncidents,
            long highPriorityIncidents,
            double totalIncidentsChange,
            double openIncidentsChange,
            long totalReparations,
            long inProgressReparations,
            long completedReparations,
            BigDecimal totalCost,
            BigDecimal averageCost,
            double totalReparationsChange,
            double inProgressReparationsChange,
            double completedReparationsChange,
            double totalCostChange
    ) {
        return DashboardSummaryDTO.builder()
            .totalIncidents(totalIncidents)
            .openIncidents(openIncidents)
            .inProgressIncidents(inProgressIncidents)
            .resolvedIncidents(resolvedIncidents)
            .highPriorityIncidents(highPriorityIncidents)
            .totalIncidentsChange(totalIncidentsChange)
            .openIncidentsChange(openIncidentsChange)
            .totalReparations(totalReparations)
            .inProgressReparations(inProgressReparations)
            .completedReparations(completedReparations)
            .totalCost(totalCost != null ? totalCost : BigDecimal.ZERO)
            .averageCost(averageCost != null ? averageCost : BigDecimal.ZERO)
            .totalReparationsChange(totalReparationsChange)
            .inProgressReparationsChange(inProgressReparationsChange)
            .completedReparationsChange(completedReparationsChange)
            .totalCostChange(totalCostChange)
            .build();
    }

    /**
     * Crée une instance de DashboardSummaryDTO avec des paramètres regroupés
     * Méthode alternative pour réduire le nombre de paramètres
     */
    public static DashboardSummaryDTO createGrouped(
            IncidentStats incidentStats,
            ReparationStats reparationStats,
            VariationStats variationStats
    ) {
        return DashboardSummaryDTO.builder()
            .totalIncidents(incidentStats.getTotalIncidents())
            .openIncidents(incidentStats.getOpenIncidents())
            .inProgressIncidents(incidentStats.getInProgressIncidents())
            .resolvedIncidents(incidentStats.getResolvedIncidents())
            .highPriorityIncidents(incidentStats.getHighPriorityIncidents())
            .totalReparations(reparationStats.getTotalReparations())
            .inProgressReparations(reparationStats.getInProgressReparations())
            .completedReparations(reparationStats.getCompletedReparations())
            .totalCost(reparationStats.getTotalCost())
            .averageCost(reparationStats.getAverageCost())
            .totalIncidentsChange(variationStats.getTotalIncidentsChange())
            .openIncidentsChange(variationStats.getOpenIncidentsChange())
            .totalReparationsChange(variationStats.getTotalReparationsChange())
            .inProgressReparationsChange(variationStats.getInProgressReparationsChange())
            .completedReparationsChange(variationStats.getCompletedReparationsChange())
            .totalCostChange(variationStats.getTotalCostChange())
            .build();
    }

    /**
     * Classe pour regrouper les statistiques d'incidents
     */
    @Data
    @Builder
    public static class IncidentStats {
        private long totalIncidents;
        private long openIncidents;
        private long inProgressIncidents;
        private long resolvedIncidents;
        private long highPriorityIncidents;
    }

    /**
     * Classe pour regrouper les statistiques de réparations
     */
    @Data
    @Builder
    public static class ReparationStats {
        private long totalReparations;
        private long inProgressReparations;
        private long completedReparations;
        private BigDecimal totalCost;
        private BigDecimal averageCost;
    }

    /**
     * Classe pour regrouper les statistiques de variations
     */
    @Data
    @Builder
    public static class VariationStats {
        private double totalIncidentsChange;
        private double openIncidentsChange;
        private double totalReparationsChange;
        private double inProgressReparationsChange;
        private double completedReparationsChange;
        private double totalCostChange;
    }

    /**
     * Vérifie et corrige les valeurs nulles dans le DTO
     */
    public void ensureNonNullValues() {
        if (this.totalCost == null) {
            this.totalCost = BigDecimal.ZERO;
        }
        if (this.averageCost == null) {
            this.averageCost = BigDecimal.ZERO;
        }
    }
}
