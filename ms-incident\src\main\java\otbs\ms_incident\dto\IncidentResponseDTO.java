package otbs.ms_incident.dto;

import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "DTO pour la réponse contenant les données d'un incident")
public class IncidentResponseDTO {
    @Schema(description = "Identifiant du véhicule associé à cet incident", example = "1")
    private Long vehiculeId;

    @Schema(description = "Immatriculation du véhicule associé à cet incident", example = "123-ABC-45")
    private String immatriculation;

    @Schema(description = "Identifiant unique de l'incident", example = "1")
    private Long id;

    @Schema(description = "Date à laquelle l'incident s'est produit", example = "2023-05-15")
    private LocalDate date;

    @Schema(description = "Type d'incident", example = "ACCIDENT", allowableValues = {"ACCIDENT", "PANNE"})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private TypeIncident type;

    @Schema(description = "Statut de l'incident", example = "A_TRAITER", allowableValues = {"A_TRAITER", "EN_COURS_TRAITEMENT", "RESOLU"})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private StatusIncident status;

    @Schema(description = "Niveau de priorité de l'incident", example = "MOYEN", allowableValues = {"CRITIQUE", "MOYEN", "FAIBLE"})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private NiveauPrioriteIncident priorite;

    @Schema(description = "Lieu où l'incident s'est produit", example = "Rue de la Paix, Paris")
    private String lieu;

    @Schema(description = "Chemin vers le constat d'accident ou rapport technique",
           example = "incidents/123/constats/constat_abc123.pdf")
    private String constat;

    @Schema(description = "Liste des chemins vers les photos de l'incident",
           example = "[\"incidents/123/photos/photo_abc123.jpg\"]")
    @Builder.Default
    private List<String> photos = new ArrayList<>();

    @Schema(description = "Description détaillée de l'incident", example = "Collision latérale avec un autre véhicule")
    private String description;

    @Schema(description = "Liste des réparations associées à cet incident")
    @Builder.Default
    private List<ReparationResponseDTO> reparations = new ArrayList<>();

    @Schema(description = "Date et heure de création de l'enregistrement", example = "2023-05-15T14:30:00")
    private LocalDateTime createdAt;

    @Schema(description = "Date et heure de dernière modification de l'enregistrement", example = "2023-05-16T09:15:00")
    private LocalDateTime updatedAt;
}