package otbs.ms_incident.dto.dashboard;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class IncidentTypeDistributionDTOTest {

    @Test
    void testNoArgsConstructor() {
        // When
        IncidentTypeDistributionDTO dto = new IncidentTypeDistributionDTO();

        // Then
        assertNull(dto.getCountByType());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Map<String, Long> countByType = new HashMap<>();
        countByType.put("ACCIDENT", 5L);
        countByType.put("PANNE", 3L);

        // When
        IncidentTypeDistributionDTO dto = new IncidentTypeDistributionDTO(countByType);

        // Then
        assertEquals(countByType, dto.getCountByType());
        assertEquals(5L, dto.getCountByType().get("ACCIDENT"));
        assertEquals(3L, dto.getCountByType().get("PANNE"));
    }

    @Test
    void testBuilder() {
        // Given
        Map<String, Long> countByType = new HashMap<>();
        countByType.put("ACCIDENT", 5L);
        countByType.put("PANNE", 3L);

        // When
        IncidentTypeDistributionDTO dto = IncidentTypeDistributionDTO.builder()
                .countByType(countByType)
                .build();

        // Then
        assertEquals(countByType, dto.getCountByType());
        assertEquals(5L, dto.getCountByType().get("ACCIDENT"));
        assertEquals(3L, dto.getCountByType().get("PANNE"));
    }

    @Test
    void testSettersAndGetters() {
        // Given
        IncidentTypeDistributionDTO dto = new IncidentTypeDistributionDTO();
        Map<String, Long> countByType = new HashMap<>();
        countByType.put("ACCIDENT", 5L);
        countByType.put("PANNE", 3L);

        // When
        dto.setCountByType(countByType);

        // Then
        assertEquals(countByType, dto.getCountByType());
        assertEquals(5L, dto.getCountByType().get("ACCIDENT"));
        assertEquals(3L, dto.getCountByType().get("PANNE"));
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        Map<String, Long> countByType1 = new HashMap<>();
        countByType1.put("ACCIDENT", 5L);
        countByType1.put("PANNE", 3L);

        Map<String, Long> countByType2 = new HashMap<>();
        countByType2.put("ACCIDENT", 5L);
        countByType2.put("PANNE", 3L);

        Map<String, Long> countByType3 = new HashMap<>();
        countByType3.put("ACCIDENT", 10L);
        countByType3.put("PANNE", 6L);

        IncidentTypeDistributionDTO dto1 = new IncidentTypeDistributionDTO(countByType1);
        IncidentTypeDistributionDTO dto2 = new IncidentTypeDistributionDTO(countByType2);
        IncidentTypeDistributionDTO dto3 = new IncidentTypeDistributionDTO(countByType3);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testToString() {
        // Given
        Map<String, Long> countByType = new HashMap<>();
        countByType.put("ACCIDENT", 5L);
        countByType.put("PANNE", 3L);
        IncidentTypeDistributionDTO dto = new IncidentTypeDistributionDTO(countByType);

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("countByType="));
        assertTrue(result.contains("ACCIDENT=5"));
        assertTrue(result.contains("PANNE=3"));
    }

    @Test
    void testEqualsWithSameObject() {
        // Given
        Map<String, Long> countByType = new HashMap<>();
        countByType.put("ACCIDENT", 5L);
        IncidentTypeDistributionDTO dto = new IncidentTypeDistributionDTO(countByType);

        // Then
        assertEquals(dto, dto);
    }

    @Test
    void testEqualsWithNull() {
        // Given
        Map<String, Long> countByType = new HashMap<>();
        countByType.put("ACCIDENT", 5L);
        IncidentTypeDistributionDTO dto = new IncidentTypeDistributionDTO(countByType);

        // Then
        assertNotEquals(null, dto);
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Given
        Map<String, Long> countByType = new HashMap<>();
        countByType.put("ACCIDENT", 5L);
        IncidentTypeDistributionDTO dto = new IncidentTypeDistributionDTO(countByType);

        // Then
        assertNotEquals("Not a DTO", dto);
    }

    @Test
    void testBuilderWithNullMap() {
        // When
        IncidentTypeDistributionDTO dto = IncidentTypeDistributionDTO.builder()
                .countByType(null)
                .build();

        // Then
        assertNull(dto.getCountByType());
    }

    @Test
    void testEqualsWithDifferentCountByType() {
        // Given
        Map<String, Long> countByType1 = new HashMap<>();
        countByType1.put("ACCIDENT", 5L);

        Map<String, Long> countByType2 = new HashMap<>();
        countByType2.put("ACCIDENT", 10L);

        IncidentTypeDistributionDTO dto1 = new IncidentTypeDistributionDTO(countByType1);
        IncidentTypeDistributionDTO dto2 = new IncidentTypeDistributionDTO(countByType2);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithOneNullCountByType() {
        // Given
        Map<String, Long> countByType = new HashMap<>();
        countByType.put("ACCIDENT", 5L);

        IncidentTypeDistributionDTO dto1 = new IncidentTypeDistributionDTO(countByType);
        IncidentTypeDistributionDTO dto2 = new IncidentTypeDistributionDTO(null);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithBothNullCountByType() {
        // Given
        IncidentTypeDistributionDTO dto1 = new IncidentTypeDistributionDTO(null);
        IncidentTypeDistributionDTO dto2 = new IncidentTypeDistributionDTO(null);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testHashCodeWithNullCountByType() {
        // Given
        IncidentTypeDistributionDTO dto = new IncidentTypeDistributionDTO(null);

        // When & Then
        assertDoesNotThrow(dto::hashCode);
    }

    @Test
    void testEqualsWithDifferentTypeKeys() {
        // Given
        Map<String, Long> countByType1 = new HashMap<>();
        countByType1.put("ACCIDENT", 5L);

        Map<String, Long> countByType2 = new HashMap<>();
        countByType2.put("PANNE", 5L);

        IncidentTypeDistributionDTO dto1 = new IncidentTypeDistributionDTO(countByType1);
        IncidentTypeDistributionDTO dto2 = new IncidentTypeDistributionDTO(countByType2);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }
}
