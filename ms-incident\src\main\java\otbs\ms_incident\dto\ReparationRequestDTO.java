package otbs.ms_incident.dto;

import otbs.ms_incident.enums.StatusReparation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PastOrPresent;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "dto pour la création ou mise à jour d'une réparation")
public class ReparationRequestDTO {

    @Schema(description = "Date de la réparation", example = "2023-06-01")
    @NotNull(message = "La date de réparation est obligatoire")
    @PastOrPresent(message = "La date de réparation doit être dans le passé ou le présent")
    private LocalDate dateReparation;

    @Schema(description = "Statut de la réparation", example = "EN_COURS", allowableValues = {"EN_COURS", "TERMINEE"})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private StatusReparation status;

    @Schema(description = "Description des travaux effectués", example = "Remplacement pare-choc avant")
    @NotBlank(message = "La description est obligatoire")
    private String description;

    @Schema(description = "Coût de la réparation", example = "1200.50")
    @NotNull(message = "Le coût est obligatoire")
    @Positive(message = "Le coût doit être positif")
    private BigDecimal cout;

    @Schema(description = "Garage ayant effectué la réparation", example = "Garage Central")
    @NotBlank(message = "Le nom du garage est obligatoire")
    private String garage;

    @Schema(description = "ID de l'incident associé à cette réparation", example = "42")
    private Long incidentId;

    @Schema(description = "Indique si la réparation a été remboursée par l'assurance", example = "true")
    private Boolean rembourse;

    @Schema(description = "Montant couvert par l'assurance", example = "1000.00")
    private BigDecimal montantCouverture;
}