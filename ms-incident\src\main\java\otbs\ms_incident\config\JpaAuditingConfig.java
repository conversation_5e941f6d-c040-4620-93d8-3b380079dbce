package otbs.ms_incident.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;

/**
 * Configuration pour l'audit JPA dans l'application.
 * Cette classe active la fonctionnalité d'audit de JPA qui permet de suivre
 * automatiquement qui a créé ou modifié une entité et quand.
 * <AUTHOR>
 */
@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class JpaAuditingConfig {

    /**
     * Fournit l'identité de l'utilisateur qui effectue les opérations d'audit.
     * 
     * @return Un fournisseur d'auditeur qui renvoie l'identité de l'utilisateur actuel
     *
     */
    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication == null || !authentication.isAuthenticated()) {
                return Optional.of("system");
            }
            
            return Optional.of(authentication.getName());
        };
    }
}