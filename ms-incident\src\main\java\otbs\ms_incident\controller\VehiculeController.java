package otbs.ms_incident.controller;

import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import otbs.ms_incident.client.VehiculeClient;
import otbs.ms_incident.client.VehiculeDto;
import otbs.ms_incident.service.IncidentService;
import otbs.ms_incident.util.ControllerUtils;

import java.util.List;

/**
 * Contrôleur REST pour la gestion des véhicules.
 * Cette classe expose les endpoints de l'API permettant d'accéder aux informations des véhicules
 * depuis le microservice véhicule.
 */
@RestController
@RequestMapping("/api/vehicules")
@Tag(name = "Véhicule", description = "API pour la gestion des véhicules")
@RequiredArgsConstructor
@Slf4j
public class VehiculeController {

    private static final String VEHICULE_SERVICE = "vehiculeService";

    private final VehiculeClient vehiculeClient;
    private final IncidentService incidentService;

    @GetMapping
    @Operation(summary = "Récupérer tous les véhicules",
            description = "Retourne la liste de tous les véhicules disponibles")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des véhicules récupérée avec succès"),
            @ApiResponse(responseCode = "503", description = "Service véhicule indisponible")
    })
    @CircuitBreaker(name = VEHICULE_SERVICE, fallbackMethod = "getAllVehiculesFallback")
    @Retry(name = VEHICULE_SERVICE)
    public ResponseEntity<List<VehiculeDto>> getAllVehicules() {
        log.info("REST request to get all vehicules");

        List<VehiculeDto> vehicules = vehiculeClient.getAllVehicules();
        log.info("Liste des véhicules récupérée avec succès: {} véhicules trouvés", vehicules.size());
        return ResponseEntity.ok(vehicules);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Récupérer un véhicule par ID",
            description = "Retourne les détails d'un véhicule spécifique d'après son ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Véhicule trouvé"),
            @ApiResponse(responseCode = "404", description = "Véhicule non trouvé"),
            @ApiResponse(responseCode = "503", description = "Service véhicule indisponible")
    })
    @CircuitBreaker(name = VEHICULE_SERVICE, fallbackMethod = "getVehiculeByIdFallback")
    @Retry(name = VEHICULE_SERVICE)
    public ResponseEntity<VehiculeDto> getVehiculeById(
            @Parameter(description = "ID du véhicule", required = true)
            @PathVariable Long id) {
        log.info("REST request to get vehicule with ID: {}", id);

        VehiculeDto vehicule = vehiculeClient.getVehiculeById(id);
        if (vehicule == null) {
            log.warn("Véhicule avec ID {} non trouvé", id);
            return ResponseEntity.notFound().build();
        }

        log.info("Véhicule avec ID {} trouvé: {}", id, vehicule);
        return ResponseEntity.ok(vehicule);
    }

    @GetMapping("/immatriculation/{immatriculation}")
    @Operation(summary = "Récupérer un véhicule par immatriculation",
            description = "Retourne les détails d'un véhicule spécifique d'après son immatriculation")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Véhicule trouvé"),
            @ApiResponse(responseCode = "404", description = "Véhicule non trouvé"),
            @ApiResponse(responseCode = "503", description = "Service véhicule indisponible")
    })
    @CircuitBreaker(name = VEHICULE_SERVICE, fallbackMethod = "getVehiculeByImmatriculationFallback")
    @Retry(name = VEHICULE_SERVICE)
    public ResponseEntity<VehiculeDto> getVehiculeByImmatriculation(
            @Parameter(description = "Immatriculation du véhicule", required = true)
            @PathVariable String immatriculation) {
        log.info("REST request to get vehicule by immatriculation: {}", immatriculation);

        VehiculeDto vehicule = vehiculeClient.getVehiculeByImmatriculation(immatriculation);
        if (vehicule == null) {
            log.warn("Véhicule avec immatriculation {} non trouvé", immatriculation);
            return ResponseEntity.notFound().build();
        }

        log.info("Véhicule avec immatriculation {} trouvé: {}", immatriculation, vehicule);
        return ResponseEntity.ok(vehicule);
    }

    @GetMapping("/etat/{etat}")
    @Operation(summary = "Récupérer les véhicules par état",
            description = "Retourne la liste des véhicules filtrés par état")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des véhicules récupérée"),
            @ApiResponse(responseCode = "400", description = "État invalide"),
            @ApiResponse(responseCode = "503", description = "Service véhicule indisponible")
    })
    @CircuitBreaker(name = VEHICULE_SERVICE, fallbackMethod = "getVehiculesByEtatFallback")
    @Retry(name = VEHICULE_SERVICE)
    public ResponseEntity<List<VehiculeDto>> getVehiculesByEtat(
            @Parameter(description = "État des véhicules (DISPONIBLE, EN_MAINTENANCE, EN_ROUTE)", required = true)
            @PathVariable String etat) {
        log.info("REST request to get vehicules by etat: {}", etat);

        // Cette fonctionnalité nécessiterait une implémentation côté service véhicule
        // Pour l'instant, on récupère tous les véhicules et on filtre côté client
        List<VehiculeDto> allVehicules = vehiculeClient.getAllVehicules();
        List<VehiculeDto> filteredVehicules = allVehicules.stream()
                .filter(v -> v.getEtat().equalsIgnoreCase(etat))
                .toList();

        log.info("Véhicules avec état {} trouvés: {}", etat, filteredVehicules.size());
        return ResponseEntity.ok(filteredVehicules);
    }

    // Méthodes de fallback

    public ResponseEntity<List<VehiculeDto>> getAllVehiculesFallback(Exception e) {
        return ControllerUtils.createServiceUnavailableResponse(e, VEHICULE_SERVICE, null);
    }

    public ResponseEntity<VehiculeDto> getVehiculeByIdFallback(Long id, Exception e) {
        return ControllerUtils.createServiceUnavailableResponse(e, VEHICULE_SERVICE, id);
    }

    public ResponseEntity<VehiculeDto> getVehiculeByImmatriculationFallback(String immatriculation, Exception e) {
        return ControllerUtils.createServiceUnavailableResponse(e, VEHICULE_SERVICE, immatriculation);
    }

    public ResponseEntity<List<VehiculeDto>> getVehiculesByEtatFallback(String etat, Exception e) {
        return ControllerUtils.createServiceUnavailableResponse(e, VEHICULE_SERVICE, etat);
    }
}
