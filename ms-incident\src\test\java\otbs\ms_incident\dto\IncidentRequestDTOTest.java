package otbs.ms_incident.dto;

import otbs.ms_incident.enums.TypeIncident;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.*;

class IncidentRequestDTOTest {

    @Test
    void testBuilderAndGetters() {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Avenue des Champs-Élysées, Paris";
        String description = "Accrochage avec un véhicule en stationnement";
        List<Long> reparationIds = Arrays.asList(1L, 2L);

        // When
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .reparationIds(reparationIds)
                .build();

        // Then
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertEquals(lieu, dto.getLieu());
        assertEquals(description, dto.getDescription());
        assertEquals(reparationIds, dto.getReparationIds());
    }

    @Test
    void testNoArgsConstructor() {
        // When
        IncidentRequestDTO dto = new IncidentRequestDTO();

        // Then
        assertNull(dto.getDate());
        assertNull(dto.getType());
        assertNull(dto.getLieu());
        assertNull(dto.getDescription());
        assertNotNull(dto.getPhotos());
        assertTrue(dto.getPhotos().isEmpty());
        assertNotNull(dto.getReparationIds());
        assertTrue(dto.getReparationIds().isEmpty());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Avenue des Champs-Élysées, Paris";
        String description = "Accrochage avec un véhicule en stationnement";
        List<Long> reparationIds = Arrays.asList(1L, 2L);

        // When
        IncidentRequestDTO dto = new IncidentRequestDTO(null, date, type, null, null, lieu, description, null, new ArrayList<>(), reparationIds);

        // Then
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertEquals(lieu, dto.getLieu());
        assertEquals(description, dto.getDescription());
        assertEquals(reparationIds, dto.getReparationIds());
    }

    @Test
    void testSetters() {
        // Given
        IncidentRequestDTO dto = new IncidentRequestDTO();
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Avenue des Champs-Élysées, Paris";
        String description = "Accrochage avec un véhicule en stationnement";
        List<Long> reparationIds = Arrays.asList(1L, 2L);

        // When
        dto.setDate(date);
        dto.setType(type);
        dto.setLieu(lieu);
        dto.setDescription(description);
        dto.setReparationIds(reparationIds);

        // Then
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertEquals(lieu, dto.getLieu());
        assertEquals(description, dto.getDescription());
        assertEquals(reparationIds, dto.getReparationIds());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Avenue des Champs-Élysées, Paris";
        String description = "Accrochage avec un véhicule en stationnement";
        List<Long> reparationIds = Arrays.asList(1L, 2L);

        IncidentRequestDTO dto1 = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .reparationIds(reparationIds)
                .vehiculeId(1L)
                .build();

        IncidentRequestDTO dto2 = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .reparationIds(reparationIds)
                .vehiculeId(1L)
                .build();

        IncidentRequestDTO dto3 = IncidentRequestDTO.builder()
                .date(date)
                .type(TypeIncident.PANNE)
                .lieu(lieu)
                .description(description)
                .reparationIds(reparationIds)
                .vehiculeId(1L)
                .build();

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testToString() {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Avenue des Champs-Élysées, Paris";
        String description = "Accrochage avec un véhicule en stationnement";
        List<Long> reparationIds = Arrays.asList(1L, 2L);

        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .reparationIds(reparationIds)
                .build();

        // When
        String toString = dto.toString();

        // Then
        assertTrue(toString.contains(date.toString()));
        assertTrue(toString.contains(type.toString()));
        assertTrue(toString.contains(lieu));
        assertTrue(toString.contains(description));
        assertTrue(toString.contains(reparationIds.toString()));
    }

    @Test
    void testNullPhotosInitializedToEmptyList() {
        // Given
        // Create a new DTO with builder
        IncidentRequestDTO dto = IncidentRequestDTO.builder().build();

        // Then - A Builder.Default should create an empty list
        assertNotNull(dto.getPhotos(), "Photos should not be null when built with default");
        assertTrue(dto.getPhotos().isEmpty(), "Photos should be empty list");

        // When we add an item
        dto.getPhotos().add("test.jpg");
        assertEquals(1, dto.getPhotos().size(), "Should be able to add to the photos list");
    }

    @Test
    void testNullReparationIdsInitializedToEmptyList() {
        // Given
        // Create a new DTO with builder
        IncidentRequestDTO dto = IncidentRequestDTO.builder().build();

        // Then - A Builder.Default should create an empty list
        assertNotNull(dto.getReparationIds(), "ReparationIds should not be null when built with default");
        assertTrue(dto.getReparationIds().isEmpty(), "ReparationIds should be empty list");

        // When we add an item
        dto.getReparationIds().add(1L);
        assertEquals(1, dto.getReparationIds().size(), "Should be able to add to the reparationIds list");
    }

    @Test
    void testEqualsWithNull() {
        // Given
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        // Then
        assertNotEquals( null,dto );
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Given
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        // Then
        assertNotEquals("Not a DTO",dto );
    }

    @Test
    void testEqualsWithAllFields() {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";
        String description = "Accident description";
        String constat = "constat.pdf";
        List<String> photos = Arrays.asList("photo1.jpg", "photo2.jpg");
        List<Long> reparationIds = Arrays.asList(1L, 2L);

        // Create base DTO
        IncidentRequestDTO base = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparationIds(reparationIds)
                .build();

        // Create identical DTO
        IncidentRequestDTO same = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparationIds(reparationIds)
                .build();

        // Create DTOs with different fields
        IncidentRequestDTO differentDate = IncidentRequestDTO.builder()
                .date(LocalDate.of(2023, 5, 16))
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparationIds(reparationIds)
                .build();

        IncidentRequestDTO differentType = IncidentRequestDTO.builder()
                .date(date)
                .type(TypeIncident.PANNE)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparationIds(reparationIds)
                .build();

        IncidentRequestDTO differentLieu = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu("Lyon")
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparationIds(reparationIds)
                .build();

        IncidentRequestDTO differentDescription = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .description("Different description")
                .constat(constat)
                .photos(photos)
                .reparationIds(reparationIds)
                .build();

        IncidentRequestDTO differentConstat = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat("different.pdf")
                .photos(photos)
                .reparationIds(reparationIds)
                .build();

        IncidentRequestDTO differentPhotos = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(Arrays.asList("different.jpg"))
                .reparationIds(reparationIds)
                .build();

        IncidentRequestDTO differentReparationIds = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparationIds(Arrays.asList(3L, 4L))
                .build();

        // Then - test reflexivity and symmetry
        assertEquals(base, base);
        assertEquals(base, same);
        assertEquals(same, base);

        // Test transitivity
        assertNotEquals(base, differentDate);
        assertNotEquals(base, differentType);
        assertNotEquals(base, differentLieu);
        assertNotEquals(base, differentDescription);
        assertNotEquals(base, differentConstat);
        assertNotEquals(base, differentPhotos);
        assertNotEquals(base, differentReparationIds);

        // Test hashCode
        assertEquals(base.hashCode(), same.hashCode());
        assertNotEquals(base.hashCode(), differentDate.hashCode());
        assertNotEquals(base.hashCode(), differentType.hashCode());
        assertNotEquals(base.hashCode(), differentLieu.hashCode());
        assertNotEquals(base.hashCode(), differentDescription.hashCode());
        assertNotEquals(base.hashCode(), differentConstat.hashCode());
        assertNotEquals(base.hashCode(), differentPhotos.hashCode());
        assertNotEquals(base.hashCode(), differentReparationIds.hashCode());
    }

    @Test
    void testEqualsAndHashCodeWithNullFields() {
        // Given
        IncidentRequestDTO dtoWithNulls = IncidentRequestDTO.builder()
                .date(null)
                .type(null)
                .lieu(null)
                .description(null)
                .constat(null)
                .build();

        IncidentRequestDTO anotherDtoWithNulls = IncidentRequestDTO.builder()
                .date(null)
                .type(null)
                .lieu(null)
                .description(null)
                .constat(null)
                .build();

        IncidentRequestDTO dtoWithValues = IncidentRequestDTO.builder()
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .description("Description")
                .constat("constat.pdf")
                .build();

        // Then
        assertEquals(dtoWithNulls, anotherDtoWithNulls);
        assertEquals(dtoWithNulls.hashCode(), anotherDtoWithNulls.hashCode());
        assertNotEquals(dtoWithNulls, dtoWithValues);
        assertNotEquals(dtoWithNulls.hashCode(), dtoWithValues.hashCode());
    }
}