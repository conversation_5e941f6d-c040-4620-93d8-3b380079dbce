package otbs.ms_incident.enums;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Types de fichiers acceptés pour les incidents
 */
@Schema(description = "Types de fichiers gérés pour les incidents")
public enum FileType {
    @Schema(description = "Photo de l'incident (jpg, jpeg, png, gif)")
    PHOTO,

    @Schema(description = "Constat d'accident ou rapport technique (pdf)")
    CONSTAT;

    // Constants to avoid duplication across the codebase
    public static final String PHOTOS_FOLDER = "photos";
    public static final String CONSTATS_FOLDER = "constats";

    /**
     * Vérifie si la chaîne fournie correspond à un type de fichier valide
     * @param value Valeur à vérifier
     * @return Le type de fichier correspondant ou null si non valide
     */
    public static FileType fromString(String value) {
        if (value == null) {
            return null;
        }

        try {
            return FileType.valueOf(value.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    /**
     * Retourne le dossier de stockage associé au type de fichier
     * @return Le nom du dossier de stockage
     */
    public String getStorageFolder() {
        return this == PHOTO ? PHOTOS_FOLDER : CONSTATS_FOLDER;
    }

    /**
     * Vérifie si l'extension est valide pour ce type de fichier
     * @param extension Extension du fichier (sans le point)
     * @return true si l'extension est valide pour ce type
     */
    public boolean isValidExtension(String extension) {
        if (extension == null) {
            return false;
        }

        extension = extension.toLowerCase();

        // Simplifier le switch pour éviter le cas default qui n'est jamais atteint
        // puisque l'enum n'a que deux valeurs
        if (this == PHOTO) {
            return extension.equals("jpg") || extension.equals("jpeg") ||
                   extension.equals("png") || extension.equals("gif");
        } else { // CONSTAT
            return extension.equals("pdf");
        }
    }
}