package otbs.ms_incident.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.OAuthFlow;
import io.swagger.v3.oas.models.security.OAuthFlows;
import io.swagger.v3.oas.models.security.Scopes;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Configuration pour la documentation OpenAPI de l'application.
 * Cette classe configure l'interface Swagger UI qui permet de visualiser et tester
 * les endpoints de l'API REST du microservice des incidents.
 */
@Configuration
public class OpenApiConfig {

    /**
     * Nom de l'application récupéré depuis les propriétés de configuration.
     * Valeur par défaut : "ms-incident"
     */
    @Value("${spring.application.name:ms-incident}")
    private String applicationName;

    /**
     * URL du serveur Keycloak pour l'authentification OAuth2
     */
    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri:http://localhost:8080/realms/Parc-Auto}")
    private String keycloakIssuerUri;
    
    /**
     * Client ID pour l'authentification OAuth2 dans Swagger UI
     */
    @Value("${springdoc.swagger-ui.oauth.client-id:ms-incident-client}")
    private String clientId;
    
    /**
     * Client Secret pour l'authentification OAuth2 dans Swagger UI
     */
    @Value("${springdoc.swagger-ui.oauth.client-secret:}")
    private String clientSecret;

    /**
     * Configure l'objet OpenAPI qui définit la documentation de l'API.
     * 
     * @return Un objet OpenAPI configuré avec les informations du service
     */
    @Bean
    public OpenAPI customOpenAPI() {
        final String securitySchemeName = "oauth2";
        final String tokenUrl = keycloakIssuerUri + "/protocol/openid-connect/token";
        final String authUrl = keycloakIssuerUri + "/protocol/openid-connect/auth";
        
        return new OpenAPI()
                .info(new Info()
                        .title("API de Gestion des Incidents et Réparations")
                        .description("API pour la gestion des incidents et des réparations de véhicules dans le système ParcAuto. " +
                                "Cette API nécessite une authentification via Keycloak.")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("ParcAuto")
                                .email("<EMAIL>")
                                .url("https://parcauto.com"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0.html")))
                .servers(List.of(
                        new Server()
                                .url("/")
                                .description("Serveur par défaut")))
                .components(new Components()
                        .addSecuritySchemes(securitySchemeName, 
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.OAUTH2)
                                        .description("Authentification OAuth2 avec Keycloak")
                                        .flows(new OAuthFlows()
                                                .password(new OAuthFlow()
                                                        .tokenUrl(tokenUrl)
                                                        .scopes(new Scopes().addString("openid", "OpenID Connect"))
                                                )
                                                .authorizationCode(new OAuthFlow()
                                                        .authorizationUrl(authUrl)
                                                        .tokenUrl(tokenUrl)
                                                        .scopes(new Scopes().addString("openid", "OpenID Connect"))
                                                )
                                        )
                        )
                )
                .addSecurityItem(new SecurityRequirement().addList(securitySchemeName));
    }

    public String getApplicationName() {
        return applicationName;
    }

    public void setApplicationName(String applicationName) {
        this.applicationName = applicationName;
    }
}