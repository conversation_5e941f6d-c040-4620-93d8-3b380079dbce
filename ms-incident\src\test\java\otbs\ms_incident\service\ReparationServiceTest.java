package otbs.ms_incident.service;

import otbs.ms_incident.dto.ReparationRequestDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;

import otbs.ms_incident.mapper.ReparationMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class ReparationServiceTest {

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationMapper reparationMapper;

    @InjectMocks
    private ReparationServiceImpl reparationService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getAllReparations_shouldReturnMappedDTOs() {
        // Arrange
        Reparation reparation1 = new Reparation();
        reparation1.setId(1L);
        reparation1.setGarage("Garage A");

        Reparation reparation2 = new Reparation();
        reparation2.setId(2L);
        reparation2.setGarage("Garage B");

        List<Reparation> reparations = Arrays.asList(reparation1, reparation2);

        ReparationResponseDTO dto1 = new ReparationResponseDTO();
        dto1.setId(1L);
        dto1.setGarage("Garage A");

        ReparationResponseDTO dto2 = new ReparationResponseDTO();
        dto2.setId(2L);
        dto2.setGarage("Garage B");

        when(reparationRepository.findAll()).thenReturn(reparations);
        when(reparationMapper.toDTOList(reparations)).thenReturn(Arrays.asList(dto1, dto2));

        // Act
        List<ReparationResponseDTO> result = reparationService.getAllReparations();

        // Assert
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0).getId());
        assertEquals(2L, result.get(1).getId());
        assertEquals("Garage A", result.get(0).getGarage());
        assertEquals("Garage B", result.get(1).getGarage());
        verify(reparationRepository, times(1)).findAll();
        verify(reparationMapper, times(1)).toDTOList(reparations);
    }

    @Test
    void getReparationById_whenReparationExists_shouldReturnMappedDTO() {
        // Arrange
        Long id = 1L;
        Reparation reparation = new Reparation();
        reparation.setId(id);
        reparation.setGarage("Garage A");
        reparation.setCout(new BigDecimal("1200.50"));

        ReparationResponseDTO dto = new ReparationResponseDTO();
        dto.setId(id);
        dto.setGarage("Garage A");
        dto.setCout(new BigDecimal("1200.50"));

        when(reparationRepository.findById(id)).thenReturn(Optional.of(reparation));
        when(reparationMapper.toDTO(reparation)).thenReturn(dto);

        // Act
        ReparationResponseDTO result = reparationService.getReparationById(id);

        // Assert
        assertNotNull(result);
        assertEquals(id, result.getId());
        assertEquals("Garage A", result.getGarage());
        assertEquals(new BigDecimal("1200.50"), result.getCout());
        verify(reparationRepository, times(1)).findById(id);
        verify(reparationMapper, times(1)).toDTO(reparation);
    }

    @Test
    void getReparationById_whenReparationDoesNotExist_shouldThrowException() {
        // Arrange
        Long id = 999L;
        when(reparationRepository.findById(id)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> reparationService.getReparationById(id));
        verify(reparationRepository, times(1)).findById(id);
        verify(reparationMapper, never()).toDTO(any());
    }

    @Test
    void createReparation_shouldSaveAndReturnMappedDTO() {
        // Arrange
        Long incidentId = 1L;
        Incident incident = new Incident();
        incident.setId(incidentId);

        ReparationRequestDTO requestDTO = new ReparationRequestDTO();
        requestDTO.setDateReparation(LocalDate.now());
        requestDTO.setGarage("Garage A");
        requestDTO.setCout(new BigDecimal("1200.50"));
        requestDTO.setIncidentId(incidentId);

        Reparation reparation = new Reparation();
        reparation.setDateReparation(LocalDate.now());
        reparation.setGarage("Garage A");
        reparation.setCout(new BigDecimal("1200.50"));

        Reparation savedReparation = new Reparation();
        savedReparation.setId(1L);
        savedReparation.setDateReparation(LocalDate.now());
        savedReparation.setGarage("Garage A");
        savedReparation.setCout(new BigDecimal("1200.50"));
        savedReparation.setIncident(incident);

        ReparationResponseDTO responseDTO = new ReparationResponseDTO();
        responseDTO.setId(1L);
        responseDTO.setDateReparation(LocalDate.now());
        responseDTO.setGarage("Garage A");
        responseDTO.setCout(new BigDecimal("1200.50"));
        responseDTO.setIncidentId(incidentId);

        when(incidentRepository.findById(incidentId)).thenReturn(Optional.of(incident));
        when(reparationMapper.toEntity(requestDTO)).thenReturn(reparation);
        when(reparationRepository.save(reparation)).thenReturn(savedReparation);
        when(reparationMapper.toDTO(savedReparation)).thenReturn(responseDTO);

        // Act
        ReparationResponseDTO result = reparationService.createReparation(requestDTO);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("Garage A", result.getGarage());
        assertEquals(new BigDecimal("1200.50"), result.getCout());
        assertEquals(incidentId, result.getIncidentId());
        verify(incidentRepository, times(1)).findById(incidentId);
        verify(reparationMapper, times(1)).toEntity(requestDTO);
        verify(reparationRepository, times(1)).save(reparation);
        verify(reparationMapper, times(1)).toDTO(savedReparation);
    }

    @Test
    void createReparation_whenIncidentNotFound_shouldThrowException() {
        // Arrange
        Long incidentId = 999L;
        ReparationRequestDTO requestDTO = new ReparationRequestDTO();
        requestDTO.setIncidentId(incidentId);
        requestDTO.setDateReparation(LocalDate.of(2023, 5, 15)); // Ajout d'une date valide pour passer la validation
        requestDTO.setCout(new BigDecimal("100.00")); // Ajout d'un coût valide pour passer la validation

        when(incidentRepository.findById(incidentId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> reparationService.createReparation(requestDTO));
        verify(incidentRepository, times(1)).findById(incidentId);
        verify(reparationMapper, never()).toEntity(any());
        verify(reparationRepository, never()).save(any());
        verify(reparationMapper, never()).toDTO(any());
    }

    // Test supprimé car la méthode getReparationsByGarage a été supprimée
}