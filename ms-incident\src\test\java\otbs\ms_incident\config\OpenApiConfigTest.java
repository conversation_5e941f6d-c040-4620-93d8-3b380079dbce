package otbs.ms_incident.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.assertj.core.api.Assertions.assertThat;

class OpenApiConfigTest {

    private OpenApiConfig openApiConfig;

    @BeforeEach
    void setUp() {
        openApiConfig = new OpenApiConfig();
        openApiConfig.setApplicationName("ms-incident");
    }

    @Test
    void customOpenAPI_shouldConfigureOpenAPICorrectly() {
        // When
        OpenAPI openAPI = openApiConfig.customOpenAPI();

        // Then
        assertThat(openAPI).isNotNull();
        
        // Verify Info object
        Info info = openAPI.getInfo();
        assertThat(info).isNotNull();
        assertThat(info.getTitle()).isEqualTo("API de Gestion des Incidents et Réparations");
        assertThat(info.getDescription()).contains("API pour la gestion des incidents et des réparations");
        assertThat(info.getVersion()).isEqualTo("1.0.0");
        
        // Verify Contact object
        Contact contact = info.getContact();
        assertThat(contact).isNotNull();
        assertThat(contact.getName()).isEqualTo("ParcAuto");
        assertThat(contact.getEmail()).isEqualTo("<EMAIL>");
        assertThat(contact.getUrl()).isEqualTo("https://parcauto.com");
        
        // Verify License object
        License license = info.getLicense();
        assertThat(license).isNotNull();
        assertThat(license.getName()).isEqualTo("Apache 2.0");
        assertThat(license.getUrl()).isEqualTo("https://www.apache.org/licenses/LICENSE-2.0.html");
        
        // Verify Servers
        assertThat(openAPI.getServers()).isNotNull();
        assertThat(openAPI.getServers()).hasSize(1);
        Server server = openAPI.getServers().get(0);
        assertThat(server.getUrl()).isEqualTo("/");
        
        // Verify Security configurations
        assertThat(openAPI.getSecurity()).isNotEmpty();
        SecurityRequirement requirement = openAPI.getSecurity().get(0);
        assertThat(requirement.get("oauth2")).isNotNull();
        
        // Verify Security Scheme
        Components components = openAPI.getComponents();
        assertThat(components).isNotNull();
        assertThat(components.getSecuritySchemes()).containsKey("oauth2");
        
        SecurityScheme securityScheme = components.getSecuritySchemes().get("oauth2");
        assertThat(securityScheme).isNotNull();
        assertThat(securityScheme.getType()).isEqualTo(SecurityScheme.Type.OAUTH2);
    }
    
    @Test
    void getterAndSetterForApplicationName_shouldWorkCorrectly() {
        // Given
        String originalName = openApiConfig.getApplicationName();
        String newName = "test-application";
        
        // When
        openApiConfig.setApplicationName(newName);
        
        // Then
        assertThat(openApiConfig.getApplicationName()).isEqualTo(newName);
        
        // Restore original value
        openApiConfig.setApplicationName(originalName);
    }
} 