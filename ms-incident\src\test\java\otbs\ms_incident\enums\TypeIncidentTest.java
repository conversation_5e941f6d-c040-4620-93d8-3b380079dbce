package otbs.ms_incident.enums;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class TypeIncidentTest {

    @Test
    void testEnumValues() {
        // Vérifier que l'enum a exactement 2 valeurs
        assertEquals(2, TypeIncident.values().length);
        
        // Vérifier que les valeurs sont celles attendues
        assertEquals(TypeIncident.ACCIDENT, TypeIncident.valueOf("ACCIDENT"));
        assertEquals(TypeIncident.PANNE, TypeIncident.valueOf("PANNE"));
    }
    
    @Test
    void testEnumToString() {
        // Vérifier la conversion en String
        assertEquals("ACCIDENT", TypeIncident.ACCIDENT.toString());
        assertEquals("PANNE", TypeIncident.PANNE.toString());
    }
    
    @Test
    void testValueOf() {
        // Tester la méthode valueOf standard avec des valeurs valides
        assertEquals(TypeIncident.ACCIDENT, TypeIncident.valueOf("ACCIDENT"));
        assertEquals(TypeIncident.PANNE, TypeIncident.valueOf("PANNE"));
    }
    
    @Test
    void testValueOfInvalid() {
        // Tester la méthode valueOf avec une valeur invalide - doit lancer une exception
        assertThrows(IllegalArgumentException.class, () -> TypeIncident.valueOf("INVALID"));
        assertThrows(IllegalArgumentException.class, () -> TypeIncident.valueOf("accident"));
        assertThrows(NullPointerException.class, () -> TypeIncident.valueOf(null));
    }
    
    @Test
    void testOrdinal() {
        // Vérifier que les ordinals sont assignés correctement
        assertEquals(0, TypeIncident.ACCIDENT.ordinal());
        assertEquals(1, TypeIncident.PANNE.ordinal());
    }
} 