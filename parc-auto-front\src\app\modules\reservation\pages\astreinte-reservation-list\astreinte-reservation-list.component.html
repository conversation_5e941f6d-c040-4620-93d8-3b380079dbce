<!-- astreinte-reservation-list.component.html - Code complet identique aux missions -->
<div class="reservation-container">
  <app-nav-guide [currentPage]="'astreintes_à_venir'"></app-nav-guide>

  <header class="page-header">
    <h1>Gestion des astreintes à venir</h1>
  </header>

  <!-- Tableau pour desktop et tablette -->
  <div class="vehicule-list">
    <table>
      <thead>
        <tr>
          <th>
            <div class="nom_colonne">
              <p>Ordres</p><p>d'astreinte</p>
            </div>
          </th>
          <th>Consultants</th>
          <th>Niveaux</th>
          <th>Date Début</th>
          <th>Date Fin</th>
          <th>Véhicule réservé</th>
          <th>Carburant</th>
          <th>
            <div class="nom_colonne">
              <p>Badge</p><p>Télépéage</p>
            </div>
          </th>
          <th>
            <div class="nom_colonne">
              <p>Nombre</p><p>Tickets Resto</p>
            </div>
          </th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let reservation of dataSource">
          <ng-container *ngIf="hasRole('PROJECT_MANAGER')  || hasRole('DIRECTEUR_GENERAL')  || hasRole('CONSULTANT')  ||
          hasRole('RESPONSABLE_BUREAU_ORDRE')">
            <td style="width: 70px;">{{ reservation.astreinteIds.join(' et ') }}</td>
            <td>
              <div style="word-wrap: break-word; white-space: normal; max-width: 150px;">
                {{ reservation.consultantNames ? reservation.consultantNames.join(', ') : 'Non assigné' }}
              </div>
            </td>
            <td>
              <div style="word-wrap: break-word; white-space: normal; max-width: 120px;">
                {{ reservation.niveauxAstreinte ? reservation.niveauxAstreinte.join(', ') : 'Non défini' }}
              </div>
            </td>
            <td>{{ reservation.dateDebut | date:'dd/MM/yyyy' }}</td>
            <td>{{ reservation.dateFin | date:'dd/MM/yyyy' }}</td>
            <td >
              <div style="display: grid; gap: 5px; justify-items: center;">
                <div *ngIf="reservation.immatriculation" class="immatricule-container">
                  <app-immatricule [immatriculation]="reservation.immatriculation"></app-immatricule>
                </div>
                <div style="display: flex; place-items: center; gap: 15px; justify-content: center;" *ngIf="hasRole('PROJECT_MANAGER')
                    || hasRole('DIRECTEUR_GENERAL')">
                  <button class="btn-action btn-repair" title="Attribuer un vehicule" (click)="attributeVehicule(reservation)">
                    <i class="fas fa-add"></i>
                  </button>
                  <button *ngIf="reservation.immatriculation" class="btn-action btn-edit" title="Editer un vehicule" (click)="updateAttributedVehicule(reservation)">
                    <i class="fas fa-edit"></i>
                  </button>
                </div>
              </div>
            </td>
            <!-- Cellule Consommations reformulée - ALIGNÉE SUR RESERVATION-LIST -->
            <td>
              <div class="consommation-container">
                <div *ngIf="!hasConsommations(reservation.reservationId) && reservation.reservationId" class="no-consommation">
                  Aucune consommation
                </div>

                <div *ngIf="hasConsommations(reservation.reservationId)" class="consommation-badges">
                  <div *ngFor="let item of getConsommationItems(reservation.reservationId)"
                      class="consommation-badge"
                      [ngClass]="getTypeClass(item.type)">
                    <!-- Affichage simplifié pour correspondre à reservation-list -->
                    <ng-container *ngIf="item.type === 'Carte'">Carte {{ item.value }}</ng-container>
                    <ng-container *ngIf="item.type === 'Frais'">Frais: {{ item.value }} DT</ng-container>
                    <ng-container *ngIf="item.type === 'Cash'">Cash #{{ item.id }}</ng-container>
                  </div>
                </div>

                <button class="btn-action btn-repair" title="Ajouter carte carburant" (click)="addCarte(reservation)" *ngIf="(hasRole('PROJECT_MANAGER')
                    || hasRole('DIRECTEUR_GENERAL') || hasRole('RESPONSABLE_BUREAU_ORDRE')) && reservation.reservationId">
                  <i class="fas fa-add"></i>
                </button>
              </div>
            </td>
            <td>
              <div style="display: grid; gap: 5px; justify-items: center;">
                <p *ngIf="reservation.numeroBadge && reservation.reservationId" class="consommation-badge badge-cash" > N° {{ reservation.numeroBadge }}</p>
                <div *ngIf="!reservation.numeroBadge && reservation.reservationId" class="no-consommation">
                  Aucun Badge
                </div>
                <button *ngIf="!reservation.numeroBadge && (hasRole('PROJECT_MANAGER')
                    || hasRole('DIRECTEUR_GENERAL') || hasRole('RESPONSABLE_BUREAU_ORDRE')) && reservation.reservationId" class="btn-action btn-repair" title="Affecter badge télépéage" (click)="addTelepeage(reservation)">
                  <i class="fas fa-add"></i>
                </button>
                <button *ngIf="reservation.numeroBadge && (hasRole('PROJECT_MANAGER')
                    || hasRole('DIRECTEUR_GENERAL') || hasRole('RESPONSABLE_BUREAU_ORDRE')) && reservation.reservationId" class="btn-action btn-edit" title="Editer un vehicule" (click)="updateAttributedTelepeage(reservation)">
                  <i class="fas fa-edit"></i>
                </button>
              </div>
            </td>
            <td>
              <div *ngIf="reservation.nombreTicketResto">{{ reservation.nombreTicketResto }}</div>
            </td>

            <!-- Actions dans une seule cellule avec alignement horizontal -->
            <td >
              <div class="actions-cell">
                <button class="btn-action btn-view" title="Voir les détails" (click)="download(reservation)" *ngIf="reservation.reservationId">
                  <i class="fa-solid fa-download"></i>
                </button>
              </div>
            </td>
          </ng-container>
        </tr>
      </tbody>
    </table>

    <!-- Cartes pour mobile -->
    <div class="mobile-cards">
      <div class="vehicule-card" *ngFor="let reservation of dataSource">
        <div class="card-header">
          <span class="status-badge">
            {{ reservation.astreinteIds }}
          </span>
        </div>

        <div class="card-row">
          <div class="label"><i class="fas fa-users fa-fw"></i> Consultants:</div>
          <div class="value">{{ reservation.consultantNames ? reservation.consultantNames.join(', ') : 'Non assigné' }}</div>
        </div>

        <div class="card-row">
          <div class="label"><i class="fas fa-calendar-alt fa-fw"></i> Date Début:</div>
          <div class="value">{{ reservation.dateDebut | date:'dd/MM/yyyy' }}</div>
        </div>

        <div class="card-row">
          <div class="label"><i class="fas fa-cogs fa-fw"></i> Date Fin:</div>
          <div class="value">
            <span class="badge">
              {{ reservation.dateFin | date:'dd/MM/yyyy' }}
            </span>
          </div>
        </div>

        <div class="card-row">
          <div class="label"><i class="fas fa-gas-pump fa-fw"></i> Véhicule:</div>
          <div class="value">
            <span class="badge">
              {{ reservation.immatriculation }}
            </span>
          </div>
        </div>

        <div class="card-row">
          <div class="label"><i class="fas fa-credit-card fa-fw"></i> Consommations:</div>
          <div class="value">
            <!-- Affichage reformulé pour mobile - ALIGNÉ SUR RESERVATION-LIST -->
            <div class="consommation-container mobile">
              <div *ngIf="!hasConsommations(reservation.reservationId)" class="no-consommation">
                Aucune consommation
              </div>

              <div *ngIf="hasConsommations(reservation.reservationId)" class="consommation-badges">
                <div *ngFor="let item of getConsommationItems(reservation.reservationId)"
                     class="consommation-badge"
                     [ngClass]="getTypeClass(item.type)">
                  <!-- Même format simplifié que pour desktop -->
                  <ng-container *ngIf="item.type === 'Carte'">Carte {{ item.value }}</ng-container>
                  <ng-container *ngIf="item.type === 'Frais'">Frais #{{ item.id }}</ng-container>
                  <ng-container *ngIf="item.type === 'Cash'">Cash #{{ item.id }}</ng-container>
                </div>
              </div>

              <button class="btn-action btn-repair" title="Ajouter carte carburant" (click)="addCarte(reservation)">
                <i class="fas fa-add"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="card-row">
          <div class="label">Badge Télépéage:</div>
          <div class="value">
            {{ reservation.numeroBadge}}
            <div *ngIf="!reservation.numeroBadge"  class="no-consommation">
              Aucun Badge
            </div>
            <button *ngIf="!reservation.numeroBadge" class="btn-action btn-repair" title="Affecter badge télépéage" (click)="addTelepeage(reservation)">
              <i class="fas fa-add"></i>
            </button>
          </div>
        </div>

        <div class="card-row">
          <div class="label">Tickets Restaurant:</div>
          <div class="value">
            <div *ngIf="reservation.nombreTicketResto">{{ reservation.nombreTicketResto }}</div>
            <div *ngIf="!reservation.nombreTicketResto" class="no-consommation">
              Aucun ticket
            </div>
          </div>
        </div>

        <div class="card-actions">
          <button class="btn-action btn-view" title="Voir les détails">
            <i class="fa-solid fa-download"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
