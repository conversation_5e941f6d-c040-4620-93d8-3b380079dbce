package otbs.ms_incident.mapper;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IncidentMapperEdgeCasesTest {

    @InjectMocks
    private IncidentMapperImpl mapper;

    @Mock
    private ReparationMapper reparationMapper;

    @Test
    void toEntity_shouldHandleNullDTO() {
        // When
        Incident entity = mapper.toEntity(null);

        // Then
        assertNull(entity);
    }

    @Test
    void toEntity_shouldHandleNullFields() {
        // Given
        IncidentRequestDTO dto = new IncidentRequestDTO();
        // All fields are null

        // When
        Incident entity = mapper.toEntity(dto);

        // Then
        assertNotNull(entity);
        assertNull(entity.getDate());
        assertNull(entity.getType());
        assertNull(entity.getStatus());
        assertEquals(NiveauPrioriteIncident.FAIBLE, entity.getPriorite());
        assertNull(entity.getLieu());
        assertNull(entity.getDescription());
        assertNull(entity.getConstat());
        assertNull(entity.getVehiculeId());
        assertEquals("system", entity.getCreatedBy());
        assertEquals("system", entity.getUpdatedBy());
    }

    @Test
    void toResponseDTO_shouldHandleNullEntity() {
        // When
        IncidentResponseDTO dto = mapper.toResponseDTO(null);

        // Then
        assertNull(dto);
    }

    @Test
    void toResponseDTO_shouldHandleNullFields() {
        // Given
        Incident entity = new Incident();
        entity.setId(1L);
        entity.setReparations(new ArrayList<>()); // Initialize empty reparations list
        // All other fields are null

        // When
        when(reparationMapper.toDTOList(any())).thenReturn(Collections.emptyList());
        IncidentResponseDTO dto = mapper.toResponseDTO(entity);

        // Then
        assertNotNull(dto);
        assertEquals(1L, dto.getId());
        assertNull(dto.getDate());
        assertNull(dto.getType());
        assertEquals(StatusIncident.A_TRAITER, dto.getStatus());
        assertEquals(NiveauPrioriteIncident.FAIBLE, dto.getPriorite());
        assertNull(dto.getLieu());
        assertNull(dto.getDescription());
        assertNull(dto.getConstat());
        assertNull(dto.getVehiculeId());
        assertNull(dto.getCreatedAt());
        assertNull(dto.getUpdatedAt());
        assertNotNull(dto.getReparations());
        assertTrue(dto.getReparations().isEmpty());
    }

    @Test
    void toResponseDTOList_shouldHandleNullList() {
        // When
        List<IncidentResponseDTO> dtos = mapper.toResponseDTOList(null);

        // Then
        assertNull(dtos);
    }

    @Test
    void toResponseDTOList_shouldHandleEmptyList() {
        // When
        List<IncidentResponseDTO> dtos = mapper.toResponseDTOList(Collections.emptyList());

        // Then
        assertNotNull(dtos);
        assertTrue(dtos.isEmpty());
    }

    @Test
    void updateIncidentFromDTO_shouldHandleNullDTO() {
        // Given
        Incident entity = new Incident();
        entity.setId(1L);
        entity.setDate(LocalDate.of(2023, 5, 15));
        entity.setType(TypeIncident.ACCIDENT);
        entity.setStatus(StatusIncident.A_TRAITER);
        entity.setPriorite(NiveauPrioriteIncident.CRITIQUE);
        entity.setLieu("Paris");
        entity.setDescription("Description originale");
        entity.setCreatedBy("test-user");
        entity.setUpdatedBy("test-user");

        // When
        mapper.updateIncidentFromDTO(null, entity);

        // Then
        assertEquals(1L, entity.getId());
        assertEquals(LocalDate.of(2023, 5, 15), entity.getDate());
        assertEquals(TypeIncident.ACCIDENT, entity.getType());
        assertEquals(StatusIncident.A_TRAITER, entity.getStatus());
        assertEquals(NiveauPrioriteIncident.CRITIQUE, entity.getPriorite());
        assertEquals("Paris", entity.getLieu());
        assertEquals("Description originale", entity.getDescription());
        assertEquals("test-user", entity.getCreatedBy());
        assertEquals("test-user", entity.getUpdatedBy());
    }

    @Test
    void updateIncidentFromDTO_shouldHandleNullEntity() {
        // Given
        IncidentRequestDTO dto = new IncidentRequestDTO();
        dto.setDate(LocalDate.of(2023, 6, 20));
        dto.setType(TypeIncident.PANNE);
        dto.setStatus(StatusIncident.EN_COURS_TRAITEMENT);
        dto.setPriorite(NiveauPrioriteIncident.MOYEN);
        dto.setLieu("Lyon");
        dto.setDescription("Nouvelle description");

        // When/Then
        // Nous savons que cette méthode va lancer une NullPointerException, donc nous vérifions cela
        assertThrows(NullPointerException.class, () -> mapper.updateIncidentFromDTO(dto, null));
    }

    @Test
    void updateIncidentFromDTO_shouldHandleNullFields() {
        // Given
        Incident entity = new Incident();
        entity.setId(1L);
        entity.setDate(LocalDate.of(2023, 5, 15));
        entity.setType(TypeIncident.ACCIDENT);
        entity.setStatus(StatusIncident.A_TRAITER);
        entity.setPriorite(NiveauPrioriteIncident.CRITIQUE);
        entity.setLieu("Paris");
        entity.setDescription("Description originale");
        entity.setCreatedBy("test-user");
        entity.setUpdatedBy("test-user");

        IncidentRequestDTO dto = new IncidentRequestDTO();
        // All fields are null

        // When
        mapper.updateIncidentFromDTO(dto, entity);

        // Then
        assertEquals(1L, entity.getId());
        assertEquals(LocalDate.of(2023, 5, 15), entity.getDate());
        assertEquals(TypeIncident.ACCIDENT, entity.getType());
        assertEquals(StatusIncident.A_TRAITER, entity.getStatus());
        assertEquals(NiveauPrioriteIncident.FAIBLE, entity.getPriorite());
        assertEquals("Paris", entity.getLieu());
        assertEquals("Description originale", entity.getDescription());
        assertEquals("test-user", entity.getCreatedBy());
        assertEquals("system", entity.getUpdatedBy());
    }

    @Test
    void transformToApiUrl_shouldHandleNullParameters() {
        // When
        String url = mapper.transformToApiUrl(null, null, null);

        // Then
        assertEquals("/api/files/incident/null/null/null", url);
    }

    @Test
    void transformFilePaths_shouldHandleNullParameters() {
        // Given
        IncidentResponseDTO dto = new IncidentResponseDTO();
        Incident incident = null;

        // When/Then
        // Nous savons que cette méthode va lancer une NullPointerException, donc nous vérifions cela
        assertThrows(NullPointerException.class, () -> mapper.transformFilePaths(dto, incident));
    }

    @Test
    void transformFilePaths_shouldHandleNullDTO() {
        // Given
        IncidentResponseDTO dto = null;
        Incident incident = new Incident();
        incident.setId(1L);
        incident.setPhotos(Arrays.asList("photo1.jpg", "photo2.jpg"));
        incident.setConstat("constat.pdf");

        // When/Then
        // Nous savons que cette méthode va lancer une NullPointerException, donc nous vérifions cela
        assertThrows(NullPointerException.class, () -> mapper.transformFilePaths(dto, incident));
    }

    @Test
    void transformFilePaths_shouldHandleNullPhotos() {
        // Given
        IncidentResponseDTO dto = new IncidentResponseDTO();
        Incident incident = new Incident();
        incident.setId(1L);
        incident.setPhotos(null);
        incident.setConstat("constat.pdf");

        // When
        mapper.transformFilePaths(dto, incident);

        // Then
        assertNotNull(dto);
        assertEquals(Collections.emptyList(), dto.getPhotos());
        assertEquals("/api/files/incident/1/CONSTAT/constat.pdf", dto.getConstat());
    }

    @Test
    void transformFilePaths_shouldHandleNullConstat() {
        // Given
        IncidentResponseDTO dto = new IncidentResponseDTO();
        Incident incident = new Incident();
        incident.setId(1L);
        incident.setPhotos(Arrays.asList("photo1.jpg", "photo2.jpg"));
        incident.setConstat(null);

        // When
        mapper.transformFilePaths(dto, incident);

        // Then
        assertNotNull(dto);
        assertNotNull(dto.getPhotos());
        assertEquals(2, dto.getPhotos().size());
        assertEquals("/api/files/incident/1/PHOTO/photo1.jpg", dto.getPhotos().get(0));
        assertEquals("/api/files/incident/1/PHOTO/photo2.jpg", dto.getPhotos().get(1));
        assertNull(dto.getConstat());
    }


}
