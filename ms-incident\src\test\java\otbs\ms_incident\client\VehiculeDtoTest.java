package otbs.ms_incident.client;

import org.junit.jupiter.api.Test;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

class VehiculeDtoTest {

    @Test
    void testNoArgsConstructor() {
        // When
        VehiculeDto dto = new VehiculeDto();

        // Then
        assertNull(dto.getIdVehicule());
        assertNull(dto.getImmatriculation());
        assertNull(dto.getMarque());
        assertNull(dto.getModele());
        assertEquals(0, dto.getKilometrage());
        assertNull(dto.getCategorie());
        assertNull(dto.getEtat());
        assertNull(dto.getDatePrelevementKilometrage());
        assertNull(dto.getTypeCarburant());
        assertEquals(0, dto.getNombreDePlaces());
        assertFalse(dto.isRemisCles());
        assertFalse(dto.isAstreinte());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Long idVehicule = 1L;
        String immatriculation = "ABC123";
        String marque = "Renault";
        String modele = "Clio";
        int kilometrage = 50000;
        String categorie = "Berline";
        String etat = "BON";
        Date datePrelevement = new Date();
        String typeCarburant = "Essence";
        int nombreDePlaces = 5;
        boolean remisCles = true;
        boolean astreinte = false;

        // When
        VehiculeDto dto = new VehiculeDto(
                idVehicule, immatriculation, marque, modele, kilometrage, categorie,
                etat, datePrelevement, typeCarburant, nombreDePlaces, remisCles, astreinte
        );

        // Then
        assertEquals(idVehicule, dto.getIdVehicule());
        assertEquals(immatriculation, dto.getImmatriculation());
        assertEquals(marque, dto.getMarque());
        assertEquals(modele, dto.getModele());
        assertEquals(kilometrage, dto.getKilometrage());
        assertEquals(categorie, dto.getCategorie());
        assertEquals(etat, dto.getEtat());
        assertEquals(datePrelevement, dto.getDatePrelevementKilometrage());
        assertEquals(typeCarburant, dto.getTypeCarburant());
        assertEquals(nombreDePlaces, dto.getNombreDePlaces());
        assertEquals(remisCles, dto.isRemisCles());
        assertEquals(astreinte, dto.isAstreinte());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        VehiculeDto dto = new VehiculeDto();
        Long idVehicule = 1L;
        String immatriculation = "ABC123";
        String marque = "Renault";
        String modele = "Clio";
        int kilometrage = 50000;
        String categorie = "Berline";
        String etat = "BON";
        Date datePrelevement = new Date();
        String typeCarburant = "Essence";
        int nombreDePlaces = 5;
        boolean remisCles = true;
        boolean astreinte = false;

        // When
        dto.setIdVehicule(idVehicule);
        dto.setImmatriculation(immatriculation);
        dto.setMarque(marque);
        dto.setModele(modele);
        dto.setKilometrage(kilometrage);
        dto.setCategorie(categorie);
        dto.setEtat(etat);
        dto.setDatePrelevementKilometrage(datePrelevement);
        dto.setTypeCarburant(typeCarburant);
        dto.setNombreDePlaces(nombreDePlaces);
        dto.setRemisCles(remisCles);
        dto.setAstreinte(astreinte);

        // Then
        assertEquals(idVehicule, dto.getIdVehicule());
        assertEquals(immatriculation, dto.getImmatriculation());
        assertEquals(marque, dto.getMarque());
        assertEquals(modele, dto.getModele());
        assertEquals(kilometrage, dto.getKilometrage());
        assertEquals(categorie, dto.getCategorie());
        assertEquals(etat, dto.getEtat());
        assertEquals(datePrelevement, dto.getDatePrelevementKilometrage());
        assertEquals(typeCarburant, dto.getTypeCarburant());
        assertEquals(nombreDePlaces, dto.getNombreDePlaces());
        assertEquals(remisCles, dto.isRemisCles());
        assertEquals(astreinte, dto.isAstreinte());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setImmatriculation("ABC123");
        dto1.setMarque("Renault");

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setImmatriculation("ABC123");
        dto2.setMarque("Renault");

        VehiculeDto dto3 = new VehiculeDto();
        dto3.setIdVehicule(2L);
        dto3.setImmatriculation("DEF456");
        dto3.setMarque("Peugeot");

        // Then
        assertEquals(dto1, dto1); // Same object
        assertEquals(dto1, dto2); // Equal objects
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3); // Different objects
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
        assertNotEquals(null, dto1); // Null comparison
        assertNotEquals("Not a DTO", dto1); // Different class
    }

    @Test
    void testEqualsWithDifferentIdVehicule() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setImmatriculation("ABC123");

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(2L);
        dto2.setImmatriculation("ABC123");

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentImmatriculation() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setImmatriculation("ABC123");

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setImmatriculation("DEF456");

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentMarque() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setImmatriculation("ABC123");
        dto1.setMarque("Renault");

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setImmatriculation("ABC123");
        dto2.setMarque("Peugeot");

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentModele() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setModele("Clio");

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setModele("Megane");

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentKilometrage() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setKilometrage(10000);

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setKilometrage(20000);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentCategorie() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setCategorie("Berline");

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setCategorie("SUV");

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentEtat() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setEtat("BON");

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setEtat("MAUVAIS");

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentDatePrelevementKilometrage() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setDatePrelevementKilometrage(new Date(1000L));

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setDatePrelevementKilometrage(new Date(2000L));

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentTypeCarburant() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setTypeCarburant("Essence");

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setTypeCarburant("Diesel");

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentNombreDePlaces() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setNombreDePlaces(5);

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setNombreDePlaces(7);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentRemisCles() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setRemisCles(true);

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setRemisCles(false);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentAstreinte() {
        // Given
        VehiculeDto dto1 = new VehiculeDto();
        dto1.setIdVehicule(1L);
        dto1.setAstreinte(true);

        VehiculeDto dto2 = new VehiculeDto();
        dto2.setIdVehicule(1L);
        dto2.setAstreinte(false);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testHashCodeWithNullFields() {
        // Given
        VehiculeDto dto = new VehiculeDto();

        // When & Then
        assertDoesNotThrow(dto::hashCode);
    }

    @Test
    void testToString() {
        // Given
        VehiculeDto dto = new VehiculeDto();
        dto.setIdVehicule(1L);
        dto.setImmatriculation("ABC123");
        dto.setMarque("Renault");

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("idVehicule=1"));
        assertTrue(result.contains("immatriculation=ABC123"));
        assertTrue(result.contains("marque=Renault"));
    }
}
