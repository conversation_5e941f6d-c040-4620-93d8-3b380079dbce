package otbs.ms_incident.entity;

import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class IncidentTest {
    @Test
    void testIncidentRelationWithReparation() {
        // Arrange
        Incident incident = new Incident();
        incident.setId(1L);
        incident.setDescription("Test incident");

        Reparation reparation = new Reparation();
        reparation.setId(1L);
        reparation.setDescription("Test reparation");
        reparation.setIncident(incident);

        List<Reparation> reparations = new ArrayList<>();
        reparations.add(reparation);
        incident.setReparations(reparations);

        // Assert
        assertEquals(1, incident.getReparations().size());
        assertEquals(incident, incident.getReparations().get(0).getIncident());
    }

    @Test
    void testGettersAndSetters() {
        // Arrange
        Incident incident = new Incident();
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        StatusIncident status = StatusIncident.A_TRAITER;
        NiveauPrioriteIncident priorite = NiveauPrioriteIncident.CRITIQUE;
        String lieu = "Test location";
        String constat = "Test constat";
        List<String> photos = Arrays.asList("test_photo1.jpg", "test_photo2.jpg");
        String description = "Test description";

        // Act
        incident.setId(1L);
        incident.setDate(date);
        incident.setType(type);
        incident.setStatus(status);
        incident.setPriorite(priorite);
        incident.setLieu(lieu);
        incident.setConstat(constat);
        incident.setPhotos(photos);
        incident.setDescription(description);

        // Assert
        assertEquals(1L, incident.getId());
        assertEquals(date, incident.getDate());
        assertEquals(type, incident.getType());
        assertEquals(status, incident.getStatus());
        assertEquals(priorite, incident.getPriorite());
        assertEquals(lieu, incident.getLieu());
        assertEquals(constat, incident.getConstat());
        assertEquals(photos, incident.getPhotos());
        assertEquals(description, incident.getDescription());
    }

    @Test
    void testAllArgsConstructor() {
        // Arrange
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        StatusIncident status = StatusIncident.EN_COURS_TRAITEMENT;
        NiveauPrioriteIncident priorite = NiveauPrioriteIncident.MOYEN;
        String lieu = "Test location";
        String constat = "Test constat";
        List<String> photos = Arrays.asList("test_photo1.jpg", "test_photo2.jpg");
        String description = "Test description";
        List<Reparation> reparations = new ArrayList<>();

        // Act
        Incident incident = new Incident(1L, null, date, type, status, priorite, lieu, constat, photos, description, reparations);

        // Assert
        assertEquals(1L, incident.getId());
        assertEquals(date, incident.getDate());
        assertEquals(type, incident.getType());
        assertEquals(status, incident.getStatus());
        assertEquals(priorite, incident.getPriorite());
        assertEquals(lieu, incident.getLieu());
        assertEquals(constat, incident.getConstat());
        assertEquals(photos, incident.getPhotos());
        assertEquals(description, incident.getDescription());
        assertEquals(reparations, incident.getReparations());
    }

    @Test
    void testNoArgsConstructor() {
        // Act
        Incident incident = new Incident();

        // Assert
        assertNull(incident.getId());
        assertNull(incident.getDate());
        assertNull(incident.getType());
        assertEquals(StatusIncident.A_TRAITER, incident.getStatus());
        assertEquals(NiveauPrioriteIncident.FAIBLE, incident.getPriorite());
        assertNull(incident.getLieu());
        assertNull(incident.getConstat());
        assertNotNull(incident.getPhotos());
        assertTrue(incident.getPhotos().isEmpty());
        assertNull(incident.getDescription());
        assertTrue(incident.getReparations() == null || incident.getReparations().isEmpty());
    }

    @Test
    void testHandlingAllTypeIncidentEnumValues() {
        // Test for each enum value
        for (TypeIncident typeValue : TypeIncident.values()) {
            // Arrange
            Incident incident = new Incident();

            // Act
            incident.setType(typeValue);

            // Assert
            assertEquals(typeValue, incident.getType());
        }
    }

    @Test
    void testHandlingAllStatusIncidentEnumValues() {
        // Test for each enum value
        for (StatusIncident statusValue : StatusIncident.values()) {
            // Arrange
            Incident incident = new Incident();

            // Act
            incident.setStatus(statusValue);

            // Assert
            assertEquals(statusValue, incident.getStatus());
        }
    }

    @Test
    void testHandlingAllNiveauPrioriteIncidentEnumValues() {
        // Test for each enum value
        for (NiveauPrioriteIncident prioriteValue : NiveauPrioriteIncident.values()) {
            // Arrange
            Incident incident = new Incident();

            // Act
            incident.setPriorite(prioriteValue);

            // Assert
            assertEquals(prioriteValue, incident.getPriorite());
        }
    }

    @Test
    void testHasConstat_shouldReturnTrueWhenConstatExists() {
        // Arrange
        Incident incident = new Incident();
        incident.setConstat("constat.pdf");

        // Act & Assert
        assertTrue(incident.hasConstat());
    }

    @Test
    void testHasConstat_shouldReturnFalseWhenConstatIsNull() {
        // Arrange
        Incident incident = new Incident();
        incident.setConstat(null);

        // Act & Assert
        assertFalse(incident.hasConstat());
    }

    @Test
    void testHasConstat_shouldReturnFalseWhenConstatIsEmpty() {
        // Arrange
        Incident incident = new Incident();
        incident.setConstat("");

        // Act & Assert
        assertFalse(incident.hasConstat());
    }

    @Test
    void testHasPhotos_shouldReturnTrueWhenPhotosExist() {
        // Arrange
        Incident incident = new Incident();
        List<String> photos = new ArrayList<>();
        photos.add("photo1.jpg");
        incident.setPhotos(photos);

        // Act & Assert
        assertTrue(incident.hasPhotos());
    }

    @Test
    void testHasPhotos_shouldReturnFalseWhenPhotosIsNull() {
        // Arrange
        Incident incident = new Incident();
        incident.setPhotos(null);

        // Act & Assert
        assertFalse(incident.hasPhotos());
    }

    @Test
    void testHasPhotos_shouldReturnFalseWhenPhotosIsEmpty() {
        // Arrange
        Incident incident = new Incident();
        incident.setPhotos(new ArrayList<>());

        // Act & Assert
        assertFalse(incident.hasPhotos());
    }

    @Test
    void testAddPhoto_shouldAddPhotoToExistingList() {
        // Arrange
        Incident incident = new Incident();
        List<String> photos = new ArrayList<>();
        photos.add("photo1.jpg");
        incident.setPhotos(photos);

        // Act
        incident.addPhoto("photo2.jpg");

        // Assert
        assertEquals(2, incident.getPhotos().size());
        assertTrue(incident.getPhotos().contains("photo1.jpg"));
        assertTrue(incident.getPhotos().contains("photo2.jpg"));
    }

    @Test
    void testAddPhoto_shouldCreateNewListWhenPhotosIsNull() {
        // Arrange
        Incident incident = new Incident();
        incident.setPhotos(null);

        // Act
        incident.addPhoto("photo1.jpg");

        // Assert
        assertNotNull(incident.getPhotos());
        assertEquals(1, incident.getPhotos().size());
        assertTrue(incident.getPhotos().contains("photo1.jpg"));
    }

    @Test
    void testRemovePhoto_shouldRemovePhotoFromExistingList() {
        // Arrange
        Incident incident = new Incident();
        List<String> photos = new ArrayList<>();
        photos.add("photo1.jpg");
        photos.add("photo2.jpg");
        incident.setPhotos(photos);

        // Act
        boolean result = incident.removePhoto("photo1.jpg");

        // Assert
        assertTrue(result);
        assertEquals(1, incident.getPhotos().size());
        assertFalse(incident.getPhotos().contains("photo1.jpg"));
        assertTrue(incident.getPhotos().contains("photo2.jpg"));
    }

    @Test
    void testRemovePhoto_shouldReturnFalseWhenPhotoNotFound() {
        // Arrange
        Incident incident = new Incident();
        List<String> photos = new ArrayList<>();
        photos.add("photo1.jpg");
        incident.setPhotos(photos);

        // Act
        boolean result = incident.removePhoto("nonexistent.jpg");

        // Assert
        assertFalse(result);
        assertEquals(1, incident.getPhotos().size());
        assertTrue(incident.getPhotos().contains("photo1.jpg"));
    }

    @Test
    void testRemovePhoto_shouldReturnFalseWhenPhotosIsNull() {
        // Arrange
        Incident incident = new Incident();
        incident.setPhotos(null);

        // Act
        boolean result = incident.removePhoto("photo1.jpg");

        // Assert
        assertFalse(result);
    }

    @Test
    void testVehiculeId_shouldSetAndGetCorrectly() {
        // Arrange
        Incident incident = new Incident();
        Long vehiculeId = 123L;

        // Act
        incident.setVehiculeId(vehiculeId);

        // Assert
        assertEquals(vehiculeId, incident.getVehiculeId());
    }
}
