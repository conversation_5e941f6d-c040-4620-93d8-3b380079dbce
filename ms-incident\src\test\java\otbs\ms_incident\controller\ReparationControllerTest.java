package otbs.ms_incident.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import otbs.ms_incident.commun.PageResponse;
import otbs.ms_incident.dto.ReparationRequestDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.enums.StatusReparation;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.service.ReparationService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReparationControllerTest {

    @Mock
    private ReparationService reparationService;

    @InjectMocks
    private ReparationController controller;

    private ReparationRequestDTO requestDTO;
    private ReparationResponseDTO responseDTO;
    private List<ReparationResponseDTO> responseDTOs;

    @BeforeEach
    void setUp() {
        requestDTO = new ReparationRequestDTO();
        requestDTO.setDateReparation(LocalDate.of(2023, 5, 15));
        requestDTO.setStatus(StatusReparation.EN_COURS);
        requestDTO.setDescription("Réparation test");
        requestDTO.setCout(new BigDecimal("1000.00"));
        requestDTO.setGarage("Garage Test");
        requestDTO.setIncidentId(1L);

        responseDTO = new ReparationResponseDTO();
        responseDTO.setId(1L);
        responseDTO.setDateReparation(LocalDate.of(2023, 5, 15));
        responseDTO.setStatus(StatusReparation.EN_COURS);
        responseDTO.setDescription("Réparation test");
        responseDTO.setCout(new BigDecimal("1000.00"));
        responseDTO.setGarage("Garage Test");
        responseDTO.setIncidentId(1L);

        ReparationResponseDTO responseDTO2 = new ReparationResponseDTO();
        responseDTO2.setId(2L);
        responseDTO2.setDateReparation(LocalDate.of(2023, 6, 20));
        responseDTO2.setStatus(StatusReparation.TERMINEE);
        responseDTO2.setDescription("Autre réparation");
        responseDTO2.setCout(new BigDecimal("2000.00"));
        responseDTO2.setGarage("Autre Garage");
        responseDTO2.setIncidentId(1L);

        responseDTOs = Arrays.asList(responseDTO, responseDTO2);
    }

    @Test
    void createReparation_shouldReturnCreatedReparation() {
        // Given
        when(reparationService.createReparation(any(ReparationRequestDTO.class))).thenReturn(responseDTO);

        // When
        ResponseEntity<ReparationResponseDTO> response = controller.createReparation(requestDTO);

        // Then
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertEquals(responseDTO, response.getBody());
    }

    @Test
    void getReparationById_shouldReturnReparation() {
        // Given
        when(reparationService.getReparationById(1L)).thenReturn(responseDTO);

        // When
        ResponseEntity<ReparationResponseDTO> response = controller.getReparationById(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(responseDTO, response.getBody());
    }

    @Test
    void getReparationById_shouldReturnNotFound_whenReparationDoesNotExist() {
        // Given
        when(reparationService.getReparationById(999L)).thenThrow(new ResourceNotFoundException("Reparation not found"));

        // When/Then
        try {
            ResponseEntity<ReparationResponseDTO> response = controller.getReparationById(999L);
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        } catch (ResourceNotFoundException e) {
            // This is also acceptable if the controller doesn't catch the exception
            assertEquals("Reparation not found", e.getMessage());
        }
    }

    @Test
    void getAllReparations_shouldReturnAllReparations() {
        // Given
        when(reparationService.getAllReparations()).thenReturn(responseDTOs);

        // When
        ResponseEntity<List<ReparationResponseDTO>> response = controller.getAllReparations();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(responseDTOs, response.getBody());
        assertEquals(2, response.getBody().size());
    }

    @Test
    void getAllReparationsPaginated_shouldReturnPagedReparations() {
        // Given
        PageResponse<ReparationResponseDTO> pageResponse = new PageResponse<>();
        pageResponse.setContent(responseDTOs);
        pageResponse.setTotalElements(2);
        pageResponse.setTotalPages(1);
        pageResponse.setNumber(0);
        pageResponse.setSize(10);

        when(reparationService.getAllReparationsPaginated(any(Pageable.class))).thenReturn(pageResponse);

        // When
        ResponseEntity<PageResponse<ReparationResponseDTO>> response = controller.getAllReparationsPaginated(0, 10, "id", "asc");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(pageResponse, response.getBody());
        assertEquals(2, response.getBody().getContent().size());
    }

    @Test
    void updateReparation_shouldReturnUpdatedReparation() {
        // Given
        when(reparationService.updateReparation(eq(1L), any(ReparationRequestDTO.class))).thenReturn(responseDTO);

        // When
        ResponseEntity<ReparationResponseDTO> response = controller.updateReparation(1L, requestDTO);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(responseDTO, response.getBody());
    }

    @Test
    void updateReparation_shouldReturnNotFound_whenReparationDoesNotExist() {
        // Given
        when(reparationService.updateReparation(eq(999L), any(ReparationRequestDTO.class)))
            .thenThrow(new ResourceNotFoundException("Reparation not found"));

        // When/Then
        try {
            ResponseEntity<ReparationResponseDTO> response = controller.updateReparation(999L, requestDTO);
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        } catch (ResourceNotFoundException e) {
            // This is also acceptable if the controller doesn't catch the exception
            assertEquals("Reparation not found", e.getMessage());
        }
    }

    @Test
    void deleteReparation_shouldReturnNoContent() {
        // Given
        doNothing().when(reparationService).deleteReparation(1L);

        // When
        ResponseEntity<Void> response = controller.deleteReparation(1L);

        // Then
        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        verify(reparationService).deleteReparation(1L);
    }

    @Test
    void deleteReparation_shouldReturnNotFound_whenReparationDoesNotExist() {
        // Given
        doThrow(new ResourceNotFoundException("Reparation not found"))
            .when(reparationService).deleteReparation(999L);

        // When/Then
        try {
            ResponseEntity<Void> response = controller.deleteReparation(999L);
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        } catch (ResourceNotFoundException e) {
            // This is also acceptable if the controller doesn't catch the exception
            assertEquals("Reparation not found", e.getMessage());
        }
    }

    @Test
    void getReparationsByIncidentId_shouldReturnReparations() {
        // Given
        when(reparationService.getReparationsByIncidentId(1L)).thenReturn(responseDTOs);

        // When
        ResponseEntity<List<ReparationResponseDTO>> response = controller.getReparationsByIncidentId(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(responseDTOs, response.getBody());
        assertEquals(2, response.getBody().size());
    }

    @Test
    void getReparationsByIncidentIdPaginated_shouldReturnPagedReparations() {
        // Given
        PageResponse<ReparationResponseDTO> pageResponse = new PageResponse<>();
        pageResponse.setContent(responseDTOs);
        pageResponse.setTotalElements(2);
        pageResponse.setTotalPages(1);
        pageResponse.setNumber(0);
        pageResponse.setSize(10);

        when(reparationService.getReparationsByIncidentIdPaginated(eq(1L), any(Pageable.class))).thenReturn(pageResponse);

        // When
        ResponseEntity<PageResponse<ReparationResponseDTO>> response = controller.getReparationsByIncidentIdPaginated(1L, 0, 10, "id", "asc");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(pageResponse, response.getBody());
        assertEquals(2, response.getBody().getContent().size());
    }

    @Test
    void updateReparationStatus_shouldReturnUpdatedReparation() {
        // Given
        when(reparationService.updateReparationStatus(1L, StatusReparation.TERMINEE)).thenReturn(responseDTO);

        // When
        ResponseEntity<ReparationResponseDTO> response = controller.updateReparationStatus(1L, StatusReparation.TERMINEE);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(responseDTO, response.getBody());
    }

    @Test
    void getAverageCost_shouldReturnAverageCost() {
        // Given
        when(reparationService.getAverageCost()).thenReturn(new BigDecimal("1500.00"));

        // When
        ResponseEntity<Map<String, BigDecimal>> response = controller.getAverageCost();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(new BigDecimal("1500.00"), response.getBody().get("value"));
    }

    @Test
    void getRepairCountByStatus_shouldReturnStatusCounts() {
        // Given
        Map<String, Long> statusCounts = Map.of(
            "EN_COURS", 1L,
            "TERMINEE", 1L
        );
        when(reparationService.getRepairCountByStatus()).thenReturn(statusCounts);

        // When
        ResponseEntity<Map<String, Long>> response = controller.getRepairCountByStatus();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().get("EN_COURS"));
        assertEquals(1L, response.getBody().get("TERMINEE"));
    }

    @Test
    void searchReparations_shouldReturnFilteredReparations() {
        // Given
        when(reparationService.searchReparations(
            any(StatusReparation.class), anyLong(), any(LocalDate.class), any(LocalDate.class),
            any(BigDecimal.class), any(BigDecimal.class), anyString()
        )).thenReturn(responseDTOs);

        // When
        ResponseEntity<List<ReparationResponseDTO>> response = controller.searchReparations(
            StatusReparation.EN_COURS, 1L, LocalDate.of(2023, 1, 1), LocalDate.of(2023, 12, 31),
            new BigDecimal("500.00"), new BigDecimal("2000.00"), "Garage"
        );

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(responseDTOs, response.getBody());
        assertEquals(2, response.getBody().size());
    }
}
