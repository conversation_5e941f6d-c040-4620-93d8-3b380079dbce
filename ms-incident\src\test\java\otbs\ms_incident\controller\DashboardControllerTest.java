package otbs.ms_incident.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.dto.dashboard.*;
import otbs.ms_incident.service.DashboardService;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DashboardControllerTest {

    @Mock
    private DashboardService dashboardService;

    @InjectMocks
    private DashboardController dashboardController;

    private LocalDate debut;
    private LocalDate fin;
    private Long vehiculeId;

    @BeforeEach
    void setUp() {
        debut = LocalDate.of(2023, 1, 1);
        fin = LocalDate.of(2023, 12, 31);
        vehiculeId = 1L;
    }

    @Test
    void getDashboardSummary_shouldReturnDashboardSummary() {
        // Given
        DashboardSummaryDTO summaryDTO = new DashboardSummaryDTO();
        summaryDTO.setTotalIncidents(10);
        summaryDTO.setOpenIncidents(5);
        summaryDTO.setTotalReparations(8);
        summaryDTO.setTotalCost(BigDecimal.valueOf(1000));

        when(dashboardService.getDashboardSummary(debut, fin, vehiculeId)).thenReturn(summaryDTO);

        // When
        ResponseEntity<DashboardSummaryDTO> response = dashboardController.getDashboardSummary(debut, fin, vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(summaryDTO, response.getBody());
        verify(dashboardService).getDashboardSummary(debut, fin, vehiculeId);
    }

    @Test
    void getRecentIncidents_shouldReturnRecentIncidents() {
        // Given
        List<IncidentResponseDTO> incidents = Arrays.asList(
                new IncidentResponseDTO(),
                new IncidentResponseDTO()
        );

        when(dashboardService.getRecentIncidents(debut, fin, vehiculeId)).thenReturn(incidents);

        // When
        ResponseEntity<List<IncidentResponseDTO>> response = dashboardController.getRecentIncidents(debut, fin, vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(incidents, response.getBody());
        assertEquals(2, response.getBody().size());
        verify(dashboardService).getRecentIncidents(debut, fin, vehiculeId);
    }

    @Test
    void getRecentRepairs_shouldReturnRecentRepairs() {
        // Given
        List<ReparationResponseDTO> repairs = Arrays.asList(
                new ReparationResponseDTO(),
                new ReparationResponseDTO()
        );

        when(dashboardService.getRecentRepairs(debut, fin, vehiculeId)).thenReturn(repairs);

        // When
        ResponseEntity<List<ReparationResponseDTO>> response = dashboardController.getRecentRepairs(debut, fin, vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(repairs, response.getBody());
        assertEquals(2, response.getBody().size());
        verify(dashboardService).getRecentRepairs(debut, fin, vehiculeId);
    }

    @Test
    void getIncidentStatusDistribution_shouldReturnIncidentStatusDistribution() {
        // Given
        IncidentStatusDistributionDTO distributionDTO = new IncidentStatusDistributionDTO(5, 3, 2);

        when(dashboardService.getIncidentStatusDistribution(debut, fin, vehiculeId)).thenReturn(distributionDTO);

        // When
        ResponseEntity<IncidentStatusDistributionDTO> response = dashboardController.getIncidentStatusDistribution(debut, fin, vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(distributionDTO, response.getBody());
        verify(dashboardService).getIncidentStatusDistribution(debut, fin, vehiculeId);
    }

    @Test
    void getIncidentTypeDistribution_shouldReturnIncidentTypeDistribution() {
        // Given
        Map<String, Long> countByType = new HashMap<>();
        countByType.put("ACCIDENT", 5L);
        countByType.put("PANNE", 3L);
        IncidentTypeDistributionDTO distributionDTO = new IncidentTypeDistributionDTO(countByType);

        when(dashboardService.getIncidentTypeDistribution(debut, fin, vehiculeId)).thenReturn(distributionDTO);

        // When
        ResponseEntity<IncidentTypeDistributionDTO> response = dashboardController.getIncidentTypeDistribution(debut, fin, vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(distributionDTO, response.getBody());
        verify(dashboardService).getIncidentTypeDistribution(debut, fin, vehiculeId);
    }

    @Test
    void getVehicleIncidentDistribution_shouldReturnVehicleIncidentDistribution() {
        // Given
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);
        countByVehicle.put(2L, 3L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        VehicleIncidentDistributionDTO distributionDTO = new VehicleIncidentDistributionDTO(countByVehicle, vehicleImmatriculations);

        when(dashboardService.getVehicleIncidentDistribution(debut, fin, vehiculeId)).thenReturn(distributionDTO);

        // When
        ResponseEntity<VehicleIncidentDistributionDTO> response = dashboardController.getVehicleIncidentDistribution(debut, fin, vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(distributionDTO, response.getBody());
        verify(dashboardService).getVehicleIncidentDistribution(debut, fin, vehiculeId);
    }

    @Test
    void getVehicleIncidentTypeDistribution_shouldReturnVehicleIncidentTypeDistribution() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        vehicle1Types.put("PANNE", 2L);
        countByVehicleAndType.put(1L, vehicle1Types);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentTypeDistributionDTO distributionDTO = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, vehicleImmatriculations);

        when(dashboardService.getVehicleIncidentTypeDistribution(debut, fin, vehiculeId)).thenReturn(distributionDTO);

        // When
        ResponseEntity<VehicleIncidentTypeDistributionDTO> response = dashboardController.getVehicleIncidentTypeDistribution(debut, fin, vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(distributionDTO, response.getBody());
        verify(dashboardService).getVehicleIncidentTypeDistribution(debut, fin, vehiculeId);
    }

    @Test
    void getRepairCostByVehicle_shouldReturnRepairCostByVehicle() {
        // Given
        Map<Long, Map<String, BigDecimal>> costByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Costs = new HashMap<>();
        vehicle1Costs.put("TOTAL_COST", BigDecimal.valueOf(1000));
        Map<String, BigDecimal> vehicle2Costs = new HashMap<>();
        vehicle2Costs.put("TOTAL_COST", BigDecimal.valueOf(1500));
        costByVehicle.put(1L, vehicle1Costs);
        costByVehicle.put(2L, vehicle2Costs);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        RepairCostByVehicleDTO costDTO = new RepairCostByVehicleDTO(costByVehicle, vehicleImmatriculations);

        when(dashboardService.getRepairCostByVehicle(debut, fin, vehiculeId)).thenReturn(costDTO);

        // When
        ResponseEntity<RepairCostByVehicleDTO> response = dashboardController.getRepairCostByVehicle(debut, fin, vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(costDTO, response.getBody());
        verify(dashboardService).getRepairCostByVehicle(debut, fin, vehiculeId);
    }

    @Test
    void getInsuranceCoverage_shouldReturnInsuranceCoverage() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        vehicle1Coverage.put("COVERED_AMOUNT", BigDecimal.valueOf(800));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        InsuranceCoverageDTO coverageDTO = new InsuranceCoverageDTO(insuranceCoverageByVehicle, vehicleImmatriculations);

        when(dashboardService.getInsuranceCoverage(debut, fin, vehiculeId)).thenReturn(coverageDTO);

        // When
        ResponseEntity<InsuranceCoverageDTO> response = dashboardController.getInsuranceCoverage(debut, fin, vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(coverageDTO, response.getBody());
        verify(dashboardService).getInsuranceCoverage(debut, fin, vehiculeId);
    }
}
