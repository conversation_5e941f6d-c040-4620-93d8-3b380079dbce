package otbs.ms_mission.client_mock;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Contrôleur REST pour la gestion des projets.
 */
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Tag(name = "Projets", description = "API pour la gestion des projets")
public class ProjectController {

    private final ProjectService projectService;

    /**
     * Récupère tous les projets.
     *
     * @return Liste de tous les projets
     */
    @GetMapping("/projects")
    @Operation(summary = "Récupère tous les projets", description = "Récupère la liste de tous les projets disponibles")
    public ResponseEntity<List<ProjectDTO>> getAllProjects() {
        return ResponseEntity.ok(projectService.getAllProjects());
    }

    /**
     * Récupère un projet par son ID.
     *
     * @param id L'ID du projet
     * @return Le projet correspondant
     */
    @GetMapping("/projects/{id}")
    @Operation(summary = "Récupère un projet par son ID", description = "Récupère les détails d'un projet spécifique")
    public ResponseEntity<ProjectDTO> getProjectById(@PathVariable Integer id) {
        return ResponseEntity.ok(projectService.getProjectById(id));
    }

    /**
     * Récupère les projets filtrés par préfixes CT et/ou AF avec recherche optionnelle.
     *
     * @param prefixes Liste des préfixes à inclure (CT, AF, ou les deux)
     * @param search Chaîne de caractères à rechercher dans le nom du projet (optionnel)
     * @return Liste des projets correspondants
     */
    @GetMapping("/projects/by-prefixes")
    @Operation(summary = "Récupère les projets par préfixes et recherche",
               description = "Récupère les projets dont le nom CONTIENT CT ou AF suivi d'un nombre (n'importe où dans le nom), " +
                           "avec possibilité de filtrer par une chaîne de caractères. " +
                           "Paramètres: prefixes (CT, AF, ou les deux), search (chaîne à rechercher dans le nom). " +
                           "Exemples: 'AFH-CT00198' sera trouvé avec prefixes=CT, 'AF05278_APPEL' sera trouvé avec prefixes=AF.")
    public ResponseEntity<List<ProjectDTO>> getProjectsByPrefixes(
            @RequestParam(value = "prefixes", required = false) List<String> prefixes,
            @RequestParam(value = "search", required = false) String search) {
        return ResponseEntity.ok(projectService.getProjectsByPrefixes(prefixes, search));
    }
}