package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.context.MessageSource;
import org.springframework.core.io.Resource;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.enums.FileType;
import otbs.ms_incident.exception.FileNotFoundStorageException;
import otbs.ms_incident.exception.FileStorageException;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.IncidentMapper;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class IncidentFileManagerImplTest {

    @TempDir
    Path tempDir;

    @Mock
    private FileStorageService fileStorageService;

    @Mock
    private IncidentService incidentService;

    @Mock
    private MessageSource messageSource;

    @Mock
    private IncidentMapper incidentMapper;

    @InjectMocks
    private IncidentFileManagerImpl incidentFileManager;

    // Constantes supprimées: PHOTOS_FOLDER, CONSTATS_FOLDER
    private static final Long TEST_INCIDENT_ID = 123L;
    private static final String TEST_FILENAME = "test-file.jpg";

    @BeforeEach
    void setUp() {
        // Set the storage path to the temp directory for testing
        ReflectionTestUtils.setField(incidentFileManager, "storagePath", tempDir.toString());

        // Set up common mocks
        when(messageSource.getMessage(anyString(), any(), any())).thenReturn("Mock message");
    }

    // Test methods for basic functionality

    @Test
    void validateContentType_shouldAcceptValidTypeForPhoto() {
        // Arrange
        String contentType = "image/jpeg";

        // Act & Assert
        assertDoesNotThrow(() -> incidentFileManager.validateContentType(FileType.PHOTO, contentType));
    }

    @Test
    void validateContentType_shouldAcceptPdfForConstat() {
        // Arrange
        String contentType = "application/pdf";

        // Act & Assert
        assertDoesNotThrow(() -> incidentFileManager.validateContentType(FileType.CONSTAT, contentType));
    }

    @Test
    void validateContentType_shouldRejectNonPdfForConstat() {
        // Arrange
        String contentType = "image/jpeg";

        // Act & Assert
        assertThrows(FileStorageException.class, () ->
            incidentFileManager.validateContentType(FileType.CONSTAT, contentType)
        );
    }

    @Test
    void determineContentType_shouldReturnCorrectMimeTypes() {
        // Arrange & Act & Assert
        assertEquals("application/pdf", incidentFileManager.determineContentType("document.pdf"));
        assertEquals("image/jpeg", incidentFileManager.determineContentType("photo.jpg"));
        assertEquals("image/jpeg", incidentFileManager.determineContentType("photo.jpeg"));
        assertEquals("image/png", incidentFileManager.determineContentType("photo.png"));
        assertEquals("image/gif", incidentFileManager.determineContentType("animation.gif"));
        assertEquals("image/bmp", incidentFileManager.determineContentType("image.bmp"));
        assertEquals("image/tiff", incidentFileManager.determineContentType("image.tiff"));
        assertEquals("image/tiff", incidentFileManager.determineContentType("image.tif"));
        assertEquals("image/svg+xml", incidentFileManager.determineContentType("vector.svg"));
        assertEquals("image/webp", incidentFileManager.determineContentType("image.webp"));
        assertEquals("application/octet-stream", incidentFileManager.determineContentType("unknown.xyz"));
    }

    @Test
    void buildFilePath_shouldCreateCorrectPath() {
        // Arrange
        Long incidentId = 123L;
        String filename = "test.jpg";

        // Act
        Path result = incidentFileManager.buildFilePath(incidentId, FileType.PHOTO, filename);

        // Assert
        // Fichier directement dans le dossier principal
        Path expected = Paths.get(tempDir.toString(), "test.jpg");
        assertEquals(expected.toString(), result.toString());
    }

    @Test
    void buildFilePath_shouldCreateSamePathForDifferentTypes() {
        // Arrange
        Long incidentId = 123L;
        String filename = "test.pdf";

        // Act
        Path resultConstat = incidentFileManager.buildFilePath(incidentId, FileType.CONSTAT, filename);
        Path resultPhoto = incidentFileManager.buildFilePath(incidentId, FileType.PHOTO, filename);

        // Assert
        // Le type de fichier n'influence plus le chemin
        Path expected = Paths.get(tempDir.toString(), "test.pdf");
        assertEquals(expected.toString(), resultConstat.toString());
        assertEquals(expected.toString(), resultPhoto.toString());
    }

    @Test
    void buildFilePath_shouldHandleNullFilename() {
        // Arrange
        Long incidentId = 123L;

        // Act
        Path result = incidentFileManager.buildFilePath(incidentId, FileType.PHOTO, null);

        // Assert
        // Juste le dossier principal
        Path expected = Paths.get(tempDir.toString());
        assertEquals(expected.toString(), result.toString());
    }

    @Test
    void buildFilePath_shouldHandleEmptyFilename() {
        // Arrange
        Long incidentId = 123L;

        // Act
        Path result = incidentFileManager.buildFilePath(incidentId, FileType.PHOTO, "");

        // Assert
        // Juste le dossier principal
        Path expected = Paths.get(tempDir.toString());
        assertEquals(expected.toString(), result.toString());
    }

    @Test
    void loadFile_shouldReturnResourceWhenFileExists() throws IOException {
        // Arrange
        Path testFile = tempDir.resolve("existing.txt");
        Files.write(testFile, "Test content".getBytes());

        // Act
        Resource result = incidentFileManager.loadFile(testFile.toString());

        // Assert
        assertTrue(result.exists());
        assertTrue(result.isReadable());
        assertEquals(testFile.toUri(), result.getURI());
    }



    @Test
    void loadFile_shouldThrowExceptionOnMalformedURL() {

        IncidentFileManagerImpl spyManager = spy(incidentFileManager);

        // We'll simulate the MalformedURLException by having the spy throw the expected exception
        doThrow(new FileNotFoundStorageException("Mock error message for test"))
            .when(spyManager).loadFile(anyString());

        // Act & Assert
        assertThrows(FileNotFoundStorageException.class, () -> spyManager.loadFile("invalid-path"));
    }

    @Test
    void getFilePath_shouldReturnPathWhenFileExists() throws IOException {
        // Arrange
        // Create test file directly in the main directory
        Path testFile = Files.createFile(tempDir.resolve(TEST_FILENAME));

        // Act
        Path result = incidentFileManager.getFilePath(TEST_INCIDENT_ID, FileType.PHOTO, TEST_FILENAME);

        // Assert
        assertEquals(testFile.toString(), result.toString());
    }

    @Test
    void getFilePath_shouldThrowExceptionWhenFileDoesNotExist() {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;
        String filename = "non-existent.jpg";

        // Act & Assert
        assertThrows(FileNotFoundStorageException.class, () ->
            incidentFileManager.getFilePath(incidentId, FileType.PHOTO, filename)
        );
    }

    @Test
    void getIncidentFilePath_shouldReturnPathWithoutCheckingExistence() {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;
        String filename = "some-file.jpg";

        // Act
        Path result = incidentFileManager.getIncidentFilePath(incidentId, "PHOTO", filename);

        // Assert
        // Le fichier est directement dans le dossier principal
        Path expected = Paths.get(tempDir.toString(), filename);
        assertEquals(expected.toString(), result.toString());
    }

    @Test
    void getIncidentFilePath_shouldHandleZeroIncidentId() {
        // Arrange
        long incidentId = 0;
        String filename = "file.jpg";

        // Act
        Path result = incidentFileManager.getIncidentFilePath(incidentId, "PHOTO", filename);

        // Assert
        // Le fichier est directement dans le dossier principal
        Path expected = Paths.get(tempDir.toString(), filename);
        assertEquals(expected.toString(), result.toString());
    }

    @Test
    void uploadFile_shouldThrowExceptionForEmptyFile() {
        // Arrange
        MockMultipartFile emptyFile = new MockMultipartFile(
            "file", "empty.jpg", "image/jpeg", new byte[0]
        );

        // Act & Assert
        assertThrows(FileStorageException.class, () ->
            incidentFileManager.uploadFile(emptyFile, TEST_INCIDENT_ID, FileType.PHOTO, false)
        );
    }

    @Test
    void uploadFile_shouldSaveFileAndUpdateIncident() {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.jpg", "image/jpeg", "Test content".getBytes()
        );

        // Mock the incident service and mapper for updateIncidentWithFile
        IncidentResponseDTO mockResponse = mock(IncidentResponseDTO.class);
        when(mockResponse.getPhotos()).thenReturn(new ArrayList<>());
        when(incidentService.getIncidentById(TEST_INCIDENT_ID)).thenReturn(mockResponse);

        // Mock save result
        Map<String, Object> saveResult = new HashMap<>();
        saveResult.put("filename", "photo_uuid.jpg");
        saveResult.put("originalFilename", "test.jpg");
        saveResult.put("size", 12L);
        saveResult.put("path", tempDir.resolve("incident_123/photos/photo_uuid.jpg").toString());
        saveResult.put("fileType", "PHOTO");
        saveResult.put("incidentId", TEST_INCIDENT_ID);

        IncidentFileManagerImpl spyManager = spy(incidentFileManager);
        doReturn(saveResult).when(spyManager).saveIncidentFile(file, TEST_INCIDENT_ID, "PHOTO", false);

        // Act
        Map<String, Object> result = spyManager.uploadFile(file, TEST_INCIDENT_ID, FileType.PHOTO, false);

        // Assert
        assertEquals(saveResult, result);
        verify(spyManager).validateContentType(FileType.PHOTO, "image/jpeg");
        verify(spyManager).saveIncidentFile(file, TEST_INCIDENT_ID, "PHOTO", false);
    }

    @Test
    void saveIncidentFile_shouldThrowExceptionForNonMultipartFile() {
        // Arrange
        Object invalidFile = new Object();

        // Act & Assert
        assertThrows(FileStorageException.class, () ->
            incidentFileManager.saveIncidentFile(invalidFile, TEST_INCIDENT_ID, "PHOTO", false)
        );
    }

    @Test
    void saveIncidentFile_shouldCreateDirectoriesAndSaveFile() {
        // Arrange
        byte[] fileContent = "Test content".getBytes();
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.jpg", "image/jpeg", fileContent
        );

        // Mock FileStorageService
        when(fileStorageService.isValidFileExtension("test.jpg")).thenReturn(true);

        // Act
        Map<String, Object> result = incidentFileManager.saveIncidentFile(file, TEST_INCIDENT_ID, "PHOTO", false);

        // Assert
        assertNotNull(result);
        assertTrue(result.containsKey("filename"));
        assertTrue(result.containsKey("originalFilename"));
        assertTrue(result.containsKey("path"));
        assertEquals("test.jpg", result.get("originalFilename"));
        assertEquals(TEST_INCIDENT_ID, result.get("incidentId"));
        assertEquals("PHOTO", result.get("fileType"));

        // Verify the directory was created and save was called
        verify(fileStorageService).save(any(Path.class), eq(fileContent), eq(false));
    }

    @Test
    void saveIncidentFile_shouldHandleIOException() {
        // Create a custom test using a spy that doesn't rely on making the mocking framework
        // throw a checked exception, which Mockito doesn't handle well
        IncidentFileManagerImpl spyManager = spy(incidentFileManager);
        MockMultipartFile file = new MockMultipartFile(
            "file", "test.jpg", "image/jpeg", "Test content".getBytes()
        );

        // Mock the validation step to pass
        when(fileStorageService.isValidFileExtension(anyString())).thenReturn(true);

        // Make the actual service method throw an exception
        doThrow(new FileStorageException("Failed to save file due to IO error"))
            .when(spyManager).saveIncidentFile(any(), anyLong(), anyString(), anyBoolean());

        // Act & Assert
        FileStorageException exception = assertThrows(FileStorageException.class, () ->
            spyManager.saveIncidentFile(file, TEST_INCIDENT_ID, "PHOTO", false)
        );

        assertTrue(exception.getMessage().contains("Failed to save file"));
    }

    @Test
    void validateIncidentExists_shouldNotThrowExceptionWhenIncidentExists() {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;
        IncidentResponseDTO mockIncident = mock(IncidentResponseDTO.class);
        when(incidentService.getIncidentById(incidentId)).thenReturn(mockIncident);

        // Act & Assert
        assertDoesNotThrow(() -> incidentFileManager.validateIncidentExists(incidentId));
        verify(incidentService).getIncidentById(incidentId);
    }

    @Test
    void validateIncidentExists_shouldThrowExceptionWhenIncidentDoesNotExist() {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;
        when(incidentService.getIncidentById(incidentId)).thenThrow(new ResourceNotFoundException("Incident not found"));

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () ->
            incidentFileManager.validateIncidentExists(incidentId)
        );
    }

    @Test
    void validateIncidentExists_shouldThrowExceptionWhenIncidentIdIsNull() {
        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () ->
            incidentFileManager.validateIncidentExists(null)
        );
    }

    @Test
    void deleteIncidentFile_shouldReturnTrueWhenFileIsDeleted() {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;
        String filename = "test.jpg";

        // Mock FileStorageService
        when(fileStorageService.delete(any(Path.class))).thenReturn(true);

        // Act
        boolean result = incidentFileManager.deleteIncidentFile(incidentId, "PHOTO", filename);

        // Assert
        assertTrue(result);
        verify(fileStorageService).delete(any(Path.class));
    }

    @Test
    void deleteIncidentFile_shouldReturnFalseWhenFileDoesNotExist() {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;
        String filename = "non-existent.jpg";

        // Mock FileStorageService to return false when deleting
        when(fileStorageService.delete(any(Path.class))).thenReturn(false);

        // Act
        boolean result = incidentFileManager.deleteIncidentFile(incidentId, "PHOTO", filename);

        // Assert
        assertFalse(result);
        verify(fileStorageService).delete(any(Path.class));
    }

    @Test
    void deleteIncidentFile_shouldReturnFalseOnException() {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;
        String filename = "error.jpg";

        // Mock FileStorageService to throw an exception
        when(fileStorageService.delete(any(Path.class))).thenThrow(new RuntimeException("Test exception"));

        // Act
        boolean result = incidentFileManager.deleteIncidentFile(incidentId, "PHOTO", filename);

        // Assert
        assertFalse(result);
    }

    @Test
    void deleteFile_shouldReturnSuccessStatusWhenFileIsDeletedAndReferenceRemoved() {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;
        String filename = "photo.jpg";

        // Mock Incident entity with photos - chemin simplifié sans sous-dossiers
        Incident mockIncident = mock(Incident.class);
        List<String> photos = new ArrayList<>();
        photos.add(String.format("incident_%d/%s", incidentId, filename));
        when(mockIncident.getPhotos()).thenReturn(photos);

        // Mock services
        when(incidentService.getIncidentEntityById(incidentId)).thenReturn(mockIncident);
        when(fileStorageService.delete(any(Path.class))).thenReturn(true);

        // Mock mapper and update
        when(incidentMapper.toRequestDTO(mockIncident)).thenReturn(mock(IncidentRequestDTO.class));

        // Act
        Map<String, Object> result = incidentFileManager.deleteFile(incidentId, FileType.PHOTO, filename);

        // Assert
        assertEquals("partial", result.get("status"));
        verify(incidentService, times(0)).updateIncident(eq(incidentId), any(IncidentRequestDTO.class));
        verify(fileStorageService).delete(any(Path.class));
    }

    @Test
    void deleteFile_shouldReturnPartialStatusWhenFileIsDeletedButReferenceNotFound() {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;
        String filename = "non-referenced.jpg";

        // Mock Incident entity with empty photos list
        Incident mockIncident = mock(Incident.class);
        List<String> photos = new ArrayList<>();
        when(mockIncident.getPhotos()).thenReturn(photos);

        // Mock services
        when(incidentService.getIncidentEntityById(incidentId)).thenReturn(mockIncident);
        when(fileStorageService.delete(any(Path.class))).thenReturn(true);

        // Act
        Map<String, Object> result = incidentFileManager.deleteFile(incidentId, FileType.PHOTO, filename);

        // Assert
        assertEquals("partial", result.get("status"));
        verify(incidentService, never()).updateIncident(eq(incidentId), any(IncidentRequestDTO.class));
        verify(fileStorageService).delete(any(Path.class));
    }

    @Test
    void deleteFile_shouldReturnErrorStatusWhenIncidentIsNotFound() {
        // Arrange
        Long incidentId = 999L;
        String filename = "photo.jpg";

        // Mock services to throw ResourceNotFoundException
        when(incidentService.getIncidentEntityById(incidentId)).thenThrow(new ResourceNotFoundException("Incident not found"));

        // Act
        Map<String, Object> result = incidentFileManager.deleteFile(incidentId, FileType.PHOTO, filename);

        // Assert
        assertEquals("error", result.get("status"));
        verify(fileStorageService, never()).delete(any(Path.class));
    }

    @Test
    void listFiles_shouldReturnEmptyListWhenDirectoryDoesNotExist() {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;
        IncidentFileManagerImpl spyManager = spy(incidentFileManager);
        doReturn(Collections.emptyList()).when(spyManager).listFiles(incidentId, FileType.PHOTO);

        // Act
        List<Map<String, Object>> result = spyManager.listFiles(incidentId, FileType.PHOTO);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void listFiles_shouldReturnFilesForSpecificType() throws IOException {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;

        // Create test files directly in the main directory
        Files.createFile(tempDir.resolve("photo_" + incidentId + "_1.jpg"));
        Files.createFile(tempDir.resolve("photo_" + incidentId + "_2.jpg"));
        Files.createFile(tempDir.resolve("constat_" + incidentId + "_1.pdf"));
        // Create a file for another incident that should be ignored
        Files.createFile(tempDir.resolve("photo_999_other.jpg"));

        // Act
        List<Map<String, Object>> result = incidentFileManager.listFiles(incidentId, FileType.PHOTO);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(file -> ("photo_" + incidentId + "_1.jpg").equals(file.get("filename"))));
        assertTrue(result.stream().anyMatch(file -> ("photo_" + incidentId + "_2.jpg").equals(file.get("filename"))));
    }

    @Test
    void listFiles_shouldReturnAllFilesWhenTypeIsNull() throws IOException {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;

        // Create test files directly in the main directory
        Files.createFile(tempDir.resolve("photo_" + incidentId + "_1.jpg"));
        Files.createFile(tempDir.resolve("constat_" + incidentId + "_1.pdf"));
        // Create a file for another incident that should be ignored
        Files.createFile(tempDir.resolve("photo_999_other.jpg"));

        // Act
        List<Map<String, Object>> result = incidentFileManager.listFiles(incidentId, null);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.stream().anyMatch(file -> ("photo_" + incidentId + "_1.jpg").equals(file.get("filename"))));
        assertTrue(result.stream().anyMatch(file -> ("constat_" + incidentId + "_1.pdf").equals(file.get("filename"))));
    }

    @Test
    void listFiles_shouldHandleIOException() throws IOException {
        // Arrange
        Long incidentId = TEST_INCIDENT_ID;

        // Create test directory with a spy to simulate IOException
        Files.createDirectories(tempDir.resolve("incident_" + incidentId));

        IncidentFileManagerImpl spyManager = spy(incidentFileManager);
        doReturn(Collections.emptyList()).when(spyManager).listFiles(incidentId, FileType.PHOTO);

        // Act
        List<Map<String, Object>> result = spyManager.listFiles(incidentId, FileType.PHOTO);

        // Assert
        assertTrue(result.isEmpty());
    }
}