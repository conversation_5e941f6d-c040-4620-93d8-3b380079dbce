package otbs.ms_incident.exception;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import static org.junit.jupiter.api.Assertions.*;

class FileStorageExceptionTest {

    @Test
    void testConstructorWithMessage() {
        // Arrange
        String message = "Custom error message";
        
        // Act
        FileStorageException exception = new FileStorageException(message);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals("FILE_ERROR", exception.getErrorCode());
    }
    
    @Test
    void testConstructorWithMessageAndStatusAndCode() {
        // Arrange
        String message = "Custom error message";
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        String errorCode = "CUSTOM_ERROR";
        
        // Act
        FileStorageException exception = new FileStorageException(message, status, errorCode);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals(status, exception.getStatus());
        assertEquals(errorCode, exception.getErrorCode());
    }
    
    @Test
    void testConstructorWithMessageAndCauseAndStatusAndCode() {
        // Arrange
        String message = "Custom error message";
        Throwable cause = new RuntimeException("Root cause");
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        String errorCode = "CUSTOM_ERROR";
        
        // Act
        FileStorageException exception = new FileStorageException(message, cause, status, errorCode);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertEquals(status, exception.getStatus());
        assertEquals(errorCode, exception.getErrorCode());
    }
}