package otbs.ms_incident.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * DTO pour la répartition des incidents par véhicule et par type
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleIncidentTypeDistributionDTO {
    private Map<Long, Map<String, Long>> countByVehicleAndType;
    private Map<Long, String> vehicleImmatriculations;
}
