package otbs.ms_incident.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import otbs.ms_incident.dto.dashboard.DashboardSummaryDTO;
import otbs.ms_incident.dto.dashboard.IncidentStatusDistributionDTO;
import otbs.ms_incident.entity.Incident;

import java.time.LocalDate;
import java.util.List;

/**
 * Repository pour les requêtes optimisées du dashboard
 */
@Repository
public interface DashboardRepository extends JpaRepository<Incident, Long> {

    /**
     * Récupère les statistiques générales pour le dashboard
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return DTO avec les statistiques générales
     */
    @Query(value = "SELECT new otbs.ms_incident.dto.dashboard.DashboardSummaryDTO(" +
           "COUNT(i), " + // Total incidents
           "COUNT(CASE WHEN i.status = 'A_TRAITER' THEN 1 ELSE NULL END), " + // Open incidents
           "COUNT(CASE WHEN i.status = 'EN_COURS_TRAITEMENT' THEN 1 ELSE NULL END), " + // In progress incidents
           "COUNT(CASE WHEN i.status = 'RESOLU' THEN 1 ELSE NULL END), " + // Resolved incidents
           "COUNT(CASE WHEN i.priorite = 'CRITIQUE' THEN 1 ELSE NULL END), " + // High priority incidents
           "(SELECT COUNT(r) FROM Reparation r WHERE r.incident.id IN " +
           "    (SELECT i2.id FROM Incident i2 WHERE i2.date BETWEEN :debut AND :fin " +
           "     AND (:vehiculeId IS NULL OR i2.vehiculeId = :vehiculeId))), " + // Total reparations
           "(SELECT COUNT(r) FROM Reparation r WHERE r.status = 'EN_COURS' AND r.incident.id IN " +
           "    (SELECT i2.id FROM Incident i2 WHERE i2.date BETWEEN :debut AND :fin " +
           "     AND (:vehiculeId IS NULL OR i2.vehiculeId = :vehiculeId))), " + // In progress reparations
           "(SELECT COUNT(r) FROM Reparation r WHERE r.status = 'TERMINEE' AND r.incident.id IN " +
           "    (SELECT i2.id FROM Incident i2 WHERE i2.date BETWEEN :debut AND :fin " +
           "     AND (:vehiculeId IS NULL OR i2.vehiculeId = :vehiculeId))), " + // Completed reparations
           "(SELECT COALESCE(SUM(r.cout), 0) FROM Reparation r WHERE r.incident.id IN " +
           "    (SELECT i2.id FROM Incident i2 WHERE i2.date BETWEEN :debut AND :fin " +
           "     AND (:vehiculeId IS NULL OR i2.vehiculeId = :vehiculeId))), " + // Total cost
           "(SELECT CASE WHEN COUNT(r) > 0 THEN COALESCE(SUM(r.cout) / COUNT(r), 0) ELSE 0 END " +
           "  FROM Reparation r WHERE r.incident.id IN " +
           "    (SELECT i2.id FROM Incident i2 WHERE i2.date BETWEEN :debut AND :fin " +
           "     AND (:vehiculeId IS NULL OR i2.vehiculeId = :vehiculeId))), " + // Average cost
           "0.0, 0.0, 0.0, 0.0, 0.0, 0.0) " + // Les variations seront calculées dans le service
           "FROM Incident i " +
           "WHERE i.date BETWEEN :debut AND :fin " +
           "AND (:vehiculeId IS NULL OR i.vehiculeId = :vehiculeId)")
    DashboardSummaryDTO getDashboardSummary(
            @Param("debut") LocalDate debut,
            @Param("fin") LocalDate fin,
            @Param("vehiculeId") Long vehiculeId);

    /**
     * Récupère les incidents récents
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @param limit Nombre maximum d'incidents à récupérer
     * @return Liste des incidents récents
     */
    @Query("SELECT i FROM Incident i " +
           "WHERE i.date BETWEEN :debut AND :fin " +
           "AND (:vehiculeId IS NULL OR i.vehiculeId = :vehiculeId) " +
           "ORDER BY i.date DESC")
    List<Incident> getRecentIncidents(
            @Param("debut") LocalDate debut,
            @Param("fin") LocalDate fin,
            @Param("vehiculeId") Long vehiculeId,
            @Param("limit") int limit);

    /**
     * Récupère la répartition des incidents par statut
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return DTO avec la répartition des incidents par statut
     */
    @Query("SELECT new otbs.ms_incident.dto.dashboard.IncidentStatusDistributionDTO(" +
           "SUM(CASE WHEN i.status = 'A_TRAITER' THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN i.status = 'EN_COURS_TRAITEMENT' THEN 1 ELSE 0 END), " +
           "SUM(CASE WHEN i.status = 'RESOLU' THEN 1 ELSE 0 END)) " +
           "FROM Incident i " +
           "WHERE i.date BETWEEN :debut AND :fin " +
           "AND (:vehiculeId IS NULL OR i.vehiculeId = :vehiculeId)")
    IncidentStatusDistributionDTO getIncidentStatusDistribution(
            @Param("debut") LocalDate debut,
            @Param("fin") LocalDate fin,
            @Param("vehiculeId") Long vehiculeId);

    /**
     * Récupère la répartition des incidents par type
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return Map avec le type d'incident comme clé et le nombre comme valeur
     */
    @Query("SELECT i.type, COUNT(i) FROM Incident i " +
           "WHERE i.date BETWEEN :debut AND :fin " +
           "AND (:vehiculeId IS NULL OR i.vehiculeId = :vehiculeId) " +
           "GROUP BY i.type")
    List<Object[]> getIncidentTypeDistribution(
            @Param("debut") LocalDate debut,
            @Param("fin") LocalDate fin,
            @Param("vehiculeId") Long vehiculeId);

    /**
     * Récupère la répartition des incidents par véhicule
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return Map avec l'ID du véhicule comme clé et le nombre comme valeur
     */
    @Query("SELECT i.vehiculeId, COUNT(i) FROM Incident i " +
           "WHERE i.date BETWEEN :debut AND :fin " +
           "AND i.vehiculeId IS NOT NULL " +
           "AND (:vehiculeId IS NULL OR i.vehiculeId = :vehiculeId) " +
           "GROUP BY i.vehiculeId")
    List<Object[]> getIncidentCountByVehicle(
            @Param("debut") LocalDate debut,
            @Param("fin") LocalDate fin,
            @Param("vehiculeId") Long vehiculeId);

    /**
     * Récupère la répartition des incidents par véhicule et par type
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return Liste de tableaux d'objets contenant l'ID du véhicule, le type d'incident et le nombre
     */
    @Query("SELECT i.vehiculeId, i.type, COUNT(i) FROM Incident i " +
           "WHERE i.date BETWEEN :debut AND :fin " +
           "AND i.vehiculeId IS NOT NULL " +
           "AND (:vehiculeId IS NULL OR i.vehiculeId = :vehiculeId) " +
           "GROUP BY i.vehiculeId, i.type")
    List<Object[]> getIncidentCountByVehicleAndType(
            @Param("debut") LocalDate debut,
            @Param("fin") LocalDate fin,
            @Param("vehiculeId") Long vehiculeId);
}
