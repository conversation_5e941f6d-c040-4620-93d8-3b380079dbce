package otbs.ms_incident.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception spécifique pour les types de fichiers non autorisés
 */
public class InvalidFileTypeException extends FileStorageException {

    /**
     * Crée une exception avec un message
     * 
     * @param message Message d'erreur
     */
    public InvalidFileTypeException(String message) {
        super(message, HttpStatus.FORBIDDEN, "INVALID_FILE_TYPE");
    }

    /**
     * Crée une exception avec un message et une extension de fichier
     * 
     * @param message Message d'erreur
     * @param fileExtension Extension de fichier non autorisée
     */
    public InvalidFileTypeException(String message, String fileExtension) {
        super(
            message + " (extension: " + fileExtension + ")", 
            HttpStatus.FORBIDDEN, 
            "INVALID_FILE_TYPE"
        );
    }
} 