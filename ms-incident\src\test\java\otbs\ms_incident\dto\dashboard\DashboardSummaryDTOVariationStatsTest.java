package otbs.ms_incident.dto.dashboard;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test for DashboardSummaryDTO.VariationStats
 */
class DashboardSummaryDTOVariationStatsTest {

    @Test
    void testBuilder_withAllFields() {
        // Given
        double totalIncidentsChange = 25.0;
        double openIncidentsChange = 15.0;
        double totalReparationsChange = 10.0;
        double inProgressReparationsChange = 5.0;
        double completedReparationsChange = 20.0;
        double totalCostChange = 30.0;

        // When
        DashboardSummaryDTO.VariationStats stats = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(totalIncidentsChange)
                .openIncidentsChange(openIncidentsChange)
                .totalReparationsChange(totalReparationsChange)
                .inProgressReparationsChange(inProgressReparationsChange)
                .completedReparationsChange(completedReparationsChange)
                .totalCostChange(totalCostChange)
                .build();

        // Then
        assertEquals(totalIncidentsChange, stats.getTotalIncidentsChange());
        assertEquals(openIncidentsChange, stats.getOpenIncidentsChange());
        assertEquals(totalReparationsChange, stats.getTotalReparationsChange());
        assertEquals(inProgressReparationsChange, stats.getInProgressReparationsChange());
        assertEquals(completedReparationsChange, stats.getCompletedReparationsChange());
        assertEquals(totalCostChange, stats.getTotalCostChange());
    }

    @Test
    void testBuilder_withMinimalFields() {
        // When
        DashboardSummaryDTO.VariationStats stats = DashboardSummaryDTO.VariationStats.builder()
                .build();

        // Then
        assertEquals(0.0, stats.getTotalIncidentsChange());
        assertEquals(0.0, stats.getOpenIncidentsChange());
        assertEquals(0.0, stats.getTotalReparationsChange());
        assertEquals(0.0, stats.getInProgressReparationsChange());
        assertEquals(0.0, stats.getCompletedReparationsChange());
        assertEquals(0.0, stats.getTotalCostChange());
    }

    @Test
    void testToString_shouldNotThrowException() {
        // Given
        DashboardSummaryDTO.VariationStats stats = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .openIncidentsChange(15.0)
                .build();

        // When & Then
        assertDoesNotThrow(() -> {
            String result = stats.toString();
            assertNotNull(result);
            assertFalse(result.isEmpty());
        });
    }

    @Test
    void testEquals_withSameObject() {
        // Given
        DashboardSummaryDTO.VariationStats stats = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .openIncidentsChange(15.0)
                .build();

        // When & Then
        assertEquals(stats, stats);
    }

    @Test
    void testEquals_withEqualObject() {
        // Given
        DashboardSummaryDTO.VariationStats stats1 = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .openIncidentsChange(15.0)
                .build();

        DashboardSummaryDTO.VariationStats stats2 = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .openIncidentsChange(15.0)
                .build();

        // When & Then
        assertEquals(stats1, stats2);
        assertEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testEquals_withDifferentObject() {
        // Given
        DashboardSummaryDTO.VariationStats stats1 = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(25.0)
                .openIncidentsChange(15.0)
                .build();

        DashboardSummaryDTO.VariationStats stats2 = DashboardSummaryDTO.VariationStats.builder()
                .totalIncidentsChange(30.0)
                .openIncidentsChange(15.0)
                .build();

        // When & Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testGettersAndSetters() {
        // Given
        DashboardSummaryDTO.VariationStats stats = DashboardSummaryDTO.VariationStats.builder().build();

        // When
        stats.setTotalIncidentsChange(25.0);
        stats.setOpenIncidentsChange(15.0);
        stats.setTotalReparationsChange(10.0);
        stats.setInProgressReparationsChange(5.0);
        stats.setCompletedReparationsChange(20.0);
        stats.setTotalCostChange(30.0);

        // Then
        assertEquals(25.0, stats.getTotalIncidentsChange());
        assertEquals(15.0, stats.getOpenIncidentsChange());
        assertEquals(10.0, stats.getTotalReparationsChange());
        assertEquals(5.0, stats.getInProgressReparationsChange());
        assertEquals(20.0, stats.getCompletedReparationsChange());
        assertEquals(30.0, stats.getTotalCostChange());
    }
}
