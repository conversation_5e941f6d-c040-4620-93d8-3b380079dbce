# Server configuration
server.port=9002
spring.application.name=ms-incident

# Configuration de stockage de fichiers
storage.path=${STORAGE_PATH:/app/storage/Reparation}
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Configuration de la langue (Fr<PERSON><PERSON> uniquement)
spring.web.locale=fr_FR
spring.web.locale-resolver=fixed
spring.messages.fallback-to-system-locale=false
spring.messages.basename=messages

# Configuration de la DB
spring.datasource.url=${DB_URL:*********************************************}
spring.datasource.username=${DB_USERNAME:parcauto}
spring.datasource.password=${DB_PASSWORD:parcauto}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=create
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Hikari Connection Pool
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=30000

# Configuration Swagger UI
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.docExpansion=none
springdoc.swagger-ui.defaultModelsExpandDepth=-1
springdoc.swagger-ui.displayRequestDuration=true
springdoc.swagger-ui.filter=true

# Eureka client configuration
eureka.client.service-url.defaultZone=${Eureka-url:http://*************:9102/eureka}
eureka.client.enabled=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true

# Logging configuration
logging.level.root=INFO
logging.level.OTBS.ms_incident=DEBUG
logging.level.com.netflix.discovery=DEBUG
logging.level.com.netflix.eureka=DEBUG
logging.level.org.springframework.security=DEBUG

# Actuator configuration
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always

# Configuration OAuth2 - Resource Server (JWT)
spring.security.oauth2.resourceserver.jwt.issuer-uri=${ISSUER_URI:http://*************:8080/realms/parc-auto}
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${JWK_SET_URI:${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs}

# Configuration OAuth2 - Client Registration pour ms-incident
spring.security.oauth2.client.registration.keycloak.client-id=${CLIENT_ID:ms-incident-client}
spring.security.oauth2.client.registration.keycloak.authorization-grant-type=${AUTH_GRANT_TYPE:password}
spring.security.oauth2.client.registration.keycloak.scope=${SCOPE:openid}

# Configuration OAuth2 - Provider (Keycloak)
spring.security.oauth2.client.provider.keycloak.issuer-uri=${PROVIDER_URI:${spring.security.oauth2.resourceserver.jwt.issuer-uri}}
spring.security.oauth2.client.provider.keycloak.user-name-attribute=${USERNAME_ATTRIBUTE:preferred_username}

# Configuration Swagger UI avec OAuth2
springdoc.swagger-ui.oauth.client-id=${SWAGGER_CLIENT_ID:${spring.security.oauth2.client.registration.keycloak.client-id}}
springdoc.swagger-ui.oauth.client-secret=${SWAGGER_CLIENT_SECRET:}




# Configuration de Feign pour activer les fallbacks
feign.circuitbreaker.enabled=true
feign.client.config.default.connectTimeout=5000
feign.client.config.default.readTimeout=5000

# Configuration specifique pour le client Feign vehicule
vehicule.service.url=http://*************:9003

# Configuration du Circuit Breaker
resilience4j.circuitbreaker.instances.vehiculeService.registerHealthIndicator=true
resilience4j.circuitbreaker.instances.vehiculeService.slidingWindowSize=10
resilience4j.circuitbreaker.instances.vehiculeService.minimumNumberOfCalls=5
resilience4j.circuitbreaker.instances.vehiculeService.permittedNumberOfCallsInHalfOpenState=3
resilience4j.circuitbreaker.instances.vehiculeService.automaticTransitionFromOpenToHalfOpenEnabled=true
resilience4j.circuitbreaker.instances.vehiculeService.waitDurationInOpenState=5s
resilience4j.circuitbreaker.instances.vehiculeService.failureRateThreshold=50
resilience4j.circuitbreaker.instances.vehiculeService.eventConsumerBufferSize=10

# Configuration du Retry
resilience4j.retry.instances.vehiculeService.maxAttempts=3
resilience4j.retry.instances.vehiculeService.waitDuration=1s
resilience4j.retry.instances.vehiculeService.enableExponentialBackoff=true
resilience4j.retry.instances.vehiculeService.exponentialBackoffMultiplier=2

# Configuration RabbitMQ Docker 
spring.rabbitmq.host=${RABBITMQ_HOST:*************}
spring.rabbitmq.port=${RABBITMQ_PORT:5672}
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}
spring.rabbitmq.virtual-host=${RABBITMQ_VHOST:/}

# Configuration de connexion
spring.rabbitmq.connection-timeout=${RABBITMQ_CONNECTION_TIMEOUT:10000}
spring.rabbitmq.requested-heartbeat=${RABBITMQ_HEARTBEAT:30}

# Configuration du publisher
spring.rabbitmq.publisher-confirm-type=correlated
spring.rabbitmq.publisher-returns=true

# Configuration du template
spring.rabbitmq.template.mandatory=true
spring.rabbitmq.template.retry.enabled=true
spring.rabbitmq.template.retry.initial-interval=1000
spring.rabbitmq.template.retry.max-attempts=3
