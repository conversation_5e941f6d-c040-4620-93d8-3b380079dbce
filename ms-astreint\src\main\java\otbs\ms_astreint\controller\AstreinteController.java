package otbs.ms_astreint.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import otbs.ms_astreint.dto.AstreinteDto;
import otbs.ms_astreint.service.AstreinteService;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/v1/astreintes")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Astreintes", description = "API pour gérer les astreintes")
@SecurityRequirement(name = "oauth2")
public class AstreinteController {

    private final AstreinteService astreinteService;

    @GetMapping
    @Operation(summary = "Récupérer toutes les astreintes", description = "Retourne la liste de toutes les astreintes")
    @ApiResponse(responseCode = "200", description = "Liste des astreintes récupérée avec succès")
    public ResponseEntity<List<AstreinteDto>> getAllAstreintes() {
        return ResponseEntity.ok(astreinteService.getAllAstreintes());
    }

    @GetMapping("/{id}")
    @Operation(summary = "Récupérer une astreinte par son ID", description = "Retourne une astreinte spécifique par son ID")
    @ApiResponse(responseCode = "200", description = "Astreinte trouvée")
    @ApiResponse(responseCode = "404", description = "Astreinte non trouvée", content = @Content)
    public ResponseEntity<AstreinteDto> getAstreinteById(@PathVariable Long id) {
        return astreinteService.getAstreinteById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping
    @Operation(summary = "Créer une nouvelle astreinte", description = "Crée une nouvelle astreinte avec les informations fournies")
    @ApiResponse(responseCode = "201", description = "Astreinte créée avec succès")
    @ApiResponse(responseCode = "400", description = "Données invalides", content = @Content)
    public ResponseEntity<AstreinteDto> createAstreinte(@RequestBody AstreinteDto astreinteDto) {
        try {
            AstreinteDto createdAstreinte = astreinteService.createAstreinte(astreinteDto);
            return ResponseEntity.status(HttpStatus.CREATED).body(createdAstreinte);
        } catch (IllegalArgumentException e) {
            log.error("Erreur lors de la création de l'astreinte", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Mettre à jour une astreinte", description = "Met à jour une astreinte existante avec les informations fournies")
    @ApiResponse(responseCode = "200", description = "Astreinte mise à jour avec succès")
    @ApiResponse(responseCode = "404", description = "Astreinte non trouvée", content = @Content)
    @ApiResponse(responseCode = "400", description = "Données invalides", content = @Content)
    public ResponseEntity<AstreinteDto> updateAstreinte(@PathVariable Long id, @RequestBody AstreinteDto astreinteDto) {
        try {
            return astreinteService.updateAstreinte(id, astreinteDto)
                    .map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
        } catch (IllegalArgumentException e) {
            log.error("Erreur lors de la mise à jour de l'astreinte", e);
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Supprimer une astreinte", description = "Supprime une astreinte existante par son ID")
    @ApiResponse(responseCode = "204", description = "Astreinte supprimée avec succès")
    @ApiResponse(responseCode = "404", description = "Astreinte non trouvée", content = @Content)
    public ResponseEntity<Void> deleteAstreinte(@PathVariable Long id) {
        boolean deleted = astreinteService.deleteAstreinte(id);
        return deleted ? ResponseEntity.noContent().build() : ResponseEntity.notFound().build();
    }

    @GetMapping("/nombre-consultants")
    @Operation(summary = "Compter les consultants d'une astreinte", description = "Retourne le nombre de consultants associés à une astreinte")
    @ApiResponse(responseCode = "200", description = "Nombre de consultants récupéré avec succès")
    public ResponseEntity<Long> countConsultantsByAstreinteId(@RequestParam Long astreinteId) {
        Long count = astreinteService.countConsultantsByAstreinteId(astreinteId);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/consultant/{idConsultant}/futures")
    @Operation(summary = "Récupérer les astreintes futures d'un consultant", description = "Retourne les astreintes futures d'un consultant donné")
    @ApiResponse(responseCode = "200", description = "Liste des astreintes futures du consultant récupérée avec succès")
    public ResponseEntity<List<AstreinteDto>> findByDateDebutAfterAndConsultantUserId(@PathVariable String idConsultant) {
        List<AstreinteDto> astreintes = astreinteService.findByDateDebutAfterAndConsultantUserId(idConsultant);
        return ResponseEntity.ok(astreintes);
    }

    @GetMapping("/date-debut-after")
    @Operation(summary = "Récupérer les astreintes futures", description = "Retourne toutes les astreintes avec une date de début future")
    @ApiResponse(responseCode = "200", description = "Liste des astreintes futures récupérée avec succès")
    public ResponseEntity<List<AstreinteDto>> findByDateDebutAfter() {
        List<AstreinteDto> astreintes = astreinteService.findByDateDebutAfter();
        return ResponseEntity.ok(astreintes);
    }

    @GetMapping("/consultant/{consultantId}/periode")
    @Operation(summary = "Récupérer les astreintes d'un consultant par période", description = "Retourne les astreintes d'un consultant pour une période donnée")
    @ApiResponse(responseCode = "200", description = "Liste des astreintes du consultant pour la période récupérée avec succès")
    public ResponseEntity<List<AstreinteDto>> findAstreintesByConsultantAndDateRange(
            @PathVariable String consultantId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateDebut,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateFin) {
        List<AstreinteDto> astreintes = astreinteService.findAstreintesByConsultantAndDateRange(consultantId, dateDebut, dateFin);
        return ResponseEntity.ok(astreintes);
    }

    @GetMapping("/periode")
    @Operation(summary = "Récupérer les astreintes par période", description = "Retourne toutes les astreintes pour une période donnée")
    @ApiResponse(responseCode = "200", description = "Liste des astreintes pour la période récupérée avec succès")
    public ResponseEntity<List<AstreinteDto>> findAstreintesByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateDebut,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateFin) {
        List<AstreinteDto> astreintes = astreinteService.findAstreintesByDateRange(dateDebut, dateFin);
        return ResponseEntity.ok(astreintes);
    }

    @GetMapping("/consultant/{consultant}")
    @Operation(summary = "Récupérer les astreintes par consultant", description = "Retourne toutes les astreintes associées à un consultant donné")
    @ApiResponse(responseCode = "200", description = "Liste des astreintes du consultant récupérée avec succès")
    public ResponseEntity<List<AstreinteDto>> getAstreintesByConsultant(@PathVariable String consultant) {
        List<AstreinteDto> astreintes = astreinteService.getAstreintesByConsultant(consultant);
        return ResponseEntity.ok(astreintes);
    }

}