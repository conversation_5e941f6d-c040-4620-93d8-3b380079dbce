package otbs.ms_incident.exception;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import static org.junit.jupiter.api.Assertions.*;

class InvalidFileTypeExceptionTest {

    @Test
    void testConstructorWithMessage() {
        // Arrange
        String message = "Invalid file type message";
        
        // Act
        InvalidFileTypeException exception = new InvalidFileTypeException(message);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertEquals("INVALID_FILE_TYPE", exception.getErrorCode());
    }
    
    @Test
    void testConstructorWithMessageAndFileExtension() {
        // Arrange
        String message = "Invalid file extension";
        String fileExtension = "exe";
        
        // Act
        InvalidFileTypeException exception = new InvalidFileTypeException(message, fileExtension);
        
        // Assert
        String expectedMessage = "Invalid file extension (extension: exe)";
        assertEquals(expectedMessage, exception.getMessage());
        assertEquals(HttpStatus.FORBIDDEN, exception.getStatus());
        assertEquals("INVALID_FILE_TYPE", exception.getErrorCode());
    }
} 