import { CommonModule } from '@angular/common';
import { Component, OnInit, Inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogActions, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { TelepeageService } from '../../services/telepeage.service';
import { AstreinteReservationService } from '../../services/astreinte-reservation.service';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Telepeage } from '../../models/Telepeage';
import { ConsommationTelepeage } from '../../models/ConsommationTelepeage';
import { AstreinteReservation } from '../../models/AstreinteReservation';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-affecter-telepeage-astreinte',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCheckboxModule,
    MatIconModule,
    MatDialogActions
  ],
  templateUrl: './affecter-telepeage-astreinte.component.html',
  styleUrls: ['./affecter-telepeage-astreinte.component.scss']
})
export class AffecterTelepeageAstreinteComponent implements OnInit {
  affecterTelepageForm: FormGroup;
  isLoading: boolean = false;
  badges: Telepeage[] = [];
  isUpdateMode: boolean = false;
  currentConsommationTelepeage: ConsommationTelepeage | null = null;
  currentReservation: AstreinteReservation | null = null;

  constructor(
    public dialogRef: MatDialogRef<AffecterTelepeageAstreinteComponent>,
    private fb: FormBuilder,
    private telepeageService: TelepeageService,
    private astreinteReservationService: AstreinteReservationService,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: { 
      reservationId: number; 
      dateDebut: string; 
      dateFin: string; 
      mode: 'add' | 'update'
    }
  ) {
    this.affecterTelepageForm = this.fb.group({
      badgeId: [''], // No Validators.required to allow null
      noBadge: [false] // Option for "Aucun badge"
    });

    this.isUpdateMode = this.data.mode === 'update';

    // Handle "Aucun badge" checkbox
    this.affecterTelepageForm.get('noBadge')?.valueChanges.subscribe((noBadge) => {
      if (noBadge) {
        this.affecterTelepageForm.patchValue({ badgeId: '' });
      }
    });
  }

  async ngOnInit() {
    try {
      // Load current reservation data
      this.currentReservation = await firstValueFrom(this.astreinteReservationService.getAstreinteReservation(this.data.reservationId));

      // If update mode, get current telepeage consumption
      if (this.isUpdateMode && this.currentReservation.consommationTelepeageId) {
        this.currentConsommationTelepeage = await firstValueFrom(this.telepeageService.getConsommationTelepeageParReservation(this.data.reservationId));
        this.affecterTelepageForm.patchValue({
          badgeId: this.currentConsommationTelepeage?.telepeageId || '',
          noBadge: !this.currentConsommationTelepeage?.telepeageId
        });
      }

      // Load available badges
      await this.loadBadges();
    } catch (err) {
      console.error('Error in ngOnInit:', err);
      this.snackBar.open('Erreur lors du chargement des données', 'Fermer', { duration: 3000 });
    }
  }

  onCheckboxChange(selectedBadgeId: number | null) {
    this.affecterTelepageForm.patchValue({
      badgeId: selectedBadgeId || '',
      noBadge: !selectedBadgeId
    });
  }

  async loadBadges(): Promise<void> {
    try {
      this.badges = await firstValueFrom(this.telepeageService.getAllAvailableTelepeagesByPeriod(this.data.dateDebut, this.data.dateFin));

      // If update mode, add current badge if not in the list
      if (this.isUpdateMode && this.currentConsommationTelepeage?.telepeageId &&
          !this.badges.some(b => b.telepeageId === this.currentConsommationTelepeage!.telepeageId)) {
        const currentBadge = await firstValueFrom(this.telepeageService.getTelepeageById(this.currentConsommationTelepeage.telepeageId));
        if (currentBadge) {
          this.badges.push(currentBadge);
        }
      }
    } catch (err) {
      console.error('Error fetching badges:', err);
      this.snackBar.open('Erreur lors du chargement des badges', 'Fermer', { duration: 3000 });
    }
  }

  async onSubmit(): Promise<void> {
    this.isLoading = true;

    try {
      if (this.isUpdateMode) {
        // Update mode: modify existing reservation
        if (this.affecterTelepageForm.value.noBadge) {
          // Update reservation to remove telepeage reference
          const updatedReservation: AstreinteReservation = {
            ...this.currentReservation!,
            consommationTelepeageId: null
          };

          await firstValueFrom(this.astreinteReservationService.updateAstreinteReservation(this.data.reservationId, updatedReservation));
        } else {
          // Update or create telepeage consumption
          const consommationData: ConsommationTelepeage = {
            id: this.currentConsommationTelepeage?.id || 0,
            reservationId: this.data.reservationId,
            telepeageId: this.affecterTelepageForm.value.badgeId,
            montant: null
          };

          let consommationTelepeage: ConsommationTelepeage;

          if (this.currentConsommationTelepeage) {
            // Update existing consumption
            consommationTelepeage = await firstValueFrom(this.telepeageService.updateAttributedBadgeTelepeage(this.data.reservationId, consommationData));
          } else {
            // Create new consumption
            consommationTelepeage = await firstValueFrom(this.telepeageService.attributionBadgeTelepeage(consommationData));
          }

          // Update reservation with new telepeage reference
          const updatedReservation: AstreinteReservation = {
            ...this.currentReservation!,
            consommationTelepeageId: consommationTelepeage.id
          };

          await firstValueFrom(this.astreinteReservationService.updateAstreinteReservation(this.data.reservationId, updatedReservation));
        }
      } else {
        // Add mode: create new telepeage consumption if badge selected
        if (!this.affecterTelepageForm.value.noBadge && this.affecterTelepageForm.value.badgeId) {
          const consommationData: ConsommationTelepeage = {
            id: 0,
            reservationId: this.data.reservationId,
            telepeageId: this.affecterTelepageForm.value.badgeId,
            montant: null
          };

          const consommationTelepeage = await firstValueFrom(this.telepeageService.attributionBadgeTelepeage(consommationData));

          // Update reservation with telepeage reference
          const updatedReservation: AstreinteReservation = {
            ...this.currentReservation!,
            consommationTelepeageId: consommationTelepeage.id
          };

          await firstValueFrom(this.astreinteReservationService.updateAstreinteReservation(this.data.reservationId, updatedReservation));
        }
      }

      this.isLoading = false;
      this.dialogRef.close(true);
      this.snackBar.open(
        this.isUpdateMode ? 'Badge télépéage mis à jour avec succès' : 'Badge télépéage affecté avec succès',
        'Fermer',
        { duration: 3000 }
      );
    } catch (err) {
      this.isLoading = false;
      console.error('Error in onSubmit:', err);
      this.snackBar.open(
        this.isUpdateMode ? 'Erreur lors de la mise à jour du badge' : 'Erreur lors de l\'affectation du badge',
        'Fermer',
        { duration: 3000 }
      );
    }
  }

  onCancel() {
    this.dialogRef.close();
  }

  trackByBadge(index: number, badge: Telepeage): number {
    return badge.telepeageId;
  }
}
