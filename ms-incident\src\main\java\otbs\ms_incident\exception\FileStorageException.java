package otbs.ms_incident.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception spécifique pour les erreurs de stockage de fichiers
 */
public class FileStorageException extends RuntimeException {
    private final HttpStatus status;
    private final String errorCode;

    /**
     * Constructeur avec message, cause, statut HTTP et code d'erreur
     */
    public FileStorageException(String message, Throwable cause, HttpStatus status, String errorCode) {
        super(message, cause);
        this.status = status;
        this.errorCode = errorCode;
    }

    /**
     * Constructeur avec message, statut HTTP et code d'erreur
     */
    public FileStorageException(String message, HttpStatus status, String errorCode) {
        super(message);
        this.status = status;
        this.errorCode = errorCode;
    }
    
    /**
     * Constructeur simple avec message uniquement
     * Utilise BAD_REQUEST comme statut HTTP par défaut et 'FILE_ERROR' comme code d'erreur
     */
    public FileStorageException(String message) {
        super(message);
        this.status = HttpStatus.BAD_REQUEST;
        this.errorCode = "FILE_ERROR";
    }

    /**
     * Obtient le statut HTTP
     */
    public HttpStatus getStatus() {
        return status;
    }

    /**
     * Obtient le code d'erreur
     */
    public String getErrorCode() {
        return errorCode;
    }
} 