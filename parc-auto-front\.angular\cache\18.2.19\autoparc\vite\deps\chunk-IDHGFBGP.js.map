{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/text-field.mjs", "../../../../../../node_modules/@angular/material/fesm2022/input.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, booleanAttribute, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { coerceElement, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { EMPTY, Subject, fromEvent } from 'rxjs';\nimport { auditTime, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n  constructor(_platform, _ngZone) {\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._monitoredElements = new Map();\n  }\n  monitor(elementOrRef) {\n    if (!this._platform.isBrowser) {\n      return EMPTY;\n    }\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      return info.subject;\n    }\n    const result = new Subject();\n    const cssClass = 'cdk-text-field-autofilled';\n    const listener = event => {\n      // Animation events fire on initial element render, we check for the presence of the autofill\n      // CSS class to make sure this is a real change in state, not just the initial render before\n      // we fire off events.\n      if (event.animationName === 'cdk-text-field-autofill-start' && !element.classList.contains(cssClass)) {\n        element.classList.add(cssClass);\n        this._ngZone.run(() => result.next({\n          target: event.target,\n          isAutofilled: true\n        }));\n      } else if (event.animationName === 'cdk-text-field-autofill-end' && element.classList.contains(cssClass)) {\n        element.classList.remove(cssClass);\n        this._ngZone.run(() => result.next({\n          target: event.target,\n          isAutofilled: false\n        }));\n      }\n    };\n    this._ngZone.runOutsideAngular(() => {\n      element.addEventListener('animationstart', listener, listenerOptions);\n      element.classList.add('cdk-text-field-autofill-monitored');\n    });\n    this._monitoredElements.set(element, {\n      subject: result,\n      unlisten: () => {\n        element.removeEventListener('animationstart', listener, listenerOptions);\n      }\n    });\n    return result;\n  }\n  stopMonitoring(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    const info = this._monitoredElements.get(element);\n    if (info) {\n      info.unlisten();\n      info.subject.complete();\n      element.classList.remove('cdk-text-field-autofill-monitored');\n      element.classList.remove('cdk-text-field-autofilled');\n      this._monitoredElements.delete(element);\n    }\n  }\n  ngOnDestroy() {\n    this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n  }\n  static {\n    this.ɵfac = function AutofillMonitor_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AutofillMonitor)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: AutofillMonitor,\n      factory: AutofillMonitor.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AutofillMonitor, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.Platform\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n  constructor(_elementRef, _autofillMonitor) {\n    this._elementRef = _elementRef;\n    this._autofillMonitor = _autofillMonitor;\n    /** Emits when the autofill state of the element changes. */\n    this.cdkAutofill = new EventEmitter();\n  }\n  ngOnInit() {\n    this._autofillMonitor.monitor(this._elementRef).subscribe(event => this.cdkAutofill.emit(event));\n  }\n  ngOnDestroy() {\n    this._autofillMonitor.stopMonitoring(this._elementRef);\n  }\n  static {\n    this.ɵfac = function CdkAutofill_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAutofill)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(AutofillMonitor));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkAutofill,\n      selectors: [[\"\", \"cdkAutofill\", \"\"]],\n      outputs: {\n        cdkAutofill: \"cdkAutofill\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAutofill, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkAutofill]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: AutofillMonitor\n  }], {\n    cdkAutofill: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n  /** Minimum amount of rows in the textarea. */\n  get minRows() {\n    return this._minRows;\n  }\n  set minRows(value) {\n    this._minRows = coerceNumberProperty(value);\n    this._setMinHeight();\n  }\n  /** Maximum amount of rows in the textarea. */\n  get maxRows() {\n    return this._maxRows;\n  }\n  set maxRows(value) {\n    this._maxRows = coerceNumberProperty(value);\n    this._setMaxHeight();\n  }\n  /** Whether autosizing is enabled or not */\n  get enabled() {\n    return this._enabled;\n  }\n  set enabled(value) {\n    // Only act if the actual value changed. This specifically helps to not run\n    // resizeToFitContent too early (i.e. before ngAfterViewInit)\n    if (this._enabled !== value) {\n      (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n    }\n  }\n  get placeholder() {\n    return this._textareaElement.placeholder;\n  }\n  set placeholder(value) {\n    this._cachedPlaceholderHeight = undefined;\n    if (value) {\n      this._textareaElement.setAttribute('placeholder', value);\n    } else {\n      this._textareaElement.removeAttribute('placeholder');\n    }\n    this._cacheTextareaPlaceholderHeight();\n  }\n  constructor(_elementRef, _platform, _ngZone, /** @breaking-change 11.0.0 make document required */\n  document) {\n    this._elementRef = _elementRef;\n    this._platform = _platform;\n    this._ngZone = _ngZone;\n    this._destroyed = new Subject();\n    this._enabled = true;\n    /**\n     * Value of minRows as of last resize. If the minRows has decreased, the\n     * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n     * does not have the same problem because it does not affect the textarea's scrollHeight.\n     */\n    this._previousMinRows = -1;\n    this._isViewInited = false;\n    /** Handles `focus` and `blur` events. */\n    this._handleFocusEvent = event => {\n      this._hasFocus = event.type === 'focus';\n    };\n    this._document = document;\n    this._textareaElement = this._elementRef.nativeElement;\n  }\n  /** Sets the minimum height of the textarea as determined by minRows. */\n  _setMinHeight() {\n    const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n    if (minHeight) {\n      this._textareaElement.style.minHeight = minHeight;\n    }\n  }\n  /** Sets the maximum height of the textarea as determined by maxRows. */\n  _setMaxHeight() {\n    const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n    if (maxHeight) {\n      this._textareaElement.style.maxHeight = maxHeight;\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      // Remember the height which we started with in case autosizing is disabled\n      this._initialHeight = this._textareaElement.style.height;\n      this.resizeToFitContent();\n      this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        fromEvent(window, 'resize').pipe(auditTime(16), takeUntil(this._destroyed)).subscribe(() => this.resizeToFitContent(true));\n        this._textareaElement.addEventListener('focus', this._handleFocusEvent);\n        this._textareaElement.addEventListener('blur', this._handleFocusEvent);\n      });\n      this._isViewInited = true;\n      this.resizeToFitContent(true);\n    }\n  }\n  ngOnDestroy() {\n    this._textareaElement.removeEventListener('focus', this._handleFocusEvent);\n    this._textareaElement.removeEventListener('blur', this._handleFocusEvent);\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /**\n   * Cache the height of a single-row textarea if it has not already been cached.\n   *\n   * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n   * maxRows. For the initial version, we will assume that the height of a single line in the\n   * textarea does not ever change.\n   */\n  _cacheTextareaLineHeight() {\n    if (this._cachedLineHeight) {\n      return;\n    }\n    // Use a clone element because we have to override some styles.\n    let textareaClone = this._textareaElement.cloneNode(false);\n    textareaClone.rows = 1;\n    // Use `position: absolute` so that this doesn't cause a browser layout and use\n    // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n    // would affect the height.\n    textareaClone.style.position = 'absolute';\n    textareaClone.style.visibility = 'hidden';\n    textareaClone.style.border = 'none';\n    textareaClone.style.padding = '0';\n    textareaClone.style.height = '';\n    textareaClone.style.minHeight = '';\n    textareaClone.style.maxHeight = '';\n    // In Firefox it happens that textarea elements are always bigger than the specified amount\n    // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n    // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n    // to hidden. This ensures that there is no invalid calculation of the line height.\n    // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n    textareaClone.style.overflow = 'hidden';\n    this._textareaElement.parentNode.appendChild(textareaClone);\n    this._cachedLineHeight = textareaClone.clientHeight;\n    textareaClone.remove();\n    // Min and max heights have to be re-calculated if the cached line height changes\n    this._setMinHeight();\n    this._setMaxHeight();\n  }\n  _measureScrollHeight() {\n    const element = this._textareaElement;\n    const previousMargin = element.style.marginBottom || '';\n    const isFirefox = this._platform.FIREFOX;\n    const needsMarginFiller = isFirefox && this._hasFocus;\n    const measuringClass = isFirefox ? 'cdk-textarea-autosize-measuring-firefox' : 'cdk-textarea-autosize-measuring';\n    // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n    // work around it by assigning a temporary margin with the same height as the `textarea` so that\n    // it occupies the same amount of space. See #23233.\n    if (needsMarginFiller) {\n      element.style.marginBottom = `${element.clientHeight}px`;\n    }\n    // Reset the textarea height to auto in order to shrink back to its default size.\n    // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n    element.classList.add(measuringClass);\n    // The measuring class includes a 2px padding to workaround an issue with Chrome,\n    // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n    const scrollHeight = element.scrollHeight - 4;\n    element.classList.remove(measuringClass);\n    if (needsMarginFiller) {\n      element.style.marginBottom = previousMargin;\n    }\n    return scrollHeight;\n  }\n  _cacheTextareaPlaceholderHeight() {\n    if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n      return;\n    }\n    if (!this.placeholder) {\n      this._cachedPlaceholderHeight = 0;\n      return;\n    }\n    const value = this._textareaElement.value;\n    this._textareaElement.value = this._textareaElement.placeholder;\n    this._cachedPlaceholderHeight = this._measureScrollHeight();\n    this._textareaElement.value = value;\n  }\n  ngDoCheck() {\n    if (this._platform.isBrowser) {\n      this.resizeToFitContent();\n    }\n  }\n  /**\n   * Resize the textarea to fit its content.\n   * @param force Whether to force a height recalculation. By default the height will be\n   *    recalculated only if the value changed since the last call.\n   */\n  resizeToFitContent(force = false) {\n    // If autosizing is disabled, just skip everything else\n    if (!this._enabled) {\n      return;\n    }\n    this._cacheTextareaLineHeight();\n    this._cacheTextareaPlaceholderHeight();\n    // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n    // in checking the height of the textarea.\n    if (!this._cachedLineHeight) {\n      return;\n    }\n    const textarea = this._elementRef.nativeElement;\n    const value = textarea.value;\n    // Only resize if the value or minRows have changed since these calculations can be expensive.\n    if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n      return;\n    }\n    const scrollHeight = this._measureScrollHeight();\n    const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n    // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n    textarea.style.height = `${height}px`;\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame !== 'undefined') {\n        requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n      } else {\n        setTimeout(() => this._scrollToCaretPosition(textarea));\n      }\n    });\n    this._previousValue = value;\n    this._previousMinRows = this._minRows;\n  }\n  /**\n   * Resets the textarea to its original size\n   */\n  reset() {\n    // Do not try to change the textarea, if the initialHeight has not been determined yet\n    // This might potentially remove styles when reset() is called before ngAfterViewInit\n    if (this._initialHeight !== undefined) {\n      this._textareaElement.style.height = this._initialHeight;\n    }\n  }\n  _noopInputHandler() {\n    // no-op handler that ensures we're running change detection on input events.\n  }\n  /** Access injected document if available or fallback to global document reference */\n  _getDocument() {\n    return this._document || document;\n  }\n  /** Use defaultView of injected document if available or fallback to global window reference */\n  _getWindow() {\n    const doc = this._getDocument();\n    return doc.defaultView || window;\n  }\n  /**\n   * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n   * prevent it from scrolling to the caret position. We need to re-set the selection\n   * in order for it to scroll to the proper position.\n   */\n  _scrollToCaretPosition(textarea) {\n    const {\n      selectionStart,\n      selectionEnd\n    } = textarea;\n    // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n    // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n    // between the time we requested the animation frame and when it was executed.\n    // Also note that we have to assert that the textarea is focused before we set the\n    // selection range. Setting the selection range on a non-focused textarea will cause\n    // it to receive focus on IE and Edge.\n    if (!this._destroyed.isStopped && this._hasFocus) {\n      textarea.setSelectionRange(selectionStart, selectionEnd);\n    }\n  }\n  static {\n    this.ɵfac = function CdkTextareaAutosize_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkTextareaAutosize)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(DOCUMENT, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkTextareaAutosize,\n      selectors: [[\"textarea\", \"cdkTextareaAutosize\", \"\"]],\n      hostAttrs: [\"rows\", \"1\", 1, \"cdk-textarea-autosize\"],\n      hostBindings: function CdkTextareaAutosize_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function CdkTextareaAutosize_input_HostBindingHandler() {\n            return ctx._noopInputHandler();\n          });\n        }\n      },\n      inputs: {\n        minRows: [0, \"cdkAutosizeMinRows\", \"minRows\"],\n        maxRows: [0, \"cdkAutosizeMaxRows\", \"maxRows\"],\n        enabled: [2, \"cdkTextareaAutosize\", \"enabled\", booleanAttribute],\n        placeholder: \"placeholder\"\n      },\n      exportAs: [\"cdkTextareaAutosize\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkTextareaAutosize, [{\n    type: Directive,\n    args: [{\n      selector: 'textarea[cdkTextareaAutosize]',\n      exportAs: 'cdkTextareaAutosize',\n      host: {\n        'class': 'cdk-textarea-autosize',\n        // Textarea elements that have the directive applied should have a single row by default.\n        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n        'rows': '1',\n        '(input)': '_noopInputHandler()'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.Platform\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], {\n    minRows: [{\n      type: Input,\n      args: ['cdkAutosizeMinRows']\n    }],\n    maxRows: [{\n      type: Input,\n      args: ['cdkAutosizeMaxRows']\n    }],\n    enabled: [{\n      type: Input,\n      args: [{\n        alias: 'cdkTextareaAutosize',\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }]\n  });\n})();\nclass TextFieldModule {\n  static {\n    this.ɵfac = function TextFieldModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TextFieldModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TextFieldModule,\n      imports: [CdkAutofill, CdkTextareaAutosize],\n      exports: [CdkAutofill, CdkTextareaAutosize]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(TextFieldModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkAutofill, CdkTextareaAutosize],\n      exports: [CdkAutofill, CdkTextareaAutosize]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n", "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { getSupportedInputTypes } from '@angular/cdk/platform';\nimport * as i4 from '@angular/cdk/text-field';\nimport { TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Optional, Self, Inject, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport * as i3 from '@angular/material/core';\nimport { _ErrorStateTracker, MatCommonModule } from '@angular/material/core';\nimport * as i5 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nexport { Mat<PERSON>rror, MatFormField, MatHint, MatLabel, MatPrefix, MatSuffix } from '@angular/material/form-field';\nimport { Subject } from 'rxjs';\n\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n  return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n/**\n * This token is used to inject the object whose value should be set into `MatInput`. If none is\n * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide\n * themselves for this token, in order to make `MatInput` delegate the getting and setting of the\n * value to them.\n */\nconst MAT_INPUT_VALUE_ACCESSOR = new InjectionToken('MAT_INPUT_VALUE_ACCESSOR');\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = ['button', 'checkbox', 'file', 'hidden', 'image', 'radio', 'range', 'reset', 'submit'];\nlet nextUniqueId = 0;\nclass MatInput {\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = coerceBooleanProperty(value);\n    // Browsers may not fire the blur event if the input is disabled too quickly.\n    // Reset from here to ensure that the element doesn't become stuck.\n    if (this.focused) {\n      this.focused = false;\n      this.stateChanges.next();\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get id() {\n    return this._id;\n  }\n  set id(value) {\n    this._id = value || this._uid;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = coerceBooleanProperty(value);\n  }\n  /** Input type of the element. */\n  get type() {\n    return this._type;\n  }\n  set type(value) {\n    this._type = value || 'text';\n    this._validateType();\n    // When using Angular inputs, developers are no longer able to set the properties on the native\n    // input element. To ensure that bindings for `type` work, we need to sync the setter\n    // with the native property. Textarea elements don't support the type property or attribute.\n    if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n      this._elementRef.nativeElement.type = this._type;\n    }\n    this._ensureWheelDefaultBehavior();\n  }\n  /** An object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get value() {\n    return this._inputValueAccessor.value;\n  }\n  set value(value) {\n    if (value !== this.value) {\n      this._inputValueAccessor.value = value;\n      this.stateChanges.next();\n    }\n  }\n  /** Whether the element is readonly. */\n  get readonly() {\n    return this._readonly;\n  }\n  set readonly(value) {\n    this._readonly = coerceBooleanProperty(value);\n  }\n  /** Whether the input is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  constructor(_elementRef, _platform, ngControl, parentForm, parentFormGroup, defaultErrorStateMatcher, inputValueAccessor, _autofillMonitor, _ngZone,\n  // TODO: Remove this once the legacy appearance has been removed. We only need\n  // to inject the form field for determining whether the placeholder has been promoted.\n  _formField) {\n    this._elementRef = _elementRef;\n    this._platform = _platform;\n    this.ngControl = ngControl;\n    this._autofillMonitor = _autofillMonitor;\n    this._ngZone = _ngZone;\n    this._formField = _formField;\n    this._uid = `mat-input-${nextUniqueId++}`;\n    this._webkitBlinkWheelListenerAttached = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    this.focused = false;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    this.stateChanges = new Subject();\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    this.controlType = 'mat-input';\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    this.autofilled = false;\n    this._disabled = false;\n    this._type = 'text';\n    this._readonly = false;\n    this._neverEmptyInputTypes = ['date', 'datetime', 'datetime-local', 'month', 'time', 'week'].filter(t => getSupportedInputTypes().has(t));\n    this._iOSKeyupListener = event => {\n      const el = event.target;\n      // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n      // indicate different things. If the value is 0, it means that the caret is at the start\n      // of the input, whereas a value of `null` means that the input doesn't support\n      // manipulating the selection range. Inputs that don't support setting the selection range\n      // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n      // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n      if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n        // Note: Just setting `0, 0` doesn't fix the issue. Setting\n        // `1, 1` fixes it for the first time that you type text and\n        // then hold delete. Toggling to `1, 1` and then back to\n        // `0, 0` seems to completely fix it.\n        el.setSelectionRange(1, 1);\n        el.setSelectionRange(0, 0);\n      }\n    };\n    this._webkitBlinkWheelListener = () => {\n      // This is a noop function and is used to enable mouse wheel input\n      // on number inputs\n      // on blink and webkit browsers.\n    };\n    const element = this._elementRef.nativeElement;\n    const nodeName = element.nodeName.toLowerCase();\n    // If no input value accessor was explicitly specified, use the element as the input value\n    // accessor.\n    this._inputValueAccessor = inputValueAccessor || element;\n    this._previousNativeValue = this.value;\n    // Force setter to be called in case id was not specified.\n    this.id = this.id;\n    // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n    // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n    // exists on iOS, we only bother to install the listener on iOS.\n    if (_platform.IOS) {\n      _ngZone.runOutsideAngular(() => {\n        _elementRef.nativeElement.addEventListener('keyup', this._iOSKeyupListener);\n      });\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, ngControl, parentFormGroup, parentForm, this.stateChanges);\n    this._isServer = !this._platform.isBrowser;\n    this._isNativeSelect = nodeName === 'select';\n    this._isTextarea = nodeName === 'textarea';\n    this._isInFormField = !!_formField;\n    if (this._isNativeSelect) {\n      this.controlType = element.multiple ? 'mat-native-select-multiple' : 'mat-native-select';\n    }\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n        this.autofilled = event.isAutofilled;\n        this.stateChanges.next();\n      });\n    }\n  }\n  ngOnChanges() {\n    this.stateChanges.next();\n  }\n  ngOnDestroy() {\n    this.stateChanges.complete();\n    if (this._platform.isBrowser) {\n      this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n    }\n    if (this._platform.IOS) {\n      this._elementRef.nativeElement.removeEventListener('keyup', this._iOSKeyupListener);\n    }\n    if (this._webkitBlinkWheelListenerAttached) {\n      this._elementRef.nativeElement.removeEventListener('wheel', this._webkitBlinkWheelListener);\n    }\n  }\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n      // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n      // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n      // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n      // disabled.\n      if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n        this.disabled = this.ngControl.disabled;\n        this.stateChanges.next();\n      }\n    }\n    // We need to dirty-check the native element's value, because there are some cases where\n    // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n    // updating the value using `emitEvent: false`).\n    this._dirtyCheckNativeValue();\n    // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n    // present or not depends on a query which is prone to \"changed after checked\" errors.\n    this._dirtyCheckPlaceholder();\n  }\n  /** Focuses the input. */\n  focus(options) {\n    this._elementRef.nativeElement.focus(options);\n  }\n  /** Refreshes the error state of the input. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** Callback for the cases where the focused state of the input changes. */\n  _focusChanged(isFocused) {\n    if (isFocused !== this.focused) {\n      this.focused = isFocused;\n      this.stateChanges.next();\n    }\n  }\n  _onInput() {\n    // This is a noop function and is used to let Angular know whenever the value changes.\n    // Angular will run a new change detection each time the `input` event has been dispatched.\n    // It's necessary that Angular recognizes the value change, because when floatingLabel\n    // is set to false and Angular forms aren't used, the placeholder won't recognize the\n    // value changes and will not disappear.\n    // Listening to the input event wouldn't be necessary when the input is using the\n    // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n  }\n  /** Does some manual dirty checking on the native input `value` property. */\n  _dirtyCheckNativeValue() {\n    const newValue = this._elementRef.nativeElement.value;\n    if (this._previousNativeValue !== newValue) {\n      this._previousNativeValue = newValue;\n      this.stateChanges.next();\n    }\n  }\n  /** Does some manual dirty checking on the native input `placeholder` attribute. */\n  _dirtyCheckPlaceholder() {\n    const placeholder = this._getPlaceholder();\n    if (placeholder !== this._previousPlaceholder) {\n      const element = this._elementRef.nativeElement;\n      this._previousPlaceholder = placeholder;\n      placeholder ? element.setAttribute('placeholder', placeholder) : element.removeAttribute('placeholder');\n    }\n  }\n  /** Gets the current placeholder of the form field. */\n  _getPlaceholder() {\n    return this.placeholder || null;\n  }\n  /** Make sure the input is a supported type. */\n  _validateType() {\n    if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMatInputUnsupportedTypeError(this._type);\n    }\n  }\n  /** Checks whether the input type is one of the types that are never empty. */\n  _isNeverEmpty() {\n    return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n  }\n  /** Checks whether the input is invalid based on the native validation. */\n  _isBadInput() {\n    // The `validity` property won't be present on platform-server.\n    let validity = this._elementRef.nativeElement.validity;\n    return validity && validity.badInput;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get empty() {\n    return !this._isNeverEmpty() && !this._elementRef.nativeElement.value && !this._isBadInput() && !this.autofilled;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    if (this._isNativeSelect) {\n      // For a single-selection `<select>`, the label should float when the selected option has\n      // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n      // overlapping the label with the options.\n      const selectElement = this._elementRef.nativeElement;\n      const firstOption = selectElement.options[0];\n      // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n      // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n      return this.focused || selectElement.multiple || !this.empty || !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label);\n    } else {\n      return this.focused || !this.empty;\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    if (ids.length) {\n      this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      this._elementRef.nativeElement.removeAttribute('aria-describedby');\n    }\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick() {\n    // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n    // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n    // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n    if (!this.focused) {\n      this.focus();\n    }\n  }\n  /** Whether the form control is a native select that is displayed inline. */\n  _isInlineSelect() {\n    const element = this._elementRef.nativeElement;\n    return this._isNativeSelect && (element.multiple || element.size > 1);\n  }\n  /**\n   * In blink and webkit browsers a focused number input does not increment or decrement its value\n   * on mouse wheel interaction unless a wheel event listener is attached to it or one of its ancestors or a passive wheel listener is attached somewhere in the DOM.\n   * For example: Hitting a tooltip once enables the mouse wheel input for all number inputs as long as it exists.\n   * In order to get reliable and intuitive behavior we apply a wheel event on our own\n   * thus making sure increment and decrement by mouse wheel works every time.\n   * @docs-private\n   */\n  _ensureWheelDefaultBehavior() {\n    if (!this._webkitBlinkWheelListenerAttached && this._type === 'number' && (this._platform.BLINK || this._platform.WEBKIT)) {\n      this._ngZone.runOutsideAngular(() => {\n        this._elementRef.nativeElement.addEventListener('wheel', this._webkitBlinkWheelListener);\n      });\n      this._webkitBlinkWheelListenerAttached = true;\n    }\n    if (this._webkitBlinkWheelListenerAttached && this._type !== 'number') {\n      this._elementRef.nativeElement.removeEventListener('wheel', this._webkitBlinkWheelListener);\n      this._webkitBlinkWheelListenerAttached = true;\n    }\n  }\n  static {\n    this.ɵfac = function MatInput_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatInput)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i2.NgControl, 10), i0.ɵɵdirectiveInject(i2.NgForm, 8), i0.ɵɵdirectiveInject(i2.FormGroupDirective, 8), i0.ɵɵdirectiveInject(i3.ErrorStateMatcher), i0.ɵɵdirectiveInject(MAT_INPUT_VALUE_ACCESSOR, 10), i0.ɵɵdirectiveInject(i4.AutofillMonitor), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_FORM_FIELD, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatInput,\n      selectors: [[\"input\", \"matInput\", \"\"], [\"textarea\", \"matInput\", \"\"], [\"select\", \"matNativeControl\", \"\"], [\"input\", \"matNativeControl\", \"\"], [\"textarea\", \"matNativeControl\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-input-element\"],\n      hostVars: 18,\n      hostBindings: function MatInput_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"focus\", function MatInput_focus_HostBindingHandler() {\n            return ctx._focusChanged(true);\n          })(\"blur\", function MatInput_blur_HostBindingHandler() {\n            return ctx._focusChanged(false);\n          })(\"input\", function MatInput_input_HostBindingHandler() {\n            return ctx._onInput();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id)(\"disabled\", ctx.disabled)(\"required\", ctx.required);\n          i0.ɵɵattribute(\"name\", ctx.name || null)(\"readonly\", ctx.readonly && !ctx._isNativeSelect || null)(\"aria-invalid\", ctx.empty && ctx.required ? null : ctx.errorState)(\"aria-required\", ctx.required)(\"id\", ctx.id);\n          i0.ɵɵclassProp(\"mat-input-server\", ctx._isServer)(\"mat-mdc-form-field-textarea-control\", ctx._isInFormField && ctx._isTextarea)(\"mat-mdc-form-field-input-control\", ctx._isInFormField)(\"mdc-text-field__input\", ctx._isInFormField)(\"mat-mdc-native-select-inline\", ctx._isInlineSelect());\n        }\n      },\n      inputs: {\n        disabled: \"disabled\",\n        id: \"id\",\n        placeholder: \"placeholder\",\n        name: \"name\",\n        required: \"required\",\n        type: \"type\",\n        errorStateMatcher: \"errorStateMatcher\",\n        userAriaDescribedBy: [0, \"aria-describedby\", \"userAriaDescribedBy\"],\n        value: \"value\",\n        readonly: \"readonly\"\n      },\n      exportAs: [\"matInput\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MatFormFieldControl,\n        useExisting: MatInput\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInput, [{\n    type: Directive,\n    args: [{\n      selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n      exportAs: 'matInput',\n      host: {\n        'class': 'mat-mdc-input-element',\n        // The BaseMatInput parent class adds `mat-input-element`, `mat-form-field-control` and\n        // `mat-form-field-autofill-control` to the CSS class list, but this should not be added for\n        // this MDC equivalent input.\n        '[class.mat-input-server]': '_isServer',\n        '[class.mat-mdc-form-field-textarea-control]': '_isInFormField && _isTextarea',\n        '[class.mat-mdc-form-field-input-control]': '_isInFormField',\n        '[class.mdc-text-field__input]': '_isInFormField',\n        '[class.mat-mdc-native-select-inline]': '_isInlineSelect()',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[id]': 'id',\n        '[disabled]': 'disabled',\n        '[required]': 'required',\n        '[attr.name]': 'name || null',\n        '[attr.readonly]': 'readonly && !_isNativeSelect || null',\n        // Only mark the input as invalid for assistive technology if it has a value since the\n        // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n        '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n        '[attr.aria-required]': 'required',\n        // Native input properties that are overwritten by Angular inputs need to be synced with\n        // the native input element. Otherwise property bindings for those don't work.\n        '[attr.id]': 'id',\n        '(focus)': '_focusChanged(true)',\n        '(blur)': '_focusChanged(false)',\n        '(input)': '_onInput()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatInput\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.Platform\n  }, {\n    type: i2.NgControl,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }]\n  }, {\n    type: i2.NgForm,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.FormGroupDirective,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i3.ErrorStateMatcher\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }, {\n      type: Inject,\n      args: [MAT_INPUT_VALUE_ACCESSOR]\n    }]\n  }, {\n    type: i4.AutofillMonitor\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i5.MatFormField,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_FORM_FIELD]\n    }]\n  }], {\n    disabled: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    required: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    userAriaDescribedBy: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    value: [{\n      type: Input\n    }],\n    readonly: [{\n      type: Input\n    }]\n  });\n})();\nclass MatInputModule {\n  static {\n    this.ɵfac = function MatInputModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatInputModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatInputModule,\n      imports: [MatCommonModule, MatFormFieldModule, MatInput],\n      exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatFormFieldModule, MatFormFieldModule, TextFieldModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatInputModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatFormFieldModule, MatInput],\n      exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,IAAM,kBAAkB,gCAAgC;AAAA,EACtD,SAAS;AACX,CAAC;AAMD,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,WAAW,SAAS;AAC9B,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,qBAAqB,oBAAI,IAAI;AAAA,EACpC;AAAA,EACA,QAAQ,cAAc;AACpB,QAAI,CAAC,KAAK,UAAU,WAAW;AAC7B,aAAO;AAAA,IACT;AACA,UAAM,UAAU,cAAc,YAAY;AAC1C,UAAM,OAAO,KAAK,mBAAmB,IAAI,OAAO;AAChD,QAAI,MAAM;AACR,aAAO,KAAK;AAAA,IACd;AACA,UAAM,SAAS,IAAI,QAAQ;AAC3B,UAAM,WAAW;AACjB,UAAM,WAAW,WAAS;AAIxB,UAAI,MAAM,kBAAkB,mCAAmC,CAAC,QAAQ,UAAU,SAAS,QAAQ,GAAG;AACpG,gBAAQ,UAAU,IAAI,QAAQ;AAC9B,aAAK,QAAQ,IAAI,MAAM,OAAO,KAAK;AAAA,UACjC,QAAQ,MAAM;AAAA,UACd,cAAc;AAAA,QAChB,CAAC,CAAC;AAAA,MACJ,WAAW,MAAM,kBAAkB,iCAAiC,QAAQ,UAAU,SAAS,QAAQ,GAAG;AACxG,gBAAQ,UAAU,OAAO,QAAQ;AACjC,aAAK,QAAQ,IAAI,MAAM,OAAO,KAAK;AAAA,UACjC,QAAQ,MAAM;AAAA,UACd,cAAc;AAAA,QAChB,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,SAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAQ,iBAAiB,kBAAkB,UAAU,eAAe;AACpE,cAAQ,UAAU,IAAI,mCAAmC;AAAA,IAC3D,CAAC;AACD,SAAK,mBAAmB,IAAI,SAAS;AAAA,MACnC,SAAS;AAAA,MACT,UAAU,MAAM;AACd,gBAAQ,oBAAoB,kBAAkB,UAAU,eAAe;AAAA,MACzE;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,eAAe,cAAc;AAC3B,UAAM,UAAU,cAAc,YAAY;AAC1C,UAAM,OAAO,KAAK,mBAAmB,IAAI,OAAO;AAChD,QAAI,MAAM;AACR,WAAK,SAAS;AACd,WAAK,QAAQ,SAAS;AACtB,cAAQ,UAAU,OAAO,mCAAmC;AAC5D,cAAQ,UAAU,OAAO,2BAA2B;AACpD,WAAK,mBAAmB,OAAO,OAAO;AAAA,IACxC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB,QAAQ,CAAC,OAAO,YAAY,KAAK,eAAe,OAAO,CAAC;AAAA,EAClF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAoB,SAAY,QAAQ,GAAM,SAAY,MAAM,CAAC;AAAA,IACpG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,MACzB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAEH,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,YAAY,aAAa,kBAAkB;AACzC,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAExB,SAAK,cAAc,IAAI,aAAa;AAAA,EACtC;AAAA,EACA,WAAW;AACT,SAAK,iBAAiB,QAAQ,KAAK,WAAW,EAAE,UAAU,WAAS,KAAK,YAAY,KAAK,KAAK,CAAC;AAAA,EACjG;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,eAAe,KAAK,WAAW;AAAA,EACvD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAgB,kBAAqB,UAAU,GAAM,kBAAkB,eAAe,CAAC;AAAA,IAC1H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,SAAS;AAAA,QACP,aAAa;AAAA,MACf;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,sBAAN,MAAM,qBAAoB;AAAA;AAAA,EAExB,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,qBAAqB,KAAK;AAC1C,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AACjB,SAAK,WAAW,qBAAqB,KAAK;AAC1C,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,QAAQ,OAAO;AAGjB,QAAI,KAAK,aAAa,OAAO;AAC3B,OAAC,KAAK,WAAW,SAAS,KAAK,mBAAmB,IAAI,IAAI,KAAK,MAAM;AAAA,IACvE;AAAA,EACF;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,2BAA2B;AAChC,QAAI,OAAO;AACT,WAAK,iBAAiB,aAAa,eAAe,KAAK;AAAA,IACzD,OAAO;AACL,WAAK,iBAAiB,gBAAgB,aAAa;AAAA,IACrD;AACA,SAAK,gCAAgC;AAAA,EACvC;AAAA,EACA,YAAY,aAAa,WAAW,SACpCA,WAAU;AACR,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,WAAW;AAMhB,SAAK,mBAAmB;AACxB,SAAK,gBAAgB;AAErB,SAAK,oBAAoB,WAAS;AAChC,WAAK,YAAY,MAAM,SAAS;AAAA,IAClC;AACA,SAAK,YAAYA;AACjB,SAAK,mBAAmB,KAAK,YAAY;AAAA,EAC3C;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,YAAY,KAAK,WAAW,KAAK,oBAAoB,GAAG,KAAK,UAAU,KAAK,iBAAiB,OAAO;AAC1G,QAAI,WAAW;AACb,WAAK,iBAAiB,MAAM,YAAY;AAAA,IAC1C;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AACd,UAAM,YAAY,KAAK,WAAW,KAAK,oBAAoB,GAAG,KAAK,UAAU,KAAK,iBAAiB,OAAO;AAC1G,QAAI,WAAW;AACb,WAAK,iBAAiB,MAAM,YAAY;AAAA,IAC1C;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU,WAAW;AAE5B,WAAK,iBAAiB,KAAK,iBAAiB,MAAM;AAClD,WAAK,mBAAmB;AACxB,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAMC,UAAS,KAAK,WAAW;AAC/B,kBAAUA,SAAQ,QAAQ,EAAE,KAAK,UAAU,EAAE,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,IAAI,CAAC;AACzH,aAAK,iBAAiB,iBAAiB,SAAS,KAAK,iBAAiB;AACtE,aAAK,iBAAiB,iBAAiB,QAAQ,KAAK,iBAAiB;AAAA,MACvE,CAAC;AACD,WAAK,gBAAgB;AACrB,WAAK,mBAAmB,IAAI;AAAA,IAC9B;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,oBAAoB,SAAS,KAAK,iBAAiB;AACzE,SAAK,iBAAiB,oBAAoB,QAAQ,KAAK,iBAAiB;AACxE,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,2BAA2B;AACzB,QAAI,KAAK,mBAAmB;AAC1B;AAAA,IACF;AAEA,QAAI,gBAAgB,KAAK,iBAAiB,UAAU,KAAK;AACzD,kBAAc,OAAO;AAIrB,kBAAc,MAAM,WAAW;AAC/B,kBAAc,MAAM,aAAa;AACjC,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,UAAU;AAC9B,kBAAc,MAAM,SAAS;AAC7B,kBAAc,MAAM,YAAY;AAChC,kBAAc,MAAM,YAAY;AAMhC,kBAAc,MAAM,WAAW;AAC/B,SAAK,iBAAiB,WAAW,YAAY,aAAa;AAC1D,SAAK,oBAAoB,cAAc;AACvC,kBAAc,OAAO;AAErB,SAAK,cAAc;AACnB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,uBAAuB;AACrB,UAAM,UAAU,KAAK;AACrB,UAAM,iBAAiB,QAAQ,MAAM,gBAAgB;AACrD,UAAM,YAAY,KAAK,UAAU;AACjC,UAAM,oBAAoB,aAAa,KAAK;AAC5C,UAAM,iBAAiB,YAAY,4CAA4C;AAI/E,QAAI,mBAAmB;AACrB,cAAQ,MAAM,eAAe,GAAG,QAAQ,YAAY;AAAA,IACtD;AAGA,YAAQ,UAAU,IAAI,cAAc;AAGpC,UAAM,eAAe,QAAQ,eAAe;AAC5C,YAAQ,UAAU,OAAO,cAAc;AACvC,QAAI,mBAAmB;AACrB,cAAQ,MAAM,eAAe;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAAA,EACA,kCAAkC;AAChC,QAAI,CAAC,KAAK,iBAAiB,KAAK,4BAA4B,QAAW;AACrE;AAAA,IACF;AACA,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,2BAA2B;AAChC;AAAA,IACF;AACA,UAAM,QAAQ,KAAK,iBAAiB;AACpC,SAAK,iBAAiB,QAAQ,KAAK,iBAAiB;AACpD,SAAK,2BAA2B,KAAK,qBAAqB;AAC1D,SAAK,iBAAiB,QAAQ;AAAA,EAChC;AAAA,EACA,YAAY;AACV,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,mBAAmB;AAAA,IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,QAAQ,OAAO;AAEhC,QAAI,CAAC,KAAK,UAAU;AAClB;AAAA,IACF;AACA,SAAK,yBAAyB;AAC9B,SAAK,gCAAgC;AAGrC,QAAI,CAAC,KAAK,mBAAmB;AAC3B;AAAA,IACF;AACA,UAAM,WAAW,KAAK,YAAY;AAClC,UAAM,QAAQ,SAAS;AAEvB,QAAI,CAAC,SAAS,KAAK,aAAa,KAAK,oBAAoB,UAAU,KAAK,gBAAgB;AACtF;AAAA,IACF;AACA,UAAM,eAAe,KAAK,qBAAqB;AAC/C,UAAM,SAAS,KAAK,IAAI,cAAc,KAAK,4BAA4B,CAAC;AAExE,aAAS,MAAM,SAAS,GAAG,MAAM;AACjC,SAAK,QAAQ,kBAAkB,MAAM;AACnC,UAAI,OAAO,0BAA0B,aAAa;AAChD,8BAAsB,MAAM,KAAK,uBAAuB,QAAQ,CAAC;AAAA,MACnE,OAAO;AACL,mBAAW,MAAM,KAAK,uBAAuB,QAAQ,CAAC;AAAA,MACxD;AAAA,IACF,CAAC;AACD,SAAK,iBAAiB;AACtB,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AAGN,QAAI,KAAK,mBAAmB,QAAW;AACrC,WAAK,iBAAiB,MAAM,SAAS,KAAK;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,oBAAoB;AAAA,EAEpB;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,aAAa;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa;AACX,UAAM,MAAM,KAAK,aAAa;AAC9B,WAAO,IAAI,eAAe;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,uBAAuB,UAAU;AAC/B,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAOJ,QAAI,CAAC,KAAK,WAAW,aAAa,KAAK,WAAW;AAChD,eAAS,kBAAkB,gBAAgB,YAAY;AAAA,IACzD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,kBAAqB,UAAU,GAAM,kBAAqB,QAAQ,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,UAAU,CAAC,CAAC;AAAA,IAClM;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,uBAAuB,EAAE,CAAC;AAAA,MACnD,WAAW,CAAC,QAAQ,KAAK,GAAG,uBAAuB;AAAA,MACnD,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,+CAA+C;AAC7E,mBAAO,IAAI,kBAAkB;AAAA,UAC/B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS,CAAC,GAAG,sBAAsB,SAAS;AAAA,QAC5C,SAAS,CAAC,GAAG,sBAAsB,SAAS;AAAA,QAC5C,SAAS,CAAC,GAAG,uBAAuB,WAAW,gBAAgB;AAAA,QAC/D,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,qBAAqB;AAAA,MAChC,YAAY;AAAA,MACZ,UAAU,CAAI,wBAAwB;AAAA,IACxC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA,QAGT,QAAQ;AAAA,QACR,WAAW;AAAA,MACb;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,aAAa,mBAAmB;AAAA,MAC1C,SAAS,CAAC,aAAa,mBAAmB;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,aAAa,mBAAmB;AAAA,MAC1C,SAAS,CAAC,aAAa,mBAAmB;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC9eH,SAAS,gCAAgC,MAAM;AAC7C,SAAO,MAAM,eAAe,IAAI,gCAAgC;AAClE;AAQA,IAAM,2BAA2B,IAAI,eAAe,0BAA0B;AAG9E,IAAM,0BAA0B,CAAC,UAAU,YAAY,QAAQ,UAAU,SAAS,SAAS,SAAS,SAAS,QAAQ;AACrH,IAAI,eAAe;AACnB,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAG5C,QAAI,KAAK,SAAS;AAChB,WAAK,UAAU;AACf,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK;AACP,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,GAAG,OAAO;AACZ,SAAK,MAAM,SAAS,KAAK;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK,aAAa,KAAK,WAAW,SAAS,aAAa,WAAW,QAAQ,KAAK;AAAA,EACzF;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAAA,EAC9C;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,OAAO;AACd,SAAK,QAAQ,SAAS;AACtB,SAAK,cAAc;AAInB,QAAI,CAAC,KAAK,eAAe,uBAAuB,EAAE,IAAI,KAAK,KAAK,GAAG;AACjE,WAAK,YAAY,cAAc,OAAO,KAAK;AAAA,IAC7C;AACA,SAAK,4BAA4B;AAAA,EACnC;AAAA;AAAA,EAEA,IAAI,oBAAoB;AACtB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,kBAAkB,OAAO;AAC3B,SAAK,mBAAmB,UAAU;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,IAAI,MAAM,OAAO;AACf,QAAI,UAAU,KAAK,OAAO;AACxB,WAAK,oBAAoB,QAAQ;AACjC,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,OAAO;AAClB,SAAK,YAAY,sBAAsB,KAAK;AAAA,EAC9C;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,mBAAmB,aAAa;AAAA,EACvC;AAAA,EACA,YAAY,aAAa,WAAW,WAAW,YAAY,iBAAiB,0BAA0B,oBAAoB,kBAAkB,SAG5I,YAAY;AACV,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,mBAAmB;AACxB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,OAAO,aAAa,cAAc;AACvC,SAAK,oCAAoC;AAKzC,SAAK,UAAU;AAKf,SAAK,eAAe,IAAI,QAAQ;AAKhC,SAAK,cAAc;AAKnB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,wBAAwB,CAAC,QAAQ,YAAY,kBAAkB,SAAS,QAAQ,MAAM,EAAE,OAAO,OAAK,uBAAuB,EAAE,IAAI,CAAC,CAAC;AACxI,SAAK,oBAAoB,WAAS;AAChC,YAAM,KAAK,MAAM;AAOjB,UAAI,CAAC,GAAG,SAAS,GAAG,mBAAmB,KAAK,GAAG,iBAAiB,GAAG;AAKjE,WAAG,kBAAkB,GAAG,CAAC;AACzB,WAAG,kBAAkB,GAAG,CAAC;AAAA,MAC3B;AAAA,IACF;AACA,SAAK,4BAA4B,MAAM;AAAA,IAIvC;AACA,UAAM,UAAU,KAAK,YAAY;AACjC,UAAM,WAAW,QAAQ,SAAS,YAAY;AAG9C,SAAK,sBAAsB,sBAAsB;AACjD,SAAK,uBAAuB,KAAK;AAEjC,SAAK,KAAK,KAAK;AAIf,QAAI,UAAU,KAAK;AACjB,cAAQ,kBAAkB,MAAM;AAC9B,oBAAY,cAAc,iBAAiB,SAAS,KAAK,iBAAiB;AAAA,MAC5E,CAAC;AAAA,IACH;AACA,SAAK,qBAAqB,IAAI,mBAAmB,0BAA0B,WAAW,iBAAiB,YAAY,KAAK,YAAY;AACpI,SAAK,YAAY,CAAC,KAAK,UAAU;AACjC,SAAK,kBAAkB,aAAa;AACpC,SAAK,cAAc,aAAa;AAChC,SAAK,iBAAiB,CAAC,CAAC;AACxB,QAAI,KAAK,iBAAiB;AACxB,WAAK,cAAc,QAAQ,WAAW,+BAA+B;AAAA,IACvE;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,iBAAiB,QAAQ,KAAK,YAAY,aAAa,EAAE,UAAU,WAAS;AAC/E,aAAK,aAAa,MAAM;AACxB,aAAK,aAAa,KAAK;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,SAAS;AAC3B,QAAI,KAAK,UAAU,WAAW;AAC5B,WAAK,iBAAiB,eAAe,KAAK,YAAY,aAAa;AAAA,IACrE;AACA,QAAI,KAAK,UAAU,KAAK;AACtB,WAAK,YAAY,cAAc,oBAAoB,SAAS,KAAK,iBAAiB;AAAA,IACpF;AACA,QAAI,KAAK,mCAAmC;AAC1C,WAAK,YAAY,cAAc,oBAAoB,SAAS,KAAK,yBAAyB;AAAA,IAC5F;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,WAAW;AAIlB,WAAK,iBAAiB;AAKtB,UAAI,KAAK,UAAU,aAAa,QAAQ,KAAK,UAAU,aAAa,KAAK,UAAU;AACjF,aAAK,WAAW,KAAK,UAAU;AAC/B,aAAK,aAAa,KAAK;AAAA,MACzB;AAAA,IACF;AAIA,SAAK,uBAAuB;AAG5B,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,MAAM,SAAS;AACb,SAAK,YAAY,cAAc,MAAM,OAAO;AAAA,EAC9C;AAAA;AAAA,EAEA,mBAAmB;AACjB,SAAK,mBAAmB,iBAAiB;AAAA,EAC3C;AAAA;AAAA,EAEA,cAAc,WAAW;AACvB,QAAI,cAAc,KAAK,SAAS;AAC9B,WAAK,UAAU;AACf,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA,EACA,WAAW;AAAA,EAQX;AAAA;AAAA,EAEA,yBAAyB;AACvB,UAAM,WAAW,KAAK,YAAY,cAAc;AAChD,QAAI,KAAK,yBAAyB,UAAU;AAC1C,WAAK,uBAAuB;AAC5B,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,yBAAyB;AACvB,UAAM,cAAc,KAAK,gBAAgB;AACzC,QAAI,gBAAgB,KAAK,sBAAsB;AAC7C,YAAM,UAAU,KAAK,YAAY;AACjC,WAAK,uBAAuB;AAC5B,oBAAc,QAAQ,aAAa,eAAe,WAAW,IAAI,QAAQ,gBAAgB,aAAa;AAAA,IACxG;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,eAAe;AAAA,EAC7B;AAAA;AAAA,EAEA,gBAAgB;AACd,QAAI,wBAAwB,QAAQ,KAAK,KAAK,IAAI,OAAO,OAAO,cAAc,eAAe,YAAY;AACvG,YAAM,gCAAgC,KAAK,KAAK;AAAA,IAClD;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB;AACd,WAAO,KAAK,sBAAsB,QAAQ,KAAK,KAAK,IAAI;AAAA,EAC1D;AAAA;AAAA,EAEA,cAAc;AAEZ,QAAI,WAAW,KAAK,YAAY,cAAc;AAC9C,WAAO,YAAY,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,QAAQ;AACV,WAAO,CAAC,KAAK,cAAc,KAAK,CAAC,KAAK,YAAY,cAAc,SAAS,CAAC,KAAK,YAAY,KAAK,CAAC,KAAK;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,mBAAmB;AACrB,QAAI,KAAK,iBAAiB;AAIxB,YAAM,gBAAgB,KAAK,YAAY;AACvC,YAAM,cAAc,cAAc,QAAQ,CAAC;AAG3C,aAAO,KAAK,WAAW,cAAc,YAAY,CAAC,KAAK,SAAS,CAAC,EAAE,cAAc,gBAAgB,MAAM,eAAe,YAAY;AAAA,IACpI,OAAO;AACL,aAAO,KAAK,WAAW,CAAC,KAAK;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,KAAK;AACrB,QAAI,IAAI,QAAQ;AACd,WAAK,YAAY,cAAc,aAAa,oBAAoB,IAAI,KAAK,GAAG,CAAC;AAAA,IAC/E,OAAO;AACL,WAAK,YAAY,cAAc,gBAAgB,kBAAkB;AAAA,IACnE;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AAIjB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA;AAAA,EAEA,kBAAkB;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,WAAO,KAAK,oBAAoB,QAAQ,YAAY,QAAQ,OAAO;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,8BAA8B;AAC5B,QAAI,CAAC,KAAK,qCAAqC,KAAK,UAAU,aAAa,KAAK,UAAU,SAAS,KAAK,UAAU,SAAS;AACzH,WAAK,QAAQ,kBAAkB,MAAM;AACnC,aAAK,YAAY,cAAc,iBAAiB,SAAS,KAAK,yBAAyB;AAAA,MACzF,CAAC;AACD,WAAK,oCAAoC;AAAA,IAC3C;AACA,QAAI,KAAK,qCAAqC,KAAK,UAAU,UAAU;AACrE,WAAK,YAAY,cAAc,oBAAoB,SAAS,KAAK,yBAAyB;AAC1F,WAAK,oCAAoC;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iBAAiB,mBAAmB;AACvD,aAAO,KAAK,qBAAqB,WAAa,kBAAqB,UAAU,GAAM,kBAAqB,QAAQ,GAAM,kBAAqB,WAAW,EAAE,GAAM,kBAAqB,QAAQ,CAAC,GAAM,kBAAqB,oBAAoB,CAAC,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,0BAA0B,EAAE,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,gBAAgB,CAAC,CAAC;AAAA,IACnc;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,SAAS,YAAY,EAAE,GAAG,CAAC,YAAY,YAAY,EAAE,GAAG,CAAC,UAAU,oBAAoB,EAAE,GAAG,CAAC,SAAS,oBAAoB,EAAE,GAAG,CAAC,YAAY,oBAAoB,EAAE,CAAC;AAAA,MAChL,WAAW,CAAC,GAAG,uBAAuB;AAAA,MACtC,UAAU;AAAA,MACV,cAAc,SAAS,sBAAsB,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,oCAAoC;AAClE,mBAAO,IAAI,cAAc,IAAI;AAAA,UAC/B,CAAC,EAAE,QAAQ,SAAS,mCAAmC;AACrD,mBAAO,IAAI,cAAc,KAAK;AAAA,UAChC,CAAC,EAAE,SAAS,SAAS,oCAAoC;AACvD,mBAAO,IAAI,SAAS;AAAA,UACtB,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,MAAM,IAAI,EAAE,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,IAAI,QAAQ;AAClF,UAAG,YAAY,QAAQ,IAAI,QAAQ,IAAI,EAAE,YAAY,IAAI,YAAY,CAAC,IAAI,mBAAmB,IAAI,EAAE,gBAAgB,IAAI,SAAS,IAAI,WAAW,OAAO,IAAI,UAAU,EAAE,iBAAiB,IAAI,QAAQ,EAAE,MAAM,IAAI,EAAE;AACjN,UAAG,YAAY,oBAAoB,IAAI,SAAS,EAAE,uCAAuC,IAAI,kBAAkB,IAAI,WAAW,EAAE,oCAAoC,IAAI,cAAc,EAAE,yBAAyB,IAAI,cAAc,EAAE,gCAAgC,IAAI,gBAAgB,CAAC;AAAA,QAC5R;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,IAAI;AAAA,QACJ,aAAa;AAAA,QACb,MAAM;AAAA,QACN,UAAU;AAAA,QACV,MAAM;AAAA,QACN,mBAAmB;AAAA,QACnB,qBAAqB,CAAC,GAAG,oBAAoB,qBAAqB;AAAA,QAClE,OAAO;AAAA,QACP,UAAU;AAAA,MACZ;AAAA,MACA,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,oBAAoB;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,UAAU,CAAC;AAAA,IACjF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA;AAAA,MAEV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA;AAAA;AAAA;AAAA,QAIT,4BAA4B;AAAA,QAC5B,+CAA+C;AAAA,QAC/C,4CAA4C;AAAA,QAC5C,iCAAiC;AAAA,QACjC,wCAAwC;AAAA;AAAA;AAAA,QAGxC,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,cAAc;AAAA,QACd,eAAe;AAAA,QACf,mBAAmB;AAAA;AAAA;AAAA,QAGnB,uBAAuB;AAAA,QACvB,wBAAwB;AAAA;AAAA;AAAA,QAGxB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AAAA,MACA,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,IAAI,CAAC;AAAA,MACH,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,oBAAoB,QAAQ;AAAA,MACvD,SAAS,CAAC,UAAU,oBAAoB,iBAAiB,eAAe;AAAA,IAC1E,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB,oBAAoB,oBAAoB,iBAAiB,eAAe;AAAA,IACrG,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,oBAAoB,QAAQ;AAAA,MACvD,SAAS,CAAC,UAAU,oBAAoB,iBAAiB,eAAe;AAAA,IAC1E,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["document", "window"]}