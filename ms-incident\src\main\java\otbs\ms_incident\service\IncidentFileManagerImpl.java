package otbs.ms_incident.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.enums.FileType;
import otbs.ms_incident.exception.FileNotFoundStorageException;
import otbs.ms_incident.exception.FileStorageException;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.IncidentMapper;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Stream;

/**
 * Implémentation du gestionnaire de fichiers spécifique aux incidents
 * Permet d'abstraire la logique de gestion des chemins et des types de fichiers
 */
@Service
public class IncidentFileManagerImpl implements IncidentFileManager {
    private static final Logger logger = LoggerFactory.getLogger(IncidentFileManagerImpl.class);

    // Constants for repeated literals
    private static final String PHOTO_TYPE = "PHOTO";
    private static final String CONSTAT_TYPE = "CONSTAT";
    private static final String INVALID_FILE_TYPE_LOG = "Invalid file type: {}";
    private static final String FILENAME_KEY = "filename";

    // Response map constants
    private static final String STATUS_KEY = "status";
    private static final String MESSAGE_KEY = "message";
    private static final String STATUS_SUCCESS = "success";
    private static final String STATUS_ERROR = "error";
    private static final String STATUS_PARTIAL = "partial";

    @Value("${storage.path:./files}")
    private String storagePath;

    private final FileStorageService fileStorageService;
    private final IncidentService incidentService;
    private final MessageSource messageSource;
    private final IncidentMapper incidentMapper;

    public IncidentFileManagerImpl(FileStorageService fileStorageService, IncidentService incidentService, MessageSource messageSource, IncidentMapper incidentMapper) {
        this.fileStorageService = fileStorageService;
        this.incidentService = incidentService;
        this.messageSource = messageSource;
        this.incidentMapper = incidentMapper;
    }

    private String getMessage(String code, Object... args) {
        return messageSource.getMessage(code, args, LocaleContextHolder.getLocale());
    }

    /**
     * Télécharge et associe un fichier à un incident
     *
     * @param file Fichier à télécharger
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param overwrite Écraser si existe déjà
     * @return Informations sur le fichier téléchargé et associé
     * @throws FileStorageException si le fichier ne peut pas être stocké
     */
    @Override
    public Map<String, Object> uploadFile(MultipartFile file, Long incidentId, FileType fileType, boolean overwrite) {
        logger.info("Uploading and associating file for incident: {}, type: {}", incidentId, fileType);

        if (file.isEmpty()) {
            logger.warn("Upload attempted with empty file");
            throw new FileStorageException(getMessage("file.storage.empty"));
        }

        // Valider le type de fichier et son contenu
        validateContentType(fileType, file.getContentType());

        // Sauvegarder le fichier
        Map<String, Object> result = saveIncidentFile(file, incidentId, fileType.name(), overwrite);

        // Mise à jour de l'incident
        updateIncidentWithFile(incidentId, fileType.name(), result);

        return result;
    }

    /**
     * Charge un fichier en tant que ressource
     *
     * @param filePath Chemin du fichier
     * @return Ressource représentant le fichier
     */
    @Override
    public Resource loadFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            Resource resource = new UrlResource(path.toUri());

            if (resource.exists() || resource.isReadable()) {
                return resource;
            } else {
                String errorMsg = getMessage("file.storage.notfound", filePath);
                throw new FileNotFoundStorageException(errorMsg);
            }
        } catch (MalformedURLException ex) {
            throw new FileNotFoundStorageException(
                getMessage("file.storage.notfound", filePath), ex
            );
        }
    }

    /**
     * Récupère le chemin d'un fichier pour un incident spécifique
     *
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier
     * @param filename Nom du fichier
     * @return Chemin vers le fichier
     */
    @Override
    public Path getFilePath(Long incidentId, FileType fileType, String filename) {
        // Tous les fichiers sont stockés directement dans le dossier principal
        Path basePath = Paths.get(storagePath);
        Path filePath = basePath.resolve(filename);

        if (!Files.exists(filePath)) {
            throw new FileNotFoundStorageException("Fichier non trouvé: " + filePath);
        }

        return filePath;
    }

    /**
     * Supprime un fichier et met à jour les références dans l'incident
     *
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param filename Nom du fichier
     * @return Map contenant le statut de la suppression
     */
    @Override
    public Map<String, Object> deleteFile(Long incidentId, FileType fileType, String filename) {
        logger.info("Deleting file and updating incident: {}, type: {}, filename: {}", incidentId, fileType, filename);

        try {
            // 1. Récupérer l'incident de la base de données
            otbs.ms_incident.entity.Incident incident = incidentService.getIncidentEntityById(incidentId);
            if (incident == null) {
                logger.error("Incident not found with id: {}", incidentId);
                throw new ResourceNotFoundException("Incident not found with id: " + incidentId);
            }

            logger.info("Incident found: {}, has photos: {}, has constat: {}",
                     incidentId,
                     incident.getPhotos() != null ? incident.getPhotos().size() : 0,
                     incident.getConstat() != null);

            // 2. Générer le chemin relatif comme il est stocké dans la base de données
            String relativePath = generateRelativePath(filename);

            logger.info("Attempting to remove file with relative path: {}", relativePath);

            // 3. Supprimer la référence au fichier dans l'entité Incident et le fichier physique
            return handleFileDeletion(incident, incidentId, fileType, filename);
        } catch (Exception e) {
            logger.error("Error deleting file: {}", e.getMessage(), e);
            Map<String, Object> result = new HashMap<>();
            result.put(STATUS_KEY, STATUS_ERROR);
            result.put(MESSAGE_KEY, "Failed to delete file: " + e.getMessage());
            return result;
        }
    }

    /**
     * Handles the actual file deletion logic, extracted from deleteFile method
     */
    private Map<String, Object> handleFileDeletion(otbs.ms_incident.entity.Incident incident, Long incidentId,
                                                  FileType fileType, String filename) {
        Map<String, Object> result = new HashMap<>();
        boolean removedFromEntity = false;

        if (FileType.CONSTAT.equals(fileType)) {
            removedFromEntity = handleConstatDeletion(incident, filename);
        } else if (FileType.PHOTO.equals(fileType)) {
            removedFromEntity = handlePhotoDeletion(incident, filename);
        }

        // Si la référence a été supprimée, mettre à jour l'entité
        if (removedFromEntity) {
            try {
                // Mettre à jour l'entité
                IncidentRequestDTO requestDTO = incidentMapper.toRequestDTO(incident);
                incidentService.updateIncident(incidentId, requestDTO);
                logger.info("Updated incident entity after file removal: {}", incidentId);
            } catch (Exception e) {
                logger.error("Failed to update incident after file removal: {}", e.getMessage(), e);
                result.put(STATUS_KEY, STATUS_PARTIAL);
                result.put(MESSAGE_KEY, "File removed but entity not updated: " + e.getMessage());
                return result;
            }
        }
        // Supprimer le fichier physique
        boolean fileDeleted = deletePhysicalFile(incidentId, fileType, filename);

        prepareFileDeleteResult(result, removedFromEntity, fileDeleted, filename);
        return result;
    }

    /**
     * Handles the deletion of constat files
     */
    private boolean handleConstatDeletion(otbs.ms_incident.entity.Incident incident, String filename) {
        logger.info("Constat value in database: {}", incident.getConstat());

        // Puisque nous stockons uniquement les noms de fichiers, nous comparons directement
        if (incident.getConstat() != null && incident.getConstat().equals(filename)) {
            // Mettre à null le champ constat
            incident.setConstat(null);
            logger.info("Removed constat reference from incident entity");
            return true;
        } else {
            logger.warn("Constat filename doesn't match: {} vs {}", incident.getConstat(), filename);
            return false;
        }
    }

    /**
     * Handles the deletion of photo files
     */
    private boolean handlePhotoDeletion(otbs.ms_incident.entity.Incident incident, String filename) {
        // Pour les photos, chercher toutes celles qui correspondent au nom de fichier
        if (incident.getPhotos() == null) {
            return false;
        }

        logger.info("Current photos in database: {}", incident.getPhotos());

        List<String> toRemove = new ArrayList<>();
        for (String photo : incident.getPhotos()) {
            // Puisque nous stockons uniquement les noms de fichiers, nous comparons directement
            if (photo.equals(filename)) {
                toRemove.add(photo);
                logger.info("Found photo to remove: {}", photo);
            }
        }

        if (!toRemove.isEmpty()) {
            incident.getPhotos().removeAll(toRemove);
            logger.info("Removed {} photo reference(s) from incident entity", toRemove.size());
            return true;
        }

        return false;
    }

    /**
     * Deletes physical file from storage
     */
    private boolean deletePhysicalFile(Long incidentId, FileType fileType, String filename) {
        try {
            // Générer le chemin complet du fichier physique
            Path filePath = buildFilePath(incidentId, fileType, filename);

            // Supprimer le fichier
            boolean deleted = fileStorageService.delete(filePath);
            if (deleted) {
                logger.info("File physically deleted: {}", filePath);
            } else {
                logger.warn("File not found for physical deletion: {}", filePath);
            }

            return deleted;
        } catch (Exception e) {
            logger.error("Error deleting physical file: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Prepares the result map for file deletion
     */
    private void prepareFileDeleteResult(Map<String, Object> result, boolean removedFromEntity,
                                        boolean fileDeleted, String filename) {
        if (removedFromEntity && fileDeleted) {
            result.put(STATUS_KEY, STATUS_SUCCESS);
            result.put(MESSAGE_KEY, "File removed completely: " + filename);
        } else if (removedFromEntity) {
            result.put(STATUS_KEY, STATUS_PARTIAL);
            result.put(MESSAGE_KEY, "File reference removed but physical file not found: " + filename);
        } else if (fileDeleted) {
            result.put(STATUS_KEY, STATUS_PARTIAL);
            result.put(MESSAGE_KEY, "Physical file deleted but no reference found to remove: " + filename);
        } else {
            result.put(STATUS_KEY, STATUS_ERROR);
            result.put(MESSAGE_KEY, "Failed to delete file: " + filename);
        }
    }

    private String generateRelativePath(String filename) {
        // Puisque nous stockons uniquement le nom du fichier dans la base de données,
        // cette méthode retourne simplement le nom du fichier
        return filename;
    }

    /**
     * Liste tous les fichiers associés à un incident
     *
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier optionnel (PHOTO ou CONSTAT)
     * @return Liste des fichiers avec leurs métadonnées
     */
    @Override
    public List<Map<String, Object>> listFiles(Long incidentId, FileType fileType) {
        logger.info("Listing files for incident: {}, fileType: {}", incidentId, fileType);

        List<Map<String, Object>> files = new ArrayList<>();
        Path basePath = Paths.get(storagePath);

        try {
            if (!Files.exists(basePath)) {
                logger.warn("Storage directory not found");
                return files; // Retourner une liste vide
            }

            // Lister tous les fichiers dans le dossier principal
            try (Stream<Path> paths = Files.list(basePath)) {
                paths.filter(Files::isRegularFile).forEach(path -> {
                    String filename = path.getFileName().toString().toLowerCase();

                    // Vérifier si le fichier appartient à l'incident spécifié
                    String incidentPrefix = "_" + incidentId + "_";
                    if (!filename.contains(incidentPrefix)) {
                        return; // Ignorer les fichiers qui n'appartiennent pas à cet incident
                    }

                    // Déterminer le type de fichier en fonction du préfixe ou de l'extension
                    String fileTypeStr;
                    if (filename.startsWith("photo_")) {
                        fileTypeStr = FileType.PHOTO.name();
                    } else if (filename.startsWith("constat_")) {
                        fileTypeStr = FileType.CONSTAT.name();
                    } else if (filename.endsWith(".jpg") || filename.endsWith(".jpeg") ||
                             filename.endsWith(".png") || filename.endsWith(".gif")) {
                        fileTypeStr = FileType.PHOTO.name();
                    } else if (filename.endsWith(".pdf")) {
                        fileTypeStr = FileType.CONSTAT.name();
                    } else {
                        // Type par défaut ou à déterminer selon les besoins
                        fileTypeStr = "UNKNOWN";
                    }

                    // Si un type spécifique est demandé, filtrer les fichiers
                    if (fileType == null || fileTypeStr.equals(fileType.name())) {
                        files.add(getFileInfo(path, fileTypeStr));
                    }
                });
            }

            return files;
        } catch (IOException e) {
            logger.error("Error listing files for incident: {}", incidentId, e);
            return files;
        }
    }

    /**
     * Valide le type MIME du fichier pour s'assurer qu'il est compatible avec le type de fichier
     *
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param contentType Type MIME du fichier
     * @throws FileStorageException si le type de fichier n'est pas valide
     */
    @Override
    public void validateContentType(FileType fileType, String contentType) {
        // Vérifier que le type de fichier est valide
        // Vérifier le type MIME pour les constats (doit être PDF)
        if (fileType == FileType.CONSTAT && contentType != null && !contentType.equalsIgnoreCase("application/pdf")) {
            logger.warn(INVALID_FILE_TYPE_LOG, contentType);
            throw new FileStorageException(getMessage("file.storage.constat.notpdf"));
        }
    }

    /**
     * Détermine le type de contenu basé sur l'extension du fichier
     *
     * @param filename Nom du fichier
     * @return Type MIME du fichier
     */
    @Override
    public String determineContentType(String filename) {
        String lowercaseFilename = filename.toLowerCase();

        if (lowercaseFilename.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowercaseFilename.endsWith(".jpg") || lowercaseFilename.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowercaseFilename.endsWith(".png")) {
            return "image/png";
        } else if (lowercaseFilename.endsWith(".gif")) {
            return "image/gif";
        } else if (lowercaseFilename.endsWith(".bmp")) {
            return "image/bmp";
        } else if (lowercaseFilename.endsWith(".tiff") || lowercaseFilename.endsWith(".tif")) {
            return "image/tiff";
        } else if (lowercaseFilename.endsWith(".svg")) {
            return "image/svg+xml";
        } else if (lowercaseFilename.endsWith(".webp")) {
            return "image/webp";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * Construit le chemin de stockage pour un fichier d'incident
     *
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param filename Nom du fichier (optionnel)
     * @return Chemin complet pour le stockage du fichier
     */
    @Override
    public Path buildFilePath(Long incidentId, FileType fileType, String filename) {
        // Tous les fichiers sont stockés directement dans le dossier principal
        Path basePath = Paths.get(storagePath);

        if (filename != null && !filename.isEmpty()) {
            return basePath.resolve(filename);
        }

        return basePath;
    }

    /**
     * Supprime un fichier d'un incident
     *
     * @param incidentId ID de l'incident
     * @param fileTypeStr Type de fichier
     * @param filename Nom du fichier
     * @return true si supprimé avec succès
     */
    @Override
    public boolean deleteIncidentFile(long incidentId, String fileTypeStr, String filename) {
        try {
            Path filePath = getIncidentFilePath(incidentId, fileTypeStr, filename);
            return fileStorageService.delete(filePath);
        } catch (FileNotFoundStorageException e) {
            // Si le fichier n'existe pas, on le signale dans les logs mais on ne lève pas d'exception
            logger.warn("File not found when attempting to delete: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            logger.error("Error deleting incident file: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * Récupère le chemin d'un fichier spécifique pour un incident
     *
     * @param incidentId ID de l'incident
     * @param fileTypeStr Type de fichier en chaîne de caractères
     * @param filename Nom du fichier
     * @return Chemin vers le fichier
     */
    @Override
    public Path getIncidentFilePath(long incidentId, String fileTypeStr, String filename) {
        // Tous les fichiers sont stockés directement dans le dossier principal
        Path basePath = Paths.get(storagePath);
        Path filePath = basePath.resolve(filename);

        // Journaliser le chemin pour le débogage
        logger.debug("Generated file path: {}", filePath);

        return filePath;
    }

    /**
     * Enregistre un fichier pour un incident spécifique
     *
     * @param file Fichier à enregistrer
     * @param incidentId ID de l'incident
     * @param fileTypeStr Type de fichier en chaîne de caractères
     * @param overwrite Écraser si existe déjà
     * @return Informations sur le fichier enregistré
     */
    @Override
    public Map<String, Object> saveIncidentFile(Object file, long incidentId, String fileTypeStr, boolean overwrite) {
        try {
            if (!(file instanceof MultipartFile)) {
                throw new FileStorageException("Le fichier doit être de type MultipartFile");
            }

            MultipartFile multipartFile = (MultipartFile) file;
            String originalFilename = multipartFile.getOriginalFilename();

            // Validation de l'extension du fichier
            fileStorageService.isValidFileExtension(originalFilename);

            // Construction du chemin - directement dans le dossier principal
            Path basePath = Paths.get(storagePath);

            // Créer le répertoire principal si nécessaire
            if (!Files.exists(basePath)) {
                Files.createDirectories(basePath);
                logger.info("Création du répertoire: {}", basePath);
            }

            // Générer un nom de fichier unique avec l'ID de l'incident inclus
            String fileExtension = originalFilename != null ?
                    originalFilename.substring(originalFilename.lastIndexOf('.')) : "";
            String prefix = PHOTO_TYPE.equals(fileTypeStr) ? "photo_" : "constat_";
            String uniqueFilename = prefix + incidentId + "_" + UUID.randomUUID().toString() + fileExtension;

            // Chemin complet du fichier - directement dans le dossier principal
            Path filePath = basePath.resolve(uniqueFilename);

            // Sauvegarder le fichier
            fileStorageService.save(filePath, multipartFile.getBytes(), overwrite);
            logger.info("Fichier enregistré: {}", filePath);

            // Préparer les informations de retour
            Map<String, Object> result = new HashMap<>();
            result.put(FILENAME_KEY, uniqueFilename);
            result.put("originalFilename", originalFilename);
            result.put("size", multipartFile.getSize());
            result.put("path", filePath.toString());
            result.put("fileType", fileTypeStr);
            result.put("incidentId", incidentId);

            return result;

        } catch (IOException e) {
            throw new FileStorageException(
                "Erreur lors de l'enregistrement du fichier: " + e.getMessage(),
                e,
                HttpStatus.INTERNAL_SERVER_ERROR,
                "FILE_SAVE_ERROR"
            );
        }
    }

    private void updateIncidentWithFile(Long incidentId, String fileType, Map<String, Object> fileInfo) {
        if (!fileInfo.containsKey(FILENAME_KEY)) {
            return;
        }

        IncidentResponseDTO incident = incidentService.getIncidentById(incidentId);
        String filename = fileInfo.get(FILENAME_KEY).toString();

        // Stocker uniquement le nom du fichier dans la base de données
        logger.info("Storing filename in database: {}", filename);

        // Mise à jour selon le type de fichier
        if (PHOTO_TYPE.equals(fileType)) {
            updateIncidentWithPhoto(incident, incidentId, filename);
        } else if (CONSTAT_TYPE.equals(fileType)) {
            updateIncidentWithConstat(incident, incidentId, filename);
        }
    }

    private void updateIncidentWithPhoto(IncidentResponseDTO incident, Long incidentId, String filePath) {
        // Ajouter le chemin de la photo à l'incident si pas déjà présent
        if (!incident.getPhotos().contains(filePath)) {
            List<String> photos = new ArrayList<>(incident.getPhotos());
            photos.add(filePath);

            IncidentRequestDTO updateRequest = new IncidentRequestDTO();
            updateRequest.setDate(incident.getDate());
            updateRequest.setType(incident.getType());
            updateRequest.setLieu(incident.getLieu());
            updateRequest.setDescription(incident.getDescription());
            updateRequest.setConstat(incident.getConstat());
            updateRequest.setPhotos(photos);

            incidentService.updateIncident(incidentId, updateRequest);
        }
    }

    private void updateIncidentWithConstat(IncidentResponseDTO incident, Long incidentId, String filePath) {
        // Mise à jour de la référence du constat
        IncidentRequestDTO updateRequest = new IncidentRequestDTO();
        updateRequest.setDate(incident.getDate());
        updateRequest.setType(incident.getType());
        updateRequest.setLieu(incident.getLieu());
        updateRequest.setDescription(incident.getDescription());
        updateRequest.setConstat(filePath);
        updateRequest.setPhotos(incident.getPhotos());

        incidentService.updateIncident(incidentId, updateRequest);
    }

    // Méthodes privées d'aide
    private Map<String, Object> getFileInfo(Path path, String fileType) {
        Map<String, Object> fileInfo = new HashMap<>();

        try {
            String filename = path.getFileName().toString();
            long size = Files.size(path);
            BasicFileAttributes attr = Files.readAttributes(path, BasicFileAttributes.class);
            Instant creationTime = attr.creationTime().toInstant();

            // Générer un chemin relatif pour l'API REST
            String relativePath = generateRelativePath(filename);

            fileInfo.put(FILENAME_KEY, filename);
            fileInfo.put("size", size);
            fileInfo.put("creationTime", creationTime);
            fileInfo.put("fileType", fileType);
            fileInfo.put("path", relativePath);

        } catch (IOException e) {
            logger.error("Could not read file attributes: {}", path, e);
        }

        return fileInfo;
    }

    /**
     * Vérifie si un incident existe dans la base de données
     *
     * @param incidentId ID de l'incident à vérifier
     * @throws ResourceNotFoundException si l'incident n'existe pas
     */
    @Override
    public void validateIncidentExists(Long incidentId) {
        logger.debug("Vérification de l'existence de l'incident avec ID: {}", incidentId);
        if (incidentId == null) {
            throw new ResourceNotFoundException("L'ID de l'incident ne peut pas être null");
        }

        // Utilisation du service d'incident pour vérifier l'existence
        try {
            incidentService.getIncidentById(incidentId);
            logger.debug("Incident avec ID: {} trouvé", incidentId);
        } catch (ResourceNotFoundException e) {
            logger.warn("Incident avec ID: {} non trouvé", incidentId);
            throw e;
        }
    }
}