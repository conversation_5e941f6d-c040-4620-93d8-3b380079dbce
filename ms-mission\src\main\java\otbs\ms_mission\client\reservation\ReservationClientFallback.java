package otbs.ms_mission.client.reservation;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Implémentation de fallback pour le client Feign ReservationClient.
 * Cette classe fournit des réponses par défaut lorsque le service de réservation est indisponible.
 */
@Component
@Slf4j
public class ReservationClientFallback implements ReservationClient {

    @Override
    public ReservationDto getReservationById(Long id) {
        log.warn("Fallback activé pour getReservationById avec id: {}", id);
        return createDefaultReservation(id, null);
    }

    @Override
    public List<ReservationDto> getReservationsByMissionId(Long idMission) {
        log.warn("Fallback activé pour getReservationsByMissionId avec idMission: {}", idMission);
        if (idMission == null) {
            return new ArrayList<>();
        }

        // Créer une réservation fictive pour cette mission
        List<ReservationDto> reservations = new ArrayList<>();
        ReservationDto reservation = createDefaultReservation(-1L, idMission);
        reservation.setReservationId(-idMission); // ID négatif pour indiquer que c'est une réservation fictive
        reservations.add(reservation);

        log.info("Création d'une réservation fictive pour la mission {}", idMission);
        return reservations;
    }

    @Override
    public ReservationDto createReservation(ReservationDto reservationDto) {
        log.warn("Fallback activé pour createReservation");
        // Nous ne pouvons pas créer de réservation en mode fallback, donc on retourne l'objet tel quel
        // mais avec un ID négatif pour indiquer qu'il n'a pas été persisté
        if (reservationDto != null) {
            reservationDto.setReservationId(-1L);
            return reservationDto;
        }
        return createDefaultReservation(-1L, null);
    }

    @Override
    public ReservationDto updateReservation(Long id, ReservationDto reservationDto) {
        log.warn("Fallback activé pour updateReservation avec id: {}", id);
        // Nous ne pouvons pas mettre à jour la réservation en mode fallback
        if (reservationDto != null) {
            reservationDto.setReservationId(id);
            return reservationDto;
        }
        return createDefaultReservation(id, null);
    }

    @Override
    public void deleteReservation(Long id) {
        log.warn("Fallback activé pour deleteReservation avec id: {}", id);
        // Rien à faire en mode fallback pour une suppression
    }

    /**
     * Crée un objet ReservationDto par défaut avec des informations minimales.
     *
     * @param id L'ID de la réservation (peut être null)
     * @param idMission L'ID de la mission associée (peut être null)
     * @return Un objet ReservationDto par défaut
     */
    private ReservationDto createDefaultReservation(Long id, Long idMission) {
        ReservationDto reservation = new ReservationDto();
        reservation.setReservationId(id);
        reservation.setDateReservation(new Date());
        reservation.setIdMission(idMission);
        reservation.setIdVehicule(null); // Pas de véhicule associé en mode fallback
        return reservation;
    }
}
