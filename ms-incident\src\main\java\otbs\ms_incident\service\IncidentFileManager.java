package otbs.ms_incident.service;

import org.springframework.web.multipart.MultipartFile;
import org.springframework.core.io.Resource;
import otbs.ms_incident.exception.FileStorageException;
import otbs.ms_incident.enums.FileType;

import java.nio.file.Path;
import java.util.List;
import java.util.Map;

/**
 * Interface de gestion des fichiers liés aux incidents
 */
public interface IncidentFileManager {

    /**
     * Télécharge et associe un fichier à un incident
     * 
     * @param file Fichier à télécharger
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param overwrite Écraser si existe déjà
     * @return Informations sur le fichier téléchargé et associé
     * @throws FileStorageException si le fichier ne peut pas être stocké
     */
    Map<String, Object> uploadFile(MultipartFile file, Long incidentId, FileType fileType, boolean overwrite);
    
    /**
     * Charge un fichier en tant que ressource
     * 
     * @param filePath Chemin du fichier
     * @return Ressource représentant le fichier
     */
    Resource loadFile(String filePath);
    
    /**
     * Récupère le chemin d'un fichier pour un incident spécifique
     * 
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier
     * @param filename Nom du fichier
     * @return Chemin vers le fichier
     */
    Path getFilePath(Long incidentId, FileType fileType, String filename);
    
    /**
     * Supprime un fichier et met à jour les références dans l'incident
     * 
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT) 
     * @param filename Nom du fichier
     * @return Map contenant le statut de la suppression
     */
    Map<String, Object> deleteFile(Long incidentId, FileType fileType, String filename);
    
    /**
     * Liste tous les fichiers associés à un incident
     * 
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier optionnel (PHOTO ou CONSTAT)
     * @return Liste des fichiers avec leurs métadonnées
     */
    List<Map<String, Object>> listFiles(Long incidentId, FileType fileType);
    
    /**
     * Valide le type MIME du fichier pour s'assurer qu'il est compatible avec le type de fichier
     * 
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param contentType Type MIME du fichier
     * @throws FileStorageException si le type de fichier n'est pas valide
     */
    void validateContentType(FileType fileType, String contentType);
    
    /**
     * Détermine le type de contenu basé sur l'extension du fichier
     * 
     * @param filename Nom du fichier
     * @return Type MIME du fichier
     */
    String determineContentType(String filename);
    
    /**
     * Construit le chemin de stockage pour un fichier d'incident
     * 
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param filename Nom du fichier (optionnel)
     * @return Chemin complet pour le stockage du fichier
     */
    Path buildFilePath(Long incidentId, FileType fileType, String filename);

    /**
     * Enregistre un fichier pour un incident spécifique
     * 
     * @param file Fichier à enregistrer
     * @param incidentId ID de l'incident
     * @param fileTypeStr Type de fichier en chaîne de caractères
     * @param overwrite Écraser si existe déjà
     * @return Informations sur le fichier enregistré
     */
    Map<String, Object> saveIncidentFile(Object file, long incidentId, String fileTypeStr, boolean overwrite);
    
    /**
     * Récupère le chemin d'un fichier spécifique pour un incident
     * 
     * @param incidentId ID de l'incident
     * @param fileTypeStr Type de fichier en chaîne de caractères
     * @param filename Nom du fichier
     * @return Chemin vers le fichier
     */
    Path getIncidentFilePath(long incidentId, String fileTypeStr, String filename);
    
    /**
     * Supprime un fichier d'un incident
     * 
     * @param incidentId ID de l'incident
     * @param fileTypeStr Type de fichier en chaîne de caractères
     * @param filename Nom du fichier
     * @return true si supprimé avec succès
     */
    boolean deleteIncidentFile(long incidentId, String fileTypeStr, String filename);
    
    /**
     * Vérifie si un incident existe dans la base de données
     * 
     * @param incidentId ID de l'incident à vérifier
     * @throws otbs.ms_incident.exception.ResourceNotFoundException si l'incident n'existe pas
     */
    void validateIncidentExists(Long incidentId);
} 