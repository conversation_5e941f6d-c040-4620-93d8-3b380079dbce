package otbs.ms_incident.mapper;

import otbs.ms_incident.dto.ReparationRequestDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.entity.Reparation;
import org.mapstruct.*;

import java.util.List;

/**
 * Interface de mapping pour les objets Reparation
 */
@Mapper(componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface ReparationMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", constant = "system")
    @Mapping(target = "updatedBy", constant = "system")
    @Mapping(target = "rembourse", constant = "false")
    @Mapping(target = "montantCouverture", expression = "java(java.math.BigDecimal.ZERO)")
    @Mapping(target = "incident", ignore = true)
    Reparation toEntity(ReparationRequestDTO requestDTO);

    @Mapping(target = "incidentId", source = "incident.id")
    @Mapping(target = "vehiculeId", source = "incident.vehiculeId")
    ReparationResponseDTO toDTO(Reparation reparation);

    List<ReparationResponseDTO> toDTOList(List<Reparation> reparations);

    /**
     * Méthode appelée après le mapping pour enrichir le DTO avec des informations supplémentaires
     */
    @AfterMapping
    default void enrichWithVehiculeInfo(@MappingTarget ReparationResponseDTO responseDTO, Reparation reparation) {
        // Si l'incident associé existe, récupérer l'ID du véhicule
        if (reparation.getIncident() != null && reparation.getIncident().getVehiculeId() != null) {
            Long vehiculeId = reparation.getIncident().getVehiculeId();
            responseDTO.setVehiculeId(vehiculeId);
        }
    }

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "incident", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", constant = "system")
    @Mapping(target = "rembourse", source = "rembourse", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "montantCouverture", source = "montantCouverture", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    void updateReparationFromDTO(ReparationRequestDTO requestDTO, @MappingTarget Reparation reparation);
}