package otbs.ms_incident.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ResourceNotFoundExceptionTest {

    @Test
    void testConstructorWithMessage() {
        // Arrange
        String message = "Resource not found";
        
        // Act
        ResourceNotFoundException exception = new ResourceNotFoundException(message);
        
        // Assert
        assertEquals(message, exception.getMessage());
    }
    
    @Test
    void testConstructorWithResourceDetails() {
        // Arrange
        String resourceName = "Incident";
        String fieldName = "id";
        Long fieldValue = 123L;
        
        // Act
        ResourceNotFoundException exception = new ResourceNotFoundException(resourceName, fieldName, fieldValue);
        
        // Assert
        String expectedMessage = String.format("%s n'a pas été trouvé avec %s : '%s'", resourceName, fieldName, fieldValue);
        assertEquals(expectedMessage, exception.getMessage());
    }
} 