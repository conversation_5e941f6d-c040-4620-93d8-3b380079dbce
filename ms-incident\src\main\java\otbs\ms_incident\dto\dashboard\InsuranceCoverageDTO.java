package otbs.ms_incident.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * DTO pour l'analyse de la couverture d'assurance
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsuranceCoverageDTO {
    private Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle;
    private Map<Long, String> vehicleImmatriculations;
}
