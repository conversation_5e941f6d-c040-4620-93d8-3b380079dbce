package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.enums.FileType;
import otbs.ms_incident.exception.FileStorageException;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.IncidentMapper;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IncidentFileManagerImplCoverageTest {

    @TempDir
    Path tempDir;

    @Mock
    private FileStorageService fileStorageService;

    @Mock
    private IncidentService incidentService;

    @Mock
    private MessageSource messageSource;

    @Mock
    private IncidentMapper incidentMapper;

    @InjectMocks
    private IncidentFileManagerImpl incidentFileManager;

    // Constantes supprimées: PHOTOS_FOLDER, CONSTATS_FOLDER
    private static final Long TEST_INCIDENT_ID = 123L;

    @BeforeEach
    void setUp() {
        // Set the storage path to the temp directory for testing
        ReflectionTestUtils.setField(incidentFileManager, "storagePath", tempDir.toString());

        // Set up common mocks with lenient settings
        lenient().when(messageSource.getMessage(anyString(), any(), any())).thenReturn("Mock message");
    }

    /**
     * Test the file upload process for photos
     */
    @Test
    void uploadFile_shouldHandlePhotos() throws IOException {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "test content".getBytes()
        );

        // Create necessary directory structure - juste le dossier principal
        Files.createDirectories(tempDir);

        // Set up mocks
        Incident incident = new Incident();
        incident.setId(TEST_INCIDENT_ID);

        lenient().when(incidentService.getIncidentEntityById(TEST_INCIDENT_ID)).thenReturn(incident);
        lenient().when(fileStorageService.isValidFileExtension(anyString())).thenReturn(true);

        // Act
        try {
            incidentFileManager.uploadFile(file, TEST_INCIDENT_ID, FileType.PHOTO, true);
        } catch (Exception e) {
            // Expected exception during testing due to mocks
        }

        // Assert - verify isValidFileExtension was called (part of the saveIncidentFile method)
        verify(fileStorageService).isValidFileExtension("test.jpg");
    }

    /**
     * Test file upload for constat (PDF) files
     */
    @Test
    void uploadFile_shouldHandleConstat() throws IOException {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "constat.pdf",
            "application/pdf",
            "test content".getBytes()
        );

        // Create necessary directory structure - juste le dossier principal
        Files.createDirectories(tempDir);

        // Set up mocks
        Incident incident = new Incident();
        incident.setId(TEST_INCIDENT_ID);

        lenient().when(incidentService.getIncidentEntityById(TEST_INCIDENT_ID)).thenReturn(incident);
        lenient().when(fileStorageService.isValidFileExtension(anyString())).thenReturn(true);

        // Act
        try {
            incidentFileManager.uploadFile(file, TEST_INCIDENT_ID, FileType.CONSTAT, true);
        } catch (Exception e) {
            // Expected exception during testing due to mocks
        }

        // Assert - verify isValidFileExtension was called (part of the saveIncidentFile method)
        verify(fileStorageService).isValidFileExtension("constat.pdf");
    }

    /**
     * Test deletion of a constat file that doesn't exist
     */
    @Test
    void deleteFile_shouldHandleNonExistentConstat() {
        // Arrange
        Incident incident = new Incident();
        incident.setId(TEST_INCIDENT_ID);
        incident.setConstat(null); // No constat set

        when(incidentService.getIncidentEntityById(TEST_INCIDENT_ID)).thenReturn(incident);

        // Act
        Map<String, Object> result = incidentFileManager.deleteFile(
            TEST_INCIDENT_ID,
            FileType.CONSTAT,
            "non-existent-constat.pdf"
        );

        // Assert
        assertEquals("error", result.get("status"));
    }

    /**
     * Test deletion of photos that don't exist
     */
    @Test
    void deleteFile_shouldHandleNonExistentPhoto() {
        // Arrange
        Incident incident = new Incident();
        incident.setId(TEST_INCIDENT_ID);
        incident.setPhotos(new ArrayList<>()); // Empty photos list

        when(incidentService.getIncidentEntityById(TEST_INCIDENT_ID)).thenReturn(incident);

        // Act
        Map<String, Object> result = incidentFileManager.deleteFile(
            TEST_INCIDENT_ID,
            FileType.PHOTO,
            "non-existent-photo.jpg"
        );

        // Assert
        assertEquals("error", result.get("status"));
    }

    /**
     * Test handling of non-regular files when getting file info
     */


    /**
     * Test validation of incident existence with a simpler approach
     */
    @Test
    void validateIncidentExists_shouldThrowException() {
        // Arrange - Create test data
        Long nonExistentId = 999L;
        String errorMessage = "Incident not found with id: " + nonExistentId;

        // Mock behavior - when incidentService.getIncidentById is called with nonExistentId, throw ResourceNotFoundException
        when(incidentService.getIncidentById(nonExistentId)).thenThrow(new ResourceNotFoundException(errorMessage));
        lenient().when(messageSource.getMessage(anyString(), any(), any())).thenReturn(errorMessage);

        // Act & Assert - Call the public method that uses validateIncidentExists internally
        Exception exception = assertThrows(ResourceNotFoundException.class, () -> {
            // Call the validateIncidentExists method directly since it's public
            incidentFileManager.validateIncidentExists(nonExistentId);
        });

        // Verify the exception message
        assertTrue(exception.getMessage().contains(String.valueOf(nonExistentId)));
    }

    /**
     * Test MultipartFile validation in saveIncidentFile
     */
    @Test
    void saveIncidentFile_shouldValidateMultipartFile() {
        // Arrange - use an object that is not a MultipartFile or String
        Object invalidFile = new Object();

        // Act & Assert
        Exception exception = assertThrows(FileStorageException.class, () -> {
            incidentFileManager.saveIncidentFile(
                invalidFile,
                TEST_INCIDENT_ID,
                "PHOTO",
                false
            );
        });

        // The actual exception message is "Le fichier doit être de type MultipartFile"
        assertNotNull(exception.getMessage());
    }
}