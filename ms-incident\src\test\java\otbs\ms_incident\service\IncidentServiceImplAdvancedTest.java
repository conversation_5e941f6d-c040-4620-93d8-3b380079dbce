package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import otbs.ms_incident.commun.PageResponse;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.IncidentMapper;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;
import otbs.ms_incident.service.NotificationEventPublisher;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IncidentServiceImplAdvancedTest {

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentMapper incidentMapper;

    @Mock
    private NotificationEventPublisher notificationEventPublisher;

    @InjectMocks
    private IncidentServiceImpl incidentService;

    private Incident incident1, incident2;
    private IncidentResponseDTO responseDTO1, responseDTO2;
    private List<Incident> incidents;

    @BeforeEach
    void setUp() {
        // Setup test incidents
        incident1 = new Incident();
        incident1.setId(1L);
        incident1.setDate(LocalDate.of(2023, 5, 15));
        incident1.setType(TypeIncident.ACCIDENT);
        incident1.setStatus(StatusIncident.A_TRAITER);
        incident1.setPriorite(NiveauPrioriteIncident.CRITIQUE);
        incident1.setLieu("Paris");
        incident1.setDescription("Accident 1");
        incident1.setVehiculeId(100L);

        incident2 = new Incident();
        incident2.setId(2L);
        incident2.setDate(LocalDate.of(2023, 6, 20));
        incident2.setType(TypeIncident.PANNE);
        incident2.setStatus(StatusIncident.EN_COURS_TRAITEMENT);
        incident2.setPriorite(NiveauPrioriteIncident.MOYEN);
        incident2.setLieu("Lyon");
        incident2.setDescription("Panne 1");
        incident2.setVehiculeId(200L);

        incidents = Arrays.asList(incident1, incident2);

        // Setup test DTOs
        responseDTO1 = new IncidentResponseDTO();
        responseDTO1.setId(1L);
        responseDTO1.setVehiculeId(100L);

        responseDTO2 = new IncidentResponseDTO();
        responseDTO2.setId(2L);
        responseDTO2.setVehiculeId(200L);
    }

    @Test
    void getIncidentsByDateRange_shouldReturnIncidentsInRange() {
        // Given
        LocalDate startDate = LocalDate.of(2023, 5, 1);
        LocalDate endDate = LocalDate.of(2023, 5, 31);

        when(incidentRepository.findByDateBetween(startDate, endDate)).thenReturn(List.of(incident1));
        when(incidentMapper.toResponseDTOList(List.of(incident1))).thenReturn(List.of(responseDTO1));

        // When
        List<IncidentResponseDTO> result = incidentService.getIncidentsByDateRange(startDate, endDate);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(responseDTO1, result.get(0));
    }

    @ParameterizedTest(name = "Test {0} with parameter {1}")
    @MethodSource("filterTestCases")
    void testIncidentFilters(String methodName, String parameter, Function<IncidentServiceImpl, List<IncidentResponseDTO>> serviceCall) {
        // Given
        if (methodName.equals("getIncidentsByLieu")) {
            when(incidentRepository.findByLieuContaining(parameter)).thenReturn(List.of(incident1));
        } else if (methodName.equals("getIncidentsByVehiculeId")) {
            when(incidentRepository.findByVehiculeId(Long.parseLong(parameter))).thenReturn(List.of(incident1));
        }
        when(incidentMapper.toResponseDTOList(List.of(incident1))).thenReturn(List.of(responseDTO1));

        // When
        List<IncidentResponseDTO> result = serviceCall.apply(incidentService);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(responseDTO1, result.get(0));
    }

    static Stream<Arguments> filterTestCases() {
        return Stream.of(
            Arguments.of("getIncidentsByLieu", "Paris",
                (Function<IncidentServiceImpl, List<IncidentResponseDTO>>) service -> service.getIncidentsByLieu("Paris")),
            Arguments.of("getIncidentsByVehiculeId", "100",
                (Function<IncidentServiceImpl, List<IncidentResponseDTO>>) service -> service.getIncidentsByVehiculeId(100L))
        );
    }

    @Test
    void countIncidentsByVehiculeId_shouldReturnCorrectCount() {
        // Given
        when(incidentRepository.countByVehiculeId(100L)).thenReturn(5L);

        // When
        Long result = incidentService.countIncidentsByVehiculeId(100L);

        // Then
        assertEquals(5L, result);
    }

    @Test
    void getAllIncidents_withPagination_shouldReturnPagedIncidents() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<Incident> page = new PageImpl<>(incidents, pageable, 2);

        when(incidentRepository.findAll(pageable)).thenReturn(page);
        when(incidentMapper.toResponseDTO(incident1)).thenReturn(responseDTO1);
        when(incidentMapper.toResponseDTO(incident2)).thenReturn(responseDTO2);

        // When
        PageResponse<IncidentResponseDTO> result = incidentService.getAllIncidents(pageable);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        assertEquals(2, result.getTotalElements());
        assertEquals(1, result.getTotalPages());
    }

    @Test
    void updateIncidentStatus_shouldUpdateStatus() {
        // Given
        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident1));
        when(incidentRepository.save(incident1)).thenReturn(incident1);
        when(incidentMapper.toResponseDTO(incident1)).thenReturn(responseDTO1);

        // When
        IncidentResponseDTO result = incidentService.updateIncidentStatus(1L, StatusIncident.RESOLU);

        // Then
        assertEquals(responseDTO1, result);
        verify(incidentRepository).save(incident1);
        assertEquals(StatusIncident.RESOLU, incident1.getStatus());
    }

    @Test
    void updateIncidentStatus_shouldThrowException_whenIncidentNotFound() {
        // Given
        when(incidentRepository.findById(999L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            incidentService.updateIncidentStatus(999L, StatusIncident.RESOLU)
        );
    }

    @Test
    void updateIncidentPriority_shouldUpdatePriority() {
        // Given
        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident1));
        when(incidentRepository.save(incident1)).thenReturn(incident1);
        when(incidentMapper.toResponseDTO(incident1)).thenReturn(responseDTO1);

        // When
        IncidentResponseDTO result = incidentService.updateIncidentPriority(1L, NiveauPrioriteIncident.FAIBLE);

        // Then
        assertEquals(responseDTO1, result);
        verify(incidentRepository).save(incident1);
        assertEquals(NiveauPrioriteIncident.FAIBLE, incident1.getPriorite());
    }

    @Test
    void updateIncidentPriority_shouldThrowException_whenIncidentNotFound() {
        // Given
        when(incidentRepository.findById(999L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            incidentService.updateIncidentPriority(999L, NiveauPrioriteIncident.FAIBLE)
        );
    }
}
