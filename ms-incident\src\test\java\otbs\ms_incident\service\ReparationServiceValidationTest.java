package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import otbs.ms_incident.dto.ReparationRequestDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.enums.StatusReparation;
import otbs.ms_incident.exception.BadRequestException;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.ReparationMapper;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
 class ReparationServiceValidationTest {

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationMapper reparationMapper;

    @InjectMocks
    private ReparationServiceImpl reparationService;

    private ReparationRequestDTO requestDTO;
    private Incident incident;
    private Reparation reparation;

    @BeforeEach
    void setUp() {
        incident = new Incident();
        incident.setId(1L);

        reparation = new Reparation();
        reparation.setId(1L);
        reparation.setDateReparation(LocalDate.of(2023, 5, 15));
        reparation.setStatus(StatusReparation.EN_COURS);
        reparation.setDescription("Réparation test");
        reparation.setCout(new BigDecimal("1000.00"));
        reparation.setGarage("Garage Test");
        reparation.setIncident(incident);

        requestDTO = new ReparationRequestDTO();
        requestDTO.setDateReparation(LocalDate.of(2023, 5, 15));
        requestDTO.setStatus(StatusReparation.EN_COURS);
        requestDTO.setDescription("Réparation test");
        requestDTO.setCout(new BigDecimal("1000.00"));
        requestDTO.setGarage("Garage Test");
        requestDTO.setIncidentId(1L);
    }

    @Test
    void createReparation_shouldHandleNullIncidentId() {
        // Given
        requestDTO.setIncidentId(null);

        // When/Then
        assertThrows(BadRequestException.class, () ->
            reparationService.createReparation(requestDTO)
        );
    }

    @Test
    void createReparation_shouldHandleNegativeCost() {
        // Given
        requestDTO.setCout(new BigDecimal("-100.00"));

        // When/Then
        assertThrows(BadRequestException.class, () ->
            reparationService.createReparation(requestDTO)
        );
    }

    @Test
    void createReparation_shouldHandleNullDate() {
        // Given
        requestDTO.setDateReparation(null);

        // When/Then
        assertThrows(BadRequestException.class, () ->
            reparationService.createReparation(requestDTO)
        );
    }

    @Test
    void createReparation_shouldHandleFutureDate() {
        // Given
        requestDTO.setDateReparation(LocalDate.now().plusDays(10));

        // When/Then
        assertThrows(BadRequestException.class, () ->
            reparationService.createReparation(requestDTO)
        );
    }

    @Test
    void updateReparation_shouldHandleNonExistentReparation() {
        // Given
        when(reparationRepository.findById(999L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.updateReparation(999L, requestDTO)
        );
    }

    @Test
    void updateReparation_shouldHandleNonExistentIncident() {
        // Given
        when(reparationRepository.findById(1L)).thenReturn(Optional.of(reparation));
        requestDTO.setIncidentId(999L);
        when(incidentRepository.findById(999L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.updateReparation(1L, requestDTO)
        );
    }

    @Test
    void updateReparation_shouldHandleNegativeCost() {
        // Given
        // Pas besoin de mock car la validation échoue avant d'appeler le repository
        requestDTO.setCout(new BigDecimal("-100.00"));

        // When/Then
        assertThrows(BadRequestException.class, () ->
            reparationService.updateReparation(1L, requestDTO)
        );
    }

    @Test
    void updateReparation_shouldHandleFutureDate() {
        // Given
        // Pas besoin de mock car la validation échoue avant d'appeler le repository
        requestDTO.setDateReparation(LocalDate.now().plusDays(10));

        // When/Then
        assertThrows(BadRequestException.class, () ->
            reparationService.updateReparation(1L, requestDTO)
        );
    }

    @Test
    void updateReparationStatus_shouldHandleNonExistentReparation() {
        // Given
        when(reparationRepository.findById(999L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.updateReparationStatus(999L, StatusReparation.TERMINEE)
        );
    }

    @Test
    void getReparationById_shouldHandleNonExistentReparation() {
        // Given
        when(reparationRepository.findById(999L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.getReparationById(999L)
        );
    }

    @Test
    void getReparationsByIncidentId_shouldHandleNonExistentIncident() {
        // Given
        when(incidentRepository.existsById(999L)).thenReturn(false);

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.getReparationsByIncidentId(999L)
        );
    }

    @Test
    void getReparationsByStatusAndIncidentId_shouldHandleNonExistentIncident() {
        // Given
        when(incidentRepository.existsById(999L)).thenReturn(false);

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.getReparationsByStatusAndIncidentId(StatusReparation.EN_COURS, 999L)
        );
    }
}
