package otbs.ms_incident.dto.dashboard;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test for DashboardSummaryDTO.ReparationStats
 */
class DashboardSummaryDTOReparationStatsTest {

    @Test
    void testBuilder_withAllFields() {
        // Given
        long totalReparations = 100;
        long inProgressReparations = 40;
        long completedReparations = 30;
        BigDecimal totalCost = new BigDecimal("50000.00");
        BigDecimal averageCost = new BigDecimal("500.00");

        // When
        DashboardSummaryDTO.ReparationStats stats = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(totalReparations)
                .inProgressReparations(inProgressReparations)
                .completedReparations(completedReparations)
                .totalCost(totalCost)
                .averageCost(averageCost)
                .build();

        // Then
        assertEquals(totalReparations, stats.getTotalReparations());
        assertEquals(inProgressReparations, stats.getInProgressReparations());
        assertEquals(completedReparations, stats.getCompletedReparations());
        assertEquals(totalCost, stats.getTotalCost());
        assertEquals(averageCost, stats.getAverageCost());
    }

    @Test
    void testBuilder_withMinimalFields() {
        // When
        DashboardSummaryDTO.ReparationStats stats = DashboardSummaryDTO.ReparationStats.builder()
                .build();

        // Then
        assertEquals(0, stats.getTotalReparations());
        assertEquals(0, stats.getInProgressReparations());
        assertEquals(0, stats.getCompletedReparations());
        assertNull(stats.getTotalCost());
        assertNull(stats.getAverageCost());
    }

    @Test
    void testBuilder_withPartialFields() {
        // Given
        long totalReparations = 100;
        BigDecimal totalCost = new BigDecimal("50000.00");

        // When
        DashboardSummaryDTO.ReparationStats stats = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(totalReparations)
                .totalCost(totalCost)
                .build();

        // Then
        assertEquals(totalReparations, stats.getTotalReparations());
        assertEquals(0, stats.getInProgressReparations());
        assertEquals(0, stats.getCompletedReparations());
        assertEquals(totalCost, stats.getTotalCost());
        assertNull(stats.getAverageCost());
    }

    @Test
    void testToString_shouldNotThrowException() {
        // Given
        DashboardSummaryDTO.ReparationStats stats = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(100)
                .totalCost(new BigDecimal("50000.00"))
                .build();

        // When & Then
        assertDoesNotThrow(() -> {
            String result = stats.toString();
            assertNotNull(result);
            assertFalse(result.isEmpty());
        });
    }

    @Test
    void testEquals_withSameObject() {
        // Given
        DashboardSummaryDTO.ReparationStats stats = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(100)
                .totalCost(new BigDecimal("50000.00"))
                .build();

        // When & Then
        assertEquals(stats, stats);
    }

    @Test
    void testEquals_withEqualObject() {
        // Given
        DashboardSummaryDTO.ReparationStats stats1 = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(100)
                .totalCost(new BigDecimal("50000.00"))
                .build();

        DashboardSummaryDTO.ReparationStats stats2 = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(100)
                .totalCost(new BigDecimal("50000.00"))
                .build();

        // When & Then
        assertEquals(stats1, stats2);
        assertEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testEquals_withDifferentObject() {
        // Given
        DashboardSummaryDTO.ReparationStats stats1 = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(100)
                .totalCost(new BigDecimal("50000.00"))
                .build();

        DashboardSummaryDTO.ReparationStats stats2 = DashboardSummaryDTO.ReparationStats.builder()
                .totalReparations(120)
                .totalCost(new BigDecimal("60000.00"))
                .build();

        // When & Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testGettersAndSetters() {
        // Given
        DashboardSummaryDTO.ReparationStats stats = DashboardSummaryDTO.ReparationStats.builder().build();

        // When
        stats.setTotalReparations(100);
        stats.setInProgressReparations(40);
        stats.setCompletedReparations(30);
        stats.setTotalCost(new BigDecimal("50000.00"));
        stats.setAverageCost(new BigDecimal("500.00"));

        // Then
        assertEquals(100, stats.getTotalReparations());
        assertEquals(40, stats.getInProgressReparations());
        assertEquals(30, stats.getCompletedReparations());
        assertEquals(new BigDecimal("50000.00"), stats.getTotalCost());
        assertEquals(new BigDecimal("500.00"), stats.getAverageCost());
    }
}
