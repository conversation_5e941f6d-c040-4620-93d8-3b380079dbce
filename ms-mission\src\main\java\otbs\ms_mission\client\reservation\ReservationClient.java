
package otbs.ms_mission.client.reservation;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Client Feign pour interagir avec le microservice ms-reservation.
 * Ce client permet d'accéder aux fonctionnalités de gestion des réservations.
 */
@FeignClient(
        name = "ms-reservation",
        url = "${reservation.service.url:http://localhost:9006}",
        fallback = ReservationClientFallback.class,
        configuration = otbs.ms_mission.config.FeignConfig.class
)
public interface ReservationClient {

    /**
     * Récupère une réservation par son identifiant.
     *
     * @param id L'identifiant de la réservation
     * @return La réservation correspondante
     */
    @GetMapping("/api/v1/reservation/getReservation/{id}")
    ReservationDto getReservationById(@PathVariable Long id);

    /**
     * Récupère toutes les réservations associées à une mission.
     * Note: Cet endpoint n'existe pas encore dans le service ms-reservation,
     * nous utilisons donc un endpoint fictif qui sera implémenté par le fallback.
     *
     * @param idMission L'identifiant de la mission
     * @return La liste des réservations associées à la mission
     */
    @GetMapping("/api/v1/reservation/mission/{idMission}")
    List<ReservationDto> getReservationsByMissionId(@PathVariable Long idMission);

    /**
     * Crée une nouvelle réservation.
     *
     * @param reservationDto Les données de la réservation à créer
     * @return La réservation créée
     */
    @PostMapping("/api/v1/reservation/addReservation")
    ReservationDto createReservation(@RequestBody ReservationDto reservationDto);

    /**
     * Met à jour une réservation existante.
     *
     * @param id L'identifiant de la réservation à mettre à jour
     * @param reservationDto Les nouvelles données de la réservation
     * @return La réservation mise à jour
     */
    @PutMapping("/api/v1/reservation/updateReservation/{id}")
    ReservationDto updateReservation(@PathVariable Long id, @RequestBody ReservationDto reservationDto);

    /**
     * Supprime une réservation.
     *
     * @param id L'identifiant de la réservation à supprimer
     */
    @DeleteMapping("/api/v1/reservation/deleteReservation/{id}")
    void deleteReservation(@PathVariable Long id);
}
