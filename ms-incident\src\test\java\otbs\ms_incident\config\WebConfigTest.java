package otbs.ms_incident.config;

import org.junit.jupiter.api.Test;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import static org.junit.jupiter.api.Assertions.*;

class WebConfigTest {

    @Test
    void corsConfigurer_ShouldReturnConfiguredWebMvcConfigurer() {
        // Arrange
        WebConfig webConfig = new WebConfig();

        // Act
        WebMvcConfigurer configurer = webConfig.corsConfigurer();

        // Assert
        assertNotNull(configurer, "The corsConfigurer should return a non-null WebMvcConfigurer");
    }

    @Test
    void addCorsMappings_ShouldBeOverridden() {
        // Arrange
        WebConfig webConfig = new WebConfig();
        WebMvcConfigurer configurer = webConfig.corsConfigurer();

        // Create a test CorsRegistry
        CorsRegistry registry = new CorsRegistry();

        // Act
        configurer.addCorsMappings(registry);

        // Assert - we can only verify that the method doesn't throw an exception
        // since CorsRegistry doesn't provide a way to inspect its configuration
        assertTrue(true, "Method should be successfully overridden");
    }

    @Test
    void webConfig_shouldHaveCorrectConfiguration() {
        // Arrange & Act
        WebConfig webConfig = new WebConfig();

        // Assert
        assertNotNull(webConfig, "WebConfig should be instantiated");
        // This test verifies that the WebConfig class can be instantiated
        // and doesn't throw any exceptions during initialization
    }
}