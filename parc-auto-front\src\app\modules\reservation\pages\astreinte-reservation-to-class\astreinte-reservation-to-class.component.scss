/* astreinte-reservation-to-class.component.scss - Code complet identique aux missions */

.reservation-container {
  padding: 1.5rem;
  max-width: 100%;
  background-color: var(--card-bg);
  border-radius: 8px;
  box-shadow: var(--shadow);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;

  h1 {
    font-size: 1.75rem;
    color: var(--text-color);
    margin: 0;
  }

  .actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
}

.nav-btn {
  border: none;
  border-radius: 4px;
  padding: 0.6rem 1.2rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &.dashboard-btn {
    background-color: var(--primary-color);
    color: white;

    &:hover {
      background-color: darken(#1a237e, 5%);
    }
  }

  &.repairs-btn {
    background-color: var(--accent-color);
    color: white;

    &:hover {
      background-color: darken(#ff9800, 5%);
    }
  }

  i {
    font-size: 0.875rem;
  }
}

.btn-add {
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.6rem 1.2rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: darken(#ff9800, 10%);
  }

  i {
    font-size: 0.875rem;
  }
}

.filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;

  .search-box {
    position: relative;
    flex: 1;
    min-width: 200px;

    input {
      width: 100%;
      padding: 0.625rem 2.5rem 0.625rem 1rem;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      background-color: var(--card-bg);
      color: var(--text-color);

      &::placeholder {
        color: rgba(var(--text-color-rgb), 0.6);
      }
    }

    i {
      position: absolute;
      right: 1rem;
      top: 50%;
      transform: translateY(-50%);
      color: var(--accent-color);
    }
  }

  .filter-options {
    display: flex;
    gap: 1rem;

    select {
      padding: 0.625rem 1rem;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      background-color: var(--card-bg);
      color: var(--text-color);
      cursor: pointer;
    }
  }
}

.vehicule-list {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  table {
    width: 100%;
    border-collapse: collapse;
    min-width: 650px;

    th, td {
      padding: 0.75rem 1rem;
      text-align: center; // Center text horizontally
      border-bottom: 1px solid var(--border-color);
      height: 95px; // Consistent height for all cells
      vertical-align: middle; // Fallback for vertical alignment
    }

    th {
      font-weight: 600;
      color: var(--text-color);
      background-color: rgba(var(--accent-color-rgb), 0.05);
      white-space: nowrap;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    td {
      align-items: center; // Center vertically
      justify-content: center; // Center horizontally
      flex-wrap: wrap; // Allow wrapping for longer content
      gap: 0.5rem; // Space between elements
    }

    // Specific styling for Véhicule cell
    td:nth-child(4) {
      .immatricule-container {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 120px;
        height: 35px;
        transform: scale(0.65);
        transform-origin: center center;
      }
    }

    // Specific styling for Cartes Carburants cell
    td:nth-child(5) {
      flex-direction: row;
      justify-content: center;
      gap: 1rem;

      p {
        margin: 0;
        text-align: center;
      }

      button {
        flex-shrink: 0;
      }
    }

    // Specific styling for Badge Télépéage cell
    td:nth-child(6) {
      flex-direction: row;
      justify-content: center;
      gap: 1rem;

      p {
        margin: 0;
        flex: 1;
        text-align: center;
      }

      button {
        flex-shrink: 0;
      }
    }

    tbody tr {
      transition: background-color 0.2s;

      &:hover {
        background-color: var(--hover-color);
      }
    }
  }
}

.status-badge, .priority-badge {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-maintenance {
  background-color: rgba(244, 67, 54, 0.15);
  color: #f44336;
}

.status-available {
  background-color: rgba(33, 150, 243, 0.15);
  color: #2196f3;
}

.status-in-route {
  background-color: rgba(76, 175, 80, 0.15);
  color: #4caf50;
}

.status-default {
  background-color: rgba(128, 128, 128, 0.15);
  color: #eeae38;
}

.badge {
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 0.85em;
  font-weight: bold;
  text-transform: uppercase;
  display: inline-block;
}

.badge-personnel {
  background-color: rgba(33, 150, 243, 0.15);
  color: #2196f3;
}

.badge-mission {
  background-color: rgba(255, 152, 0, 0.15);
  color: #ff9800;
}

.badge-essence {
  background-color: rgba(76, 175, 80, 0.15);
  color: #4caf50;
}

.badge-electrique {
  background-color: rgba(255, 152, 0, 0.15);
  color: #ff9800;
}

.badge-gazoil {
  background-color: rgba(158, 158, 158, 0.15);
  color: #9e9e9e;
}

.badge-default {
  background-color: rgba(33, 150, 243, 0.15);
  color: #2196f3;
}

/* Styles pour la cellule d'actions - Boutons côte à côte */
.actions-cell {
  display: flex !important; /* Force le display flex */
  flex-direction: row !important; /* Assure une direction horizontale */
  justify-content: center !important; /* Centre horizontalement */
  align-items: center !important; /* Centre verticalement */
  gap: 0.2rem !important; /* Espace très réduit entre les boutons */
  padding: 0 !important; /* Supprime tout padding supplémentaire */
}

.btn-action {
  width: 2rem;
  height: 2rem;
  min-width: 2rem; /* Assure une taille minimale */
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  padding: 0; /* Éliminer tout padding supplémentaire */

  &.btn-view {
    background-color: rgba(33, 150, 243, 0.15);
    color: #2196f3;

    &:hover {
      background-color: rgba(33, 150, 243, 0.25);
    }
  }

  &.btn-edit {
    background-color: rgba(255, 152, 0, 0.15);
    color: #ff9800;

    &:hover {
      background-color: rgba(255, 152, 0, 0.25);
    }
  }

  &.btn-repair {
    background-color: rgba(76, 175, 80, 0.15);
    color: #4caf50;

    &:hover {
      background-color: rgba(76, 175, 80, 0.25);
    }
  }

  &.btn-delete {
    background-color: rgba(244, 67, 54, 0.1);
    color: #f44336;

    &:hover {
      background-color: rgba(244, 67, 54, 0.2);
    }
  }

  &.btn-fuel {
    background-color: #28a745;
    color: white;

    &:hover {
      background-color: #218838;
    }
  }

  i {
    font-size: 16px;
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 1.5rem;
  gap: 1rem;

  .btn-page {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-color);

    &:hover {
      background-color: var(--hover-color);
      color: var(--accent-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transform: translateY(-2px);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: var(--card-bg);
        color: var(--text-color);
        box-shadow: none;
        transform: none;
      }
    }
  }

  span {
    font-size: 0.9rem;
    color: var(--text-color);
  }
}

/* ---- Styles des badges de consommation ---- */
.consommation-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 100%;
  min-height: 60px;
  justify-content: center;
}

.no-consommation {
  color: rgba(var(--text-color-rgb, 102, 102, 102), 0.7);
  font-size: 0.85rem;
  font-style: italic;
  margin-bottom: 8px;
}

.consommation-badges {
  display: flex;
  justify-content: center;
  gap: 0.4rem;
  margin-bottom: 0.5rem;
  width: 200px;
}

.consommation-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  font-size: 0.75rem;
  font-weight: 500;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
  white-space: nowrap;
  margin: 0.1rem;
}

.badge-carte {
  background-color: rgba(33, 150, 243, 0.2);
  color: #2196f3;
}

.badge-frais {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ff9800;
}

.badge-cash {
  background-color: rgba(76, 175, 80, 0.2);
  color: #4caf50;
}

.badge-default {
  background-color: rgba(158, 158, 158, 0.2);
  color: #9e9e9e;
}

.details-toggle {
  border: none;
  background-color: #f5f5f5;
  color: #888;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.65rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  
  &:hover {
    background-color: #e8e8e8;
    color: #666;
  }
}

.details-panel {
  position: absolute;
  top: calc(100% + 5px);
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--card-bg, white);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0.75rem;
  z-index: 100;
  width: max-content;
  max-width: 200px;
  border: 1px solid var(--border-color, #e0e0e0);
  
  &::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%) rotate(45deg);
    width: 12px;
    height: 12px;
    background-color: var(--card-bg, white);
    border-left: 1px solid var(--border-color, #e0e0e0);
    border-top: 1px solid var(--border-color, #e0e0e0);
  }
}

.detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.85rem;
  padding: 5px 0;
  
  &:not(:last-child) {
    border-bottom: 1px solid rgba(var(--border-color-rgb, 224, 224, 224), 0.3);
  }
  
  .detail-type {
    font-weight: 500;
    color: rgba(var(--text-color-rgb, 102, 102, 102), 0.8);
  }
  
  .detail-value {
    font-weight: 600;
    color: var(--text-color, #333);
  }
}

.consommation-container .btn-action.btn-repair {
  margin-top: 0.5rem;
  background-color: rgba(76, 175, 80, 0.15);
  color: #4caf50;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ---- Responsive Styles ---- */
.mobile-cards {
  display: none;
}

@media (max-width: 992px) {
  .reservation-container {
    padding: 1rem;
  }

  .filters {
    flex-direction: column;

    .search-box {
      width: 100%;
    }

    .filter-options {
      width: 100%;
      justify-content: space-between;

      select {
        flex: colder;
      }
    }
  }

  .vehicule-list {
    margin: 0 -1rem;
    border-radius: 0;

    table {
      min-width: 600px;

      th, td {
        padding: 0.5rem;
        height: 60px; // Reduced height for smaller screens
      }
    }
  }

  .btn-action {
    width: 1.8rem;
    height: 1.8rem;
  }
}

@media (max-width: 768px) {
  .vehicule-list {
    overflow-x: visible;

    table {
      display: none;
    }
  }

  .mobile-cards {
    display: block !important;
    margin-top: 1rem;
  }

  .vehicule-card {
    background-color: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    padding: 1.25rem;
    margin-bottom: 1.25rem;
    border-left: 4px solid var(--accent-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 40%;
      height: 4px;
      background: linear-gradient(to right, transparent, var(--accent-color));
      opacity: 0.7;
    }

    &:active {
      transform: scale(0.98);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 1rem;
      align-items: center;

      .vehicule-id {
        font-weight: 600;
        color: var(--text-color);
        font-size: 1.1rem;
      }
    }

    .card-row {
      display: flex;
      margin-bottom: 0.75rem;
      align-items: center;

      .label {
        width: 40%;
        font-size: 0.85rem;
        color: rgba(var(--text-color-rgb), 0.7);
        font-weight: 500;
      }

      .value {
        width: 60%;
        font-weight: 500;
      }
    }

    .card-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.75rem;
      margin-top: 0.75rem;
      border-top: 1px solid rgba(var(--border-color-rgb), 0.5);
      padding-top: 0.75rem;

      .btn-action {
        width: 2.5rem;
        height: 2.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &:active {
          transform: scale(0.9);
        }
      }
    }
  }
  
  .consommation-container.mobile {
    .consommation-badges {
      justify-content: flex-start;
    }
    
    .details-panel {
      left: 0;
      transform: none;
      width: 100%;
      max-width: none;
      
      &::before {
        left: 20px;
      }
    }
  }
  
  .consommation-badges {
    justify-content: flex-start;
  }
  
  .consommation-badge {
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }
}

@media (max-width: 480px) {
  .reservation-container {
    padding: 0.75rem;
    border-radius: 6px;
  }

  .page-header {
    h1 {
      font-size: 1.4rem;
    }
  }

  .btn-add {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .filters {
    gap: 0.5rem;

    .filter-options {
      flex-wrap: wrap;
      gap: 0.5rem;

      select {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
      }
    }

    .search-box input {
      padding: 0.5rem 2.5rem 0.5rem 0.75rem;
      font-size: 0.9rem;
    }
  }

  .mobile-cards {
    .vehicule-card {
      padding: 0.75rem 1rem;

      .card-header {
        margin-bottom: 0.75rem;

        .vehicule-id {
          font-size: 1rem;
        }

        .status-badge {
          padding: 0.2rem 0.5rem;
          font-size: 0.7rem;
        }
      }

      .card-row {
        margin-bottom: 0.5rem;

        .label {
          width: 35%;
          font-size: 0.75rem;

          i {
            margin-right: 0.25rem;
            opacity: 0.7;
          }
        }

        .value {
          width: 65%;
          font-size: 0.85rem;
        }
      }

      .card-actions {
        padding-top: 0.5rem;
        margin-top: 0.5rem;

        .btn-action {
          width: 2.2rem;
          height: 2.2rem;
        }
      }
    }
  }

  .pagination {
    margin-top: 1rem;

    .btn-page {
      width: 2rem;
      height: 2rem;
    }

    span {
      font-size: 0.8rem;
    }
  }
}

/* Angular Material styling overrides */
::ng-deep .mat-mdc-paginator-container {
  background-color: var(--sidebar-bg);
  color: var(--text-color);
}

::ng-deep .mat-mdc-select {
  color: var(--text-color) !important;
}

::ng-deep .mat-mdc-paginator-icon {
  fill: var(--text-color) !important;
}

::ng-deep .mat-mdc-icon-button[disabled] {
  fill: var(--text-color) !important;
}

.nom_colonne{
  place-items: center;
}

.checkbox-large {
  width: 15px;
  height: 15px;
  transform: scale(1.5); /* Agrandit la case à cocher */
  transform-origin: center;
  cursor: pointer;
}

/* Styles pour l'affichage des frais */
.frais-amount {
  font-weight: 600;
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.no-frais {
  color: rgba(var(--text-color-rgb, 102, 102, 102), 0.7);
  font-size: 0.85rem;
  font-style: italic;
}