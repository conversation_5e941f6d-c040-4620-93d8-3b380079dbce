package otbs.ms_incident.config;

import feign.RequestTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FeignClientInterceptorAdvancedTest {

    @Mock
    private RequestTemplate requestTemplate;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private JwtAuthenticationToken jwtAuthenticationToken;

    @Mock
    private Jwt jwt;

    @InjectMocks
    private FeignClientInterceptor interceptor;

    @BeforeEach
    void setUp() {
        // Setup common mocks
        when(jwtAuthenticationToken.getToken()).thenReturn(jwt);
        when(jwt.getTokenValue()).thenReturn("test-token");
    }

    @Test
    void apply_shouldAddAuthorizationHeader_whenJwtAuthenticationTokenIsPresent() {
        // Given
        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(jwtAuthenticationToken);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate).header("Authorization", "Bearer test-token");
        }
    }

    @Test
    void apply_shouldNotAddAuthorizationHeader_whenAuthenticationIsNull() {
        // Given
        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(null);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate, never()).header(eq("Authorization"), anyString());
        }
    }

    @Test
    void apply_shouldNotAddAuthorizationHeader_whenAuthenticationIsNotJwtAuthenticationToken() {
        // Given
        Authentication nonJwtAuthentication = mock(Authentication.class);
        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(nonJwtAuthentication);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate, never()).header(eq("Authorization"), anyString());
        }
    }

    @Test
    void apply_shouldHandleNullAuthentication() {
        // Given
        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(null);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate, never()).header(eq("Authorization"), anyString());
        }
    }

    @Test
    void apply_shouldHandleNullTokenValue() {
        // Given
        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(jwtAuthenticationToken);
            when(jwt.getTokenValue()).thenReturn(null);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate).header("Authorization", "Bearer null");
        }
    }

    @Test
    void apply_shouldHandleEmptyTokenValue() {
        // Given
        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(jwtAuthenticationToken);
            when(jwt.getTokenValue()).thenReturn("");

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate).header("Authorization", "Bearer ");
        }
    }
}
