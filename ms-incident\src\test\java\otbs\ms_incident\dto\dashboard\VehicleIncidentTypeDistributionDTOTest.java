package otbs.ms_incident.dto.dashboard;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class VehicleIncidentTypeDistributionDTOTest {

    @Test
    void testNoArgsConstructor() {
        // When
        VehicleIncidentTypeDistributionDTO dto = new VehicleIncidentTypeDistributionDTO();

        // Then
        assertNull(dto.getCountByVehicleAndType());
        assertNull(dto.getVehicleImmatriculations());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        vehicle1Types.put("PANNE", 2L);
        Map<String, Long> vehicle2Types = new HashMap<>();
        vehicle2Types.put("ACCIDENT", 1L);
        vehicle2Types.put("PANNE", 2L);
        countByVehicleAndType.put(1L, vehicle1Types);
        countByVehicleAndType.put(2L, vehicle2Types);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        // When
        VehicleIncidentTypeDistributionDTO dto = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, vehicleImmatriculations);

        // Then
        assertEquals(countByVehicleAndType, dto.getCountByVehicleAndType());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
        assertEquals(3L, dto.getCountByVehicleAndType().get(1L).get("ACCIDENT"));
        assertEquals(2L, dto.getCountByVehicleAndType().get(1L).get("PANNE"));
        assertEquals(1L, dto.getCountByVehicleAndType().get(2L).get("ACCIDENT"));
        assertEquals(2L, dto.getCountByVehicleAndType().get(2L).get("PANNE"));
        assertEquals("ABC123", dto.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", dto.getVehicleImmatriculations().get(2L));
    }

    @Test
    void testBuilder() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        vehicle1Types.put("PANNE", 2L);
        Map<String, Long> vehicle2Types = new HashMap<>();
        vehicle2Types.put("ACCIDENT", 1L);
        vehicle2Types.put("PANNE", 2L);
        countByVehicleAndType.put(1L, vehicle1Types);
        countByVehicleAndType.put(2L, vehicle2Types);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        // When
        VehicleIncidentTypeDistributionDTO dto = VehicleIncidentTypeDistributionDTO.builder()
                .countByVehicleAndType(countByVehicleAndType)
                .vehicleImmatriculations(vehicleImmatriculations)
                .build();

        // Then
        assertEquals(countByVehicleAndType, dto.getCountByVehicleAndType());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
        assertEquals(3L, dto.getCountByVehicleAndType().get(1L).get("ACCIDENT"));
        assertEquals(2L, dto.getCountByVehicleAndType().get(1L).get("PANNE"));
        assertEquals(1L, dto.getCountByVehicleAndType().get(2L).get("ACCIDENT"));
        assertEquals(2L, dto.getCountByVehicleAndType().get(2L).get("PANNE"));
        assertEquals("ABC123", dto.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", dto.getVehicleImmatriculations().get(2L));
    }

    @Test
    void testSettersAndGetters() {
        // Given
        VehicleIncidentTypeDistributionDTO dto = new VehicleIncidentTypeDistributionDTO();
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        vehicle1Types.put("PANNE", 2L);
        Map<String, Long> vehicle2Types = new HashMap<>();
        vehicle2Types.put("ACCIDENT", 1L);
        vehicle2Types.put("PANNE", 2L);
        countByVehicleAndType.put(1L, vehicle1Types);
        countByVehicleAndType.put(2L, vehicle2Types);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        // When
        dto.setCountByVehicleAndType(countByVehicleAndType);
        dto.setVehicleImmatriculations(vehicleImmatriculations);

        // Then
        assertEquals(countByVehicleAndType, dto.getCountByVehicleAndType());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
        assertEquals(3L, dto.getCountByVehicleAndType().get(1L).get("ACCIDENT"));
        assertEquals(2L, dto.getCountByVehicleAndType().get(1L).get("PANNE"));
        assertEquals(1L, dto.getCountByVehicleAndType().get(2L).get("ACCIDENT"));
        assertEquals(2L, dto.getCountByVehicleAndType().get(2L).get("PANNE"));
        assertEquals("ABC123", dto.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", dto.getVehicleImmatriculations().get(2L));
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType1 = new HashMap<>();
        Map<String, Long> vehicle1Types1 = new HashMap<>();
        vehicle1Types1.put("ACCIDENT", 3L);
        vehicle1Types1.put("PANNE", 2L);
        countByVehicleAndType1.put(1L, vehicle1Types1);

        Map<Long, String> vehicleImmatriculations1 = new HashMap<>();
        vehicleImmatriculations1.put(1L, "ABC123");

        Map<Long, Map<String, Long>> countByVehicleAndType2 = new HashMap<>();
        Map<String, Long> vehicle1Types2 = new HashMap<>();
        vehicle1Types2.put("ACCIDENT", 3L);
        vehicle1Types2.put("PANNE", 2L);
        countByVehicleAndType2.put(1L, vehicle1Types2);

        Map<Long, String> vehicleImmatriculations2 = new HashMap<>();
        vehicleImmatriculations2.put(1L, "ABC123");

        Map<Long, Map<String, Long>> countByVehicleAndType3 = new HashMap<>();
        Map<String, Long> vehicle1Types3 = new HashMap<>();
        vehicle1Types3.put("ACCIDENT", 5L);
        vehicle1Types3.put("PANNE", 4L);
        countByVehicleAndType3.put(1L, vehicle1Types3);

        Map<Long, String> vehicleImmatriculations3 = new HashMap<>();
        vehicleImmatriculations3.put(1L, "GHI789");

        VehicleIncidentTypeDistributionDTO dto1 = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType1, vehicleImmatriculations1);
        VehicleIncidentTypeDistributionDTO dto2 = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType2, vehicleImmatriculations2);
        VehicleIncidentTypeDistributionDTO dto3 = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType3, vehicleImmatriculations3);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testToString() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        vehicle1Types.put("PANNE", 2L);
        countByVehicleAndType.put(1L, vehicle1Types);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentTypeDistributionDTO dto = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, vehicleImmatriculations);

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("countByVehicleAndType="));
        assertTrue(result.contains("vehicleImmatriculations="));
    }

    @Test
    void testEqualsWithSameObject() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        countByVehicleAndType.put(1L, vehicle1Types);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentTypeDistributionDTO dto = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, vehicleImmatriculations);

        // Then
        assertEquals(dto, dto);
    }

    @Test
    void testEqualsWithNull() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        countByVehicleAndType.put(1L, vehicle1Types);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentTypeDistributionDTO dto = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, vehicleImmatriculations);

        // Then
        assertNotEquals(null, dto);
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        countByVehicleAndType.put(1L, vehicle1Types);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentTypeDistributionDTO dto = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, vehicleImmatriculations);

        // Then
        assertNotEquals("Not a DTO", dto);
    }

    @Test
    void testBuilderWithNullMaps() {
        // When
        VehicleIncidentTypeDistributionDTO dto = VehicleIncidentTypeDistributionDTO.builder()
                .countByVehicleAndType(null)
                .vehicleImmatriculations(null)
                .build();

        // Then
        assertNull(dto.getCountByVehicleAndType());
        assertNull(dto.getVehicleImmatriculations());
    }

    @Test
    void testEqualsWithDifferentCountByVehicleAndType() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType1 = new HashMap<>();
        Map<String, Long> vehicle1Types1 = new HashMap<>();
        vehicle1Types1.put("ACCIDENT", 3L);
        countByVehicleAndType1.put(1L, vehicle1Types1);

        Map<Long, Map<String, Long>> countByVehicleAndType2 = new HashMap<>();
        Map<String, Long> vehicle1Types2 = new HashMap<>();
        vehicle1Types2.put("ACCIDENT", 5L);
        countByVehicleAndType2.put(1L, vehicle1Types2);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentTypeDistributionDTO dto1 = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType1, vehicleImmatriculations);
        VehicleIncidentTypeDistributionDTO dto2 = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType2, vehicleImmatriculations);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentVehicleImmatriculations() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        countByVehicleAndType.put(1L, vehicle1Types);

        Map<Long, String> vehicleImmatriculations1 = new HashMap<>();
        vehicleImmatriculations1.put(1L, "ABC123");

        Map<Long, String> vehicleImmatriculations2 = new HashMap<>();
        vehicleImmatriculations2.put(1L, "DEF456");

        VehicleIncidentTypeDistributionDTO dto1 = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, vehicleImmatriculations1);
        VehicleIncidentTypeDistributionDTO dto2 = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, vehicleImmatriculations2);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithOneNullCountByVehicleAndType() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        countByVehicleAndType.put(1L, vehicle1Types);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentTypeDistributionDTO dto1 = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, vehicleImmatriculations);
        VehicleIncidentTypeDistributionDTO dto2 = new VehicleIncidentTypeDistributionDTO(null, vehicleImmatriculations);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithOneNullVehicleImmatriculations() {
        // Given
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();
        Map<String, Long> vehicle1Types = new HashMap<>();
        vehicle1Types.put("ACCIDENT", 3L);
        countByVehicleAndType.put(1L, vehicle1Types);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentTypeDistributionDTO dto1 = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, vehicleImmatriculations);
        VehicleIncidentTypeDistributionDTO dto2 = new VehicleIncidentTypeDistributionDTO(countByVehicleAndType, null);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithBothNullMaps() {
        // Given
        VehicleIncidentTypeDistributionDTO dto1 = new VehicleIncidentTypeDistributionDTO(null, null);
        VehicleIncidentTypeDistributionDTO dto2 = new VehicleIncidentTypeDistributionDTO(null, null);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testHashCodeWithNullMaps() {
        // Given
        VehicleIncidentTypeDistributionDTO dto = new VehicleIncidentTypeDistributionDTO(null, null);

        // When & Then
        assertDoesNotThrow(dto::hashCode);
    }
}
