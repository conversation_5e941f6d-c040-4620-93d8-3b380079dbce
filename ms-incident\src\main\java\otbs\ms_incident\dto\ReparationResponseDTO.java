package otbs.ms_incident.dto;

import otbs.ms_incident.enums.StatusReparation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "dto pour la réponse contenant les données d'une réparation")
public class ReparationResponseDTO {
    @Schema(description = "Identifiant unique de la réparation", example = "1")
    private Long id;

    @Schema(description = "Date de la réparation", example = "2023-06-01")
    private LocalDate dateReparation;

    @Schema(description = "Statut de la réparation", example = "EN_COURS", allowableValues = {"EN_COURS", "TERMINEE"})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private StatusReparation status;

    @Schema(description = "Description des travaux effectués", example = "Remplacement pare-choc avant")
    private String description;

    @Schema(description = "Coût de la réparation", example = "1200.50")
    private BigDecimal cout;

    @Schema(description = "Garage ayant effectué la réparation", example = "Garage Central")
    private String garage;

    @Schema(description = "ID de l'incident associé à cette réparation", example = "42")
    private Long incidentId;

    @Schema(description = "ID du véhicule associé à cette réparation", example = "101")
    private Long vehiculeId;

    @Schema(description = "Immatriculation du véhicule associé à cette réparation", example = "123-ABC")
    private String immatriculation;

    @Schema(description = "Indique si la réparation a été remboursée par l'assurance", example = "true")
    private Boolean rembourse;

    @Schema(description = "Montant couvert par l'assurance", example = "1000.00")
    private BigDecimal montantCouverture;

    @Schema(description = "Date et heure de création de l'enregistrement", example = "2023-05-15T14:30:00")
    private LocalDateTime createdAt;

    @Schema(description = "Date et heure de dernière modification de l'enregistrement", example = "2023-05-16T09:15:00")
    private LocalDateTime updatedAt;
}