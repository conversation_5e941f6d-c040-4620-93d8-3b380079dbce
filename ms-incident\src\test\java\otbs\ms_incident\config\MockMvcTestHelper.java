package otbs.ms_incident.config;

import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors;
import org.springframework.test.web.servlet.request.RequestPostProcessor;


/**
 * Helper utilities to facilitate testing with Spring Security and MockMvc.
 */
class MockMvcTestHelper {

    /**
     * Creates a RequestPostProcessor for a user with ADMINISTRATEUR role
     */
    public static RequestPostProcessor withAdminRole() {
        return SecurityMockMvcRequestPostProcessors.user("admin").roles("ADMINISTRATEUR");
    }

    /**
     * Creates a RequestPostProcessor for a user with RESPONSABLE_SERVICE_GENERAUX role
     */
    public static RequestPostProcessor withGestionnaireRole() {
        return SecurityMockMvcRequestPostProcessors.user("gestionnaire").roles("RESPONSABLE_SERVICE_GENERAUX");
    }

    /**
     * Creates a RequestPostProcessor for a user with DIRECTEUR_GENERAL role
     */
    public static RequestPostProcessor withDirecteurRole() {
        return SecurityMockMvcRequestPostProcessors.user("directeur").roles("DIRECTEUR_GENERAL");
    }

    /**
     * Creates a RequestPostProcessor for a user with DIRECTEUR_TECHNIQUE role
     */
    public static RequestPostProcessor withDirecteurTechniqueRole() {
        return SecurityMockMvcRequestPostProcessors.user("directeur_technique").roles("DIRECTEUR_TECHNIQUE");
    }

    /**
     * Creates a RequestPostProcessor for a user with CONSULTANT role
     */
    public static RequestPostProcessor withConsultantRole() {
        return SecurityMockMvcRequestPostProcessors.user("consultant").roles("CONSULTANT");
    }

    /**
     * Creates a RequestPostProcessor for a user with all available roles
     */
    public static RequestPostProcessor withAllRoles() {
        return SecurityMockMvcRequestPostProcessors.user("superuser")
                .roles("ADMINISTRATEUR", "RESPONSABLE_SERVICE_GENERAUX", "DIRECTEUR_GENERAL", 
                      "DIRECTEUR_TECHNIQUE", "CONSULTANT");
    }
    
    /**
     * Creates a RequestPostProcessor for CSRF protection
     */
    public static RequestPostProcessor withCsrf() {
        return SecurityMockMvcRequestPostProcessors.csrf();
    }
    
    /**
     * Creates a fully authenticated request post processor
     */
    public static RequestPostProcessor anonymous() {
        return SecurityMockMvcRequestPostProcessors.anonymous();
    }

    /**
     * Creates a RequestPostProcessor for a user with no roles
     */
    public static RequestPostProcessor withNoRoles() {
        return SecurityMockMvcRequestPostProcessors.user("user").roles();
    }
} 