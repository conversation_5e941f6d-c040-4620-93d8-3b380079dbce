package otbs.ms_incident.config;

import feign.Logger;
import feign.RequestInterceptor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FeignConfigTest {

    private FeignConfig config;

    @Mock
    private HttpRequest httpRequest;

    @Mock
    private ClientHttpRequestExecution execution;

    @Mock
    private ClientHttpResponse clientHttpResponse;

    @Mock
    private HttpHeaders httpHeaders;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private JwtAuthenticationToken jwtAuthenticationToken;

    @Mock
    private Jwt jwt;

    @BeforeEach
    void setUp() {
        config = new FeignConfig();
    }

    @Test
    void feignLoggerLevel_shouldReturnFullLevel() {
        // When
        Logger.Level level = config.feignLoggerLevel();

        // Then
        assertNotNull(level);
        assertEquals(Logger.Level.FULL, level);
    }

    @Test
    void restTemplate_shouldReturnRestTemplateWithInterceptor() {
        // When
        RestTemplate restTemplate = config.restTemplate();

        // Then
        assertNotNull(restTemplate);
        assertEquals(1, restTemplate.getInterceptors().size());
        assertTrue(restTemplate.getInterceptors().get(0) instanceof ClientHttpRequestInterceptor);
    }

    @Test
    void feignRequestInterceptor_shouldReturnFeignClientInterceptor() {
        // When
        RequestInterceptor interceptor = config.feignRequestInterceptor();

        // Then
        assertNotNull(interceptor);
        assertTrue(interceptor instanceof FeignClientInterceptor);
    }

    @Test
    void restTemplateInterceptor_shouldHaveCorrectType() {
        // Given
        RestTemplate restTemplate = config.restTemplate();

        // Then
        assertNotNull(restTemplate);
        assertFalse(restTemplate.getInterceptors().isEmpty());
        ClientHttpRequestInterceptor interceptor = restTemplate.getInterceptors().get(0);
        // Vérifie que l'intercepteur est une instance de classe interne JwtTokenInterceptor
        assertTrue(interceptor.getClass().getName().contains("JwtTokenInterceptor"));
    }

    @Test
    void jwtTokenInterceptor_shouldAddAuthorizationHeader_whenJwtAuthenticationTokenIsPresent() throws Exception {
        // Given
        RestTemplate restTemplate = config.restTemplate();
        ClientHttpRequestInterceptor interceptor = restTemplate.getInterceptors().get(0);
        byte[] body = "test".getBytes();

        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            // Setup mocks
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(jwtAuthenticationToken);
            when(jwtAuthenticationToken.getToken()).thenReturn(jwt);
            when(jwt.getTokenValue()).thenReturn("test-token");
            when(httpRequest.getHeaders()).thenReturn(httpHeaders);
            when(execution.execute(httpRequest, body)).thenReturn(clientHttpResponse);

            // When
            Method interceptMethod = interceptor.getClass().getDeclaredMethod("intercept",
                    HttpRequest.class, byte[].class, ClientHttpRequestExecution.class);
            interceptMethod.setAccessible(true);
            interceptMethod.invoke(interceptor, httpRequest, body, execution);

            // Then
            verify(httpHeaders).add("Authorization", "Bearer test-token");
            verify(execution).execute(httpRequest, body);
        }
    }

    @Test
    void jwtTokenInterceptor_shouldNotAddAuthorizationHeader_whenAuthenticationIsNull() throws Exception {
        // Given
        RestTemplate restTemplate = config.restTemplate();
        ClientHttpRequestInterceptor interceptor = restTemplate.getInterceptors().get(0);
        byte[] body = "test".getBytes();

        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            // Setup mocks
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(null);
            when(httpRequest.getHeaders()).thenReturn(httpHeaders);
            when(execution.execute(httpRequest, body)).thenReturn(clientHttpResponse);

            // When
            Method interceptMethod = interceptor.getClass().getDeclaredMethod("intercept",
                    HttpRequest.class, byte[].class, ClientHttpRequestExecution.class);
            interceptMethod.setAccessible(true);
            interceptMethod.invoke(interceptor, httpRequest, body, execution);

            // Then
            verify(httpHeaders, never()).add(eq("Authorization"), anyString());
            verify(execution).execute(httpRequest, body);
        }
    }

    @Test
    void jwtTokenInterceptor_shouldNotAddAuthorizationHeader_whenAuthenticationIsNotJwtAuthenticationToken() throws Exception {
        // Given
        RestTemplate restTemplate = config.restTemplate();
        ClientHttpRequestInterceptor interceptor = restTemplate.getInterceptors().get(0);
        byte[] body = "test".getBytes();

        // Créer une authentification qui n'est pas JwtAuthenticationToken
        Authentication nonJwtAuthentication = mock(Authentication.class);

        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            // Setup mocks
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(nonJwtAuthentication);
            when(httpRequest.getHeaders()).thenReturn(httpHeaders);
            when(execution.execute(httpRequest, body)).thenReturn(clientHttpResponse);

            // When
            Method interceptMethod = interceptor.getClass().getDeclaredMethod("intercept",
                    HttpRequest.class, byte[].class, ClientHttpRequestExecution.class);
            interceptMethod.setAccessible(true);
            interceptMethod.invoke(interceptor, httpRequest, body, execution);

            // Then
            verify(httpHeaders, never()).add(eq("Authorization"), anyString());
            verify(execution).execute(httpRequest, body);
        }
    }

    @Test
    void vehiculeServiceUrl_shouldBeInjected() throws Exception {
        // Given
        String testUrl = "http://test-url";
        Field urlField = FeignConfig.class.getDeclaredField("vehiculeServiceUrl");
        urlField.setAccessible(true);
        urlField.set(config, testUrl);

        // Then
        assertEquals(testUrl, urlField.get(config));
    }
}
