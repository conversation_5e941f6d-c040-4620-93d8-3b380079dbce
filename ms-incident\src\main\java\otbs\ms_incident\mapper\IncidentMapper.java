package otbs.ms_incident.mapper;

import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import org.mapstruct.*;

import java.util.List;

/**
 * Interface de mapping pour les objets Incident
 */
@Mapper(componentModel = "spring",
        uses = {ReparationMapper.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface IncidentMapper {

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", constant = "system")
    @Mapping(target = "updatedBy", constant = "system")
    @Mapping(target = "reparations", ignore = true)
    Incident toEntity(IncidentRequestDTO requestDTO);

    @Mapping(target = "reparations", source = "reparations")
    IncidentResponseDTO toResponseDTO(Incident incident);

    /**
     * Transforme les noms de fichiers en URLs relatives pour l'API REST
     * Cette méthode est appelée après la conversion de l'entité en DTO
     */
    @AfterMapping
    default void transformFilePaths(@MappingTarget IncidentResponseDTO responseDTO, Incident incident) {
        // Transformer les noms des photos en URLs
        if (incident.getPhotos() != null && !incident.getPhotos().isEmpty()) {
            List<String> transformedPhotos = incident.getPhotos().stream()
                .map(filename -> transformToApiUrl(incident.getId(), "PHOTO", filename))
                .collect(java.util.stream.Collectors.toList());
            responseDTO.setPhotos(transformedPhotos);
        }

        // Transformer le nom du constat en URL
        if (incident.getConstat() != null && !incident.getConstat().isEmpty()) {
            String transformedConstat = transformToApiUrl(incident.getId(), "CONSTAT", incident.getConstat());
            responseDTO.setConstat(transformedConstat);
        }
    }

    /**
     * Transforme un nom de fichier en URL relative pour l'API REST
     */
    default String transformToApiUrl(Long incidentId, String fileType, String filename) {
        return "/api/files/incident/" + incidentId + "/" + fileType + "/" + filename;
    }

    List<IncidentResponseDTO> toResponseDTOList(List<Incident> incidents);

    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "updatedBy", constant = "system")
    @Mapping(target = "reparations", ignore = true)
    void updateIncidentFromDTO(IncidentRequestDTO requestDTO, @MappingTarget Incident incident);

    /**
     * Convertit une entité Incident en DTO de requête
     * Cette méthode est utile pour les mises à jour d'une entité existante
     *
     * @param incident L'entité à convertir
     * @return Le DTO de requête correspondant
     */
    IncidentRequestDTO toRequestDTO(Incident incident);

    /**
     * Méthode par défaut pour gérer correctement la mise à jour des photos
     * Si les photos sont null dans le DTO, on garde les photos actuelles de l'incident
     * Si les photos ne sont pas null dans le DTO, on met à jour les photos de l'incident
     */
    @AfterMapping
    default void handlePhotos(IncidentRequestDTO requestDTO, @MappingTarget Incident incident) {
        if (requestDTO.getPhotos() == null && incident.getPhotos() != null) {
            // Ne rien faire, garder les photos existantes
            // Cette méthode est appelée après updateIncidentFromDTO et permet de corriger
            // le comportement par défaut de MapStruct avec les listes
        } else if (requestDTO.getPhotos() != null) {
            // Mettre à jour les photos avec celles du DTO
            incident.setPhotos(requestDTO.getPhotos());
        }

        // S'assurer que la priorité est définie comme FAIBLE si elle est null
        if (incident.getPriorite() == null) {
            incident.setPriorite(NiveauPrioriteIncident.FAIBLE);
        }
    }
}