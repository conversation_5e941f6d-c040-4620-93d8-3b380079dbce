# Microservice de Gestion des Incidents (ms-incident)

![Version](https://img.shields.io/badge/version-0.0.1--SNAPSHOT-blue) 
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.4.3-brightgreen) 
![Java](https://img.shields.io/badge/Java-17-orange)
![Spring Cloud](https://img.shields.io/badge/Spring%20Cloud-2024.0.0-blue)


## 📋 Description

Le microservice `ms-incident` est un composant essentiel de l'architecture de gestion de parc automobile (ParcAuto). Il est responsable de la gestion complète du cycle de vie des incidents (accidents et pannes) qui surviennent aux véhicules, ainsi que du suivi des réparations associées à ces incidents. Ce service s'intègre dans une architecture microservices plus large via Spring Cloud.

## 📑 Table des matières

- [Fonctionnalités](#-fonctionnalités)
- [Technologies utilisées](#-technologies-utilisées)
- [Architecture](#-architecture)
- [Structure du projet](#-structure-du-projet)
- [Modèles de données](#-modèles-de-données)
- [API REST](#-api-rest)
- [Gestion des fichiers](#-gestion-des-fichiers)
- [Sécurité](#-sécurité)
- [Installation et démarrage](#-installation-et-démarrage)
- [Documentation API](#-documentation-api)
- [Monitoring](#-monitoring)
- [CI/CD et Qualité](#-cicd-et-qualité)
- [Contribution](#-contribution)
- [Dépannage](#-dépannage)
- [Licence](#-licence)

## 🚀 Fonctionnalités

- **Gestion complète des incidents**
  - Création, consultation, mise à jour et suppression d'incidents
  - Catégorisation des incidents par type (ACCIDENT, PANNE)
  - Attachement de documents (constats, rapports) et photos
  - Recherche avancée par type, date et lieu

- **Gestion des réparations**
  - Association de réparations aux incidents
  - Suivi des coûts de réparation
  - Gestion des remboursements d'assurance
  - Calcul des montants couverts

- **Stockage de fichiers**
  - Gestion de l'upload de fichiers (constats, photos)
  - Support de plusieurs formats de fichiers
  - Validation des fichiers téléchargés
  - Stockage sécurisé sur système de fichiers

## 💻 Technologies utilisées

- **Backend**
  - Java 17
  - Spring Boot 3.4.3
  - Spring Cloud 2024.0.0
  - Spring Data JPA
  - Spring Security avec OAuth2/JWT

- **Persistance**
  - PostgreSQL
  - Hibernate ORM

- **Architecture Cloud**
  - Eureka (Service Discovery)
  - Spring Cloud Config
  - Spring Cloud Gateway

- **Cache**
  - Caffeine (mise en cache en mémoire)

- **Documentation et tests**
  - Swagger/OpenAPI 3.0
  - JUnit 5
  - Mockito
  - SonarQube

- **Templating**
  - Thymeleaf (pour les interfaces de test)

- **Outils de développement**
  - Lombok
  - MapStruct
  - Maven
  - Git

## 🏛️ Architecture

Ce microservice s'intègre dans une architecture de microservices plus large avec :

- Un serveur de configuration centralisée (config-server)
- Un serveur de découverte de services (eureka-server)
- Une passerelle API (gateway-server)
- Un serveur d'authentification et d'autorisation (Keycloak)

### Schéma d'architecture



## 📂 Structure du projet

La structure du projet suit une architecture en couches claire et modulaire, respectant les principes de séparation des responsabilités :

```
ms-incident/
├── src/main/java/otbs/ms_incident/
│   ├── controller/           # API REST et points d'entrée HTTP
│   │   ├── IncidentController.java        # Gestion des incidents
│   │   ├── ReparationController.java      # Gestion des réparations
│   │   ├── FileStorageController.java     # Upload/download de fichiers
│   │   └── FileUploadViewController.java  # Interface d'upload Thymeleaf
│   │
│   ├── dto/                  # Objets de transfert de données (DTO)
│   │   ├── IncidentRequestDTO.java        # Données entrantes d'incident
│   │   ├── IncidentResponseDTO.java       # Données sortantes d'incident
│   │   ├── ReparationRequestDTO.java      # Données entrantes de réparation
│   │   └── ReparationResponseDTO.java     # Données sortantes de réparation
│   │
│   ├── entity/               # Entités JPA (modèle de données)
│   │   ├── BaseEntity.java               # Classe abstraite avec champs d'audit
│   │   ├── Incident.java                 # Entité principale d'incident
│   │   └── Reparation.java               # Entité associée de réparation
│   │
│   ├── enums/                # Types énumérés
│   │   ├── TypeIncident.java             # Types d'incidents (ACCIDENT, PANNE)
│   │   └── FileType.java                 # Types de fichiers (PHOTO, CONSTAT)
│   │
│   ├── exception/            # Gestion d'erreurs personnalisée
│   │   ├── BadRequestException.java      # Erreurs de validation (400)
│   │   ├── ResourceNotFoundException.java # Ressource non trouvée (404)
│   │   ├── FileStorageException.java     # Erreurs de stockage de fichiers
│   │   ├── FileNotFoundStorageException.java # Fichier non trouvé
│   │   ├── InvalidFileTypeException.java # Type de fichier invalide
│   │   ├── ErrorResponse.java            # Structure de réponse d'erreur
│   │   └── GlobalExceptionHandler.java   # Intercepteur d'exceptions global
│   │
│   ├── mapper/               # Conversion entity-DTO avec MapStruct
│   │   ├── IncidentMapper.java           # Mappeur pour les incidents
│   │   └── ReparationMapper.java         # Mappeur pour les réparations
│   │
│   ├── repository/           # Couche d'accès aux données (JPA)
│   │   ├── IncidentRepository.java       # Requêtes liées aux incidents
│   │   └── ReparationRepository.java     # Requêtes liées aux réparations
│   │
│   ├── service/              # Logique métier et orchestration
│   │   ├── IncidentService.java          # Interface de service d'incidents
│   │   ├── IncidentServiceImpl.java      # Implémentation du service d'incidents
│   │   ├── ReparationService.java        # Interface de service de réparations
│   │   ├── ReparationServiceImpl.java    # Implémentation du service de réparations
│   │   ├── FileStorageService.java       # Interface de stockage de fichiers
│   │   ├── FileStorageServiceImpl.java   # Implémentation du stockage de fichiers
│   │   ├── IncidentFileManager.java      # Interface de gestion des fichiers d'incidents
│   │   └── IncidentFileManagerImpl.java  # Implémentation de la gestion des fichiers
│   │
│   ├── config/               # Configuration de l'application
│   │   ├── OpenApiConfig.java            # Configuration de Swagger/OpenAPI
│   │   ├── SecurityConfig.java           # Configuration de Spring Security
│   │   ├── AuditConfig.java              # Configuration de l'audit JPA
│   │   ├── CacheConfig.java              # Configuration du cache Caffeine
│   │   ├── WebConfig.java                # Configuration des ressources web
│   │   ├── JpaAuditingConfig.java        # Configuration de l'auditeur JPA
│   │   └── MessageConfig.java            # Configuration de l'internationalisation
│   │
│   └── MsIncidentApplication.java        # Point d'entrée de l'application
│
├── src/main/resources/       # Ressources et fichiers de configuration
│   ├── templates/            # Templates HTML Thymeleaf
│   │   └── upload.html                   # Interface d'upload de fichiers
│   │
│   ├── static/               # Ressources statiques (CSS, JS, images)
│   │   ├── css/                          # Feuilles de style CSS
│   │   └── js/                           # Scripts JavaScript
│   │
│   ├── application.properties            # Configuration principale
│   ├── bootstrap.properties              # Configuration Cloud Config
│   ├── messages.properties               # Messages internationalisés (fr)
│   └── logback-spring.xml                # Configuration de logging
│
└── src/test/                 # Tests unitaires et d'intégration
    ├── java/otbs/ms_incident/
    │   ├── controller/                   # Tests des contrôleurs
    │   ├── service/                      # Tests des services
    │   ├── repository/                   # Tests des repositories
    │   ├── mapper/                       # Tests des mappeurs
    │   ├── config/                       # Tests des configurations
    │   └── entity/                       # Tests des entités
    │
    └── resources/
        └── application-test.properties   # Configuration pour les tests
```

### Points clés de l'organisation

- **Architecture en couches** : Séparation claire entre présentation (controllers), logique métier (services) et accès aux données (repositories)
- **Approche DTO** : Utilisation de DTOs distincts pour les requêtes et les réponses, évitant l'exposition directe des entités
- **Mapping automatisé** : MapStruct pour la conversion efficace entre entités et DTOs
- **Gestion d'erreurs robuste** : Exceptions personnalisées et handler global pour des réponses d'erreur cohérentes
- **Extensibilité** : Structure modulaire facilitant l'ajout de nouvelles fonctionnalités

### Conventions de nommage

Le projet suit des conventions strictes pour une meilleure lisibilité et maintenabilité :

- **Classes** : Noms en PascalCase, suffixés selon leur rôle (ex: `IncidentController`, `ReparationService`)
- **Interfaces** : Noms simples, implémentations suffixées par `Impl` (ex: `FileStorageService`, `FileStorageServiceImpl`)
- **Méthodes** : Verbes d'action en camelCase reflétant leur rôle (ex: `createIncident`, `getIncidentById`)
- **Packages** : Organisés par fonction, tous en minuscules (ex: `controller`, `service`)

### Séparation des responsabilités

Chaque composant a une responsabilité unique et bien définie :

- **Controllers** : Traitement des requêtes HTTP, validation des entrées, formatage des réponses
- **Services** : Logique métier, orchestration des opérations, gestion des transactions
- **Repositories** : Accès aux données, requêtes personnalisées
- **Mappers** : Transformation de données entre les différentes couches
- **Entities** : Structure de données JPA, contraintes, relations

## 📊 Modèles de données

### BaseEntity

Classe abstraite qui fournit des champs d'audit pour toutes les entités du système :

- **id**: Identifiant unique généré automatiquement
- **createdAt**: Date et heure de création de l'entité
- **updatedAt**: Date et heure de la dernière modification
- **createdBy**: Identifiant de l'utilisateur ayant créé l'entité
- **updatedBy**: Identifiant de l'utilisateur ayant effectué la dernière modification

### Incident

Représente un événement survenu à un véhicule :

- **id**: Identifiant unique
- **date**: Date de l'incident
- **type**: Type d'incident (ACCIDENT ou PANNE)
- **lieu**: Emplacement où l'incident s'est produit
- **constat**: Référence au constat d'accident ou rapport technique
- **photos**: Liste des URLs des photos de l'incident
- **description**: Description détaillée
- **reparations**: Liste des réparations associées (relation OneToMany)

### Réparation

Représente les travaux effectués suite à un incident :

- **id**: Identifiant unique
- **dateReparation**: Date de la réparation
- **description**: Description des travaux
- **cout**: Coût total de la réparation
- **garage**: Garage ayant effectué la réparation
- **rembourse**: Statut de remboursement par l'assurance
- **montantCouverture**: Montant couvert par l'assurance
- **incident**: Référence à l'incident associé (relation ManyToOne)

## 🌐 API REST

### Gestion des Incidents

| Méthode | Endpoint | Description | Rôles autorisés |
|---------|----------|-------------|----------------|
| `POST` | `/api/incidents` | Créer un nouvel incident | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX, DIRECTEUR_TECHNIQUE, CONSULTANT |
| `GET` | `/api/incidents` | Récupérer tous les incidents | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX, DIRECTEUR_GENERAL |
| `GET` | `/api/incidents/{id}` | Récupérer un incident par ID | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX, DIRECTEUR_GENERAL |
| `PUT` | `/api/incidents/{id}` | Mettre à jour un incident | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX, DIRECTEUR_TECHNIQUE, CONSULTANT |
| `DELETE` | `/api/incidents/{id}` | Supprimer un incident | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX |
| `GET` | `/api/incidents/type/{type}` | Rechercher des incidents par type | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX, DIRECTEUR_GENERAL |
| `GET` | `/api/incidents/date` | Rechercher des incidents par plage de dates | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX, DIRECTEUR_GENERAL |
| `GET` | `/api/incidents/lieu` | Rechercher des incidents par lieu | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX, DIRECTEUR_GENERAL |

### Gestion des Réparations

| Méthode | Endpoint | Description | Rôles autorisés |
|---------|----------|-------------|----------------|
| `POST` | `/api/reparations/incident/{incidentId}` | Créer une réparation pour un incident | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX |
| `GET` | `/api/reparations` | Récupérer toutes les réparations | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX, DIRECTEUR_GENERAL |
| `GET` | `/api/reparations/{id}` | Récupérer une réparation par ID | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX, DIRECTEUR_GENERAL |
| `GET` | `/api/reparations/incident/{incidentId}` | Récupérer les réparations d'un incident | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX, DIRECTEUR_GENERAL |
| `PUT` | `/api/reparations/{id}` | Mettre à jour une réparation | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX |
| `DELETE` | `/api/reparations/{id}` | Supprimer une réparation | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX |
| `GET` | `/api/reparations/remboursement` | Récupérer les réparations par statut de remboursement | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX |

### Gestion des Fichiers

| Méthode | Endpoint | Description | Rôles autorisés |
|---------|----------|-------------|----------------|
| `POST` | `/api/files/upload/incident/{incidentId}/constat` | Télécharger un constat pour un incident | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX |
| `POST` | `/api/files/upload/incident/{incidentId}/photo` | Télécharger une photo pour un incident | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX |
| `GET` | `/api/files/download/{fileType}/{fileName}` | Télécharger un fichier | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX |
| `DELETE` | `/api/files/{fileType}/{fileName}` | Supprimer un fichier | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX |
| `GET` | `/upload` | Interface d'upload (Thymeleaf) | ADMINISTRATEUR, RESPONSABLE_SERVICE_GENERAUX |

## 📁 Gestion des fichiers

Le service prend en charge l'upload et le téléchargement de deux types de fichiers :
- **Constats d'accident/pannes** : Documents officiels relatifs aux incidents (formats PDF, DOC, DOCX)
- **Photos** : Images des véhicules endommagés (formats JPG, JPEG, PNG, WEBP, GIF, BMP, TIFF)

### Architecture de stockage

Les fichiers sont stockés dans un système de fichiers monté (NFS) avec la structure hiérarchique suivante :

```
/mnt/nfs_storage/Reparation/
├── incident_1/
│   ├── constats/
│   │   ├── constat_uuid1.pdf
│   │   └── constat_uuid2.pdf
│   └── photos/
│       ├── photo_uuid1.jpg
│       ├── photo_uuid2.png
│       └── photo_uuid3.webp
├── incident_2/
│   ├── constats/
│   │   └── constat_uuid4.pdf
│   └── photos/
│       └── photo_uuid5.jpg
└── ...
```

Cette architecture permet:
- **Isolation des données** : Chaque incident possède son propre dossier isolé
- **Organisation logique** : Séparation claire entre constats et photos
- **Navigation simplifiée** : Structure intuitive pour la maintenance manuelle si nécessaire

### Processus de gestion

Le cycle de vie complet des fichiers est géré par le service:

1. **Téléchargement (Upload)**
   - Validation du type MIME et de l'extension du fichier
   - Génération d'un nom de fichier unique avec UUID
   - Sanitisation des métadonnées potentiellement sensibles
   - Création dynamique des dossiers nécessaires si absents
   - Stockage physique du fichier

2. **Association à l'incident**
   - Enregistrement du chemin du fichier dans l'entité Incident
   - Pour les constats: stockage dans le champ `constat` de l'entité
   - Pour les photos: ajout à la collection `photos` de l'entité

3. **Récupération (Download)**
   - Résolution du chemin physique à partir de l'ID d'incident et du type
   - Vérification des autorisations basée sur les rôles

 

4. **Suppression**
   - Suppression physique du fichier
   - Mise à jour des références dans l'entité Incident

### Sécurité et validation

Le service applique plusieurs couches de sécurité:

- **Validation de type MIME** : Seuls les types configurés sont acceptés
  - Pour les constats: `application/pdf`, `application/msword`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
  - Pour les photos: `image/jpeg`, `image/png`, `image/webp`, `image/gif`, `image/bmp`, `image/tiff`

- **Limites de taille** : Configuration stricte via `spring.servlet.multipart.*`
  - Limite par fichier: 10 MB
  - Limite par requête: 10 MB

- **Nomenclature sécurisée**
  - Format: `[type]_[UUID].[extension]`
  - Exemple: `photo_550e8400-e29b-41d4-a716-************.jpg`


- **Isolation des chemins**
  - Vérification que le chemin résolu est bien un sous-chemin du stockage configuré
  - Prévention des attaques de type Path Traversal

### Configuration

La gestion des fichiers est configurable via les variables d'environnement:

| Variable | Description | Valeur par défaut |
|----------|-------------|-------------------|
| `STORAGE_PATH` | Chemin racine du stockage | `/mnt/nfs_storage/Reparation` |

Le chemin peut être modifié en production sans changement de code, permettant une flexibilité dans le déploiement.


## 🔒 Sécurité

Le microservice utilise Spring Security avec OAuth2/JWT pour l'authentification et l'autorisation :

- **Authentification** : Basée sur JWT (JSON Web Tokens) fournis par un serveur Keycloak
- **Autorisation** : Contrôle d'accès basé sur les rôles (RBAC)
- **Rôles supportés** :
  - ADMINISTRATEUR : Accès complet à toutes les fonctionnalités
  - RESPONSABLE_SERVICE_GENERAUX : Gestion des incidents et réparations
  - DIRECTEUR_TECHNIQUE : Création et mise à jour d'incidents uniquement
  - DIRECTEUR_GENERAL : Consultation des incidents et réparations
  - CONSULTANT : Création et mise à jour d'incidents uniquement

Les endpoints sont sécurisés avec l'annotation `@PreAuthorize` qui vérifie les rôles de l'utilisateur avant d'autoriser l'accès.

## 🛠️ Installation et démarrage

### Prérequis

- Java 17 ou supérieur
- Maven 3.8+
- PostgreSQL 14+
- Les autres microservices de l'écosystème ParcAuto (config-server, eureka-server)
- Système de fichiers monté pour le stockage des documents

### Variables d'environnement

| Variable | Description | Valeur par défaut |
|----------|-------------|-------------------|
| `SERVER_PORT` | Port du serveur | 9002 |
| `SPRING_CONFIG_IMPORT` | Mode d'import de configuration | configserver: |
| `CONFIG_SERVER_URL` | URL du serveur Config | http://*************:9101 |
| `STORAGE_PATH` | Chemin du stockage NFS | /mnt/nfs_storage/Reparation |
| `DATABASE_URL` | URL de connexion à la base de données | ********************************************* |
| `DATABASE_USER` | Nom d'utilisateur de la base de données | parcauto |
| `DATABASE_PASSWORD` | Mot de passe de la base de données | parcauto |
| `EUREKA_SERVICE` | URL du serveur Eureka | http://*************:9102/eureka/ |
| `KEYCLOAK_URL` | URL du serveur Keycloak | http://*************:8080/realms/Parc-Auto |

### Installation avec Maven

1. Cloner le repository
   ```bash
   git clone https://gitlab.com/parcauto/ms-incident.git
   cd ms-incident
   ```

2. Compiler avec Maven
```bash
mvn clean package
```

3. Exécuter l'application
```bash
java -jar target/ms-incident-0.0.1-SNAPSHOT.jar
```

Ou avec Maven :
```bash
mvn spring-boot:run
```

### Installation avec Docker

1. Construire l'image Docker
   ```bash
   docker build -t ms-incident .
   ```

2. Exécuter le conteneur
   ```bash
   docker run -d -p 9002:9002 \
     -e CONFIG_SERVER_URL=http://config-server:9101 \
     -e EUREKA_SERVICE=http://eureka-server:9102/eureka/ \
     -e STORAGE_PATH=/mnt/nfs_storage/Reparation \
     -v /chemin/local/vers/stockage:/mnt/nfs_storage/Reparation \
     --name ms-incident ms-incident
   ```

## 📚 Documentation API

La documentation complète de l'API est disponible via Swagger UI :

```
http://localhost:9002/swagger-ui.html
```

La documentation inclut :
- Description détaillée de chaque endpoint
- Schémas des objets de requête et de réponse
- Codes de statut HTTP possibles
- Exemples de requêtes et réponses
- Support de l'authentification OAuth2 pour tester les endpoints sécurisés

## 📈 Monitoring

Le service expose des endpoints d'actuator pour le monitoring :

- `/actuator/health` - État de santé du service
- `/actuator/info` - Informations sur l'application
- `/actuator/metrics` - Métriques détaillées
- `/actuator/env` - Variables d'environnement configurées
- `/actuator/loggers` - Configuration des loggers

Ces endpoints peuvent être consommés par un système de monitoring comme Prometheus/Grafana.

## 🔄 CI/CD et Qualité

Le projet utilise GitLab CI/CD pour l'intégration et le déploiement continus, avec les étapes suivantes :

1. **sonarqube-check** : Analyse statique du code via SonarQube
2. **sonarqube-vulnerability-report** : Génération de rapport de vulnérabilités
3. **package** : Construction du fichier JAR avec Maven
4. **build_deploy** : Construction de l'image Docker et déploiement avec Podman

Le pipeline CI/CD est configuré dans le fichier `.gitlab-ci.yml`.

### Qualité du code

Le projet utilise SonarQube pour l'analyse de la qualité du code, avec les métriques suivantes :

- Couverture de code (via JaCoCo)
- Analyse des vulnérabilités
- Dette technique
- Duplication de code
- Respect des conventions de codage

## 🔧 Dépannage

### Problèmes courants

1. **Erreur de connexion à la base de données**
   - Vérifier les paramètres de connexion dans le fichier `application.properties`
   - S'assurer que PostgreSQL est en cours d'exécution
   - Vérifier les logs pour les détails de l'erreur

2. **Service non visible dans Eureka**
   - Vérifier que le serveur Eureka est en cours d'exécution
   - Vérifier la variable d'environnement `EUREKA_SERVICE`
   - S'assurer que le service a bien démarré et s'est inscrit correctement

3. **Erreur d'authentification OAuth2**
   - Vérifier que Keycloak est en cours d'exécution
   - Vérifier la configuration OAuth2 dans `application.properties`
   - S'assurer que les rôles sont correctement configurés dans Keycloak


### Logs

Les logs sont configurés via `logback-spring.xml` et sont disponibles dans le répertoire `logs/`. Le niveau de log peut être ajusté en modifiant ce fichier ou via l'endpoint actuator `/actuator/loggers`.



© 2024 OTBS - Tous droits réservés

