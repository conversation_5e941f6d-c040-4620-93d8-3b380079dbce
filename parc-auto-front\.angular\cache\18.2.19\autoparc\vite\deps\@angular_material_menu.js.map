{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/menu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Optional, Input, Directive, QueryList, EventEmitter, inject, Injector, afterNextRender, TemplateRef, ContentChildren, ViewChild, ContentChild, Output, ChangeDetectorRef, Self, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { FocusKeyManager, isFakeTouchstartFromScreenReader, isFakeMousedownFromScreenReader } from '@angular/cdk/a11y';\nimport { UP_ARROW, DOWN_ARROW, RIGHT_ARROW, LEFT_ARROW, ESCAPE, hasModifierKey, ENTER, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, merge, Subscription, of, asapScheduler } from 'rxjs';\nimport { startWith, switchMap, takeUntil, filter, take, delay } from 'rxjs/operators';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport { MatRipple, MatRippleModule, MatCommonModule } from '@angular/material/core';\nimport { TemplatePortal, DomPortalOutlet } from '@angular/cdk/portal';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i3 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\n\n/**\n * Injection token used to provide the parent menu to menu-specific components.\n * @docs-private\n */\nconst _c0 = [\"mat-menu-item\", \"\"];\nconst _c1 = [[[\"mat-icon\"], [\"\", \"matMenuItemIcon\", \"\"]], \"*\"];\nconst _c2 = [\"mat-icon, [matMenuItemIcon]\", \"*\"];\nfunction MatMenuItem_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 2);\n    i0.ɵɵelement(1, \"polygon\", 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c3 = [\"*\"];\nfunction MatMenu_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵlistener(\"keydown\", function MatMenu_ng_template_0_Template_div_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._handleKeydown($event));\n    })(\"click\", function MatMenu_ng_template_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closed.emit(\"click\"));\n    })(\"@transformMenu.start\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationStart($event));\n    })(\"@transformMenu.done\", function MatMenu_ng_template_0_Template_div_animation_transformMenu_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1._onAnimationDone($event));\n    });\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵprojection(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1._classList);\n    i0.ɵɵproperty(\"id\", ctx_r1.panelId)(\"@transformMenu\", ctx_r1._panelAnimationState);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaLabel || null)(\"aria-labelledby\", ctx_r1.ariaLabelledby || null)(\"aria-describedby\", ctx_r1.ariaDescribedby || null);\n  }\n}\nconst MAT_MENU_PANEL = new InjectionToken('MAT_MENU_PANEL');\n\n/**\n * Single item inside a `mat-menu`. Provides the menu item styling and accessibility treatment.\n */\nclass MatMenuItem {\n  constructor(_elementRef, _document, _focusMonitor, _parentMenu, _changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._document = _document;\n    this._focusMonitor = _focusMonitor;\n    this._parentMenu = _parentMenu;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** ARIA role for the menu item. */\n    this.role = 'menuitem';\n    /** Whether the menu item is disabled. */\n    this.disabled = false;\n    /** Whether ripples are disabled on the menu item. */\n    this.disableRipple = false;\n    /** Stream that emits when the menu item is hovered. */\n    this._hovered = new Subject();\n    /** Stream that emits when the menu item is focused. */\n    this._focused = new Subject();\n    /** Whether the menu item is highlighted. */\n    this._highlighted = false;\n    /** Whether the menu item acts as a trigger for a sub-menu. */\n    this._triggersSubmenu = false;\n    _parentMenu?.addItem?.(this);\n  }\n  /** Focuses the menu item. */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._getHostElement(), origin, options);\n    } else {\n      this._getHostElement().focus(options);\n    }\n    this._focused.next(this);\n  }\n  ngAfterViewInit() {\n    if (this._focusMonitor) {\n      // Start monitoring the element, so it gets the appropriate focused classes. We want\n      // to show the focus style for menu items only when the focus was not caused by a\n      // mouse or touch interaction.\n      this._focusMonitor.monitor(this._elementRef, false);\n    }\n  }\n  ngOnDestroy() {\n    if (this._focusMonitor) {\n      this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    if (this._parentMenu && this._parentMenu.removeItem) {\n      this._parentMenu.removeItem(this);\n    }\n    this._hovered.complete();\n    this._focused.complete();\n  }\n  /** Used to set the `tabindex`. */\n  _getTabIndex() {\n    return this.disabled ? '-1' : '0';\n  }\n  /** Returns the host DOM element. */\n  _getHostElement() {\n    return this._elementRef.nativeElement;\n  }\n  /** Prevents the default element actions if it is disabled. */\n  _checkDisabled(event) {\n    if (this.disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  }\n  /** Emits to the hover stream. */\n  _handleMouseEnter() {\n    this._hovered.next(this);\n  }\n  /** Gets the label to be used when determining whether the option should be focused. */\n  getLabel() {\n    const clone = this._elementRef.nativeElement.cloneNode(true);\n    const icons = clone.querySelectorAll('mat-icon, .material-icons');\n    // Strip away icons, so they don't show up in the text.\n    for (let i = 0; i < icons.length; i++) {\n      icons[i].remove();\n    }\n    return clone.textContent?.trim() || '';\n  }\n  _setHighlighted(isHighlighted) {\n    // We need to mark this for check for the case where the content is coming from a\n    // `matMenuContent` whose change detection tree is at the declaration position,\n    // not the insertion position. See #23175.\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._highlighted = isHighlighted;\n    this._changeDetectorRef?.markForCheck();\n  }\n  _setTriggersSubmenu(triggersSubmenu) {\n    // @breaking-change 12.0.0 Remove null check for `_changeDetectorRef`.\n    this._triggersSubmenu = triggersSubmenu;\n    this._changeDetectorRef?.markForCheck();\n  }\n  _hasFocus() {\n    return this._document && this._document.activeElement === this._getHostElement();\n  }\n  static {\n    this.ɵfac = function MatMenuItem_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuItem)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatMenuItem,\n      selectors: [[\"\", \"mat-menu-item\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-menu-item\", \"mat-mdc-focus-indicator\"],\n      hostVars: 8,\n      hostBindings: function MatMenuItem_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatMenuItem_click_HostBindingHandler($event) {\n            return ctx._checkDisabled($event);\n          })(\"mouseenter\", function MatMenuItem_mouseenter_HostBindingHandler() {\n            return ctx._handleMouseEnter();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx._getTabIndex())(\"aria-disabled\", ctx.disabled)(\"disabled\", ctx.disabled || null);\n          i0.ɵɵclassProp(\"mat-mdc-menu-item-highlighted\", ctx._highlighted)(\"mat-mdc-menu-item-submenu-trigger\", ctx._triggersSubmenu);\n        }\n      },\n      inputs: {\n        role: \"role\",\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n        disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute]\n      },\n      exportAs: [\"matMenuItem\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      ngContentSelectors: _c2,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"mat-mdc-menu-item-text\"], [\"matRipple\", \"\", 1, \"mat-mdc-menu-ripple\", 3, \"matRippleDisabled\", \"matRippleTrigger\"], [\"viewBox\", \"0 0 5 10\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mat-mdc-menu-submenu-icon\"], [\"points\", \"0,0 5,5 0,10\"]],\n      template: function MatMenuItem_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"span\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"div\", 1);\n          i0.ɵɵtemplate(4, MatMenuItem_Conditional_4_Template, 2, 0, \":svg:svg\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleTrigger\", ctx._getHostElement());\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx._triggersSubmenu ? 4 : -1);\n        }\n      },\n      dependencies: [MatRipple],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuItem, [{\n    type: Component,\n    args: [{\n      selector: '[mat-menu-item]',\n      exportAs: 'matMenuItem',\n      host: {\n        '[attr.role]': 'role',\n        'class': 'mat-mdc-menu-item mat-mdc-focus-indicator',\n        '[class.mat-mdc-menu-item-highlighted]': '_highlighted',\n        '[class.mat-mdc-menu-item-submenu-trigger]': '_triggersSubmenu',\n        '[attr.tabindex]': '_getTabIndex()',\n        '[attr.aria-disabled]': 'disabled',\n        '[attr.disabled]': 'disabled || null',\n        '(click)': '_checkDisabled($event)',\n        '(mouseenter)': '_handleMouseEnter()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      imports: [MatRipple],\n      template: \"<ng-content select=\\\"mat-icon, [matMenuItemIcon]\\\"></ng-content>\\n<span class=\\\"mat-mdc-menu-item-text\\\"><ng-content></ng-content></span>\\n<div class=\\\"mat-mdc-menu-ripple\\\" matRipple\\n     [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n     [matRippleTrigger]=\\\"_getHostElement()\\\">\\n</div>\\n\\n@if (_triggersSubmenu) {\\n     <svg\\n       class=\\\"mat-mdc-menu-submenu-icon\\\"\\n       viewBox=\\\"0 0 5 10\\\"\\n       focusable=\\\"false\\\"\\n       aria-hidden=\\\"true\\\"><polygon points=\\\"0,0 5,5 0,10\\\"/></svg>\\n}\\n\"\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_MENU_PANEL]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    role: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Throws an exception for the case when menu's x-position value isn't valid.\n * In other words, it doesn't match 'before' or 'after'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionX() {\n  throw Error(`xPosition value must be either 'before' or after'.\n      Example: <mat-menu xPosition=\"before\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when menu's y-position value isn't valid.\n * In other words, it doesn't match 'above' or 'below'.\n * @docs-private\n */\nfunction throwMatMenuInvalidPositionY() {\n  throw Error(`yPosition value must be either 'above' or below'.\n      Example: <mat-menu yPosition=\"above\" #menu=\"matMenu\"></mat-menu>`);\n}\n/**\n * Throws an exception for the case when a menu is assigned\n * to a trigger that is placed inside the same menu.\n * @docs-private\n */\nfunction throwMatMenuRecursiveError() {\n  throw Error(`matMenuTriggerFor: menu cannot contain its own trigger. Assign a menu that is ` + `not a parent of the trigger or move the trigger outside of the menu.`);\n}\n\n/**\n * Injection token that can be used to reference instances of `MatMenuContent`. It serves\n * as alternative token to the actual `MatMenuContent` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_MENU_CONTENT = new InjectionToken('MatMenuContent');\n/** Menu content that will be rendered lazily once the menu is opened. */\nclass MatMenuContent {\n  constructor(_template, _componentFactoryResolver, _appRef, _injector, _viewContainerRef, _document, _changeDetectorRef) {\n    this._template = _template;\n    this._componentFactoryResolver = _componentFactoryResolver;\n    this._appRef = _appRef;\n    this._injector = _injector;\n    this._viewContainerRef = _viewContainerRef;\n    this._document = _document;\n    this._changeDetectorRef = _changeDetectorRef;\n    /** Emits when the menu content has been attached. */\n    this._attached = new Subject();\n  }\n  /**\n   * Attaches the content with a particular context.\n   * @docs-private\n   */\n  attach(context = {}) {\n    if (!this._portal) {\n      this._portal = new TemplatePortal(this._template, this._viewContainerRef);\n    }\n    this.detach();\n    if (!this._outlet) {\n      this._outlet = new DomPortalOutlet(this._document.createElement('div'), this._componentFactoryResolver, this._appRef, this._injector);\n    }\n    const element = this._template.elementRef.nativeElement;\n    // Because we support opening the same menu from different triggers (which in turn have their\n    // own `OverlayRef` panel), we have to re-insert the host element every time, otherwise we\n    // risk it staying attached to a pane that's no longer in the DOM.\n    element.parentNode.insertBefore(this._outlet.outletElement, element);\n    // When `MatMenuContent` is used in an `OnPush` component, the insertion of the menu\n    // content via `createEmbeddedView` does not cause the content to be seen as \"dirty\"\n    // by Angular. This causes the `@ContentChildren` for menu items within the menu to\n    // not be updated by Angular. By explicitly marking for check here, we tell Angular that\n    // it needs to check for new menu items and update the `@ContentChild` in `MatMenu`.\n    // @breaking-change 9.0.0 Make change detector ref required\n    this._changeDetectorRef?.markForCheck();\n    this._portal.attach(this._outlet, context);\n    this._attached.next();\n  }\n  /**\n   * Detaches the content.\n   * @docs-private\n   */\n  detach() {\n    if (this._portal.isAttached) {\n      this._portal.detach();\n    }\n  }\n  ngOnDestroy() {\n    if (this._outlet) {\n      this._outlet.dispose();\n    }\n  }\n  static {\n    this.ɵfac = function MatMenuContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuContent)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ApplicationRef), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatMenuContent,\n      selectors: [[\"ng-template\", \"matMenuContent\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }])]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matMenuContent]',\n      providers: [{\n        provide: MAT_MENU_CONTENT,\n        useExisting: MatMenuContent\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: i0.ApplicationRef\n  }, {\n    type: i0.Injector\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\n\n/**\n * Animations used by the mat-menu component.\n * Animation duration and timing values are based on:\n * https://material.io/guidelines/components/menus.html#menus-usage\n * @docs-private\n */\nconst matMenuAnimations = {\n  /**\n   * This animation controls the menu panel's entry and exit from the page.\n   *\n   * When the menu panel is added to the DOM, it scales in and fades in its border.\n   *\n   * When the menu panel is removed from the DOM, it simply fades out after a brief\n   * delay to display the ripple.\n   */\n  transformMenu: trigger('transformMenu', [state('void', style({\n    opacity: 0,\n    transform: 'scale(0.8)'\n  })), transition('void => enter', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n    opacity: 1,\n    transform: 'scale(1)'\n  }))), transition('* => void', animate('100ms 25ms linear', style({\n    opacity: 0\n  })))]),\n  /**\n   * This animation fades in the background color and content of the menu panel\n   * after its containing element is scaled in.\n   */\n  fadeInItems: trigger('fadeInItems', [\n  // TODO(crisbeto): this is inside the `transformMenu`\n  // now. Remove next time we do breaking changes.\n  state('showing', style({\n    opacity: 1\n  })), transition('void => *', [style({\n    opacity: 0\n  }), animate('400ms 100ms cubic-bezier(0.55, 0, 0.55, 0.2)')])])\n};\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst fadeInItems = matMenuAnimations.fadeInItems;\n/**\n * @deprecated\n * @breaking-change 8.0.0\n * @docs-private\n */\nconst transformMenu = matMenuAnimations.transformMenu;\nlet menuPanelUid = 0;\n/** Injection token to be used to override the default options for `mat-menu`. */\nconst MAT_MENU_DEFAULT_OPTIONS = new InjectionToken('mat-menu-default-options', {\n  providedIn: 'root',\n  factory: MAT_MENU_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_MENU_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    overlapTrigger: false,\n    xPosition: 'after',\n    yPosition: 'below',\n    backdropClass: 'cdk-overlay-transparent-backdrop'\n  };\n}\nclass MatMenu {\n  /** Position of the menu in the X axis. */\n  get xPosition() {\n    return this._xPosition;\n  }\n  set xPosition(value) {\n    if (value !== 'before' && value !== 'after' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionX();\n    }\n    this._xPosition = value;\n    this.setPositionClasses();\n  }\n  /** Position of the menu in the Y axis. */\n  get yPosition() {\n    return this._yPosition;\n  }\n  set yPosition(value) {\n    if (value !== 'above' && value !== 'below' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwMatMenuInvalidPositionY();\n    }\n    this._yPosition = value;\n    this.setPositionClasses();\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @param classes list of class names\n   */\n  set panelClass(classes) {\n    const previousPanelClass = this._previousPanelClass;\n    const newClassList = {\n      ...this._classList\n    };\n    if (previousPanelClass && previousPanelClass.length) {\n      previousPanelClass.split(' ').forEach(className => {\n        newClassList[className] = false;\n      });\n    }\n    this._previousPanelClass = classes;\n    if (classes && classes.length) {\n      classes.split(' ').forEach(className => {\n        newClassList[className] = true;\n      });\n      this._elementRef.nativeElement.className = '';\n    }\n    this._classList = newClassList;\n  }\n  /**\n   * This method takes classes set on the host mat-menu element and applies them on the\n   * menu template that displays in the overlay container.  Otherwise, it's difficult\n   * to style the containing menu from outside the component.\n   * @deprecated Use `panelClass` instead.\n   * @breaking-change 8.0.0\n   */\n  get classList() {\n    return this.panelClass;\n  }\n  set classList(classes) {\n    this.panelClass = classes;\n  }\n  constructor(_elementRef,\n  /**\n   * @deprecated Unused param, will be removed.\n   * @breaking-change 19.0.0\n   */\n  _unusedNgZone, defaultOptions,\n  // @breaking-change 15.0.0 `_changeDetectorRef` to become a required parameter.\n  _changeDetectorRef) {\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elevationPrefix = 'mat-elevation-z';\n    this._baseElevation = null;\n    /** Only the direct descendant menu items. */\n    this._directDescendantItems = new QueryList();\n    /** Classes to be applied to the menu panel. */\n    this._classList = {};\n    /** Current state of the panel animation. */\n    this._panelAnimationState = 'void';\n    /** Emits whenever an animation on the menu completes. */\n    this._animationDone = new Subject();\n    /** Event emitted when the menu is closed. */\n    this.closed = new EventEmitter();\n    /**\n     * Event emitted when the menu is closed.\n     * @deprecated Switch to `closed` instead\n     * @breaking-change 8.0.0\n     */\n    this.close = this.closed;\n    this.panelId = `mat-menu-panel-${menuPanelUid++}`;\n    this._injector = inject(Injector);\n    this.overlayPanelClass = defaultOptions.overlayPanelClass || '';\n    this._xPosition = defaultOptions.xPosition;\n    this._yPosition = defaultOptions.yPosition;\n    this.backdropClass = defaultOptions.backdropClass;\n    this.overlapTrigger = defaultOptions.overlapTrigger;\n    this.hasBackdrop = defaultOptions.hasBackdrop;\n  }\n  ngOnInit() {\n    this.setPositionClasses();\n  }\n  ngAfterContentInit() {\n    this._updateDirectDescendants();\n    this._keyManager = new FocusKeyManager(this._directDescendantItems).withWrap().withTypeAhead().withHomeAndEnd();\n    this._keyManager.tabOut.subscribe(() => this.closed.emit('tab'));\n    // If a user manually (programmatically) focuses a menu item, we need to reflect that focus\n    // change back to the key manager. Note that we don't need to unsubscribe here because _focused\n    // is internal and we know that it gets completed on destroy.\n    this._directDescendantItems.changes.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._focused)))).subscribe(focusedItem => this._keyManager.updateActiveItem(focusedItem));\n    this._directDescendantItems.changes.subscribe(itemsList => {\n      // Move focus to another item, if the active item is removed from the list.\n      // We need to debounce the callback, because multiple items might be removed\n      // in quick succession.\n      const manager = this._keyManager;\n      if (this._panelAnimationState === 'enter' && manager.activeItem?._hasFocus()) {\n        const items = itemsList.toArray();\n        const index = Math.max(0, Math.min(items.length - 1, manager.activeItemIndex || 0));\n        if (items[index] && !items[index].disabled) {\n          manager.setActiveItem(index);\n        } else {\n          manager.setNextItemActive();\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._directDescendantItems.destroy();\n    this.closed.complete();\n    this._firstItemFocusRef?.destroy();\n  }\n  /** Stream that emits whenever the hovered menu item changes. */\n  _hovered() {\n    // Coerce the `changes` property because Angular types it as `Observable<any>`\n    const itemChanges = this._directDescendantItems.changes;\n    return itemChanges.pipe(startWith(this._directDescendantItems), switchMap(items => merge(...items.map(item => item._hovered))));\n  }\n  /*\n   * Registers a menu item with the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  addItem(_item) {}\n  /**\n   * Removes an item from the menu.\n   * @docs-private\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 9.0.0\n   */\n  removeItem(_item) {}\n  /** Handle a keyboard event from the menu, delegating to the appropriate action. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n    switch (keyCode) {\n      case ESCAPE:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this.closed.emit('keydown');\n        }\n        break;\n      case LEFT_ARROW:\n        if (this.parentMenu && this.direction === 'ltr') {\n          this.closed.emit('keydown');\n        }\n        break;\n      case RIGHT_ARROW:\n        if (this.parentMenu && this.direction === 'rtl') {\n          this.closed.emit('keydown');\n        }\n        break;\n      default:\n        if (keyCode === UP_ARROW || keyCode === DOWN_ARROW) {\n          manager.setFocusOrigin('keyboard');\n        }\n        manager.onKeydown(event);\n        return;\n    }\n    // Don't allow the event to propagate if we've already handled it, or it may\n    // end up reaching other overlays that were opened earlier (see #22694).\n    event.stopPropagation();\n  }\n  /**\n   * Focus the first item in the menu.\n   * @param origin Action from which the focus originated. Used to set the correct styling.\n   */\n  focusFirstItem(origin = 'program') {\n    // Wait for `afterNextRender` to ensure iOS VoiceOver screen reader focuses the first item (#24735).\n    this._firstItemFocusRef?.destroy();\n    this._firstItemFocusRef = afterNextRender(() => {\n      let menuPanel = null;\n      if (this._directDescendantItems.length) {\n        // Because the `mat-menuPanel` is at the DOM insertion point, not inside the overlay, we don't\n        // have a nice way of getting a hold of the menuPanel panel. We can't use a `ViewChild` either\n        // because the panel is inside an `ng-template`. We work around it by starting from one of\n        // the items and walking up the DOM.\n        menuPanel = this._directDescendantItems.first._getHostElement().closest('[role=\"menu\"]');\n      }\n      // If an item in the menuPanel is already focused, avoid overriding the focus.\n      if (!menuPanel || !menuPanel.contains(document.activeElement)) {\n        const manager = this._keyManager;\n        manager.setFocusOrigin(origin).setFirstItemActive();\n        // If there's no active item at this point, it means that all the items are disabled.\n        // Move focus to the menuPanel panel so keyboard events like Escape still work. Also this will\n        // give _some_ feedback to screen readers.\n        if (!manager.activeItem && menuPanel) {\n          menuPanel.focus();\n        }\n      }\n    }, {\n      injector: this._injector\n    });\n  }\n  /**\n   * Resets the active item in the menu. This is used when the menu is opened, allowing\n   * the user to start from the first option when pressing the down arrow.\n   */\n  resetActiveItem() {\n    this._keyManager.setActiveItem(-1);\n  }\n  /**\n   * Sets the menu panel elevation.\n   * @param depth Number of parent menus that come before the menu.\n   */\n  setElevation(depth) {\n    // The base elevation depends on which version of the spec\n    // we're running so we have to resolve it at runtime.\n    if (this._baseElevation === null) {\n      const styles = typeof getComputedStyle === 'function' ? getComputedStyle(this._elementRef.nativeElement) : null;\n      const value = styles?.getPropertyValue('--mat-menu-base-elevation-level') || '8';\n      this._baseElevation = parseInt(value);\n    }\n    // The elevation starts at the base and increases by one for each level.\n    // Capped at 24 because that's the maximum elevation defined in the Material design spec.\n    const elevation = Math.min(this._baseElevation + depth, 24);\n    const newElevation = `${this._elevationPrefix}${elevation}`;\n    const customElevation = Object.keys(this._classList).find(className => {\n      return className.startsWith(this._elevationPrefix);\n    });\n    if (!customElevation || customElevation === this._previousElevation) {\n      const newClassList = {\n        ...this._classList\n      };\n      if (this._previousElevation) {\n        newClassList[this._previousElevation] = false;\n      }\n      newClassList[newElevation] = true;\n      this._previousElevation = newElevation;\n      this._classList = newClassList;\n    }\n  }\n  /**\n   * Adds classes to the menu panel based on its position. Can be used by\n   * consumers to add specific styling based on the position.\n   * @param posX Position of the menu along the x axis.\n   * @param posY Position of the menu along the y axis.\n   * @docs-private\n   */\n  setPositionClasses(posX = this.xPosition, posY = this.yPosition) {\n    this._classList = {\n      ...this._classList,\n      ['mat-menu-before']: posX === 'before',\n      ['mat-menu-after']: posX === 'after',\n      ['mat-menu-above']: posY === 'above',\n      ['mat-menu-below']: posY === 'below'\n    };\n    // @breaking-change 15.0.0 Remove null check for `_changeDetectorRef`.\n    this._changeDetectorRef?.markForCheck();\n  }\n  /** Starts the enter animation. */\n  _startAnimation() {\n    // @breaking-change 8.0.0 Combine with _resetAnimation.\n    this._panelAnimationState = 'enter';\n  }\n  /** Resets the panel animation to its initial state. */\n  _resetAnimation() {\n    // @breaking-change 8.0.0 Combine with _startAnimation.\n    this._panelAnimationState = 'void';\n  }\n  /** Callback that is invoked when the panel animation completes. */\n  _onAnimationDone(event) {\n    this._animationDone.next(event);\n    this._isAnimating = false;\n  }\n  _onAnimationStart(event) {\n    this._isAnimating = true;\n    // Scroll the content element to the top as soon as the animation starts. This is necessary,\n    // because we move focus to the first item while it's still being animated, which can throw\n    // the browser off when it determines the scroll position. Alternatively we can move focus\n    // when the animation is done, however moving focus asynchronously will interrupt screen\n    // readers which are in the process of reading out the menu already. We take the `element`\n    // from the `event` since we can't use a `ViewChild` to access the pane.\n    if (event.toState === 'enter' && this._keyManager.activeItemIndex === 0) {\n      event.element.scrollTop = 0;\n    }\n  }\n  /**\n   * Sets up a stream that will keep track of any newly-added menu items and will update the list\n   * of direct descendants. We collect the descendants this way, because `_allItems` can include\n   * items that are part of child menus, and using a custom way of registering items is unreliable\n   * when it comes to maintaining the item order.\n   */\n  _updateDirectDescendants() {\n    this._allItems.changes.pipe(startWith(this._allItems)).subscribe(items => {\n      this._directDescendantItems.reset(items.filter(item => item._parentMenu === this));\n      this._directDescendantItems.notifyOnChanges();\n    });\n  }\n  static {\n    this.ɵfac = function MatMenu_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenu)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_MENU_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatMenu,\n      selectors: [[\"mat-menu\"]],\n      contentQueries: function MatMenu_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_MENU_CONTENT, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 5);\n          i0.ɵɵcontentQuery(dirIndex, MatMenuItem, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lazyContent = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allItems = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.items = _t);\n        }\n      },\n      viewQuery: function MatMenu_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n        }\n      },\n      hostVars: 3,\n      hostBindings: function MatMenu_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-label\", null)(\"aria-labelledby\", null)(\"aria-describedby\", null);\n        }\n      },\n      inputs: {\n        backdropClass: \"backdropClass\",\n        ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [0, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [0, \"aria-describedby\", \"ariaDescribedby\"],\n        xPosition: \"xPosition\",\n        yPosition: \"yPosition\",\n        overlapTrigger: [2, \"overlapTrigger\", \"overlapTrigger\", booleanAttribute],\n        hasBackdrop: [2, \"hasBackdrop\", \"hasBackdrop\", value => value == null ? null : booleanAttribute(value)],\n        panelClass: [0, \"class\", \"panelClass\"],\n        classList: \"classList\"\n      },\n      outputs: {\n        closed: \"closed\",\n        close: \"close\"\n      },\n      exportAs: [\"matMenu\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c3,\n      decls: 1,\n      vars: 0,\n      consts: [[\"tabindex\", \"-1\", \"role\", \"menu\", 1, \"mat-mdc-menu-panel\", \"mat-mdc-elevation-specific\", 3, \"keydown\", \"click\", \"id\"], [1, \"mat-mdc-menu-content\"]],\n      template: function MatMenu_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, MatMenu_ng_template_0_Template, 3, 7, \"ng-template\");\n        }\n      },\n      styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-app-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-app-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-app-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-app-label-large-weight))}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape, var(--mat-app-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-app-surface-container));will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.mat-mdc-menu-panel.ng-animating:has(.mat-mdc-menu-content:empty){display:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-app-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing);margin-top:var(--mat-menu-divider-top-spacing)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:var(--mat-menu-item-leading-spacing);padding-right:var(--mat-menu-item-trailing-spacing);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px}[dir=rtl] .mat-mdc-menu-item{padding-right:var(--mat-menu-item-leading-spacing);padding-left:var(--mat-menu-item-trailing-spacing)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing);padding-right:var(--mat-menu-item-with-icon-trailing-spacing)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-right:var(--mat-menu-item-with-icon-leading-spacing);padding-left:var(--mat-menu-item-with-icon-trailing-spacing)}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-app-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-app-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing);height:var(--mat-menu-item-icon-size);width:var(--mat-menu-item-icon-size)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenu, [{\n    type: Component,\n    args: [{\n      selector: 'mat-menu',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      exportAs: 'matMenu',\n      host: {\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[attr.aria-describedby]': 'null'\n      },\n      animations: [matMenuAnimations.transformMenu, matMenuAnimations.fadeInItems],\n      providers: [{\n        provide: MAT_MENU_PANEL,\n        useExisting: MatMenu\n      }],\n      standalone: true,\n      template: \"<ng-template>\\n  <div\\n    class=\\\"mat-mdc-menu-panel mat-mdc-elevation-specific\\\"\\n    [id]=\\\"panelId\\\"\\n    [class]=\\\"_classList\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\"\\n    (click)=\\\"closed.emit('click')\\\"\\n    [@transformMenu]=\\\"_panelAnimationState\\\"\\n    (@transformMenu.start)=\\\"_onAnimationStart($event)\\\"\\n    (@transformMenu.done)=\\\"_onAnimationDone($event)\\\"\\n    tabindex=\\\"-1\\\"\\n    role=\\\"menu\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"ariaLabelledby || null\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby || null\\\">\\n    <div class=\\\"mat-mdc-menu-content\\\">\\n      <ng-content></ng-content>\\n    </div>\\n  </div>\\n</ng-template>\\n\",\n      styles: [\"mat-menu{display:none}.mat-mdc-menu-content{margin:0;padding:8px 0;outline:0}.mat-mdc-menu-content,.mat-mdc-menu-content .mat-mdc-menu-item .mat-mdc-menu-item-text{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;flex:1;white-space:normal;font-family:var(--mat-menu-item-label-text-font, var(--mat-app-label-large-font));line-height:var(--mat-menu-item-label-text-line-height, var(--mat-app-label-large-line-height));font-size:var(--mat-menu-item-label-text-size, var(--mat-app-label-large-size));letter-spacing:var(--mat-menu-item-label-text-tracking, var(--mat-app-label-large-tracking));font-weight:var(--mat-menu-item-label-text-weight, var(--mat-app-label-large-weight))}.mat-mdc-menu-panel{min-width:112px;max-width:280px;overflow:auto;-webkit-overflow-scrolling:touch;box-sizing:border-box;outline:0;border-radius:var(--mat-menu-container-shape, var(--mat-app-corner-extra-small));background-color:var(--mat-menu-container-color, var(--mat-app-surface-container));will-change:transform,opacity}.mat-mdc-menu-panel.ng-animating{pointer-events:none}.mat-mdc-menu-panel.ng-animating:has(.mat-mdc-menu-content:empty){display:none}.cdk-high-contrast-active .mat-mdc-menu-panel{outline:solid 1px}.mat-mdc-menu-panel .mat-divider{color:var(--mat-menu-divider-color, var(--mat-app-surface-variant));margin-bottom:var(--mat-menu-divider-bottom-spacing);margin-top:var(--mat-menu-divider-top-spacing)}.mat-mdc-menu-item{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:var(--mat-menu-item-leading-spacing);padding-right:var(--mat-menu-item-trailing-spacing);-webkit-user-select:none;user-select:none;cursor:pointer;outline:none;border:none;-webkit-tap-highlight-color:rgba(0,0,0,0);cursor:pointer;width:100%;text-align:left;box-sizing:border-box;color:inherit;font-size:inherit;background:none;text-decoration:none;margin:0;min-height:48px}[dir=rtl] .mat-mdc-menu-item{padding-right:var(--mat-menu-item-leading-spacing);padding-left:var(--mat-menu-item-trailing-spacing)}.mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-left:var(--mat-menu-item-with-icon-leading-spacing);padding-right:var(--mat-menu-item-with-icon-trailing-spacing)}[dir=rtl] .mat-mdc-menu-item:has(.material-icons,mat-icon,[matButtonIcon]){padding-right:var(--mat-menu-item-with-icon-leading-spacing);padding-left:var(--mat-menu-item-with-icon-trailing-spacing)}.mat-mdc-menu-item::-moz-focus-inner{border:0}.mat-mdc-menu-item,.mat-mdc-menu-item:visited,.mat-mdc-menu-item:link{color:var(--mat-menu-item-label-text-color, var(--mat-app-on-surface))}.mat-mdc-menu-item .mat-icon-no-color,.mat-mdc-menu-item .mat-mdc-menu-submenu-icon{color:var(--mat-menu-item-icon-color, var(--mat-app-on-surface-variant))}.mat-mdc-menu-item[disabled]{cursor:default;opacity:.38}.mat-mdc-menu-item[disabled]::after{display:block;position:absolute;content:\\\"\\\";top:0;left:0;bottom:0;right:0}.mat-mdc-menu-item:focus{outline:0}.mat-mdc-menu-item .mat-icon{flex-shrink:0;margin-right:var(--mat-menu-item-spacing);height:var(--mat-menu-item-icon-size);width:var(--mat-menu-item-icon-size)}[dir=rtl] .mat-mdc-menu-item{text-align:right}[dir=rtl] .mat-mdc-menu-item .mat-icon{margin-right:0;margin-left:var(--mat-menu-item-spacing)}.mat-mdc-menu-item:not([disabled]):hover{background-color:var(--mat-menu-item-hover-state-layer-color)}.mat-mdc-menu-item:not([disabled]).cdk-program-focused,.mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused,.mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted{background-color:var(--mat-menu-item-focus-state-layer-color)}.cdk-high-contrast-active .mat-mdc-menu-item{margin-top:1px}.mat-mdc-menu-submenu-icon{width:var(--mat-menu-item-icon-size);height:10px;fill:currentColor;padding-left:var(--mat-menu-item-spacing)}[dir=rtl] .mat-mdc-menu-submenu-icon{padding-right:var(--mat-menu-item-spacing);padding-left:0}[dir=rtl] .mat-mdc-menu-submenu-icon polygon{transform:scaleX(-1);transform-origin:center}.cdk-high-contrast-active .mat-mdc-menu-submenu-icon{fill:CanvasText}.mat-mdc-menu-item .mat-mdc-menu-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_MENU_DEFAULT_OPTIONS]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    _allItems: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: true\n      }]\n    }],\n    backdropClass: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    xPosition: [{\n      type: Input\n    }],\n    yPosition: [{\n      type: Input\n    }],\n    templateRef: [{\n      type: ViewChild,\n      args: [TemplateRef]\n    }],\n    items: [{\n      type: ContentChildren,\n      args: [MatMenuItem, {\n        descendants: false\n      }]\n    }],\n    lazyContent: [{\n      type: ContentChild,\n      args: [MAT_MENU_CONTENT]\n    }],\n    overlapTrigger: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hasBackdrop: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? null : booleanAttribute(value)\n      }]\n    }],\n    panelClass: [{\n      type: Input,\n      args: ['class']\n    }],\n    classList: [{\n      type: Input\n    }],\n    closed: [{\n      type: Output\n    }],\n    close: [{\n      type: Output\n    }]\n  });\n})();\n\n/** Injection token that determines the scroll handling while the menu is open. */\nconst MAT_MENU_SCROLL_STRATEGY = new InjectionToken('mat-menu-scroll-strategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.reposition();\n  }\n});\n/** @docs-private */\nfunction MAT_MENU_SCROLL_STRATEGY_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.reposition();\n}\n/** @docs-private */\nconst MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_MENU_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: MAT_MENU_SCROLL_STRATEGY_FACTORY\n};\n/** Options for binding a passive event listener. */\nconst passiveEventListenerOptions = normalizePassiveListenerOptions({\n  passive: true\n});\n/**\n * Default top padding of the menu panel.\n * @deprecated No longer being used. Will be removed.\n * @breaking-change 15.0.0\n */\nconst MENU_PANEL_TOP_PADDING = 8;\n/** Directive applied to an element that should trigger a `mat-menu`. */\nclass MatMenuTrigger {\n  /**\n   * @deprecated\n   * @breaking-change 8.0.0\n   */\n  get _deprecatedMatMenuTriggerFor() {\n    return this.menu;\n  }\n  set _deprecatedMatMenuTriggerFor(v) {\n    this.menu = v;\n  }\n  /** References the menu instance that the trigger is associated with. */\n  get menu() {\n    return this._menu;\n  }\n  set menu(menu) {\n    if (menu === this._menu) {\n      return;\n    }\n    this._menu = menu;\n    this._menuCloseSubscription.unsubscribe();\n    if (menu) {\n      if (menu === this._parentMaterialMenu && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwMatMenuRecursiveError();\n      }\n      this._menuCloseSubscription = menu.close.subscribe(reason => {\n        this._destroyMenu(reason);\n        // If a click closed the menu, we should close the entire chain of nested menus.\n        if ((reason === 'click' || reason === 'tab') && this._parentMaterialMenu) {\n          this._parentMaterialMenu.closed.emit(reason);\n        }\n      });\n    }\n    this._menuItemInstance?._setTriggersSubmenu(this.triggersSubmenu());\n  }\n  constructor(_overlay, _element, _viewContainerRef, scrollStrategy, parentMenu,\n  // `MatMenuTrigger` is commonly used in combination with a `MatMenuItem`.\n  // tslint:disable-next-line: lightweight-tokens\n  _menuItemInstance, _dir, _focusMonitor, _ngZone) {\n    this._overlay = _overlay;\n    this._element = _element;\n    this._viewContainerRef = _viewContainerRef;\n    this._menuItemInstance = _menuItemInstance;\n    this._dir = _dir;\n    this._focusMonitor = _focusMonitor;\n    this._ngZone = _ngZone;\n    this._overlayRef = null;\n    this._menuOpen = false;\n    this._closingActionsSubscription = Subscription.EMPTY;\n    this._hoverSubscription = Subscription.EMPTY;\n    this._menuCloseSubscription = Subscription.EMPTY;\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    /**\n     * Handles touch start events on the trigger.\n     * Needs to be an arrow function so we can easily use addEventListener and removeEventListener.\n     */\n    this._handleTouchStart = event => {\n      if (!isFakeTouchstartFromScreenReader(event)) {\n        this._openedBy = 'touch';\n      }\n    };\n    // Tracking input type is necessary so it's possible to only auto-focus\n    // the first item of the list when the menu is opened via the keyboard\n    this._openedBy = undefined;\n    /**\n     * Whether focus should be restored when the menu is closed.\n     * Note that disabling this option can have accessibility implications\n     * and it's up to you to manage focus, if you decide to turn it off.\n     */\n    this.restoreFocus = true;\n    /** Event emitted when the associated menu is opened. */\n    this.menuOpened = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is opened.\n     * @deprecated Switch to `menuOpened` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onMenuOpen = this.menuOpened;\n    /** Event emitted when the associated menu is closed. */\n    this.menuClosed = new EventEmitter();\n    /**\n     * Event emitted when the associated menu is closed.\n     * @deprecated Switch to `menuClosed` instead\n     * @breaking-change 8.0.0\n     */\n    // tslint:disable-next-line:no-output-on-prefix\n    this.onMenuClose = this.menuClosed;\n    this._scrollStrategy = scrollStrategy;\n    this._parentMaterialMenu = parentMenu instanceof MatMenu ? parentMenu : undefined;\n    _element.nativeElement.addEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n  }\n  ngAfterContentInit() {\n    this._handleHover();\n  }\n  ngOnDestroy() {\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._overlayRef = null;\n    }\n    this._element.nativeElement.removeEventListener('touchstart', this._handleTouchStart, passiveEventListenerOptions);\n    this._menuCloseSubscription.unsubscribe();\n    this._closingActionsSubscription.unsubscribe();\n    this._hoverSubscription.unsubscribe();\n  }\n  /** Whether the menu is open. */\n  get menuOpen() {\n    return this._menuOpen;\n  }\n  /** The text direction of the containing app. */\n  get dir() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Whether the menu triggers a sub-menu or a top-level one. */\n  triggersSubmenu() {\n    return !!(this._menuItemInstance && this._parentMaterialMenu && this.menu);\n  }\n  /** Toggles the menu between the open and closed states. */\n  toggleMenu() {\n    return this._menuOpen ? this.closeMenu() : this.openMenu();\n  }\n  /** Opens the menu. */\n  openMenu() {\n    const menu = this.menu;\n    if (this._menuOpen || !menu) {\n      return;\n    }\n    const overlayRef = this._createOverlay(menu);\n    const overlayConfig = overlayRef.getConfig();\n    const positionStrategy = overlayConfig.positionStrategy;\n    this._setPosition(menu, positionStrategy);\n    overlayConfig.hasBackdrop = menu.hasBackdrop == null ? !this.triggersSubmenu() : menu.hasBackdrop;\n    overlayRef.attach(this._getPortal(menu));\n    if (menu.lazyContent) {\n      menu.lazyContent.attach(this.menuData);\n    }\n    this._closingActionsSubscription = this._menuClosingActions().subscribe(() => this.closeMenu());\n    this._initMenu(menu);\n    if (menu instanceof MatMenu) {\n      menu._startAnimation();\n      menu._directDescendantItems.changes.pipe(takeUntil(menu.close)).subscribe(() => {\n        // Re-adjust the position without locking when the amount of items\n        // changes so that the overlay is allowed to pick a new optimal position.\n        positionStrategy.withLockedPosition(false).reapplyLastPosition();\n        positionStrategy.withLockedPosition(true);\n      });\n    }\n  }\n  /** Closes the menu. */\n  closeMenu() {\n    this.menu?.close.emit();\n  }\n  /**\n   * Focuses the menu trigger.\n   * @param origin Source of the menu trigger's focus.\n   */\n  focus(origin, options) {\n    if (this._focusMonitor && origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  /**\n   * Updates the position of the menu to ensure that it fits all options within the viewport.\n   */\n  updatePosition() {\n    this._overlayRef?.updatePosition();\n  }\n  /** Closes the menu and does the necessary cleanup. */\n  _destroyMenu(reason) {\n    if (!this._overlayRef || !this.menuOpen) {\n      return;\n    }\n    const menu = this.menu;\n    this._closingActionsSubscription.unsubscribe();\n    this._overlayRef.detach();\n    // Always restore focus if the user is navigating using the keyboard or the menu was opened\n    // programmatically. We don't restore for non-root triggers, because it can prevent focus\n    // from making it back to the root trigger when closing a long chain of menus by clicking\n    // on the backdrop.\n    if (this.restoreFocus && (reason === 'keydown' || !this._openedBy || !this.triggersSubmenu())) {\n      this.focus(this._openedBy);\n    }\n    this._openedBy = undefined;\n    if (menu instanceof MatMenu) {\n      menu._resetAnimation();\n      if (menu.lazyContent) {\n        // Wait for the exit animation to finish before detaching the content.\n        menu._animationDone.pipe(filter(event => event.toState === 'void'), take(1),\n        // Interrupt if the content got re-attached.\n        takeUntil(menu.lazyContent._attached)).subscribe({\n          next: () => menu.lazyContent.detach(),\n          // No matter whether the content got re-attached, reset the menu.\n          complete: () => this._setIsMenuOpen(false)\n        });\n      } else {\n        this._setIsMenuOpen(false);\n      }\n    } else {\n      this._setIsMenuOpen(false);\n      menu?.lazyContent?.detach();\n    }\n  }\n  /**\n   * This method sets the menu state to open and focuses the first item if\n   * the menu was opened via the keyboard.\n   */\n  _initMenu(menu) {\n    menu.parentMenu = this.triggersSubmenu() ? this._parentMaterialMenu : undefined;\n    menu.direction = this.dir;\n    this._setMenuElevation(menu);\n    menu.focusFirstItem(this._openedBy || 'program');\n    this._setIsMenuOpen(true);\n  }\n  /** Updates the menu elevation based on the amount of parent menus that it has. */\n  _setMenuElevation(menu) {\n    if (menu.setElevation) {\n      let depth = 0;\n      let parentMenu = menu.parentMenu;\n      while (parentMenu) {\n        depth++;\n        parentMenu = parentMenu.parentMenu;\n      }\n      menu.setElevation(depth);\n    }\n  }\n  // set state rather than toggle to support triggers sharing a menu\n  _setIsMenuOpen(isOpen) {\n    if (isOpen !== this._menuOpen) {\n      this._menuOpen = isOpen;\n      this._menuOpen ? this.menuOpened.emit() : this.menuClosed.emit();\n      if (this.triggersSubmenu()) {\n        this._menuItemInstance._setHighlighted(isOpen);\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * This method creates the overlay from the provided menu's template and saves its\n   * OverlayRef so that it can be attached to the DOM when openMenu is called.\n   */\n  _createOverlay(menu) {\n    if (!this._overlayRef) {\n      const config = this._getOverlayConfig(menu);\n      this._subscribeToPositions(menu, config.positionStrategy);\n      this._overlayRef = this._overlay.create(config);\n      // Consume the `keydownEvents` in order to prevent them from going to another overlay.\n      // Ideally we'd also have our keyboard event logic in here, however doing so will\n      // break anybody that may have implemented the `MatMenuPanel` themselves.\n      this._overlayRef.keydownEvents().subscribe();\n    }\n    return this._overlayRef;\n  }\n  /**\n   * This method builds the configuration object needed to create the overlay, the OverlayState.\n   * @returns OverlayConfig\n   */\n  _getOverlayConfig(menu) {\n    return new OverlayConfig({\n      positionStrategy: this._overlay.position().flexibleConnectedTo(this._element).withLockedPosition().withGrowAfterOpen().withTransformOriginOn('.mat-menu-panel, .mat-mdc-menu-panel'),\n      backdropClass: menu.backdropClass || 'cdk-overlay-transparent-backdrop',\n      panelClass: menu.overlayPanelClass,\n      scrollStrategy: this._scrollStrategy(),\n      direction: this._dir\n    });\n  }\n  /**\n   * Listens to changes in the position of the overlay and sets the correct classes\n   * on the menu based on the new position. This ensures the animation origin is always\n   * correct, even if a fallback position is used for the overlay.\n   */\n  _subscribeToPositions(menu, position) {\n    if (menu.setPositionClasses) {\n      position.positionChanges.subscribe(change => {\n        const posX = change.connectionPair.overlayX === 'start' ? 'after' : 'before';\n        const posY = change.connectionPair.overlayY === 'top' ? 'below' : 'above';\n        // @breaking-change 15.0.0 Remove null check for `ngZone`.\n        // `positionChanges` fires outside of the `ngZone` and `setPositionClasses` might be\n        // updating something in the view so we need to bring it back in.\n        if (this._ngZone) {\n          this._ngZone.run(() => menu.setPositionClasses(posX, posY));\n        } else {\n          menu.setPositionClasses(posX, posY);\n        }\n      });\n    }\n  }\n  /**\n   * Sets the appropriate positions on a position strategy\n   * so the overlay connects with the trigger correctly.\n   * @param positionStrategy Strategy whose position to update.\n   */\n  _setPosition(menu, positionStrategy) {\n    let [originX, originFallbackX] = menu.xPosition === 'before' ? ['end', 'start'] : ['start', 'end'];\n    let [overlayY, overlayFallbackY] = menu.yPosition === 'above' ? ['bottom', 'top'] : ['top', 'bottom'];\n    let [originY, originFallbackY] = [overlayY, overlayFallbackY];\n    let [overlayX, overlayFallbackX] = [originX, originFallbackX];\n    let offsetY = 0;\n    if (this.triggersSubmenu()) {\n      // When the menu is a sub-menu, it should always align itself\n      // to the edges of the trigger, instead of overlapping it.\n      overlayFallbackX = originX = menu.xPosition === 'before' ? 'start' : 'end';\n      originFallbackX = overlayX = originX === 'end' ? 'start' : 'end';\n      if (this._parentMaterialMenu) {\n        if (this._parentInnerPadding == null) {\n          const firstItem = this._parentMaterialMenu.items.first;\n          this._parentInnerPadding = firstItem ? firstItem._getHostElement().offsetTop : 0;\n        }\n        offsetY = overlayY === 'bottom' ? this._parentInnerPadding : -this._parentInnerPadding;\n      }\n    } else if (!menu.overlapTrigger) {\n      originY = overlayY === 'top' ? 'bottom' : 'top';\n      originFallbackY = overlayFallbackY === 'top' ? 'bottom' : 'top';\n    }\n    positionStrategy.withPositions([{\n      originX,\n      originY,\n      overlayX,\n      overlayY,\n      offsetY\n    }, {\n      originX: originFallbackX,\n      originY,\n      overlayX: overlayFallbackX,\n      overlayY,\n      offsetY\n    }, {\n      originX,\n      originY: originFallbackY,\n      overlayX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }, {\n      originX: originFallbackX,\n      originY: originFallbackY,\n      overlayX: overlayFallbackX,\n      overlayY: overlayFallbackY,\n      offsetY: -offsetY\n    }]);\n  }\n  /** Returns a stream that emits whenever an action that should close the menu occurs. */\n  _menuClosingActions() {\n    const backdrop = this._overlayRef.backdropClick();\n    const detachments = this._overlayRef.detachments();\n    const parentClose = this._parentMaterialMenu ? this._parentMaterialMenu.closed : of();\n    const hover = this._parentMaterialMenu ? this._parentMaterialMenu._hovered().pipe(filter(active => active !== this._menuItemInstance), filter(() => this._menuOpen)) : of();\n    return merge(backdrop, parentClose, hover, detachments);\n  }\n  /** Handles mouse presses on the trigger. */\n  _handleMousedown(event) {\n    if (!isFakeMousedownFromScreenReader(event)) {\n      // Since right or middle button clicks won't trigger the `click` event,\n      // we shouldn't consider the menu as opened by mouse in those cases.\n      this._openedBy = event.button === 0 ? 'mouse' : undefined;\n      // Since clicking on the trigger won't close the menu if it opens a sub-menu,\n      // we should prevent focus from moving onto it via click to avoid the\n      // highlight from lingering on the menu item.\n      if (this.triggersSubmenu()) {\n        event.preventDefault();\n      }\n    }\n  }\n  /** Handles key presses on the trigger. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    // Pressing enter on the trigger will trigger the click handler later.\n    if (keyCode === ENTER || keyCode === SPACE) {\n      this._openedBy = 'keyboard';\n    }\n    if (this.triggersSubmenu() && (keyCode === RIGHT_ARROW && this.dir === 'ltr' || keyCode === LEFT_ARROW && this.dir === 'rtl')) {\n      this._openedBy = 'keyboard';\n      this.openMenu();\n    }\n  }\n  /** Handles click events on the trigger. */\n  _handleClick(event) {\n    if (this.triggersSubmenu()) {\n      // Stop event propagation to avoid closing the parent menu.\n      event.stopPropagation();\n      this.openMenu();\n    } else {\n      this.toggleMenu();\n    }\n  }\n  /** Handles the cases where the user hovers over the trigger. */\n  _handleHover() {\n    // Subscribe to changes in the hovered item in order to toggle the panel.\n    if (!this.triggersSubmenu() || !this._parentMaterialMenu) {\n      return;\n    }\n    this._hoverSubscription = this._parentMaterialMenu._hovered()\n    // Since we might have multiple competing triggers for the same menu (e.g. a sub-menu\n    // with different data and triggers), we have to delay it by a tick to ensure that\n    // it won't be closed immediately after it is opened.\n    .pipe(filter(active => active === this._menuItemInstance && !active.disabled), delay(0, asapScheduler)).subscribe(() => {\n      this._openedBy = 'mouse';\n      // If the same menu is used between multiple triggers, it might still be animating\n      // while the new trigger tries to re-open it. Wait for the animation to finish\n      // before doing so. Also interrupt if the user moves to another item.\n      if (this.menu instanceof MatMenu && this.menu._isAnimating) {\n        // We need the `delay(0)` here in order to avoid\n        // 'changed after checked' errors in some cases. See #12194.\n        this.menu._animationDone.pipe(take(1), delay(0, asapScheduler), takeUntil(this._parentMaterialMenu._hovered())).subscribe(() => this.openMenu());\n      } else {\n        this.openMenu();\n      }\n    });\n  }\n  /** Gets the portal that should be attached to the overlay. */\n  _getPortal(menu) {\n    // Note that we can avoid this check by keeping the portal on the menu panel.\n    // While it would be cleaner, we'd have to introduce another required method on\n    // `MatMenuPanel`, making it harder to consume.\n    if (!this._portal || this._portal.templateRef !== menu.templateRef) {\n      this._portal = new TemplatePortal(menu.templateRef, this._viewContainerRef);\n    }\n    return this._portal;\n  }\n  static {\n    this.ɵfac = function MatMenuTrigger_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuTrigger)(i0.ɵɵdirectiveInject(i1$1.Overlay), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(MAT_MENU_SCROLL_STRATEGY), i0.ɵɵdirectiveInject(MAT_MENU_PANEL, 8), i0.ɵɵdirectiveInject(MatMenuItem, 10), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatMenuTrigger,\n      selectors: [[\"\", \"mat-menu-trigger-for\", \"\"], [\"\", \"matMenuTriggerFor\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-menu-trigger\"],\n      hostVars: 3,\n      hostBindings: function MatMenuTrigger_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatMenuTrigger_click_HostBindingHandler($event) {\n            return ctx._handleClick($event);\n          })(\"mousedown\", function MatMenuTrigger_mousedown_HostBindingHandler($event) {\n            return ctx._handleMousedown($event);\n          })(\"keydown\", function MatMenuTrigger_keydown_HostBindingHandler($event) {\n            return ctx._handleKeydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-haspopup\", ctx.menu ? \"menu\" : null)(\"aria-expanded\", ctx.menuOpen)(\"aria-controls\", ctx.menuOpen ? ctx.menu.panelId : null);\n        }\n      },\n      inputs: {\n        _deprecatedMatMenuTriggerFor: [0, \"mat-menu-trigger-for\", \"_deprecatedMatMenuTriggerFor\"],\n        menu: [0, \"matMenuTriggerFor\", \"menu\"],\n        menuData: [0, \"matMenuTriggerData\", \"menuData\"],\n        restoreFocus: [0, \"matMenuTriggerRestoreFocus\", \"restoreFocus\"]\n      },\n      outputs: {\n        menuOpened: \"menuOpened\",\n        onMenuOpen: \"onMenuOpen\",\n        menuClosed: \"menuClosed\",\n        onMenuClose: \"onMenuClose\"\n      },\n      exportAs: [\"matMenuTrigger\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuTrigger, [{\n    type: Directive,\n    args: [{\n      selector: `[mat-menu-trigger-for], [matMenuTriggerFor]`,\n      host: {\n        'class': 'mat-mdc-menu-trigger',\n        '[attr.aria-haspopup]': 'menu ? \"menu\" : null',\n        '[attr.aria-expanded]': 'menuOpen',\n        '[attr.aria-controls]': 'menuOpen ? menu.panelId : null',\n        '(click)': '_handleClick($event)',\n        '(mousedown)': '_handleMousedown($event)',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      exportAs: 'matMenuTrigger',\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$1.Overlay\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_MENU_SCROLL_STRATEGY]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_MENU_PANEL]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: MatMenuItem,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Self\n    }]\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: i0.NgZone\n  }], {\n    _deprecatedMatMenuTriggerFor: [{\n      type: Input,\n      args: ['mat-menu-trigger-for']\n    }],\n    menu: [{\n      type: Input,\n      args: ['matMenuTriggerFor']\n    }],\n    menuData: [{\n      type: Input,\n      args: ['matMenuTriggerData']\n    }],\n    restoreFocus: [{\n      type: Input,\n      args: ['matMenuTriggerRestoreFocus']\n    }],\n    menuOpened: [{\n      type: Output\n    }],\n    onMenuOpen: [{\n      type: Output\n    }],\n    menuClosed: [{\n      type: Output\n    }],\n    onMenuClose: [{\n      type: Output\n    }]\n  });\n})();\nclass MatMenuModule {\n  static {\n    this.ɵfac = function MatMenuModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatMenuModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatMenuModule,\n      imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule, MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      exports: [CdkScrollableModule, MatMenu, MatCommonModule, MatMenuItem, MatMenuContent, MatMenuTrigger]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER],\n      imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule, CdkScrollableModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatMenuModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, MatRippleModule, MatCommonModule, OverlayModule, MatMenu, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      exports: [CdkScrollableModule, MatMenu, MatCommonModule, MatMenuItem, MatMenuContent, MatMenuTrigger],\n      providers: [MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_MENU_CONTENT, MAT_MENU_DEFAULT_OPTIONS, MAT_MENU_PANEL, MAT_MENU_SCROLL_STRATEGY, MAT_MENU_SCROLL_STRATEGY_FACTORY_PROVIDER, MENU_PANEL_TOP_PADDING, MatMenu, MatMenuContent, MatMenuItem, MatMenuModule, MatMenuTrigger, fadeInItems, matMenuAnimations, transformMenu };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAM,MAAM,CAAC,iBAAiB,EAAE;AAChC,IAAM,MAAM,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC,GAAG,GAAG;AAC7D,IAAM,MAAM,CAAC,+BAA+B,GAAG;AAC/C,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,WAAW,CAAC;AAC5B,IAAG,aAAa;AAAA,EAClB;AACF;AACA,IAAM,MAAM,CAAC,GAAG;AAChB,SAAS,+BAA+B,IAAI,KAAK;AAC/C,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,WAAW,SAAS,sDAAsD,QAAQ;AAC9F,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,eAAe,MAAM,CAAC;AAAA,IACrD,CAAC,EAAE,SAAS,SAAS,sDAAsD;AACzE,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,KAAK,OAAO,CAAC;AAAA,IACnD,CAAC,EAAE,wBAAwB,SAAS,4EAA4E,QAAQ;AACtH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,kBAAkB,MAAM,CAAC;AAAA,IACxD,CAAC,EAAE,uBAAuB,SAAS,2EAA2E,QAAQ;AACpH,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,iBAAiB,MAAM,CAAC;AAAA,IACvD,CAAC;AACD,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,aAAa,CAAC;AACjB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,OAAO,UAAU;AAC/B,IAAG,WAAW,MAAM,OAAO,OAAO,EAAE,kBAAkB,OAAO,oBAAoB;AACjF,IAAG,YAAY,cAAc,OAAO,aAAa,IAAI,EAAE,mBAAmB,OAAO,kBAAkB,IAAI,EAAE,oBAAoB,OAAO,mBAAmB,IAAI;AAAA,EAC7J;AACF;AACA,IAAM,iBAAiB,IAAI,eAAe,gBAAgB;AAK1D,IAAM,cAAN,MAAM,aAAY;AAAA,EAChB,YAAY,aAAa,WAAW,eAAe,aAAa,oBAAoB;AAClF,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,qBAAqB;AAE1B,SAAK,OAAO;AAEZ,SAAK,WAAW;AAEhB,SAAK,gBAAgB;AAErB,SAAK,WAAW,IAAI,QAAQ;AAE5B,SAAK,WAAW,IAAI,QAAQ;AAE5B,SAAK,eAAe;AAEpB,SAAK,mBAAmB;AACxB,iBAAa,UAAU,IAAI;AAAA,EAC7B;AAAA;AAAA,EAEA,MAAM,QAAQ,SAAS;AACrB,QAAI,KAAK,iBAAiB,QAAQ;AAChC,WAAK,cAAc,SAAS,KAAK,gBAAgB,GAAG,QAAQ,OAAO;AAAA,IACrE,OAAO;AACL,WAAK,gBAAgB,EAAE,MAAM,OAAO;AAAA,IACtC;AACA,SAAK,SAAS,KAAK,IAAI;AAAA,EACzB;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,eAAe;AAItB,WAAK,cAAc,QAAQ,KAAK,aAAa,KAAK;AAAA,IACpD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,eAAe,KAAK,WAAW;AAAA,IACpD;AACA,QAAI,KAAK,eAAe,KAAK,YAAY,YAAY;AACnD,WAAK,YAAY,WAAW,IAAI;AAAA,IAClC;AACA,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA;AAAA,EAEA,eAAe;AACb,WAAO,KAAK,WAAW,OAAO;AAAA,EAChC;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,QAAI,KAAK,UAAU;AACjB,YAAM,eAAe;AACrB,YAAM,gBAAgB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA,EAEA,oBAAoB;AAClB,SAAK,SAAS,KAAK,IAAI;AAAA,EACzB;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,QAAQ,KAAK,YAAY,cAAc,UAAU,IAAI;AAC3D,UAAM,QAAQ,MAAM,iBAAiB,2BAA2B;AAEhE,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,CAAC,EAAE,OAAO;AAAA,IAClB;AACA,WAAO,MAAM,aAAa,KAAK,KAAK;AAAA,EACtC;AAAA,EACA,gBAAgB,eAAe;AAK7B,SAAK,eAAe;AACpB,SAAK,oBAAoB,aAAa;AAAA,EACxC;AAAA,EACA,oBAAoB,iBAAiB;AAEnC,SAAK,mBAAmB;AACxB,SAAK,oBAAoB,aAAa;AAAA,EACxC;AAAA,EACA,YAAY;AACV,WAAO,KAAK,aAAa,KAAK,UAAU,kBAAkB,KAAK,gBAAgB;AAAA,EACjF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oBAAoB,mBAAmB;AAC1D,aAAO,KAAK,qBAAqB,cAAgB,kBAAqB,UAAU,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,YAAY,GAAM,kBAAkB,gBAAgB,CAAC,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IAC/O;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,iBAAiB,EAAE,CAAC;AAAA,MACrC,WAAW,CAAC,GAAG,qBAAqB,yBAAyB;AAAA,MAC7D,UAAU;AAAA,MACV,cAAc,SAAS,yBAAyB,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,qCAAqC,QAAQ;AAC3E,mBAAO,IAAI,eAAe,MAAM;AAAA,UAClC,CAAC,EAAE,cAAc,SAAS,4CAA4C;AACpE,mBAAO,IAAI,kBAAkB;AAAA,UAC/B,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,QAAQ,IAAI,IAAI,EAAE,YAAY,IAAI,aAAa,CAAC,EAAE,iBAAiB,IAAI,QAAQ,EAAE,YAAY,IAAI,YAAY,IAAI;AAChI,UAAG,YAAY,iCAAiC,IAAI,YAAY,EAAE,qCAAqC,IAAI,gBAAgB;AAAA,QAC7H;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,MAAM;AAAA,QACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,QACtD,eAAe,CAAC,GAAG,iBAAiB,iBAAiB,gBAAgB;AAAA,MACvE;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,mBAAmB;AAAA,MAC9D,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,GAAG,CAAC,aAAa,IAAI,GAAG,uBAAuB,GAAG,qBAAqB,kBAAkB,GAAG,CAAC,WAAW,YAAY,aAAa,SAAS,eAAe,QAAQ,GAAG,2BAA2B,GAAG,CAAC,UAAU,cAAc,CAAC;AAAA,MACjQ,UAAU,SAAS,qBAAqB,IAAI,KAAK;AAC/C,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,GAAG;AACtB,UAAG,aAAa,CAAC;AACjB,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,aAAa,GAAG,CAAC;AACpB,UAAG,aAAa;AAChB,UAAG,UAAU,GAAG,OAAO,CAAC;AACxB,UAAG,WAAW,GAAG,oCAAoC,GAAG,GAAG,YAAY,CAAC;AAAA,QAC1E;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,qBAAqB,IAAI,iBAAiB,IAAI,QAAQ,EAAE,oBAAoB,IAAI,gBAAgB,CAAC;AAC/G,UAAG,UAAU;AACb,UAAG,cAAc,IAAI,mBAAmB,IAAI,EAAE;AAAA,QAChD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS;AAAA,MACxB,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,aAAa,CAAC;AAAA,IACpF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,eAAe;AAAA,QACf,SAAS;AAAA,QACT,yCAAyC;AAAA,QACzC,6CAA6C;AAAA,QAC7C,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,mBAAmB;AAAA,QACnB,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB;AAAA,MACA,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY;AAAA,MACZ,SAAS,CAAC,SAAS;AAAA,MACnB,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAOH,SAAS,+BAA+B;AACtC,QAAM,MAAM;AAAA,wEAC0D;AACxE;AAMA,SAAS,+BAA+B;AACtC,QAAM,MAAM;AAAA,uEACyD;AACvE;AAMA,SAAS,6BAA6B;AACpC,QAAM,MAAM,oJAAyJ;AACvK;AAOA,IAAM,mBAAmB,IAAI,eAAe,gBAAgB;AAE5D,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,YAAY,WAAW,2BAA2B,SAAS,WAAW,mBAAmB,WAAW,oBAAoB;AACtH,SAAK,YAAY;AACjB,SAAK,4BAA4B;AACjC,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAE1B,SAAK,YAAY,IAAI,QAAQ;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU,CAAC,GAAG;AACnB,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU,IAAI,eAAe,KAAK,WAAW,KAAK,iBAAiB;AAAA,IAC1E;AACA,SAAK,OAAO;AACZ,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU,IAAI,gBAAgB,KAAK,UAAU,cAAc,KAAK,GAAG,KAAK,2BAA2B,KAAK,SAAS,KAAK,SAAS;AAAA,IACtI;AACA,UAAM,UAAU,KAAK,UAAU,WAAW;AAI1C,YAAQ,WAAW,aAAa,KAAK,QAAQ,eAAe,OAAO;AAOnE,SAAK,oBAAoB,aAAa;AACtC,SAAK,QAAQ,OAAO,KAAK,SAAS,OAAO;AACzC,SAAK,UAAU,KAAK;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACP,QAAI,KAAK,QAAQ,YAAY;AAC3B,WAAK,QAAQ,OAAO;AAAA,IACtB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,SAAS;AAChB,WAAK,QAAQ,QAAQ;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,kBAAqB,WAAW,GAAM,kBAAqB,0BAAwB,GAAM,kBAAqB,cAAc,GAAM,kBAAqB,QAAQ,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,QAAQ,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IAC7U;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,kBAAkB,EAAE,CAAC;AAAA,MACjD,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,CAAC;AAAA,IACL,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAQH,IAAM,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASxB,eAAe,QAAQ,iBAAiB,CAAC,MAAM,QAAQ,MAAM;AAAA,IAC3D,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC,CAAC,GAAG,WAAW,iBAAiB,QAAQ,oCAAoC,MAAM;AAAA,IACjF,SAAS;AAAA,IACT,WAAW;AAAA,EACb,CAAC,CAAC,CAAC,GAAG,WAAW,aAAa,QAAQ,qBAAqB,MAAM;AAAA,IAC/D,SAAS;AAAA,EACX,CAAC,CAAC,CAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKL,aAAa,QAAQ,eAAe;AAAA;AAAA;AAAA,IAGpC,MAAM,WAAW,MAAM;AAAA,MACrB,SAAS;AAAA,IACX,CAAC,CAAC;AAAA,IAAG,WAAW,aAAa,CAAC,MAAM;AAAA,MAClC,SAAS;AAAA,IACX,CAAC,GAAG,QAAQ,8CAA8C,CAAC,CAAC;AAAA,EAAC,CAAC;AAChE;AAMA,IAAM,cAAc,kBAAkB;AAMtC,IAAM,gBAAgB,kBAAkB;AACxC,IAAI,eAAe;AAEnB,IAAM,2BAA2B,IAAI,eAAe,4BAA4B;AAAA,EAC9E,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAED,SAAS,mCAAmC;AAC1C,SAAO;AAAA,IACL,gBAAgB;AAAA,IAChB,WAAW;AAAA,IACX,WAAW;AAAA,IACX,eAAe;AAAA,EACjB;AACF;AACA,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA,EAEZ,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,QAAI,UAAU,YAAY,UAAU,YAAY,OAAO,cAAc,eAAe,YAAY;AAC9F,mCAA6B;AAAA,IAC/B;AACA,SAAK,aAAa;AAClB,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,QAAI,UAAU,WAAW,UAAU,YAAY,OAAO,cAAc,eAAe,YAAY;AAC7F,mCAA6B;AAAA,IAC/B;AACA,SAAK,aAAa;AAClB,SAAK,mBAAmB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,WAAW,SAAS;AACtB,UAAM,qBAAqB,KAAK;AAChC,UAAM,eAAe,mBAChB,KAAK;AAEV,QAAI,sBAAsB,mBAAmB,QAAQ;AACnD,yBAAmB,MAAM,GAAG,EAAE,QAAQ,eAAa;AACjD,qBAAa,SAAS,IAAI;AAAA,MAC5B,CAAC;AAAA,IACH;AACA,SAAK,sBAAsB;AAC3B,QAAI,WAAW,QAAQ,QAAQ;AAC7B,cAAQ,MAAM,GAAG,EAAE,QAAQ,eAAa;AACtC,qBAAa,SAAS,IAAI;AAAA,MAC5B,CAAC;AACD,WAAK,YAAY,cAAc,YAAY;AAAA,IAC7C;AACA,SAAK,aAAa;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,SAAS;AACrB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,YAAY,aAKZ,eAAe,gBAEf,oBAAoB;AAClB,SAAK,cAAc;AACnB,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AACxB,SAAK,iBAAiB;AAEtB,SAAK,yBAAyB,IAAI,UAAU;AAE5C,SAAK,aAAa,CAAC;AAEnB,SAAK,uBAAuB;AAE5B,SAAK,iBAAiB,IAAI,QAAQ;AAElC,SAAK,SAAS,IAAI,aAAa;AAM/B,SAAK,QAAQ,KAAK;AAClB,SAAK,UAAU,kBAAkB,cAAc;AAC/C,SAAK,YAAY,OAAO,QAAQ;AAChC,SAAK,oBAAoB,eAAe,qBAAqB;AAC7D,SAAK,aAAa,eAAe;AACjC,SAAK,aAAa,eAAe;AACjC,SAAK,gBAAgB,eAAe;AACpC,SAAK,iBAAiB,eAAe;AACrC,SAAK,cAAc,eAAe;AAAA,EACpC;AAAA,EACA,WAAW;AACT,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,qBAAqB;AACnB,SAAK,yBAAyB;AAC9B,SAAK,cAAc,IAAI,gBAAgB,KAAK,sBAAsB,EAAE,SAAS,EAAE,cAAc,EAAE,eAAe;AAC9G,SAAK,YAAY,OAAO,UAAU,MAAM,KAAK,OAAO,KAAK,KAAK,CAAC;AAI/D,SAAK,uBAAuB,QAAQ,KAAK,UAAU,KAAK,sBAAsB,GAAG,UAAU,WAAS,MAAM,GAAG,MAAM,IAAI,UAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,iBAAe,KAAK,YAAY,iBAAiB,WAAW,CAAC;AACxN,SAAK,uBAAuB,QAAQ,UAAU,eAAa;AAIzD,YAAM,UAAU,KAAK;AACrB,UAAI,KAAK,yBAAyB,WAAW,QAAQ,YAAY,UAAU,GAAG;AAC5E,cAAM,QAAQ,UAAU,QAAQ;AAChC,cAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,MAAM,SAAS,GAAG,QAAQ,mBAAmB,CAAC,CAAC;AAClF,YAAI,MAAM,KAAK,KAAK,CAAC,MAAM,KAAK,EAAE,UAAU;AAC1C,kBAAQ,cAAc,KAAK;AAAA,QAC7B,OAAO;AACL,kBAAQ,kBAAkB;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,QAAQ;AAC1B,SAAK,uBAAuB,QAAQ;AACpC,SAAK,OAAO,SAAS;AACrB,SAAK,oBAAoB,QAAQ;AAAA,EACnC;AAAA;AAAA,EAEA,WAAW;AAET,UAAM,cAAc,KAAK,uBAAuB;AAChD,WAAO,YAAY,KAAK,UAAU,KAAK,sBAAsB,GAAG,UAAU,WAAS,MAAM,GAAG,MAAM,IAAI,UAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC;AAAA,EAChI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhB,WAAW,OAAO;AAAA,EAAC;AAAA;AAAA,EAEnB,eAAe,OAAO;AACpB,UAAM,UAAU,MAAM;AACtB,UAAM,UAAU,KAAK;AACrB,YAAQ,SAAS;AAAA,MACf,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,gBAAM,eAAe;AACrB,eAAK,OAAO,KAAK,SAAS;AAAA,QAC5B;AACA;AAAA,MACF,KAAK;AACH,YAAI,KAAK,cAAc,KAAK,cAAc,OAAO;AAC/C,eAAK,OAAO,KAAK,SAAS;AAAA,QAC5B;AACA;AAAA,MACF,KAAK;AACH,YAAI,KAAK,cAAc,KAAK,cAAc,OAAO;AAC/C,eAAK,OAAO,KAAK,SAAS;AAAA,QAC5B;AACA;AAAA,MACF;AACE,YAAI,YAAY,YAAY,YAAY,YAAY;AAClD,kBAAQ,eAAe,UAAU;AAAA,QACnC;AACA,gBAAQ,UAAU,KAAK;AACvB;AAAA,IACJ;AAGA,UAAM,gBAAgB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS,WAAW;AAEjC,SAAK,oBAAoB,QAAQ;AACjC,SAAK,qBAAqB,gBAAgB,MAAM;AAC9C,UAAI,YAAY;AAChB,UAAI,KAAK,uBAAuB,QAAQ;AAKtC,oBAAY,KAAK,uBAAuB,MAAM,gBAAgB,EAAE,QAAQ,eAAe;AAAA,MACzF;AAEA,UAAI,CAAC,aAAa,CAAC,UAAU,SAAS,SAAS,aAAa,GAAG;AAC7D,cAAM,UAAU,KAAK;AACrB,gBAAQ,eAAe,MAAM,EAAE,mBAAmB;AAIlD,YAAI,CAAC,QAAQ,cAAc,WAAW;AACpC,oBAAU,MAAM;AAAA,QAClB;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,UAAU,KAAK;AAAA,IACjB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AAChB,SAAK,YAAY,cAAc,EAAE;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO;AAGlB,QAAI,KAAK,mBAAmB,MAAM;AAChC,YAAM,SAAS,OAAO,qBAAqB,aAAa,iBAAiB,KAAK,YAAY,aAAa,IAAI;AAC3G,YAAM,QAAQ,QAAQ,iBAAiB,iCAAiC,KAAK;AAC7E,WAAK,iBAAiB,SAAS,KAAK;AAAA,IACtC;AAGA,UAAM,YAAY,KAAK,IAAI,KAAK,iBAAiB,OAAO,EAAE;AAC1D,UAAM,eAAe,GAAG,KAAK,gBAAgB,GAAG,SAAS;AACzD,UAAM,kBAAkB,OAAO,KAAK,KAAK,UAAU,EAAE,KAAK,eAAa;AACrE,aAAO,UAAU,WAAW,KAAK,gBAAgB;AAAA,IACnD,CAAC;AACD,QAAI,CAAC,mBAAmB,oBAAoB,KAAK,oBAAoB;AACnE,YAAM,eAAe,mBAChB,KAAK;AAEV,UAAI,KAAK,oBAAoB;AAC3B,qBAAa,KAAK,kBAAkB,IAAI;AAAA,MAC1C;AACA,mBAAa,YAAY,IAAI;AAC7B,WAAK,qBAAqB;AAC1B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,mBAAmB,OAAO,KAAK,WAAW,OAAO,KAAK,WAAW;AAC/D,SAAK,aAAa,iCACb,KAAK,aADQ;AAAA,MAEhB,CAAC,iBAAiB,GAAG,SAAS;AAAA,MAC9B,CAAC,gBAAgB,GAAG,SAAS;AAAA,MAC7B,CAAC,gBAAgB,GAAG,SAAS;AAAA,MAC7B,CAAC,gBAAgB,GAAG,SAAS;AAAA,IAC/B;AAEA,SAAK,oBAAoB,aAAa;AAAA,EACxC;AAAA;AAAA,EAEA,kBAAkB;AAEhB,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,kBAAkB;AAEhB,SAAK,uBAAuB;AAAA,EAC9B;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,SAAK,eAAe,KAAK,KAAK;AAC9B,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,kBAAkB,OAAO;AACvB,SAAK,eAAe;AAOpB,QAAI,MAAM,YAAY,WAAW,KAAK,YAAY,oBAAoB,GAAG;AACvE,YAAM,QAAQ,YAAY;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,2BAA2B;AACzB,SAAK,UAAU,QAAQ,KAAK,UAAU,KAAK,SAAS,CAAC,EAAE,UAAU,WAAS;AACxE,WAAK,uBAAuB,MAAM,MAAM,OAAO,UAAQ,KAAK,gBAAgB,IAAI,CAAC;AACjF,WAAK,uBAAuB,gBAAgB;AAAA,IAC9C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gBAAgB,mBAAmB;AACtD,aAAO,KAAK,qBAAqB,UAAY,kBAAqB,UAAU,GAAM,kBAAqB,MAAM,GAAM,kBAAkB,wBAAwB,GAAM,kBAAqB,iBAAiB,CAAC;AAAA,IAC5M;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,CAAC;AAAA,MACxB,gBAAgB,SAAS,uBAAuB,IAAI,KAAK,UAAU;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,kBAAkB,CAAC;AAC/C,UAAG,eAAe,UAAU,aAAa,CAAC;AAC1C,UAAG,eAAe,UAAU,aAAa,CAAC;AAAA,QAC5C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAClE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,YAAY;AAC7D,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ;AAAA,QAC3D;AAAA,MACF;AAAA,MACA,WAAW,SAAS,cAAc,IAAI,KAAK;AACzC,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,aAAa,CAAC;AAAA,QAC/B;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,QACpE;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,qBAAqB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,cAAc,IAAI,EAAE,mBAAmB,IAAI,EAAE,oBAAoB,IAAI;AAAA,QACtF;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,eAAe;AAAA,QACf,WAAW,CAAC,GAAG,cAAc,WAAW;AAAA,QACxC,gBAAgB,CAAC,GAAG,mBAAmB,gBAAgB;AAAA,QACvD,iBAAiB,CAAC,GAAG,oBAAoB,iBAAiB;AAAA,QAC1D,WAAW;AAAA,QACX,WAAW;AAAA,QACX,gBAAgB,CAAC,GAAG,kBAAkB,kBAAkB,gBAAgB;AAAA,QACxE,aAAa,CAAC,GAAG,eAAe,eAAe,WAAS,SAAS,OAAO,OAAO,iBAAiB,KAAK,CAAC;AAAA,QACtG,YAAY,CAAC,GAAG,SAAS,YAAY;AAAA,QACrC,WAAW;AAAA,MACb;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,QACR,OAAO;AAAA,MACT;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA6B,mBAAmB;AAAA,MACxD,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,YAAY,MAAM,QAAQ,QAAQ,GAAG,sBAAsB,8BAA8B,GAAG,WAAW,SAAS,IAAI,GAAG,CAAC,GAAG,sBAAsB,CAAC;AAAA,MAC5J,UAAU,SAAS,iBAAiB,IAAI,KAAK;AAC3C,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,WAAW,GAAG,gCAAgC,GAAG,GAAG,aAAa;AAAA,QACtE;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,ylIAA2lI;AAAA,MACpmI,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,kBAAkB,eAAe,kBAAkB,WAAW;AAAA,MAC5E;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,SAAS,CAAC;AAAA,IAChF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,qBAAqB;AAAA,QACrB,0BAA0B;AAAA,QAC1B,2BAA2B;AAAA,MAC7B;AAAA,MACA,YAAY,CAAC,kBAAkB,eAAe,kBAAkB,WAAW;AAAA,MAC3E,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,MACZ,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MACV,QAAQ,CAAC,ylIAA2lI;AAAA,IACtmI,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,IACpB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,QAClB,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,OAAO,iBAAiB,KAAK;AAAA,MACnE,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAM,2BAA2B,IAAI,eAAe,4BAA4B;AAAA,EAC9E,YAAY;AAAA,EACZ,SAAS,MAAM;AACb,UAAM,UAAU,OAAO,OAAO;AAC9B,WAAO,MAAM,QAAQ,iBAAiB,WAAW;AAAA,EACnD;AACF,CAAC;AAED,SAAS,iCAAiC,SAAS;AACjD,SAAO,MAAM,QAAQ,iBAAiB,WAAW;AACnD;AAEA,IAAM,4CAA4C;AAAA,EAChD,SAAS;AAAA,EACT,MAAM,CAAC,OAAO;AAAA,EACd,YAAY;AACd;AAEA,IAAM,8BAA8B,gCAAgC;AAAA,EAClE,SAAS;AACX,CAAC;AAMD,IAAM,yBAAyB;AAE/B,IAAM,iBAAN,MAAM,gBAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,IAAI,+BAA+B;AACjC,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,6BAA6B,GAAG;AAClC,SAAK,OAAO;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,KAAK,MAAM;AACb,QAAI,SAAS,KAAK,OAAO;AACvB;AAAA,IACF;AACA,SAAK,QAAQ;AACb,SAAK,uBAAuB,YAAY;AACxC,QAAI,MAAM;AACR,UAAI,SAAS,KAAK,wBAAwB,OAAO,cAAc,eAAe,YAAY;AACxF,mCAA2B;AAAA,MAC7B;AACA,WAAK,yBAAyB,KAAK,MAAM,UAAU,YAAU;AAC3D,aAAK,aAAa,MAAM;AAExB,aAAK,WAAW,WAAW,WAAW,UAAU,KAAK,qBAAqB;AACxE,eAAK,oBAAoB,OAAO,KAAK,MAAM;AAAA,QAC7C;AAAA,MACF,CAAC;AAAA,IACH;AACA,SAAK,mBAAmB,oBAAoB,KAAK,gBAAgB,CAAC;AAAA,EACpE;AAAA,EACA,YAAY,UAAU,UAAU,mBAAmB,gBAAgB,YAGnE,mBAAmB,MAAM,eAAe,SAAS;AAC/C,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,OAAO;AACZ,SAAK,gBAAgB;AACrB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,8BAA8B,aAAa;AAChD,SAAK,qBAAqB,aAAa;AACvC,SAAK,yBAAyB,aAAa;AAC3C,SAAK,qBAAqB,OAAO,iBAAiB;AAKlD,SAAK,oBAAoB,WAAS;AAChC,UAAI,CAAC,iCAAiC,KAAK,GAAG;AAC5C,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AAGA,SAAK,YAAY;AAMjB,SAAK,eAAe;AAEpB,SAAK,aAAa,IAAI,aAAa;AAOnC,SAAK,aAAa,KAAK;AAEvB,SAAK,aAAa,IAAI,aAAa;AAOnC,SAAK,cAAc,KAAK;AACxB,SAAK,kBAAkB;AACvB,SAAK,sBAAsB,sBAAsB,UAAU,aAAa;AACxE,aAAS,cAAc,iBAAiB,cAAc,KAAK,mBAAmB,2BAA2B;AAAA,EAC3G;AAAA,EACA,qBAAqB;AACnB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,QAAQ;AACzB,WAAK,cAAc;AAAA,IACrB;AACA,SAAK,SAAS,cAAc,oBAAoB,cAAc,KAAK,mBAAmB,2BAA2B;AACjH,SAAK,uBAAuB,YAAY;AACxC,SAAK,4BAA4B,YAAY;AAC7C,SAAK,mBAAmB,YAAY;AAAA,EACtC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAEA,IAAI,MAAM;AACR,WAAO,KAAK,QAAQ,KAAK,KAAK,UAAU,QAAQ,QAAQ;AAAA,EAC1D;AAAA;AAAA,EAEA,kBAAkB;AAChB,WAAO,CAAC,EAAE,KAAK,qBAAqB,KAAK,uBAAuB,KAAK;AAAA,EACvE;AAAA;AAAA,EAEA,aAAa;AACX,WAAO,KAAK,YAAY,KAAK,UAAU,IAAI,KAAK,SAAS;AAAA,EAC3D;AAAA;AAAA,EAEA,WAAW;AACT,UAAM,OAAO,KAAK;AAClB,QAAI,KAAK,aAAa,CAAC,MAAM;AAC3B;AAAA,IACF;AACA,UAAM,aAAa,KAAK,eAAe,IAAI;AAC3C,UAAM,gBAAgB,WAAW,UAAU;AAC3C,UAAM,mBAAmB,cAAc;AACvC,SAAK,aAAa,MAAM,gBAAgB;AACxC,kBAAc,cAAc,KAAK,eAAe,OAAO,CAAC,KAAK,gBAAgB,IAAI,KAAK;AACtF,eAAW,OAAO,KAAK,WAAW,IAAI,CAAC;AACvC,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,OAAO,KAAK,QAAQ;AAAA,IACvC;AACA,SAAK,8BAA8B,KAAK,oBAAoB,EAAE,UAAU,MAAM,KAAK,UAAU,CAAC;AAC9F,SAAK,UAAU,IAAI;AACnB,QAAI,gBAAgB,SAAS;AAC3B,WAAK,gBAAgB;AACrB,WAAK,uBAAuB,QAAQ,KAAK,UAAU,KAAK,KAAK,CAAC,EAAE,UAAU,MAAM;AAG9E,yBAAiB,mBAAmB,KAAK,EAAE,oBAAoB;AAC/D,yBAAiB,mBAAmB,IAAI;AAAA,MAC1C,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA,EAEA,YAAY;AACV,SAAK,MAAM,MAAM,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ,SAAS;AACrB,QAAI,KAAK,iBAAiB,QAAQ;AAChC,WAAK,cAAc,SAAS,KAAK,UAAU,QAAQ,OAAO;AAAA,IAC5D,OAAO;AACL,WAAK,SAAS,cAAc,MAAM,OAAO;AAAA,IAC3C;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,SAAK,aAAa,eAAe;AAAA,EACnC;AAAA;AAAA,EAEA,aAAa,QAAQ;AACnB,QAAI,CAAC,KAAK,eAAe,CAAC,KAAK,UAAU;AACvC;AAAA,IACF;AACA,UAAM,OAAO,KAAK;AAClB,SAAK,4BAA4B,YAAY;AAC7C,SAAK,YAAY,OAAO;AAKxB,QAAI,KAAK,iBAAiB,WAAW,aAAa,CAAC,KAAK,aAAa,CAAC,KAAK,gBAAgB,IAAI;AAC7F,WAAK,MAAM,KAAK,SAAS;AAAA,IAC3B;AACA,SAAK,YAAY;AACjB,QAAI,gBAAgB,SAAS;AAC3B,WAAK,gBAAgB;AACrB,UAAI,KAAK,aAAa;AAEpB,aAAK,eAAe;AAAA,UAAK,OAAO,WAAS,MAAM,YAAY,MAAM;AAAA,UAAG,KAAK,CAAC;AAAA;AAAA,UAE1E,UAAU,KAAK,YAAY,SAAS;AAAA,QAAC,EAAE,UAAU;AAAA,UAC/C,MAAM,MAAM,KAAK,YAAY,OAAO;AAAA;AAAA,UAEpC,UAAU,MAAM,KAAK,eAAe,KAAK;AAAA,QAC3C,CAAC;AAAA,MACH,OAAO;AACL,aAAK,eAAe,KAAK;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,WAAK,eAAe,KAAK;AACzB,YAAM,aAAa,OAAO;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM;AACd,SAAK,aAAa,KAAK,gBAAgB,IAAI,KAAK,sBAAsB;AACtE,SAAK,YAAY,KAAK;AACtB,SAAK,kBAAkB,IAAI;AAC3B,SAAK,eAAe,KAAK,aAAa,SAAS;AAC/C,SAAK,eAAe,IAAI;AAAA,EAC1B;AAAA;AAAA,EAEA,kBAAkB,MAAM;AACtB,QAAI,KAAK,cAAc;AACrB,UAAI,QAAQ;AACZ,UAAI,aAAa,KAAK;AACtB,aAAO,YAAY;AACjB;AACA,qBAAa,WAAW;AAAA,MAC1B;AACA,WAAK,aAAa,KAAK;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,QAAQ;AACrB,QAAI,WAAW,KAAK,WAAW;AAC7B,WAAK,YAAY;AACjB,WAAK,YAAY,KAAK,WAAW,KAAK,IAAI,KAAK,WAAW,KAAK;AAC/D,UAAI,KAAK,gBAAgB,GAAG;AAC1B,aAAK,kBAAkB,gBAAgB,MAAM;AAAA,MAC/C;AACA,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM;AACnB,QAAI,CAAC,KAAK,aAAa;AACrB,YAAM,SAAS,KAAK,kBAAkB,IAAI;AAC1C,WAAK,sBAAsB,MAAM,OAAO,gBAAgB;AACxD,WAAK,cAAc,KAAK,SAAS,OAAO,MAAM;AAI9C,WAAK,YAAY,cAAc,EAAE,UAAU;AAAA,IAC7C;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,MAAM;AACtB,WAAO,IAAI,cAAc;AAAA,MACvB,kBAAkB,KAAK,SAAS,SAAS,EAAE,oBAAoB,KAAK,QAAQ,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,sBAAsB,sCAAsC;AAAA,MACnL,eAAe,KAAK,iBAAiB;AAAA,MACrC,YAAY,KAAK;AAAA,MACjB,gBAAgB,KAAK,gBAAgB;AAAA,MACrC,WAAW,KAAK;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAAsB,MAAM,UAAU;AACpC,QAAI,KAAK,oBAAoB;AAC3B,eAAS,gBAAgB,UAAU,YAAU;AAC3C,cAAM,OAAO,OAAO,eAAe,aAAa,UAAU,UAAU;AACpE,cAAM,OAAO,OAAO,eAAe,aAAa,QAAQ,UAAU;AAIlE,YAAI,KAAK,SAAS;AAChB,eAAK,QAAQ,IAAI,MAAM,KAAK,mBAAmB,MAAM,IAAI,CAAC;AAAA,QAC5D,OAAO;AACL,eAAK,mBAAmB,MAAM,IAAI;AAAA,QACpC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,MAAM,kBAAkB;AACnC,QAAI,CAAC,SAAS,eAAe,IAAI,KAAK,cAAc,WAAW,CAAC,OAAO,OAAO,IAAI,CAAC,SAAS,KAAK;AACjG,QAAI,CAAC,UAAU,gBAAgB,IAAI,KAAK,cAAc,UAAU,CAAC,UAAU,KAAK,IAAI,CAAC,OAAO,QAAQ;AACpG,QAAI,CAAC,SAAS,eAAe,IAAI,CAAC,UAAU,gBAAgB;AAC5D,QAAI,CAAC,UAAU,gBAAgB,IAAI,CAAC,SAAS,eAAe;AAC5D,QAAI,UAAU;AACd,QAAI,KAAK,gBAAgB,GAAG;AAG1B,yBAAmB,UAAU,KAAK,cAAc,WAAW,UAAU;AACrE,wBAAkB,WAAW,YAAY,QAAQ,UAAU;AAC3D,UAAI,KAAK,qBAAqB;AAC5B,YAAI,KAAK,uBAAuB,MAAM;AACpC,gBAAM,YAAY,KAAK,oBAAoB,MAAM;AACjD,eAAK,sBAAsB,YAAY,UAAU,gBAAgB,EAAE,YAAY;AAAA,QACjF;AACA,kBAAU,aAAa,WAAW,KAAK,sBAAsB,CAAC,KAAK;AAAA,MACrE;AAAA,IACF,WAAW,CAAC,KAAK,gBAAgB;AAC/B,gBAAU,aAAa,QAAQ,WAAW;AAC1C,wBAAkB,qBAAqB,QAAQ,WAAW;AAAA,IAC5D;AACA,qBAAiB,cAAc,CAAC;AAAA,MAC9B;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG;AAAA,MACD,SAAS;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV;AAAA,MACA;AAAA,IACF,GAAG;AAAA,MACD;AAAA,MACA,SAAS;AAAA,MACT;AAAA,MACA,UAAU;AAAA,MACV,SAAS,CAAC;AAAA,IACZ,GAAG;AAAA,MACD,SAAS;AAAA,MACT,SAAS;AAAA,MACT,UAAU;AAAA,MACV,UAAU;AAAA,MACV,SAAS,CAAC;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA,EAEA,sBAAsB;AACpB,UAAM,WAAW,KAAK,YAAY,cAAc;AAChD,UAAM,cAAc,KAAK,YAAY,YAAY;AACjD,UAAM,cAAc,KAAK,sBAAsB,KAAK,oBAAoB,SAAS,GAAG;AACpF,UAAM,QAAQ,KAAK,sBAAsB,KAAK,oBAAoB,SAAS,EAAE,KAAK,OAAO,YAAU,WAAW,KAAK,iBAAiB,GAAG,OAAO,MAAM,KAAK,SAAS,CAAC,IAAI,GAAG;AAC1K,WAAO,MAAM,UAAU,aAAa,OAAO,WAAW;AAAA,EACxD;AAAA;AAAA,EAEA,iBAAiB,OAAO;AACtB,QAAI,CAAC,gCAAgC,KAAK,GAAG;AAG3C,WAAK,YAAY,MAAM,WAAW,IAAI,UAAU;AAIhD,UAAI,KAAK,gBAAgB,GAAG;AAC1B,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,UAAM,UAAU,MAAM;AAEtB,QAAI,YAAY,SAAS,YAAY,OAAO;AAC1C,WAAK,YAAY;AAAA,IACnB;AACA,QAAI,KAAK,gBAAgB,MAAM,YAAY,eAAe,KAAK,QAAQ,SAAS,YAAY,cAAc,KAAK,QAAQ,QAAQ;AAC7H,WAAK,YAAY;AACjB,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA;AAAA,EAEA,aAAa,OAAO;AAClB,QAAI,KAAK,gBAAgB,GAAG;AAE1B,YAAM,gBAAgB;AACtB,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA,EAEA,eAAe;AAEb,QAAI,CAAC,KAAK,gBAAgB,KAAK,CAAC,KAAK,qBAAqB;AACxD;AAAA,IACF;AACA,SAAK,qBAAqB,KAAK,oBAAoB,SAAS,EAI3D,KAAK,OAAO,YAAU,WAAW,KAAK,qBAAqB,CAAC,OAAO,QAAQ,GAAG,MAAM,GAAG,aAAa,CAAC,EAAE,UAAU,MAAM;AACtH,WAAK,YAAY;AAIjB,UAAI,KAAK,gBAAgB,WAAW,KAAK,KAAK,cAAc;AAG1D,aAAK,KAAK,eAAe,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG,aAAa,GAAG,UAAU,KAAK,oBAAoB,SAAS,CAAC,CAAC,EAAE,UAAU,MAAM,KAAK,SAAS,CAAC;AAAA,MACjJ,OAAO;AACL,aAAK,SAAS;AAAA,MAChB;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,WAAW,MAAM;AAIf,QAAI,CAAC,KAAK,WAAW,KAAK,QAAQ,gBAAgB,KAAK,aAAa;AAClE,WAAK,UAAU,IAAI,eAAe,KAAK,aAAa,KAAK,iBAAiB;AAAA,IAC5E;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAmB,kBAAuB,OAAO,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,wBAAwB,GAAM,kBAAkB,gBAAgB,CAAC,GAAM,kBAAkB,aAAa,EAAE,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,MAAM,CAAC;AAAA,IACzZ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,wBAAwB,EAAE,GAAG,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MAC3E,WAAW,CAAC,GAAG,sBAAsB;AAAA,MACrC,UAAU;AAAA,MACV,cAAc,SAAS,4BAA4B,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,wCAAwC,QAAQ;AAC9E,mBAAO,IAAI,aAAa,MAAM;AAAA,UAChC,CAAC,EAAE,aAAa,SAAS,4CAA4C,QAAQ;AAC3E,mBAAO,IAAI,iBAAiB,MAAM;AAAA,UACpC,CAAC,EAAE,WAAW,SAAS,0CAA0C,QAAQ;AACvE,mBAAO,IAAI,eAAe,MAAM;AAAA,UAClC,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,iBAAiB,IAAI,OAAO,SAAS,IAAI,EAAE,iBAAiB,IAAI,QAAQ,EAAE,iBAAiB,IAAI,WAAW,IAAI,KAAK,UAAU,IAAI;AAAA,QAClJ;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,8BAA8B,CAAC,GAAG,wBAAwB,8BAA8B;AAAA,QACxF,MAAM,CAAC,GAAG,qBAAqB,MAAM;AAAA,QACrC,UAAU,CAAC,GAAG,sBAAsB,UAAU;AAAA,QAC9C,cAAc,CAAC,GAAG,8BAA8B,cAAc;AAAA,MAChE;AAAA,MACA,SAAS;AAAA,QACP,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,gBAAgB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,WAAW;AAAA,QACX,eAAe;AAAA,QACf,aAAa;AAAA,MACf;AAAA,MACA,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,8BAA8B,CAAC;AAAA,MAC7B,MAAM;AAAA,MACN,MAAM,CAAC,sBAAsB;AAAA,IAC/B,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,mBAAmB;AAC5D,aAAO,KAAK,qBAAqB,gBAAe;AAAA,IAClD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,cAAc,iBAAiB,iBAAiB,eAAe,SAAS,aAAa,gBAAgB,cAAc;AAAA,MAC7H,SAAS,CAAC,qBAAqB,SAAS,iBAAiB,aAAa,gBAAgB,cAAc;AAAA,IACtG,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,yCAAyC;AAAA,MACrD,SAAS,CAAC,cAAc,iBAAiB,iBAAiB,eAAe,qBAAqB,eAAe;AAAA,IAC/G,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,iBAAiB,iBAAiB,eAAe,SAAS,aAAa,gBAAgB,cAAc;AAAA,MAC7H,SAAS,CAAC,qBAAqB,SAAS,iBAAiB,aAAa,gBAAgB,cAAc;AAAA,MACpG,WAAW,CAAC,yCAAyC;AAAA,IACvD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}