
package otbs.ms_mission.commun;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Classe générique pour les réponses paginées
 * @param <T> Type des éléments contenus dans la page
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {
    /**
     * Contenu de la page (liste d'éléments)
     */
    private List<T> content;

    /**
     * Numéro de la page actuelle (commence à 0)
     */
    private int number;

    /**
     * Taille de la page (nombre d'éléments par page)
     */
    private int size;

    /**
     * Nombre total d'éléments dans toutes les pages
     */
    private long totalElements;

    /**
     * Nombre total de pages
     */
    private int totalPages;

    /**
     * Indique si c'est la première page
     */
    private boolean first;

    /**
     * Indique si c'est la dernière page
     */
    private boolean last;

    /**
     * Indique si la page est vide
     */
    private boolean empty;
}

