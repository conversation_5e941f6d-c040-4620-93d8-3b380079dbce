package otbs.ms_incident.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;

import java.util.Arrays;

/**
 * Test Security Configuration that allows specific requests during testing
 * while enforcing role-based security for validation in tests
 */
@TestConfiguration
@EnableWebSecurity
class TestSecurityConfig {


    
    @Bean
    @Primary
    public UserDetailsService userDetailsService() {
        UserDetails adminUser = User.builder()
            .username("admin")
            .password("{noop}password")
            .authorities(Arrays.asList(
                new SimpleGrantedAuthority("ROLE_ADMINISTRATEUR")
            ))
            .build();
        
        UserDetails gestionnaireUser = User.builder()
            .username("gestionnaire")
            .password("{noop}password")
            .authorities(Arrays.asList(
                new SimpleGrantedAuthority("ROLE_RESPONSABLE_SERVICE_GENERAUX")
            ))
            .build();
        
        UserDetails directeurUser = User.builder()
            .username("directeur")
            .password("{noop}password")
            .authorities(Arrays.asList(
                new SimpleGrantedAuthority("ROLE_DIRECTEUR_GENERAL")
            ))
            .build();
        
        UserDetails superUser = User.builder()
            .username("superuser")
            .password("{noop}password")
            .authorities(Arrays.asList(
                new SimpleGrantedAuthority("ROLE_ADMINISTRATEUR"),
                new SimpleGrantedAuthority("ROLE_RESPONSABLE_SERVICE_GENERAUX"),
                new SimpleGrantedAuthority("ROLE_DIRECTEUR_GENERAL"),
                new SimpleGrantedAuthority("ROLE_DIRECTEUR_TECHNIQUE"),
                new SimpleGrantedAuthority("ROLE_CONSULTANT")
            ))
            .build();
        
        return new InMemoryUserDetailsManager(
            adminUser, gestionnaireUser, directeurUser, superUser
        );
    }
} 