{"version": 3, "sources": ["../../../../../../node_modules/@tanstack/query-core/build/modern/subscribable.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/utils.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/focusManager.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/onlineManager.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/thenable.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/retryer.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/notifyManager.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/removable.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/query.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/queryCache.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/mutation.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/mutationCache.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/infiniteQueryBehavior.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/queryClient.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/queryObserver.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/queriesObserver.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/mutationObserver.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/hydration.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/streamedQuery.js", "../../../../../../node_modules/@tanstack/query-core/build/modern/types.js", "../../../../../../node_modules/@tanstack/angular-query-experimental/build/index.mjs"], "sourcesContent": ["// src/subscribable.ts\nvar Subscribable = class {\n  constructor() {\n    this.listeners = /* @__PURE__ */new Set();\n    this.subscribe = this.subscribe.bind(this);\n  }\n  subscribe(listener) {\n    this.listeners.add(listener);\n    this.onSubscribe();\n    return () => {\n      this.listeners.delete(listener);\n      this.onUnsubscribe();\n    };\n  }\n  hasListeners() {\n    return this.listeners.size > 0;\n  }\n  onSubscribe() {}\n  onUnsubscribe() {}\n};\nexport { Subscribable };\n", "// src/utils.ts\nvar isServer = typeof window === \"undefined\" || \"Deno\" in globalThis;\nfunction noop() {}\nfunction functionalUpdate(updater, input) {\n  return typeof updater === \"function\" ? updater(input) : updater;\n}\nfunction isValidTimeout(value) {\n  return typeof value === \"number\" && value >= 0 && value !== Infinity;\n}\nfunction timeUntilStale(updatedAt, staleTime) {\n  return Math.max(updatedAt + (staleTime || 0) - Date.now(), 0);\n}\nfunction resolveStaleTime(staleTime, query) {\n  return typeof staleTime === \"function\" ? staleTime(query) : staleTime;\n}\nfunction resolveEnabled(enabled, query) {\n  return typeof enabled === \"function\" ? enabled(query) : enabled;\n}\nfunction matchQuery(filters, query) {\n  const {\n    type = \"all\",\n    exact,\n    fetchStatus,\n    predicate,\n    queryKey,\n    stale\n  } = filters;\n  if (queryKey) {\n    if (exact) {\n      if (query.queryHash !== hashQueryKeyByOptions(queryKey, query.options)) {\n        return false;\n      }\n    } else if (!partialMatchKey(query.queryKey, queryKey)) {\n      return false;\n    }\n  }\n  if (type !== \"all\") {\n    const isActive = query.isActive();\n    if (type === \"active\" && !isActive) {\n      return false;\n    }\n    if (type === \"inactive\" && isActive) {\n      return false;\n    }\n  }\n  if (typeof stale === \"boolean\" && query.isStale() !== stale) {\n    return false;\n  }\n  if (fetchStatus && fetchStatus !== query.state.fetchStatus) {\n    return false;\n  }\n  if (predicate && !predicate(query)) {\n    return false;\n  }\n  return true;\n}\nfunction matchMutation(filters, mutation) {\n  const {\n    exact,\n    status,\n    predicate,\n    mutationKey\n  } = filters;\n  if (mutationKey) {\n    if (!mutation.options.mutationKey) {\n      return false;\n    }\n    if (exact) {\n      if (hashKey(mutation.options.mutationKey) !== hashKey(mutationKey)) {\n        return false;\n      }\n    } else if (!partialMatchKey(mutation.options.mutationKey, mutationKey)) {\n      return false;\n    }\n  }\n  if (status && mutation.state.status !== status) {\n    return false;\n  }\n  if (predicate && !predicate(mutation)) {\n    return false;\n  }\n  return true;\n}\nfunction hashQueryKeyByOptions(queryKey, options) {\n  const hashFn = options?.queryKeyHashFn || hashKey;\n  return hashFn(queryKey);\n}\nfunction hashKey(queryKey) {\n  return JSON.stringify(queryKey, (_, val) => isPlainObject(val) ? Object.keys(val).sort().reduce((result, key) => {\n    result[key] = val[key];\n    return result;\n  }, {}) : val);\n}\nfunction partialMatchKey(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (a && b && typeof a === \"object\" && typeof b === \"object\") {\n    return Object.keys(b).every(key => partialMatchKey(a[key], b[key]));\n  }\n  return false;\n}\nfunction replaceEqualDeep(a, b) {\n  if (a === b) {\n    return a;\n  }\n  const array = isPlainArray(a) && isPlainArray(b);\n  if (array || isPlainObject(a) && isPlainObject(b)) {\n    const aItems = array ? a : Object.keys(a);\n    const aSize = aItems.length;\n    const bItems = array ? b : Object.keys(b);\n    const bSize = bItems.length;\n    const copy = array ? [] : {};\n    let equalItems = 0;\n    for (let i = 0; i < bSize; i++) {\n      const key = array ? i : bItems[i];\n      if ((!array && aItems.includes(key) || array) && a[key] === void 0 && b[key] === void 0) {\n        copy[key] = void 0;\n        equalItems++;\n      } else {\n        copy[key] = replaceEqualDeep(a[key], b[key]);\n        if (copy[key] === a[key] && a[key] !== void 0) {\n          equalItems++;\n        }\n      }\n    }\n    return aSize === bSize && equalItems === aSize ? a : copy;\n  }\n  return b;\n}\nfunction shallowEqualObjects(a, b) {\n  if (!b || Object.keys(a).length !== Object.keys(b).length) {\n    return false;\n  }\n  for (const key in a) {\n    if (a[key] !== b[key]) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction isPlainArray(value) {\n  return Array.isArray(value) && value.length === Object.keys(value).length;\n}\nfunction isPlainObject(o) {\n  if (!hasObjectPrototype(o)) {\n    return false;\n  }\n  const ctor = o.constructor;\n  if (ctor === void 0) {\n    return true;\n  }\n  const prot = ctor.prototype;\n  if (!hasObjectPrototype(prot)) {\n    return false;\n  }\n  if (!prot.hasOwnProperty(\"isPrototypeOf\")) {\n    return false;\n  }\n  if (Object.getPrototypeOf(o) !== Object.prototype) {\n    return false;\n  }\n  return true;\n}\nfunction hasObjectPrototype(o) {\n  return Object.prototype.toString.call(o) === \"[object Object]\";\n}\nfunction sleep(timeout) {\n  return new Promise(resolve => {\n    setTimeout(resolve, timeout);\n  });\n}\nfunction replaceData(prevData, data, options) {\n  if (typeof options.structuralSharing === \"function\") {\n    return options.structuralSharing(prevData, data);\n  } else if (options.structuralSharing !== false) {\n    if (process.env.NODE_ENV !== \"production\") {\n      try {\n        return replaceEqualDeep(prevData, data);\n      } catch (error) {\n        console.error(`Structural sharing requires data to be JSON serializable. To fix this, turn off structuralSharing or return JSON-serializable data from your queryFn. [${options.queryHash}]: ${error}`);\n        throw error;\n      }\n    }\n    return replaceEqualDeep(prevData, data);\n  }\n  return data;\n}\nfunction keepPreviousData(previousData) {\n  return previousData;\n}\nfunction addToEnd(items, item, max = 0) {\n  const newItems = [...items, item];\n  return max && newItems.length > max ? newItems.slice(1) : newItems;\n}\nfunction addToStart(items, item, max = 0) {\n  const newItems = [item, ...items];\n  return max && newItems.length > max ? newItems.slice(0, -1) : newItems;\n}\nvar skipToken = Symbol();\nfunction ensureQueryFn(options, fetchOptions) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (options.queryFn === skipToken) {\n      console.error(`Attempted to invoke queryFn when set to skipToken. This is likely a configuration error. Query hash: '${options.queryHash}'`);\n    }\n  }\n  if (!options.queryFn && fetchOptions?.initialPromise) {\n    return () => fetchOptions.initialPromise;\n  }\n  if (!options.queryFn || options.queryFn === skipToken) {\n    return () => Promise.reject(new Error(`Missing queryFn: '${options.queryHash}'`));\n  }\n  return options.queryFn;\n}\nfunction shouldThrowError(throwOnError, params) {\n  if (typeof throwOnError === \"function\") {\n    return throwOnError(...params);\n  }\n  return !!throwOnError;\n}\nexport { addToEnd, addToStart, ensureQueryFn, functionalUpdate, hashKey, hashQueryKeyByOptions, isPlainArray, isPlainObject, isServer, isValidTimeout, keepPreviousData, matchMutation, matchQuery, noop, partialMatchKey, replaceData, replaceEqualDeep, resolveEnabled, resolveStaleTime, shallowEqualObjects, shouldThrowError, skipToken, sleep, timeUntilStale };\n", "// src/focusManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar FocusManager = class extends Subscribable {\n  #focused;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = onFocus => {\n      if (!isServer && window.addEventListener) {\n        const listener = () => onFocus();\n        window.addEventListener(\"visibilitychange\", listener, false);\n        return () => {\n          window.removeEventListener(\"visibilitychange\", listener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(focused => {\n      if (typeof focused === \"boolean\") {\n        this.setFocused(focused);\n      } else {\n        this.onFocus();\n      }\n    });\n  }\n  setFocused(focused) {\n    const changed = this.#focused !== focused;\n    if (changed) {\n      this.#focused = focused;\n      this.onFocus();\n    }\n  }\n  onFocus() {\n    const isFocused = this.isFocused();\n    this.listeners.forEach(listener => {\n      listener(isFocused);\n    });\n  }\n  isFocused() {\n    if (typeof this.#focused === \"boolean\") {\n      return this.#focused;\n    }\n    return globalThis.document?.visibilityState !== \"hidden\";\n  }\n};\nvar focusManager = new FocusManager();\nexport { FocusManager, focusManager };\n", "// src/onlineManager.ts\nimport { Subscribable } from \"./subscribable.js\";\nimport { isServer } from \"./utils.js\";\nvar OnlineManager = class extends Subscribable {\n  #online = true;\n  #cleanup;\n  #setup;\n  constructor() {\n    super();\n    this.#setup = onOnline => {\n      if (!isServer && window.addEventListener) {\n        const onlineListener = () => onOnline(true);\n        const offlineListener = () => onOnline(false);\n        window.addEventListener(\"online\", onlineListener, false);\n        window.addEventListener(\"offline\", offlineListener, false);\n        return () => {\n          window.removeEventListener(\"online\", onlineListener);\n          window.removeEventListener(\"offline\", offlineListener);\n        };\n      }\n      return;\n    };\n  }\n  onSubscribe() {\n    if (!this.#cleanup) {\n      this.setEventListener(this.#setup);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#cleanup?.();\n      this.#cleanup = void 0;\n    }\n  }\n  setEventListener(setup) {\n    this.#setup = setup;\n    this.#cleanup?.();\n    this.#cleanup = setup(this.setOnline.bind(this));\n  }\n  setOnline(online) {\n    const changed = this.#online !== online;\n    if (changed) {\n      this.#online = online;\n      this.listeners.forEach(listener => {\n        listener(online);\n      });\n    }\n  }\n  isOnline() {\n    return this.#online;\n  }\n};\nvar onlineManager = new OnlineManager();\nexport { OnlineManager, onlineManager };\n", "// src/thenable.ts\nimport { noop } from \"./utils.js\";\nfunction pendingThenable() {\n  let resolve;\n  let reject;\n  const thenable = new Promise((_resolve, _reject) => {\n    resolve = _resolve;\n    reject = _reject;\n  });\n  thenable.status = \"pending\";\n  thenable.catch(() => {});\n  function finalize(data) {\n    Object.assign(thenable, data);\n    delete thenable.resolve;\n    delete thenable.reject;\n  }\n  thenable.resolve = value => {\n    finalize({\n      status: \"fulfilled\",\n      value\n    });\n    resolve(value);\n  };\n  thenable.reject = reason => {\n    finalize({\n      status: \"rejected\",\n      reason\n    });\n    reject(reason);\n  };\n  return thenable;\n}\nfunction tryResolveSync(promise) {\n  let data;\n  promise.then(result => {\n    data = result;\n    return result;\n  })?.catch(noop);\n  if (data !== void 0) {\n    return {\n      data\n    };\n  }\n  return void 0;\n}\nexport { pendingThenable, tryResolveSync };\n", "// src/retryer.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport { isServer, sleep } from \"./utils.js\";\nfunction defaultRetryDelay(failureCount) {\n  return Math.min(1e3 * 2 ** failureCount, 3e4);\n}\nfunction canFetch(networkMode) {\n  return (networkMode ?? \"online\") === \"online\" ? onlineManager.isOnline() : true;\n}\nvar CancelledError = class extends Error {\n  constructor(options) {\n    super(\"CancelledError\");\n    this.revert = options?.revert;\n    this.silent = options?.silent;\n  }\n};\nfunction isCancelledError(value) {\n  return value instanceof CancelledError;\n}\nfunction createRetryer(config) {\n  let isRetryCancelled = false;\n  let failureCount = 0;\n  let isResolved = false;\n  let continueFn;\n  const thenable = pendingThenable();\n  const cancel = cancelOptions => {\n    if (!isResolved) {\n      reject(new CancelledError(cancelOptions));\n      config.abort?.();\n    }\n  };\n  const cancelRetry = () => {\n    isRetryCancelled = true;\n  };\n  const continueRetry = () => {\n    isRetryCancelled = false;\n  };\n  const canContinue = () => focusManager.isFocused() && (config.networkMode === \"always\" || onlineManager.isOnline()) && config.canRun();\n  const canStart = () => canFetch(config.networkMode) && config.canRun();\n  const resolve = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onSuccess?.(value);\n      continueFn?.();\n      thenable.resolve(value);\n    }\n  };\n  const reject = value => {\n    if (!isResolved) {\n      isResolved = true;\n      config.onError?.(value);\n      continueFn?.();\n      thenable.reject(value);\n    }\n  };\n  const pause = () => {\n    return new Promise(continueResolve => {\n      continueFn = value => {\n        if (isResolved || canContinue()) {\n          continueResolve(value);\n        }\n      };\n      config.onPause?.();\n    }).then(() => {\n      continueFn = void 0;\n      if (!isResolved) {\n        config.onContinue?.();\n      }\n    });\n  };\n  const run = () => {\n    if (isResolved) {\n      return;\n    }\n    let promiseOrValue;\n    const initialPromise = failureCount === 0 ? config.initialPromise : void 0;\n    try {\n      promiseOrValue = initialPromise ?? config.fn();\n    } catch (error) {\n      promiseOrValue = Promise.reject(error);\n    }\n    Promise.resolve(promiseOrValue).then(resolve).catch(error => {\n      if (isResolved) {\n        return;\n      }\n      const retry = config.retry ?? (isServer ? 0 : 3);\n      const retryDelay = config.retryDelay ?? defaultRetryDelay;\n      const delay = typeof retryDelay === \"function\" ? retryDelay(failureCount, error) : retryDelay;\n      const shouldRetry = retry === true || typeof retry === \"number\" && failureCount < retry || typeof retry === \"function\" && retry(failureCount, error);\n      if (isRetryCancelled || !shouldRetry) {\n        reject(error);\n        return;\n      }\n      failureCount++;\n      config.onFail?.(failureCount, error);\n      sleep(delay).then(() => {\n        return canContinue() ? void 0 : pause();\n      }).then(() => {\n        if (isRetryCancelled) {\n          reject(error);\n        } else {\n          run();\n        }\n      });\n    });\n  };\n  return {\n    promise: thenable,\n    cancel,\n    continue: () => {\n      continueFn?.();\n      return thenable;\n    },\n    cancelRetry,\n    continueRetry,\n    canStart,\n    start: () => {\n      if (canStart()) {\n        run();\n      } else {\n        pause().then(run);\n      }\n      return thenable;\n    }\n  };\n}\nexport { CancelledError, canFetch, createRetryer, isCancelledError };\n", "// src/notifyManager.ts\nvar defaultScheduler = cb => setTimeout(cb, 0);\nfunction createNotifyManager() {\n  let queue = [];\n  let transactions = 0;\n  let notifyFn = callback => {\n    callback();\n  };\n  let batchNotifyFn = callback => {\n    callback();\n  };\n  let scheduleFn = defaultScheduler;\n  const schedule = callback => {\n    if (transactions) {\n      queue.push(callback);\n    } else {\n      scheduleFn(() => {\n        notifyFn(callback);\n      });\n    }\n  };\n  const flush = () => {\n    const originalQueue = queue;\n    queue = [];\n    if (originalQueue.length) {\n      scheduleFn(() => {\n        batchNotifyFn(() => {\n          originalQueue.forEach(callback => {\n            notifyFn(callback);\n          });\n        });\n      });\n    }\n  };\n  return {\n    batch: callback => {\n      let result;\n      transactions++;\n      try {\n        result = callback();\n      } finally {\n        transactions--;\n        if (!transactions) {\n          flush();\n        }\n      }\n      return result;\n    },\n    /**\n     * All calls to the wrapped function will be batched.\n     */\n    batchCalls: callback => {\n      return (...args) => {\n        schedule(() => {\n          callback(...args);\n        });\n      };\n    },\n    schedule,\n    /**\n     * Use this method to set a custom notify function.\n     * This can be used to for example wrap notifications with `React.act` while running tests.\n     */\n    setNotifyFunction: fn => {\n      notifyFn = fn;\n    },\n    /**\n     * Use this method to set a custom function to batch notifications together into a single tick.\n     * By default React Query will use the batch function provided by ReactDOM or React Native.\n     */\n    setBatchNotifyFunction: fn => {\n      batchNotifyFn = fn;\n    },\n    setScheduler: fn => {\n      scheduleFn = fn;\n    }\n  };\n}\nvar notifyManager = createNotifyManager();\nexport { createNotifyManager, defaultScheduler, notifyManager };\n", "// src/removable.ts\nimport { isServer, isValidTimeout } from \"./utils.js\";\nvar Removable = class {\n  #gcTimeout;\n  destroy() {\n    this.clearGcTimeout();\n  }\n  scheduleGc() {\n    this.clearGcTimeout();\n    if (isValidTimeout(this.gcTime)) {\n      this.#gcTimeout = setTimeout(() => {\n        this.optionalRemove();\n      }, this.gcTime);\n    }\n  }\n  updateGcTime(newGcTime) {\n    this.gcTime = Math.max(this.gcTime || 0, newGcTime ?? (isServer ? Infinity : 5 * 60 * 1e3));\n  }\n  clearGcTimeout() {\n    if (this.#gcTimeout) {\n      clearTimeout(this.#gcTimeout);\n      this.#gcTimeout = void 0;\n    }\n  }\n};\nexport { Removable };\n", "// src/query.ts\nimport { ensureQueryFn, noop, replaceData, resolveEnabled, skipToken, timeUntilStale } from \"./utils.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { canFetch, createRetryer, isCancelledError } from \"./retryer.js\";\nimport { Removable } from \"./removable.js\";\nvar Query = class extends Removable {\n  #initialState;\n  #revertState;\n  #cache;\n  #client;\n  #retryer;\n  #defaultOptions;\n  #abortSignalConsumed;\n  constructor(config) {\n    super();\n    this.#abortSignalConsumed = false;\n    this.#defaultOptions = config.defaultOptions;\n    this.setOptions(config.options);\n    this.observers = [];\n    this.#client = config.client;\n    this.#cache = this.#client.getQueryCache();\n    this.queryKey = config.queryKey;\n    this.queryHash = config.queryHash;\n    this.#initialState = getDefaultState(this.options);\n    this.state = config.state ?? this.#initialState;\n    this.scheduleGc();\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  get promise() {\n    return this.#retryer?.promise;\n  }\n  setOptions(options) {\n    this.options = {\n      ...this.#defaultOptions,\n      ...options\n    };\n    this.updateGcTime(this.options.gcTime);\n  }\n  optionalRemove() {\n    if (!this.observers.length && this.state.fetchStatus === \"idle\") {\n      this.#cache.remove(this);\n    }\n  }\n  setData(newData, options) {\n    const data = replaceData(this.state.data, newData, this.options);\n    this.#dispatch({\n      data,\n      type: \"success\",\n      dataUpdatedAt: options?.updatedAt,\n      manual: options?.manual\n    });\n    return data;\n  }\n  setState(state, setStateOptions) {\n    this.#dispatch({\n      type: \"setState\",\n      state,\n      setStateOptions\n    });\n  }\n  cancel(options) {\n    const promise = this.#retryer?.promise;\n    this.#retryer?.cancel(options);\n    return promise ? promise.then(noop).catch(noop) : Promise.resolve();\n  }\n  destroy() {\n    super.destroy();\n    this.cancel({\n      silent: true\n    });\n  }\n  reset() {\n    this.destroy();\n    this.setState(this.#initialState);\n  }\n  isActive() {\n    return this.observers.some(observer => resolveEnabled(observer.options.enabled, this) !== false);\n  }\n  isDisabled() {\n    if (this.getObserversCount() > 0) {\n      return !this.isActive();\n    }\n    return this.options.queryFn === skipToken || this.state.dataUpdateCount + this.state.errorUpdateCount === 0;\n  }\n  isStale() {\n    if (this.state.isInvalidated) {\n      return true;\n    }\n    if (this.getObserversCount() > 0) {\n      return this.observers.some(observer => observer.getCurrentResult().isStale);\n    }\n    return this.state.data === void 0;\n  }\n  isStaleByTime(staleTime = 0) {\n    return this.state.isInvalidated || this.state.data === void 0 || !timeUntilStale(this.state.dataUpdatedAt, staleTime);\n  }\n  onFocus() {\n    const observer = this.observers.find(x => x.shouldFetchOnWindowFocus());\n    observer?.refetch({\n      cancelRefetch: false\n    });\n    this.#retryer?.continue();\n  }\n  onOnline() {\n    const observer = this.observers.find(x => x.shouldFetchOnReconnect());\n    observer?.refetch({\n      cancelRefetch: false\n    });\n    this.#retryer?.continue();\n  }\n  addObserver(observer) {\n    if (!this.observers.includes(observer)) {\n      this.observers.push(observer);\n      this.clearGcTimeout();\n      this.#cache.notify({\n        type: \"observerAdded\",\n        query: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    if (this.observers.includes(observer)) {\n      this.observers = this.observers.filter(x => x !== observer);\n      if (!this.observers.length) {\n        if (this.#retryer) {\n          if (this.#abortSignalConsumed) {\n            this.#retryer.cancel({\n              revert: true\n            });\n          } else {\n            this.#retryer.cancelRetry();\n          }\n        }\n        this.scheduleGc();\n      }\n      this.#cache.notify({\n        type: \"observerRemoved\",\n        query: this,\n        observer\n      });\n    }\n  }\n  getObserversCount() {\n    return this.observers.length;\n  }\n  invalidate() {\n    if (!this.state.isInvalidated) {\n      this.#dispatch({\n        type: \"invalidate\"\n      });\n    }\n  }\n  fetch(options, fetchOptions) {\n    if (this.state.fetchStatus !== \"idle\") {\n      if (this.state.data !== void 0 && fetchOptions?.cancelRefetch) {\n        this.cancel({\n          silent: true\n        });\n      } else if (this.#retryer) {\n        this.#retryer.continueRetry();\n        return this.#retryer.promise;\n      }\n    }\n    if (options) {\n      this.setOptions(options);\n    }\n    if (!this.options.queryFn) {\n      const observer = this.observers.find(x => x.options.queryFn);\n      if (observer) {\n        this.setOptions(observer.options);\n      }\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      if (!Array.isArray(this.options.queryKey)) {\n        console.error(`As of v4, queryKey needs to be an Array. If you are using a string like 'repoData', please change it to an Array, e.g. ['repoData']`);\n      }\n    }\n    const abortController = new AbortController();\n    const addSignalProperty = object => {\n      Object.defineProperty(object, \"signal\", {\n        enumerable: true,\n        get: () => {\n          this.#abortSignalConsumed = true;\n          return abortController.signal;\n        }\n      });\n    };\n    const fetchFn = () => {\n      const queryFn = ensureQueryFn(this.options, fetchOptions);\n      const queryFnContext = {\n        client: this.#client,\n        queryKey: this.queryKey,\n        meta: this.meta\n      };\n      addSignalProperty(queryFnContext);\n      this.#abortSignalConsumed = false;\n      if (this.options.persister) {\n        return this.options.persister(queryFn, queryFnContext, this);\n      }\n      return queryFn(queryFnContext);\n    };\n    const context = {\n      fetchOptions,\n      options: this.options,\n      queryKey: this.queryKey,\n      client: this.#client,\n      state: this.state,\n      fetchFn\n    };\n    addSignalProperty(context);\n    this.options.behavior?.onFetch(context, this);\n    this.#revertState = this.state;\n    if (this.state.fetchStatus === \"idle\" || this.state.fetchMeta !== context.fetchOptions?.meta) {\n      this.#dispatch({\n        type: \"fetch\",\n        meta: context.fetchOptions?.meta\n      });\n    }\n    const onError = error => {\n      if (!(isCancelledError(error) && error.silent)) {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n      if (!isCancelledError(error)) {\n        this.#cache.config.onError?.(error, this);\n        this.#cache.config.onSettled?.(this.state.data, error, this);\n      }\n      this.scheduleGc();\n    };\n    this.#retryer = createRetryer({\n      initialPromise: fetchOptions?.initialPromise,\n      fn: context.fetchFn,\n      abort: abortController.abort.bind(abortController),\n      onSuccess: data => {\n        if (data === void 0) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(`Query data cannot be undefined. Please make sure to return a value other than undefined from your query function. Affected query key: ${this.queryHash}`);\n          }\n          onError(new Error(`${this.queryHash} data is undefined`));\n          return;\n        }\n        try {\n          this.setData(data);\n        } catch (error) {\n          onError(error);\n          return;\n        }\n        this.#cache.config.onSuccess?.(data, this);\n        this.#cache.config.onSettled?.(data, this.state.error, this);\n        this.scheduleGc();\n      },\n      onError,\n      onFail: (failureCount, error) => {\n        this.#dispatch({\n          type: \"failed\",\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.#dispatch({\n          type: \"pause\"\n        });\n      },\n      onContinue: () => {\n        this.#dispatch({\n          type: \"continue\"\n        });\n      },\n      retry: context.options.retry,\n      retryDelay: context.options.retryDelay,\n      networkMode: context.options.networkMode,\n      canRun: () => true\n    });\n    return this.#retryer.start();\n  }\n  #dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            fetchFailureCount: action.failureCount,\n            fetchFailureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            fetchStatus: \"paused\"\n          };\n        case \"continue\":\n          return {\n            ...state,\n            fetchStatus: \"fetching\"\n          };\n        case \"fetch\":\n          return {\n            ...state,\n            ...fetchState(state.data, this.options),\n            fetchMeta: action.meta ?? null\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            dataUpdateCount: state.dataUpdateCount + 1,\n            dataUpdatedAt: action.dataUpdatedAt ?? Date.now(),\n            error: null,\n            isInvalidated: false,\n            status: \"success\",\n            ...(!action.manual && {\n              fetchStatus: \"idle\",\n              fetchFailureCount: 0,\n              fetchFailureReason: null\n            })\n          };\n        case \"error\":\n          const error = action.error;\n          if (isCancelledError(error) && error.revert && this.#revertState) {\n            return {\n              ...this.#revertState,\n              fetchStatus: \"idle\"\n            };\n          }\n          return {\n            ...state,\n            error,\n            errorUpdateCount: state.errorUpdateCount + 1,\n            errorUpdatedAt: Date.now(),\n            fetchFailureCount: state.fetchFailureCount + 1,\n            fetchFailureReason: error,\n            fetchStatus: \"idle\",\n            status: \"error\"\n          };\n        case \"invalidate\":\n          return {\n            ...state,\n            isInvalidated: true\n          };\n        case \"setState\":\n          return {\n            ...state,\n            ...action.state\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.observers.forEach(observer => {\n        observer.onQueryUpdate();\n      });\n      this.#cache.notify({\n        query: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction fetchState(data, options) {\n  return {\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchStatus: canFetch(options.networkMode) ? \"fetching\" : \"paused\",\n    ...(data === void 0 && {\n      error: null,\n      status: \"pending\"\n    })\n  };\n}\nfunction getDefaultState(options) {\n  const data = typeof options.initialData === \"function\" ? options.initialData() : options.initialData;\n  const hasData = data !== void 0;\n  const initialDataUpdatedAt = hasData ? typeof options.initialDataUpdatedAt === \"function\" ? options.initialDataUpdatedAt() : options.initialDataUpdatedAt : 0;\n  return {\n    data,\n    dataUpdateCount: 0,\n    dataUpdatedAt: hasData ? initialDataUpdatedAt ?? Date.now() : 0,\n    error: null,\n    errorUpdateCount: 0,\n    errorUpdatedAt: 0,\n    fetchFailureCount: 0,\n    fetchFailureReason: null,\n    fetchMeta: null,\n    isInvalidated: false,\n    status: hasData ? \"success\" : \"pending\",\n    fetchStatus: \"idle\"\n  };\n}\nexport { Query, fetchState };\n", "// src/queryCache.ts\nimport { hashQueryKeyByOptions, matchQuery } from \"./utils.js\";\nimport { Query } from \"./query.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar QueryCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#queries = /* @__PURE__ */new Map();\n  }\n  #queries;\n  build(client, options, state) {\n    const queryKey = options.queryKey;\n    const queryHash = options.queryHash ?? hashQueryKeyByOptions(queryKey, options);\n    let query = this.get(queryHash);\n    if (!query) {\n      query = new Query({\n        client,\n        queryKey,\n        queryHash,\n        options: client.defaultQueryOptions(options),\n        state,\n        defaultOptions: client.getQueryDefaults(queryKey)\n      });\n      this.add(query);\n    }\n    return query;\n  }\n  add(query) {\n    if (!this.#queries.has(query.queryHash)) {\n      this.#queries.set(query.queryHash, query);\n      this.notify({\n        type: \"added\",\n        query\n      });\n    }\n  }\n  remove(query) {\n    const queryInMap = this.#queries.get(query.queryHash);\n    if (queryInMap) {\n      query.destroy();\n      if (queryInMap === query) {\n        this.#queries.delete(query.queryHash);\n      }\n      this.notify({\n        type: \"removed\",\n        query\n      });\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.getAll().forEach(query => {\n        this.remove(query);\n      });\n    });\n  }\n  get(queryHash) {\n    return this.#queries.get(queryHash);\n  }\n  getAll() {\n    return [...this.#queries.values()];\n  }\n  find(filters) {\n    const defaultedFilters = {\n      exact: true,\n      ...filters\n    };\n    return this.getAll().find(query => matchQuery(defaultedFilters, query));\n  }\n  findAll(filters = {}) {\n    const queries = this.getAll();\n    return Object.keys(filters).length > 0 ? queries.filter(query => matchQuery(filters, query)) : queries;\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(listener => {\n        listener(event);\n      });\n    });\n  }\n  onFocus() {\n    notifyManager.batch(() => {\n      this.getAll().forEach(query => {\n        query.onFocus();\n      });\n    });\n  }\n  onOnline() {\n    notifyManager.batch(() => {\n      this.getAll().forEach(query => {\n        query.onOnline();\n      });\n    });\n  }\n};\nexport { QueryCache };\n", "// src/mutation.ts\nimport { notify<PERSON>anager } from \"./notifyManager.js\";\nimport { Removable } from \"./removable.js\";\nimport { createRetryer } from \"./retryer.js\";\nvar Mutation = class extends Removable {\n  #observers;\n  #mutationCache;\n  #retryer;\n  constructor(config) {\n    super();\n    this.mutationId = config.mutationId;\n    this.#mutationCache = config.mutationCache;\n    this.#observers = [];\n    this.state = config.state || getDefaultState();\n    this.setOptions(config.options);\n    this.scheduleGc();\n  }\n  setOptions(options) {\n    this.options = options;\n    this.updateGcTime(this.options.gcTime);\n  }\n  get meta() {\n    return this.options.meta;\n  }\n  addObserver(observer) {\n    if (!this.#observers.includes(observer)) {\n      this.#observers.push(observer);\n      this.clearGcTimeout();\n      this.#mutationCache.notify({\n        type: \"observerAdded\",\n        mutation: this,\n        observer\n      });\n    }\n  }\n  removeObserver(observer) {\n    this.#observers = this.#observers.filter(x => x !== observer);\n    this.scheduleGc();\n    this.#mutationCache.notify({\n      type: \"observerRemoved\",\n      mutation: this,\n      observer\n    });\n  }\n  optionalRemove() {\n    if (!this.#observers.length) {\n      if (this.state.status === \"pending\") {\n        this.scheduleGc();\n      } else {\n        this.#mutationCache.remove(this);\n      }\n    }\n  }\n  continue() {\n    return this.#retryer?.continue() ??\n    // continuing a mutation assumes that variables are set, mutation must have been dehydrated before\n    this.execute(this.state.variables);\n  }\n  async execute(variables) {\n    const onContinue = () => {\n      this.#dispatch({\n        type: \"continue\"\n      });\n    };\n    this.#retryer = createRetryer({\n      fn: () => {\n        if (!this.options.mutationFn) {\n          return Promise.reject(new Error(\"No mutationFn found\"));\n        }\n        return this.options.mutationFn(variables);\n      },\n      onFail: (failureCount, error) => {\n        this.#dispatch({\n          type: \"failed\",\n          failureCount,\n          error\n        });\n      },\n      onPause: () => {\n        this.#dispatch({\n          type: \"pause\"\n        });\n      },\n      onContinue,\n      retry: this.options.retry ?? 0,\n      retryDelay: this.options.retryDelay,\n      networkMode: this.options.networkMode,\n      canRun: () => this.#mutationCache.canRun(this)\n    });\n    const restored = this.state.status === \"pending\";\n    const isPaused = !this.#retryer.canStart();\n    try {\n      if (restored) {\n        onContinue();\n      } else {\n        this.#dispatch({\n          type: \"pending\",\n          variables,\n          isPaused\n        });\n        await this.#mutationCache.config.onMutate?.(variables, this);\n        const context = await this.options.onMutate?.(variables);\n        if (context !== this.state.context) {\n          this.#dispatch({\n            type: \"pending\",\n            context,\n            variables,\n            isPaused\n          });\n        }\n      }\n      const data = await this.#retryer.start();\n      await this.#mutationCache.config.onSuccess?.(data, variables, this.state.context, this);\n      await this.options.onSuccess?.(data, variables, this.state.context);\n      await this.#mutationCache.config.onSettled?.(data, null, this.state.variables, this.state.context, this);\n      await this.options.onSettled?.(data, null, variables, this.state.context);\n      this.#dispatch({\n        type: \"success\",\n        data\n      });\n      return data;\n    } catch (error) {\n      try {\n        await this.#mutationCache.config.onError?.(error, variables, this.state.context, this);\n        await this.options.onError?.(error, variables, this.state.context);\n        await this.#mutationCache.config.onSettled?.(void 0, error, this.state.variables, this.state.context, this);\n        await this.options.onSettled?.(void 0, error, variables, this.state.context);\n        throw error;\n      } finally {\n        this.#dispatch({\n          type: \"error\",\n          error\n        });\n      }\n    } finally {\n      this.#mutationCache.runNext(this);\n    }\n  }\n  #dispatch(action) {\n    const reducer = state => {\n      switch (action.type) {\n        case \"failed\":\n          return {\n            ...state,\n            failureCount: action.failureCount,\n            failureReason: action.error\n          };\n        case \"pause\":\n          return {\n            ...state,\n            isPaused: true\n          };\n        case \"continue\":\n          return {\n            ...state,\n            isPaused: false\n          };\n        case \"pending\":\n          return {\n            ...state,\n            context: action.context,\n            data: void 0,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            isPaused: action.isPaused,\n            status: \"pending\",\n            variables: action.variables,\n            submittedAt: Date.now()\n          };\n        case \"success\":\n          return {\n            ...state,\n            data: action.data,\n            failureCount: 0,\n            failureReason: null,\n            error: null,\n            status: \"success\",\n            isPaused: false\n          };\n        case \"error\":\n          return {\n            ...state,\n            data: void 0,\n            error: action.error,\n            failureCount: state.failureCount + 1,\n            failureReason: action.error,\n            isPaused: false,\n            status: \"error\"\n          };\n      }\n    };\n    this.state = reducer(this.state);\n    notifyManager.batch(() => {\n      this.#observers.forEach(observer => {\n        observer.onMutationUpdate(action);\n      });\n      this.#mutationCache.notify({\n        mutation: this,\n        type: \"updated\",\n        action\n      });\n    });\n  }\n};\nfunction getDefaultState() {\n  return {\n    context: void 0,\n    data: void 0,\n    error: null,\n    failureCount: 0,\n    failureReason: null,\n    isPaused: false,\n    status: \"idle\",\n    variables: void 0,\n    submittedAt: 0\n  };\n}\nexport { Mutation, getDefaultState };\n", "// src/mutationCache.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Mutation } from \"./mutation.js\";\nimport { matchMutation, noop } from \"./utils.js\";\nimport { Subscribable } from \"./subscribable.js\";\nvar MutationCache = class extends Subscribable {\n  constructor(config = {}) {\n    super();\n    this.config = config;\n    this.#mutations = /* @__PURE__ */new Set();\n    this.#scopes = /* @__PURE__ */new Map();\n    this.#mutationId = 0;\n  }\n  #mutations;\n  #scopes;\n  #mutationId;\n  build(client, options, state) {\n    const mutation = new Mutation({\n      mutationCache: this,\n      mutationId: ++this.#mutationId,\n      options: client.defaultMutationOptions(options),\n      state\n    });\n    this.add(mutation);\n    return mutation;\n  }\n  add(mutation) {\n    this.#mutations.add(mutation);\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const scopedMutations = this.#scopes.get(scope);\n      if (scopedMutations) {\n        scopedMutations.push(mutation);\n      } else {\n        this.#scopes.set(scope, [mutation]);\n      }\n    }\n    this.notify({\n      type: \"added\",\n      mutation\n    });\n  }\n  remove(mutation) {\n    if (this.#mutations.delete(mutation)) {\n      const scope = scopeFor(mutation);\n      if (typeof scope === \"string\") {\n        const scopedMutations = this.#scopes.get(scope);\n        if (scopedMutations) {\n          if (scopedMutations.length > 1) {\n            const index = scopedMutations.indexOf(mutation);\n            if (index !== -1) {\n              scopedMutations.splice(index, 1);\n            }\n          } else if (scopedMutations[0] === mutation) {\n            this.#scopes.delete(scope);\n          }\n        }\n      }\n    }\n    this.notify({\n      type: \"removed\",\n      mutation\n    });\n  }\n  canRun(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const mutationsWithSameScope = this.#scopes.get(scope);\n      const firstPendingMutation = mutationsWithSameScope?.find(m => m.state.status === \"pending\");\n      return !firstPendingMutation || firstPendingMutation === mutation;\n    } else {\n      return true;\n    }\n  }\n  runNext(mutation) {\n    const scope = scopeFor(mutation);\n    if (typeof scope === \"string\") {\n      const foundMutation = this.#scopes.get(scope)?.find(m => m !== mutation && m.state.isPaused);\n      return foundMutation?.continue() ?? Promise.resolve();\n    } else {\n      return Promise.resolve();\n    }\n  }\n  clear() {\n    notifyManager.batch(() => {\n      this.#mutations.forEach(mutation => {\n        this.notify({\n          type: \"removed\",\n          mutation\n        });\n      });\n      this.#mutations.clear();\n      this.#scopes.clear();\n    });\n  }\n  getAll() {\n    return Array.from(this.#mutations);\n  }\n  find(filters) {\n    const defaultedFilters = {\n      exact: true,\n      ...filters\n    };\n    return this.getAll().find(mutation => matchMutation(defaultedFilters, mutation));\n  }\n  findAll(filters = {}) {\n    return this.getAll().filter(mutation => matchMutation(filters, mutation));\n  }\n  notify(event) {\n    notifyManager.batch(() => {\n      this.listeners.forEach(listener => {\n        listener(event);\n      });\n    });\n  }\n  resumePausedMutations() {\n    const pausedMutations = this.getAll().filter(x => x.state.isPaused);\n    return notifyManager.batch(() => Promise.all(pausedMutations.map(mutation => mutation.continue().catch(noop))));\n  }\n};\nfunction scopeFor(mutation) {\n  return mutation.options.scope?.id;\n}\nexport { MutationCache };\n", "// src/infiniteQueryBehavior.ts\nimport { addToEnd, addToStart, ensureQueryFn } from \"./utils.js\";\nfunction infiniteQueryBehavior(pages) {\n  return {\n    onFetch: (context, query) => {\n      const options = context.options;\n      const direction = context.fetchOptions?.meta?.fetchMore?.direction;\n      const oldPages = context.state.data?.pages || [];\n      const oldPageParams = context.state.data?.pageParams || [];\n      let result = {\n        pages: [],\n        pageParams: []\n      };\n      let currentPage = 0;\n      const fetchFn = async () => {\n        let cancelled = false;\n        const addSignalProperty = object => {\n          Object.defineProperty(object, \"signal\", {\n            enumerable: true,\n            get: () => {\n              if (context.signal.aborted) {\n                cancelled = true;\n              } else {\n                context.signal.addEventListener(\"abort\", () => {\n                  cancelled = true;\n                });\n              }\n              return context.signal;\n            }\n          });\n        };\n        const queryFn = ensureQueryFn(context.options, context.fetchOptions);\n        const fetchPage = async (data, param, previous) => {\n          if (cancelled) {\n            return Promise.reject();\n          }\n          if (param == null && data.pages.length) {\n            return Promise.resolve(data);\n          }\n          const queryFnContext = {\n            client: context.client,\n            queryKey: context.queryKey,\n            pageParam: param,\n            direction: previous ? \"backward\" : \"forward\",\n            meta: context.options.meta\n          };\n          addSignalProperty(queryFnContext);\n          const page = await queryFn(queryFnContext);\n          const {\n            maxPages\n          } = context.options;\n          const addTo = previous ? addToStart : addToEnd;\n          return {\n            pages: addTo(data.pages, page, maxPages),\n            pageParams: addTo(data.pageParams, param, maxPages)\n          };\n        };\n        if (direction && oldPages.length) {\n          const previous = direction === \"backward\";\n          const pageParamFn = previous ? getPreviousPageParam : getNextPageParam;\n          const oldData = {\n            pages: oldPages,\n            pageParams: oldPageParams\n          };\n          const param = pageParamFn(options, oldData);\n          result = await fetchPage(oldData, param, previous);\n        } else {\n          const remainingPages = pages ?? oldPages.length;\n          do {\n            const param = currentPage === 0 ? oldPageParams[0] ?? options.initialPageParam : getNextPageParam(options, result);\n            if (currentPage > 0 && param == null) {\n              break;\n            }\n            result = await fetchPage(result, param);\n            currentPage++;\n          } while (currentPage < remainingPages);\n        }\n        return result;\n      };\n      if (context.options.persister) {\n        context.fetchFn = () => {\n          return context.options.persister?.(fetchFn, {\n            client: context.client,\n            queryKey: context.queryKey,\n            meta: context.options.meta,\n            signal: context.signal\n          }, query);\n        };\n      } else {\n        context.fetchFn = fetchFn;\n      }\n    }\n  };\n}\nfunction getNextPageParam(options, {\n  pages,\n  pageParams\n}) {\n  const lastIndex = pages.length - 1;\n  return pages.length > 0 ? options.getNextPageParam(pages[lastIndex], pages, pageParams[lastIndex], pageParams) : void 0;\n}\nfunction getPreviousPageParam(options, {\n  pages,\n  pageParams\n}) {\n  return pages.length > 0 ? options.getPreviousPageParam?.(pages[0], pages, pageParams[0], pageParams) : void 0;\n}\nfunction hasNextPage(options, data) {\n  if (!data) return false;\n  return getNextPageParam(options, data) != null;\n}\nfunction hasPreviousPage(options, data) {\n  if (!data || !options.getPreviousPageParam) return false;\n  return getPreviousPageParam(options, data) != null;\n}\nexport { hasNextPage, hasPreviousPage, infiniteQueryBehavior };\n", "// src/queryClient.ts\nimport { functionalUpdate, hashKey, hashQueryKeyByOptions, noop, partialMatchKey, resolveStaleTime, skipToken } from \"./utils.js\";\nimport { QueryCache } from \"./queryCache.js\";\nimport { MutationCache } from \"./mutationCache.js\";\nimport { focusManager } from \"./focusManager.js\";\nimport { onlineManager } from \"./onlineManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar QueryClient = class {\n  #queryCache;\n  #mutationCache;\n  #defaultOptions;\n  #queryDefaults;\n  #mutationDefaults;\n  #mountCount;\n  #unsubscribeFocus;\n  #unsubscribeOnline;\n  constructor(config = {}) {\n    this.#queryCache = config.queryCache || new QueryCache();\n    this.#mutationCache = config.mutationCache || new MutationCache();\n    this.#defaultOptions = config.defaultOptions || {};\n    this.#queryDefaults = /* @__PURE__ */new Map();\n    this.#mutationDefaults = /* @__PURE__ */new Map();\n    this.#mountCount = 0;\n  }\n  mount() {\n    this.#mountCount++;\n    if (this.#mountCount !== 1) return;\n    this.#unsubscribeFocus = focusManager.subscribe(async focused => {\n      if (focused) {\n        await this.resumePausedMutations();\n        this.#queryCache.onFocus();\n      }\n    });\n    this.#unsubscribeOnline = onlineManager.subscribe(async online => {\n      if (online) {\n        await this.resumePausedMutations();\n        this.#queryCache.onOnline();\n      }\n    });\n  }\n  unmount() {\n    this.#mountCount--;\n    if (this.#mountCount !== 0) return;\n    this.#unsubscribeFocus?.();\n    this.#unsubscribeFocus = void 0;\n    this.#unsubscribeOnline?.();\n    this.#unsubscribeOnline = void 0;\n  }\n  isFetching(filters) {\n    return this.#queryCache.findAll({\n      ...filters,\n      fetchStatus: \"fetching\"\n    }).length;\n  }\n  isMutating(filters) {\n    return this.#mutationCache.findAll({\n      ...filters,\n      status: \"pending\"\n    }).length;\n  }\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData(queryKey) {\n    const options = this.defaultQueryOptions({\n      queryKey\n    });\n    return this.#queryCache.get(options.queryHash)?.state.data;\n  }\n  ensureQueryData(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    const query = this.#queryCache.build(this, defaultedOptions);\n    const cachedData = query.state.data;\n    if (cachedData === void 0) {\n      return this.fetchQuery(options);\n    }\n    if (options.revalidateIfStale && query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))) {\n      void this.prefetchQuery(defaultedOptions);\n    }\n    return Promise.resolve(cachedData);\n  }\n  getQueriesData(filters) {\n    return this.#queryCache.findAll(filters).map(({\n      queryKey,\n      state\n    }) => {\n      const data = state.data;\n      return [queryKey, data];\n    });\n  }\n  setQueryData(queryKey, updater, options) {\n    const defaultedOptions = this.defaultQueryOptions({\n      queryKey\n    });\n    const query = this.#queryCache.get(defaultedOptions.queryHash);\n    const prevData = query?.state.data;\n    const data = functionalUpdate(updater, prevData);\n    if (data === void 0) {\n      return void 0;\n    }\n    return this.#queryCache.build(this, defaultedOptions).setData(data, {\n      ...options,\n      manual: true\n    });\n  }\n  setQueriesData(filters, updater, options) {\n    return notifyManager.batch(() => this.#queryCache.findAll(filters).map(({\n      queryKey\n    }) => [queryKey, this.setQueryData(queryKey, updater, options)]));\n  }\n  getQueryState(queryKey) {\n    const options = this.defaultQueryOptions({\n      queryKey\n    });\n    return this.#queryCache.get(options.queryHash)?.state;\n  }\n  removeQueries(filters) {\n    const queryCache = this.#queryCache;\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        queryCache.remove(query);\n      });\n    });\n  }\n  resetQueries(filters, options) {\n    const queryCache = this.#queryCache;\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach(query => {\n        query.reset();\n      });\n      return this.refetchQueries({\n        type: \"active\",\n        ...filters\n      }, options);\n    });\n  }\n  cancelQueries(filters, cancelOptions = {}) {\n    const defaultedCancelOptions = {\n      revert: true,\n      ...cancelOptions\n    };\n    const promises = notifyManager.batch(() => this.#queryCache.findAll(filters).map(query => query.cancel(defaultedCancelOptions)));\n    return Promise.all(promises).then(noop).catch(noop);\n  }\n  invalidateQueries(filters, options = {}) {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach(query => {\n        query.invalidate();\n      });\n      if (filters?.refetchType === \"none\") {\n        return Promise.resolve();\n      }\n      return this.refetchQueries({\n        ...filters,\n        type: filters?.refetchType ?? filters?.type ?? \"active\"\n      }, options);\n    });\n  }\n  refetchQueries(filters, options = {}) {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true\n    };\n    const promises = notifyManager.batch(() => this.#queryCache.findAll(filters).filter(query => !query.isDisabled()).map(query => {\n      let promise = query.fetch(void 0, fetchOptions);\n      if (!fetchOptions.throwOnError) {\n        promise = promise.catch(noop);\n      }\n      return query.state.fetchStatus === \"paused\" ? Promise.resolve() : promise;\n    }));\n    return Promise.all(promises).then(noop);\n  }\n  fetchQuery(options) {\n    const defaultedOptions = this.defaultQueryOptions(options);\n    if (defaultedOptions.retry === void 0) {\n      defaultedOptions.retry = false;\n    }\n    const query = this.#queryCache.build(this, defaultedOptions);\n    return query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query)) ? query.fetch(defaultedOptions) : Promise.resolve(query.state.data);\n  }\n  prefetchQuery(options) {\n    return this.fetchQuery(options).then(noop).catch(noop);\n  }\n  fetchInfiniteQuery(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.fetchQuery(options);\n  }\n  prefetchInfiniteQuery(options) {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop);\n  }\n  ensureInfiniteQueryData(options) {\n    options.behavior = infiniteQueryBehavior(options.pages);\n    return this.ensureQueryData(options);\n  }\n  resumePausedMutations() {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations();\n    }\n    return Promise.resolve();\n  }\n  getQueryCache() {\n    return this.#queryCache;\n  }\n  getMutationCache() {\n    return this.#mutationCache;\n  }\n  getDefaultOptions() {\n    return this.#defaultOptions;\n  }\n  setDefaultOptions(options) {\n    this.#defaultOptions = options;\n  }\n  setQueryDefaults(queryKey, options) {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options\n    });\n  }\n  getQueryDefaults(queryKey) {\n    const defaults = [...this.#queryDefaults.values()];\n    const result = {};\n    defaults.forEach(queryDefault => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  setMutationDefaults(mutationKey, options) {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options\n    });\n  }\n  getMutationDefaults(mutationKey) {\n    const defaults = [...this.#mutationDefaults.values()];\n    const result = {};\n    defaults.forEach(queryDefault => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions);\n      }\n    });\n    return result;\n  }\n  defaultQueryOptions(options) {\n    if (options._defaulted) {\n      return options;\n    }\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true\n    };\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(defaultedOptions.queryKey, defaultedOptions);\n    }\n    if (defaultedOptions.refetchOnReconnect === void 0) {\n      defaultedOptions.refetchOnReconnect = defaultedOptions.networkMode !== \"always\";\n    }\n    if (defaultedOptions.throwOnError === void 0) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense;\n    }\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = \"offlineFirst\";\n    }\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false;\n    }\n    return defaultedOptions;\n  }\n  defaultMutationOptions(options) {\n    if (options?._defaulted) {\n      return options;\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...(options?.mutationKey && this.getMutationDefaults(options.mutationKey)),\n      ...options,\n      _defaulted: true\n    };\n  }\n  clear() {\n    this.#queryCache.clear();\n    this.#mutationCache.clear();\n  }\n};\nexport { QueryClient };\n", "// src/queryObserver.ts\nimport { focusManager } from \"./focusManager.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { fetchState } from \"./query.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { pendingThenable } from \"./thenable.js\";\nimport { isServer, isValidTimeout, noop, replaceData, resolveEnabled, resolveStaleTime, shallowEqualObjects, timeUntilStale } from \"./utils.js\";\nvar QueryObserver = class extends Subscribable {\n  constructor(client, options) {\n    super();\n    this.options = options;\n    this.#client = client;\n    this.#selectError = null;\n    this.#currentThenable = pendingThenable();\n    if (!this.options.experimental_prefetchInRender) {\n      this.#currentThenable.reject(new Error(\"experimental_prefetchInRender feature flag is not enabled\"));\n    }\n    this.bindMethods();\n    this.setOptions(options);\n  }\n  #client;\n  #currentQuery = void 0;\n  #currentQueryInitialState = void 0;\n  #currentResult = void 0;\n  #currentResultState;\n  #currentResultOptions;\n  #currentThenable;\n  #selectError;\n  #selectFn;\n  #selectResult;\n  // This property keeps track of the last query with defined data.\n  // It will be used to pass the previous data and query to the placeholder function between renders.\n  #lastQueryWithDefinedData;\n  #staleTimeoutId;\n  #refetchIntervalId;\n  #currentRefetchInterval;\n  #trackedProps = /* @__PURE__ */new Set();\n  bindMethods() {\n    this.refetch = this.refetch.bind(this);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#currentQuery.addObserver(this);\n      if (shouldFetchOnMount(this.#currentQuery, this.options)) {\n        this.#executeFetch();\n      } else {\n        this.updateResult();\n      }\n      this.#updateTimers();\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.destroy();\n    }\n  }\n  shouldFetchOnReconnect() {\n    return shouldFetchOn(this.#currentQuery, this.options, this.options.refetchOnReconnect);\n  }\n  shouldFetchOnWindowFocus() {\n    return shouldFetchOn(this.#currentQuery, this.options, this.options.refetchOnWindowFocus);\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */new Set();\n    this.#clearStaleTimeout();\n    this.#clearRefetchInterval();\n    this.#currentQuery.removeObserver(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    const prevQuery = this.#currentQuery;\n    this.options = this.#client.defaultQueryOptions(options);\n    if (this.options.enabled !== void 0 && typeof this.options.enabled !== \"boolean\" && typeof this.options.enabled !== \"function\" && typeof resolveEnabled(this.options.enabled, this.#currentQuery) !== \"boolean\") {\n      throw new Error(\"Expected enabled to be a boolean or a callback that returns a boolean\");\n    }\n    this.#updateQuery();\n    this.#currentQuery.setOptions(this.options);\n    if (prevOptions._defaulted && !shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getQueryCache().notify({\n        type: \"observerOptionsUpdated\",\n        query: this.#currentQuery,\n        observer: this\n      });\n    }\n    const mounted = this.hasListeners();\n    if (mounted && shouldFetchOptionally(this.#currentQuery, prevQuery, this.options, prevOptions)) {\n      this.#executeFetch();\n    }\n    this.updateResult();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || resolveStaleTime(this.options.staleTime, this.#currentQuery) !== resolveStaleTime(prevOptions.staleTime, this.#currentQuery))) {\n      this.#updateStaleTimeout();\n    }\n    const nextRefetchInterval = this.#computeRefetchInterval();\n    if (mounted && (this.#currentQuery !== prevQuery || resolveEnabled(this.options.enabled, this.#currentQuery) !== resolveEnabled(prevOptions.enabled, this.#currentQuery) || nextRefetchInterval !== this.#currentRefetchInterval)) {\n      this.#updateRefetchInterval(nextRefetchInterval);\n    }\n  }\n  getOptimisticResult(options) {\n    const query = this.#client.getQueryCache().build(this.#client, options);\n    const result = this.createResult(query, options);\n    if (shouldAssignObserverCurrentProperties(this, result)) {\n      this.#currentResult = result;\n      this.#currentResultOptions = this.options;\n      this.#currentResultState = this.#currentQuery.state;\n    }\n    return result;\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  trackResult(result, onPropTracked) {\n    return new Proxy(result, {\n      get: (target, key) => {\n        this.trackProp(key);\n        onPropTracked?.(key);\n        return Reflect.get(target, key);\n      }\n    });\n  }\n  trackProp(key) {\n    this.#trackedProps.add(key);\n  }\n  getCurrentQuery() {\n    return this.#currentQuery;\n  }\n  refetch({\n    ...options\n  } = {}) {\n    return this.fetch({\n      ...options\n    });\n  }\n  fetchOptimistic(options) {\n    const defaultedOptions = this.#client.defaultQueryOptions(options);\n    const query = this.#client.getQueryCache().build(this.#client, defaultedOptions);\n    return query.fetch().then(() => this.createResult(query, defaultedOptions));\n  }\n  fetch(fetchOptions) {\n    return this.#executeFetch({\n      ...fetchOptions,\n      cancelRefetch: fetchOptions.cancelRefetch ?? true\n    }).then(() => {\n      this.updateResult();\n      return this.#currentResult;\n    });\n  }\n  #executeFetch(fetchOptions) {\n    this.#updateQuery();\n    let promise = this.#currentQuery.fetch(this.options, fetchOptions);\n    if (!fetchOptions?.throwOnError) {\n      promise = promise.catch(noop);\n    }\n    return promise;\n  }\n  #updateStaleTimeout() {\n    this.#clearStaleTimeout();\n    const staleTime = resolveStaleTime(this.options.staleTime, this.#currentQuery);\n    if (isServer || this.#currentResult.isStale || !isValidTimeout(staleTime)) {\n      return;\n    }\n    const time = timeUntilStale(this.#currentResult.dataUpdatedAt, staleTime);\n    const timeout = time + 1;\n    this.#staleTimeoutId = setTimeout(() => {\n      if (!this.#currentResult.isStale) {\n        this.updateResult();\n      }\n    }, timeout);\n  }\n  #computeRefetchInterval() {\n    return (typeof this.options.refetchInterval === \"function\" ? this.options.refetchInterval(this.#currentQuery) : this.options.refetchInterval) ?? false;\n  }\n  #updateRefetchInterval(nextInterval) {\n    this.#clearRefetchInterval();\n    this.#currentRefetchInterval = nextInterval;\n    if (isServer || resolveEnabled(this.options.enabled, this.#currentQuery) === false || !isValidTimeout(this.#currentRefetchInterval) || this.#currentRefetchInterval === 0) {\n      return;\n    }\n    this.#refetchIntervalId = setInterval(() => {\n      if (this.options.refetchIntervalInBackground || focusManager.isFocused()) {\n        this.#executeFetch();\n      }\n    }, this.#currentRefetchInterval);\n  }\n  #updateTimers() {\n    this.#updateStaleTimeout();\n    this.#updateRefetchInterval(this.#computeRefetchInterval());\n  }\n  #clearStaleTimeout() {\n    if (this.#staleTimeoutId) {\n      clearTimeout(this.#staleTimeoutId);\n      this.#staleTimeoutId = void 0;\n    }\n  }\n  #clearRefetchInterval() {\n    if (this.#refetchIntervalId) {\n      clearInterval(this.#refetchIntervalId);\n      this.#refetchIntervalId = void 0;\n    }\n  }\n  createResult(query, options) {\n    const prevQuery = this.#currentQuery;\n    const prevOptions = this.options;\n    const prevResult = this.#currentResult;\n    const prevResultState = this.#currentResultState;\n    const prevResultOptions = this.#currentResultOptions;\n    const queryChange = query !== prevQuery;\n    const queryInitialState = queryChange ? query.state : this.#currentQueryInitialState;\n    const {\n      state\n    } = query;\n    let newState = {\n      ...state\n    };\n    let isPlaceholderData = false;\n    let data;\n    if (options._optimisticResults) {\n      const mounted = this.hasListeners();\n      const fetchOnMount = !mounted && shouldFetchOnMount(query, options);\n      const fetchOptionally = mounted && shouldFetchOptionally(query, prevQuery, options, prevOptions);\n      if (fetchOnMount || fetchOptionally) {\n        newState = {\n          ...newState,\n          ...fetchState(state.data, query.options)\n        };\n      }\n      if (options._optimisticResults === \"isRestoring\") {\n        newState.fetchStatus = \"idle\";\n      }\n    }\n    let {\n      error,\n      errorUpdatedAt,\n      status\n    } = newState;\n    data = newState.data;\n    let skipSelect = false;\n    if (options.placeholderData !== void 0 && data === void 0 && status === \"pending\") {\n      let placeholderData;\n      if (prevResult?.isPlaceholderData && options.placeholderData === prevResultOptions?.placeholderData) {\n        placeholderData = prevResult.data;\n        skipSelect = true;\n      } else {\n        placeholderData = typeof options.placeholderData === \"function\" ? options.placeholderData(this.#lastQueryWithDefinedData?.state.data, this.#lastQueryWithDefinedData) : options.placeholderData;\n      }\n      if (placeholderData !== void 0) {\n        status = \"success\";\n        data = replaceData(prevResult?.data, placeholderData, options);\n        isPlaceholderData = true;\n      }\n    }\n    if (options.select && data !== void 0 && !skipSelect) {\n      if (prevResult && data === prevResultState?.data && options.select === this.#selectFn) {\n        data = this.#selectResult;\n      } else {\n        try {\n          this.#selectFn = options.select;\n          data = options.select(data);\n          data = replaceData(prevResult?.data, data, options);\n          this.#selectResult = data;\n          this.#selectError = null;\n        } catch (selectError) {\n          this.#selectError = selectError;\n        }\n      }\n    }\n    if (this.#selectError) {\n      error = this.#selectError;\n      data = this.#selectResult;\n      errorUpdatedAt = Date.now();\n      status = \"error\";\n    }\n    const isFetching = newState.fetchStatus === \"fetching\";\n    const isPending = status === \"pending\";\n    const isError = status === \"error\";\n    const isLoading = isPending && isFetching;\n    const hasData = data !== void 0;\n    const result = {\n      status,\n      fetchStatus: newState.fetchStatus,\n      isPending,\n      isSuccess: status === \"success\",\n      isError,\n      isInitialLoading: isLoading,\n      isLoading,\n      data,\n      dataUpdatedAt: newState.dataUpdatedAt,\n      error,\n      errorUpdatedAt,\n      failureCount: newState.fetchFailureCount,\n      failureReason: newState.fetchFailureReason,\n      errorUpdateCount: newState.errorUpdateCount,\n      isFetched: newState.dataUpdateCount > 0 || newState.errorUpdateCount > 0,\n      isFetchedAfterMount: newState.dataUpdateCount > queryInitialState.dataUpdateCount || newState.errorUpdateCount > queryInitialState.errorUpdateCount,\n      isFetching,\n      isRefetching: isFetching && !isPending,\n      isLoadingError: isError && !hasData,\n      isPaused: newState.fetchStatus === \"paused\",\n      isPlaceholderData,\n      isRefetchError: isError && hasData,\n      isStale: isStale(query, options),\n      refetch: this.refetch,\n      promise: this.#currentThenable\n    };\n    const nextResult = result;\n    if (this.options.experimental_prefetchInRender) {\n      const finalizeThenableIfPossible = thenable => {\n        if (nextResult.status === \"error\") {\n          thenable.reject(nextResult.error);\n        } else if (nextResult.data !== void 0) {\n          thenable.resolve(nextResult.data);\n        }\n      };\n      const recreateThenable = () => {\n        const pending = this.#currentThenable = nextResult.promise = pendingThenable();\n        finalizeThenableIfPossible(pending);\n      };\n      const prevThenable = this.#currentThenable;\n      switch (prevThenable.status) {\n        case \"pending\":\n          if (query.queryHash === prevQuery.queryHash) {\n            finalizeThenableIfPossible(prevThenable);\n          }\n          break;\n        case \"fulfilled\":\n          if (nextResult.status === \"error\" || nextResult.data !== prevThenable.value) {\n            recreateThenable();\n          }\n          break;\n        case \"rejected\":\n          if (nextResult.status !== \"error\" || nextResult.error !== prevThenable.reason) {\n            recreateThenable();\n          }\n          break;\n      }\n    }\n    return nextResult;\n  }\n  updateResult() {\n    const prevResult = this.#currentResult;\n    const nextResult = this.createResult(this.#currentQuery, this.options);\n    this.#currentResultState = this.#currentQuery.state;\n    this.#currentResultOptions = this.options;\n    if (this.#currentResultState.data !== void 0) {\n      this.#lastQueryWithDefinedData = this.#currentQuery;\n    }\n    if (shallowEqualObjects(nextResult, prevResult)) {\n      return;\n    }\n    this.#currentResult = nextResult;\n    const shouldNotifyListeners = () => {\n      if (!prevResult) {\n        return true;\n      }\n      const {\n        notifyOnChangeProps\n      } = this.options;\n      const notifyOnChangePropsValue = typeof notifyOnChangeProps === \"function\" ? notifyOnChangeProps() : notifyOnChangeProps;\n      if (notifyOnChangePropsValue === \"all\" || !notifyOnChangePropsValue && !this.#trackedProps.size) {\n        return true;\n      }\n      const includedProps = new Set(notifyOnChangePropsValue ?? this.#trackedProps);\n      if (this.options.throwOnError) {\n        includedProps.add(\"error\");\n      }\n      return Object.keys(this.#currentResult).some(key => {\n        const typedKey = key;\n        const changed = this.#currentResult[typedKey] !== prevResult[typedKey];\n        return changed && includedProps.has(typedKey);\n      });\n    };\n    this.#notify({\n      listeners: shouldNotifyListeners()\n    });\n  }\n  #updateQuery() {\n    const query = this.#client.getQueryCache().build(this.#client, this.options);\n    if (query === this.#currentQuery) {\n      return;\n    }\n    const prevQuery = this.#currentQuery;\n    this.#currentQuery = query;\n    this.#currentQueryInitialState = query.state;\n    if (this.hasListeners()) {\n      prevQuery?.removeObserver(this);\n      query.addObserver(this);\n    }\n  }\n  onQueryUpdate() {\n    this.updateResult();\n    if (this.hasListeners()) {\n      this.#updateTimers();\n    }\n  }\n  #notify(notifyOptions) {\n    notifyManager.batch(() => {\n      if (notifyOptions.listeners) {\n        this.listeners.forEach(listener => {\n          listener(this.#currentResult);\n        });\n      }\n      this.#client.getQueryCache().notify({\n        query: this.#currentQuery,\n        type: \"observerResultsUpdated\"\n      });\n    });\n  }\n};\nfunction shouldLoadOnMount(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.state.data === void 0 && !(query.state.status === \"error\" && options.retryOnMount === false);\n}\nfunction shouldFetchOnMount(query, options) {\n  return shouldLoadOnMount(query, options) || query.state.data !== void 0 && shouldFetchOn(query, options, options.refetchOnMount);\n}\nfunction shouldFetchOn(query, options, field) {\n  if (resolveEnabled(options.enabled, query) !== false) {\n    const value = typeof field === \"function\" ? field(query) : field;\n    return value === \"always\" || value !== false && isStale(query, options);\n  }\n  return false;\n}\nfunction shouldFetchOptionally(query, prevQuery, options, prevOptions) {\n  return (query !== prevQuery || resolveEnabled(prevOptions.enabled, query) === false) && (!options.suspense || query.state.status !== \"error\") && isStale(query, options);\n}\nfunction isStale(query, options) {\n  return resolveEnabled(options.enabled, query) !== false && query.isStaleByTime(resolveStaleTime(options.staleTime, query));\n}\nfunction shouldAssignObserverCurrentProperties(observer, optimisticResult) {\n  if (!shallowEqualObjects(observer.getCurrentResult(), optimisticResult)) {\n    return true;\n  }\n  return false;\n}\nexport { QueryObserver };\n", "// src/queriesObserver.ts\nimport { notifyManager } from \"./notifyManager.js\";\nimport { QueryObserver } from \"./queryObserver.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { replaceEqualDeep } from \"./utils.js\";\nfunction difference(array1, array2) {\n  return array1.filter(x => !array2.includes(x));\n}\nfunction replaceAt(array, index, value) {\n  const copy = array.slice(0);\n  copy[index] = value;\n  return copy;\n}\nvar QueriesObserver = class extends Subscribable {\n  #client;\n  #result;\n  #queries;\n  #options;\n  #observers;\n  #combinedResult;\n  #lastCombine;\n  #lastResult;\n  #observerMatches = [];\n  constructor(client, queries, options) {\n    super();\n    this.#client = client;\n    this.#options = options;\n    this.#queries = [];\n    this.#observers = [];\n    this.#result = [];\n    this.setQueries(queries);\n  }\n  onSubscribe() {\n    if (this.listeners.size === 1) {\n      this.#observers.forEach(observer => {\n        observer.subscribe(result => {\n          this.#onUpdate(observer, result);\n        });\n      });\n    }\n  }\n  onUnsubscribe() {\n    if (!this.listeners.size) {\n      this.destroy();\n    }\n  }\n  destroy() {\n    this.listeners = /* @__PURE__ */new Set();\n    this.#observers.forEach(observer => {\n      observer.destroy();\n    });\n  }\n  setQueries(queries, options) {\n    this.#queries = queries;\n    this.#options = options;\n    if (process.env.NODE_ENV !== \"production\") {\n      const queryHashes = queries.map(query => this.#client.defaultQueryOptions(query).queryHash);\n      if (new Set(queryHashes).size !== queryHashes.length) {\n        console.warn(\"[QueriesObserver]: Duplicate Queries found. This might result in unexpected behavior.\");\n      }\n    }\n    notifyManager.batch(() => {\n      const prevObservers = this.#observers;\n      const newObserverMatches = this.#findMatchingObservers(this.#queries);\n      this.#observerMatches = newObserverMatches;\n      newObserverMatches.forEach(match => match.observer.setOptions(match.defaultedQueryOptions));\n      const newObservers = newObserverMatches.map(match => match.observer);\n      const newResult = newObservers.map(observer => observer.getCurrentResult());\n      const hasIndexChange = newObservers.some((observer, index) => observer !== prevObservers[index]);\n      if (prevObservers.length === newObservers.length && !hasIndexChange) {\n        return;\n      }\n      this.#observers = newObservers;\n      this.#result = newResult;\n      if (!this.hasListeners()) {\n        return;\n      }\n      difference(prevObservers, newObservers).forEach(observer => {\n        observer.destroy();\n      });\n      difference(newObservers, prevObservers).forEach(observer => {\n        observer.subscribe(result => {\n          this.#onUpdate(observer, result);\n        });\n      });\n      this.#notify();\n    });\n  }\n  getCurrentResult() {\n    return this.#result;\n  }\n  getQueries() {\n    return this.#observers.map(observer => observer.getCurrentQuery());\n  }\n  getObservers() {\n    return this.#observers;\n  }\n  getOptimisticResult(queries, combine) {\n    const matches = this.#findMatchingObservers(queries);\n    const result = matches.map(match => match.observer.getOptimisticResult(match.defaultedQueryOptions));\n    return [result, r => {\n      return this.#combineResult(r ?? result, combine);\n    }, () => {\n      return this.#trackResult(result, matches);\n    }];\n  }\n  #trackResult(result, matches) {\n    return matches.map((match, index) => {\n      const observerResult = result[index];\n      return !match.defaultedQueryOptions.notifyOnChangeProps ? match.observer.trackResult(observerResult, accessedProp => {\n        matches.forEach(m => {\n          m.observer.trackProp(accessedProp);\n        });\n      }) : observerResult;\n    });\n  }\n  #combineResult(input, combine) {\n    if (combine) {\n      if (!this.#combinedResult || this.#result !== this.#lastResult || combine !== this.#lastCombine) {\n        this.#lastCombine = combine;\n        this.#lastResult = this.#result;\n        this.#combinedResult = replaceEqualDeep(this.#combinedResult, combine(input));\n      }\n      return this.#combinedResult;\n    }\n    return input;\n  }\n  #findMatchingObservers(queries) {\n    const prevObserversMap = new Map(this.#observers.map(observer => [observer.options.queryHash, observer]));\n    const observers = [];\n    queries.forEach(options => {\n      const defaultedOptions = this.#client.defaultQueryOptions(options);\n      const match = prevObserversMap.get(defaultedOptions.queryHash);\n      if (match) {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: match\n        });\n      } else {\n        observers.push({\n          defaultedQueryOptions: defaultedOptions,\n          observer: new QueryObserver(this.#client, defaultedOptions)\n        });\n      }\n    });\n    return observers;\n  }\n  #onUpdate(observer, result) {\n    const index = this.#observers.indexOf(observer);\n    if (index !== -1) {\n      this.#result = replaceAt(this.#result, index, result);\n      this.#notify();\n    }\n  }\n  #notify() {\n    if (this.hasListeners()) {\n      const previousResult = this.#combinedResult;\n      const newTracked = this.#trackResult(this.#result, this.#observerMatches);\n      const newResult = this.#combineResult(newTracked, this.#options?.combine);\n      if (previousResult !== newResult) {\n        notifyManager.batch(() => {\n          this.listeners.forEach(listener => {\n            listener(this.#result);\n          });\n        });\n      }\n    }\n  }\n};\nexport { QueriesObserver };\n", "// src/infiniteQueryObserver.ts\nimport { QueryObserver } from \"./queryObserver.js\";\nimport { hasNextPage, hasPreviousPage, infiniteQueryBehavior } from \"./infiniteQueryBehavior.js\";\nvar InfiniteQueryObserver = class extends QueryObserver {\n  constructor(client, options) {\n    super(client, options);\n  }\n  bindMethods() {\n    super.bindMethods();\n    this.fetchNextPage = this.fetchNextPage.bind(this);\n    this.fetchPreviousPage = this.fetchPreviousPage.bind(this);\n  }\n  setOptions(options) {\n    super.setOptions({\n      ...options,\n      behavior: infiniteQueryBehavior()\n    });\n  }\n  getOptimisticResult(options) {\n    options.behavior = infiniteQueryBehavior();\n    return super.getOptimisticResult(options);\n  }\n  fetchNextPage(options) {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: {\n          direction: \"forward\"\n        }\n      }\n    });\n  }\n  fetchPreviousPage(options) {\n    return this.fetch({\n      ...options,\n      meta: {\n        fetchMore: {\n          direction: \"backward\"\n        }\n      }\n    });\n  }\n  createResult(query, options) {\n    const {\n      state\n    } = query;\n    const parentResult = super.createResult(query, options);\n    const {\n      isFetching,\n      isRefetching,\n      isError,\n      isRefetchError\n    } = parentResult;\n    const fetchDirection = state.fetchMeta?.fetchMore?.direction;\n    const isFetchNextPageError = isError && fetchDirection === \"forward\";\n    const isFetchingNextPage = isFetching && fetchDirection === \"forward\";\n    const isFetchPreviousPageError = isError && fetchDirection === \"backward\";\n    const isFetchingPreviousPage = isFetching && fetchDirection === \"backward\";\n    const result = {\n      ...parentResult,\n      fetchNextPage: this.fetchNextPage,\n      fetchPreviousPage: this.fetchPreviousPage,\n      hasNextPage: hasNextPage(options, state.data),\n      hasPreviousPage: hasPreviousPage(options, state.data),\n      isFetchNextPageError,\n      isFetchingNextPage,\n      isFetchPreviousPageError,\n      isFetchingPreviousPage,\n      isRefetchError: isRefetchError && !isFetchNextPageError && !isFetchPreviousPageError,\n      isRefetching: isRefetching && !isFetchingNextPage && !isFetchingPreviousPage\n    };\n    return result;\n  }\n};\nexport { InfiniteQueryObserver };\n", "// src/mutationObserver.ts\nimport { getDefaultState } from \"./mutation.js\";\nimport { notifyManager } from \"./notifyManager.js\";\nimport { Subscribable } from \"./subscribable.js\";\nimport { hashKey, shallowEqualObjects } from \"./utils.js\";\nvar MutationObserver = class extends Subscribable {\n  #client;\n  #currentResult = void 0;\n  #currentMutation;\n  #mutateOptions;\n  constructor(client, options) {\n    super();\n    this.#client = client;\n    this.setOptions(options);\n    this.bindMethods();\n    this.#updateResult();\n  }\n  bindMethods() {\n    this.mutate = this.mutate.bind(this);\n    this.reset = this.reset.bind(this);\n  }\n  setOptions(options) {\n    const prevOptions = this.options;\n    this.options = this.#client.defaultMutationOptions(options);\n    if (!shallowEqualObjects(this.options, prevOptions)) {\n      this.#client.getMutationCache().notify({\n        type: \"observerOptionsUpdated\",\n        mutation: this.#currentMutation,\n        observer: this\n      });\n    }\n    if (prevOptions?.mutationKey && this.options.mutationKey && hashKey(prevOptions.mutationKey) !== hashKey(this.options.mutationKey)) {\n      this.reset();\n    } else if (this.#currentMutation?.state.status === \"pending\") {\n      this.#currentMutation.setOptions(this.options);\n    }\n  }\n  onUnsubscribe() {\n    if (!this.hasListeners()) {\n      this.#currentMutation?.removeObserver(this);\n    }\n  }\n  onMutationUpdate(action) {\n    this.#updateResult();\n    this.#notify(action);\n  }\n  getCurrentResult() {\n    return this.#currentResult;\n  }\n  reset() {\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = void 0;\n    this.#updateResult();\n    this.#notify();\n  }\n  mutate(variables, options) {\n    this.#mutateOptions = options;\n    this.#currentMutation?.removeObserver(this);\n    this.#currentMutation = this.#client.getMutationCache().build(this.#client, this.options);\n    this.#currentMutation.addObserver(this);\n    return this.#currentMutation.execute(variables);\n  }\n  #updateResult() {\n    const state = this.#currentMutation?.state ?? getDefaultState();\n    this.#currentResult = {\n      ...state,\n      isPending: state.status === \"pending\",\n      isSuccess: state.status === \"success\",\n      isError: state.status === \"error\",\n      isIdle: state.status === \"idle\",\n      mutate: this.mutate,\n      reset: this.reset\n    };\n  }\n  #notify(action) {\n    notifyManager.batch(() => {\n      if (this.#mutateOptions && this.hasListeners()) {\n        const variables = this.#currentResult.variables;\n        const context = this.#currentResult.context;\n        if (action?.type === \"success\") {\n          this.#mutateOptions.onSuccess?.(action.data, variables, context);\n          this.#mutateOptions.onSettled?.(action.data, null, variables, context);\n        } else if (action?.type === \"error\") {\n          this.#mutateOptions.onError?.(action.error, variables, context);\n          this.#mutateOptions.onSettled?.(void 0, action.error, variables, context);\n        }\n      }\n      this.listeners.forEach(listener => {\n        listener(this.#currentResult);\n      });\n    });\n  }\n};\nexport { MutationObserver };\n", "// src/hydration.ts\nimport { tryResolveSync } from \"./thenable.js\";\nfunction defaultTransformerFn(data) {\n  return data;\n}\nfunction dehydrateMutation(mutation) {\n  return {\n    mutationKey: mutation.options.mutationKey,\n    state: mutation.state,\n    ...(mutation.options.scope && {\n      scope: mutation.options.scope\n    }),\n    ...(mutation.meta && {\n      meta: mutation.meta\n    })\n  };\n}\nfunction dehydrateQuery(query, serializeData, shouldRedactErrors) {\n  return {\n    dehydratedAt: Date.now(),\n    state: {\n      ...query.state,\n      ...(query.state.data !== void 0 && {\n        data: serializeData(query.state.data)\n      })\n    },\n    queryKey: query.queryKey,\n    queryHash: query.queryHash,\n    ...(query.state.status === \"pending\" && {\n      promise: query.promise?.then(serializeData).catch(error => {\n        if (!shouldRedactErrors(error)) {\n          return Promise.reject(error);\n        }\n        if (process.env.NODE_ENV !== \"production\") {\n          console.error(`A query that was dehydrated as pending ended up rejecting. [${query.queryHash}]: ${error}; The error will be redacted in production builds`);\n        }\n        return Promise.reject(new Error(\"redacted\"));\n      })\n    }),\n    ...(query.meta && {\n      meta: query.meta\n    })\n  };\n}\nfunction defaultShouldDehydrateMutation(mutation) {\n  return mutation.state.isPaused;\n}\nfunction defaultShouldDehydrateQuery(query) {\n  return query.state.status === \"success\";\n}\nfunction defaultShouldRedactErrors(_) {\n  return true;\n}\nfunction dehydrate(client, options = {}) {\n  const filterMutation = options.shouldDehydrateMutation ?? client.getDefaultOptions().dehydrate?.shouldDehydrateMutation ?? defaultShouldDehydrateMutation;\n  const mutations = client.getMutationCache().getAll().flatMap(mutation => filterMutation(mutation) ? [dehydrateMutation(mutation)] : []);\n  const filterQuery = options.shouldDehydrateQuery ?? client.getDefaultOptions().dehydrate?.shouldDehydrateQuery ?? defaultShouldDehydrateQuery;\n  const shouldRedactErrors = options.shouldRedactErrors ?? client.getDefaultOptions().dehydrate?.shouldRedactErrors ?? defaultShouldRedactErrors;\n  const serializeData = options.serializeData ?? client.getDefaultOptions().dehydrate?.serializeData ?? defaultTransformerFn;\n  const queries = client.getQueryCache().getAll().flatMap(query => filterQuery(query) ? [dehydrateQuery(query, serializeData, shouldRedactErrors)] : []);\n  return {\n    mutations,\n    queries\n  };\n}\nfunction hydrate(client, dehydratedState, options) {\n  if (typeof dehydratedState !== \"object\" || dehydratedState === null) {\n    return;\n  }\n  const mutationCache = client.getMutationCache();\n  const queryCache = client.getQueryCache();\n  const deserializeData = options?.defaultOptions?.deserializeData ?? client.getDefaultOptions().hydrate?.deserializeData ?? defaultTransformerFn;\n  const mutations = dehydratedState.mutations || [];\n  const queries = dehydratedState.queries || [];\n  mutations.forEach(({\n    state,\n    ...mutationOptions\n  }) => {\n    mutationCache.build(client, {\n      ...client.getDefaultOptions().hydrate?.mutations,\n      ...options?.defaultOptions?.mutations,\n      ...mutationOptions\n    }, state);\n  });\n  queries.forEach(({\n    queryKey,\n    state,\n    queryHash,\n    meta,\n    promise,\n    dehydratedAt\n  }) => {\n    const syncData = promise ? tryResolveSync(promise) : void 0;\n    const rawData = state.data === void 0 ? syncData?.data : state.data;\n    const data = rawData === void 0 ? rawData : deserializeData(rawData);\n    let query = queryCache.get(queryHash);\n    const existingQueryIsPending = query?.state.status === \"pending\";\n    const existingQueryIsFetching = query?.state.fetchStatus === \"fetching\";\n    if (query) {\n      const hasNewerSyncData = syncData &&\n      // We only need this undefined check to handle older dehydration\n      // payloads that might not have dehydratedAt\n      dehydratedAt !== void 0 && dehydratedAt > query.state.dataUpdatedAt;\n      if (state.dataUpdatedAt > query.state.dataUpdatedAt || hasNewerSyncData) {\n        const {\n          fetchStatus: _ignored,\n          ...serializedState\n        } = state;\n        query.setState({\n          ...serializedState,\n          data\n        });\n      }\n    } else {\n      query = queryCache.build(client, {\n        ...client.getDefaultOptions().hydrate?.queries,\n        ...options?.defaultOptions?.queries,\n        queryKey,\n        queryHash,\n        meta\n      },\n      // Reset fetch status to idle to avoid\n      // query being stuck in fetching state upon hydration\n      {\n        ...state,\n        data,\n        fetchStatus: \"idle\",\n        status: data !== void 0 ? \"success\" : state.status\n      });\n    }\n    if (promise && !existingQueryIsPending && !existingQueryIsFetching && (\n    // Only hydrate if dehydration is newer than any existing data,\n    // this is always true for new queries\n    dehydratedAt === void 0 || dehydratedAt > query.state.dataUpdatedAt)) {\n      void query.fetch(void 0, {\n        // RSC transformed promises are not thenable\n        initialPromise: Promise.resolve(promise).then(deserializeData)\n      });\n    }\n  });\n}\nexport { defaultShouldDehydrateMutation, defaultShouldDehydrateQuery, dehydrate, hydrate };\n", "// src/streamedQuery.ts\nimport { addToEnd } from \"./utils.js\";\nfunction streamedQuery({\n  queryFn,\n  refetchMode = \"reset\",\n  maxChunks\n}) {\n  return async context => {\n    const query = context.client.getQueryCache().find({\n      queryKey: context.queryKey,\n      exact: true\n    });\n    const isRefetch = !!query && query.state.data !== void 0;\n    if (isRefetch && refetchMode === \"reset\") {\n      query.setState({\n        status: \"pending\",\n        data: void 0,\n        error: null,\n        fetchStatus: \"fetching\"\n      });\n    }\n    let result = [];\n    const stream = await queryFn(context);\n    for await (const chunk of stream) {\n      if (context.signal.aborted) {\n        break;\n      }\n      if (!isRefetch || refetchMode !== \"replace\") {\n        context.client.setQueryData(context.queryKey, (prev = []) => {\n          return addToEnd(prev, chunk, maxChunks);\n        });\n      }\n      result = addToEnd(result, chunk, maxChunks);\n    }\n    if (isRefetch && refetchMode === \"replace\" && !context.signal.aborted) {\n      context.client.setQueryData(context.queryKey, result);\n    }\n    return context.client.getQueryData(context.queryKey);\n  };\n}\nexport { streamedQuery };\n", "// src/types.ts\nvar dataTagSymbol = Symbol(\"dataTagSymbol\");\nvar dataTagErrorSymbol = Symbol(\"dataTagErrorSymbol\");\nvar unsetMarker = Symbol(\"unsetMarker\");\nexport { dataTagErrorSymbol, dataTagSymbol, unsetMarker };\n", "// src/index.ts\nexport * from \"@tanstack/query-core\";\n\n// src/query-options.ts\nfunction queryOptions(options) {\n  return options;\n}\n\n// src/mutation-options.ts\nfunction mutationOptions(options) {\n  return options;\n}\n\n// src/infinite-query-options.ts\nfunction infiniteQueryOptions(options) {\n  return options;\n}\n\n// src/inject-infinite-query.ts\nimport { InfiniteQueryObserver } from \"@tanstack/query-core\";\nimport { Injector as Injector2, assertInInjectionContext as assertInInjectionContext2, inject as inject3, runInInjectionContext } from \"@angular/core\";\n\n// src/create-base-query.ts\nimport { NgZone, VERSION, computed as computed2, effect, inject as inject2, signal as signal2, untracked as untracked2 } from \"@angular/core\";\nimport { QueryClient, notifyManager, shouldThrowError } from \"@tanstack/query-core\";\n\n// src/signal-proxy.ts\nimport { computed, untracked } from \"@angular/core\";\nfunction signalProxy(inputSignal) {\n  const internalState = {};\n  return new Proxy(internalState, {\n    get(target, prop) {\n      const computedField = target[prop];\n      if (computedField) return computedField;\n      const targetField = untracked(inputSignal)[prop];\n      if (typeof targetField === \"function\") return targetField;\n      return target[prop] = computed(() => inputSignal()[prop]);\n    },\n    has(_, prop) {\n      return !!untracked(inputSignal)[prop];\n    },\n    ownKeys() {\n      return Reflect.ownKeys(untracked(inputSignal));\n    },\n    getOwnPropertyDescriptor() {\n      return {\n        enumerable: true,\n        configurable: true\n      };\n    }\n  });\n}\n\n// src/inject-is-restoring.ts\nimport { InjectionToken, Injector, assertInInjectionContext, inject, signal } from \"@angular/core\";\nvar IS_RESTORING = new InjectionToken(typeof ngDevMode === \"undefined\" || ngDevMode ? \"TANSTACK_QUERY_IS_RESTORING\" : \"\", {\n  // Default value when not provided\n  factory: () => signal(false).asReadonly()\n});\nfunction injectIsRestoring(options) {\n  !options?.injector && assertInInjectionContext(injectIsRestoring);\n  const injector = options?.injector ?? inject(Injector);\n  return injector.get(IS_RESTORING);\n}\nfunction provideIsRestoring(isRestoring) {\n  return {\n    provide: IS_RESTORING,\n    useValue: isRestoring\n  };\n}\n\n// src/create-base-query.ts\nfunction createBaseQuery(optionsFn, Observer) {\n  const ngZone = inject2(NgZone);\n  const queryClient = inject2(QueryClient);\n  const isRestoring = injectIsRestoring();\n  const defaultedOptionsSignal = computed2(() => {\n    const defaultedOptions = queryClient.defaultQueryOptions(optionsFn());\n    defaultedOptions._optimisticResults = isRestoring() ? \"isRestoring\" : \"optimistic\";\n    return defaultedOptions;\n  });\n  const observerSignal = (() => {\n    let instance = null;\n    return computed2(() => {\n      return instance ||= new Observer(queryClient, defaultedOptionsSignal());\n    });\n  })();\n  const optimisticResultSignal = computed2(() => observerSignal().getOptimisticResult(defaultedOptionsSignal()));\n  const resultFromSubscriberSignal = signal2(null);\n  effect(onCleanup => {\n    const observer = observerSignal();\n    const defaultedOptions = defaultedOptionsSignal();\n    untracked2(() => {\n      observer.setOptions(defaultedOptions);\n    });\n    onCleanup(() => {\n      ngZone.run(() => resultFromSubscriberSignal.set(null));\n    });\n  }, {\n    // Set allowSignalWrites to support Angular < v19\n    // Set to undefined to avoid warning on newer versions\n    allowSignalWrites: VERSION.major < \"19\" || void 0\n  });\n  effect(onCleanup => {\n    const observer = observerSignal();\n    const unsubscribe = isRestoring() ? () => void 0 : untracked2(() => ngZone.runOutsideAngular(() => observer.subscribe(notifyManager.batchCalls(state => {\n      ngZone.run(() => {\n        if (state.isError && !state.isFetching && shouldThrowError(observer.options.throwOnError, [state.error, observer.getCurrentQuery()])) {\n          ngZone.onError.emit(state.error);\n          throw state.error;\n        }\n        resultFromSubscriberSignal.set(state);\n      });\n    }))));\n    onCleanup(unsubscribe);\n  });\n  return signalProxy(computed2(() => {\n    const subscriberResult = resultFromSubscriberSignal();\n    const optimisticResult = optimisticResultSignal();\n    return subscriberResult ?? optimisticResult;\n  }));\n}\n\n// src/inject-infinite-query.ts\nfunction injectInfiniteQuery(injectInfiniteQueryFn, options) {\n  !options?.injector && assertInInjectionContext2(injectInfiniteQuery);\n  const injector = options?.injector ?? inject3(Injector2);\n  return runInInjectionContext(injector, () => createBaseQuery(injectInfiniteQueryFn, InfiniteQueryObserver));\n}\n\n// src/inject-is-fetching.ts\nimport { DestroyRef, Injector as Injector3, NgZone as NgZone2, assertInInjectionContext as assertInInjectionContext3, inject as inject4, signal as signal3 } from \"@angular/core\";\nimport { QueryClient as QueryClient2, notifyManager as notifyManager2 } from \"@tanstack/query-core\";\nfunction injectIsFetching(filters, options) {\n  !options?.injector && assertInInjectionContext3(injectIsFetching);\n  const injector = options?.injector ?? inject4(Injector3);\n  const destroyRef = injector.get(DestroyRef);\n  const ngZone = injector.get(NgZone2);\n  const queryClient = injector.get(QueryClient2);\n  const cache = queryClient.getQueryCache();\n  let isFetching = queryClient.isFetching(filters);\n  const result = signal3(isFetching);\n  const unsubscribe = ngZone.runOutsideAngular(() => cache.subscribe(notifyManager2.batchCalls(() => {\n    const newIsFetching = queryClient.isFetching(filters);\n    if (isFetching !== newIsFetching) {\n      isFetching = newIsFetching;\n      ngZone.run(() => {\n        result.set(isFetching);\n      });\n    }\n  })));\n  destroyRef.onDestroy(unsubscribe);\n  return result;\n}\n\n// src/inject-is-mutating.ts\nimport { DestroyRef as DestroyRef2, Injector as Injector4, NgZone as NgZone3, assertInInjectionContext as assertInInjectionContext4, inject as inject5, signal as signal4 } from \"@angular/core\";\nimport { QueryClient as QueryClient3, notifyManager as notifyManager3 } from \"@tanstack/query-core\";\nfunction injectIsMutating(filters, options) {\n  !options?.injector && assertInInjectionContext4(injectIsMutating);\n  const injector = options?.injector ?? inject5(Injector4);\n  const destroyRef = injector.get(DestroyRef2);\n  const ngZone = injector.get(NgZone3);\n  const queryClient = injector.get(QueryClient3);\n  const cache = queryClient.getMutationCache();\n  let isMutating = queryClient.isMutating(filters);\n  const result = signal4(isMutating);\n  const unsubscribe = ngZone.runOutsideAngular(() => cache.subscribe(notifyManager3.batchCalls(() => {\n    const newIsMutating = queryClient.isMutating(filters);\n    if (isMutating !== newIsMutating) {\n      isMutating = newIsMutating;\n      ngZone.run(() => {\n        result.set(isMutating);\n      });\n    }\n  })));\n  destroyRef.onDestroy(unsubscribe);\n  return result;\n}\n\n// src/inject-mutation.ts\nimport { DestroyRef as DestroyRef3, Injector as Injector5, NgZone as NgZone4, assertInInjectionContext as assertInInjectionContext5, computed as computed3, effect as effect2, inject as inject6, signal as signal5, untracked as untracked3 } from \"@angular/core\";\nimport { MutationObserver, QueryClient as QueryClient4, notifyManager as notifyManager4, shouldThrowError as shouldThrowError2 } from \"@tanstack/query-core\";\n\n// src/util/index.ts\nfunction noop() {}\n\n// src/inject-mutation.ts\nfunction injectMutation(injectMutationFn, options) {\n  !options?.injector && assertInInjectionContext5(injectMutation);\n  const injector = options?.injector ?? inject6(Injector5);\n  const destroyRef = injector.get(DestroyRef3);\n  const ngZone = injector.get(NgZone4);\n  const queryClient = injector.get(QueryClient4);\n  const optionsSignal = computed3(injectMutationFn);\n  const observerSignal = (() => {\n    let instance = null;\n    return computed3(() => {\n      return instance ||= new MutationObserver(queryClient, optionsSignal());\n    });\n  })();\n  const mutateFnSignal = computed3(() => {\n    const observer = observerSignal();\n    return (variables, mutateOptions) => {\n      observer.mutate(variables, mutateOptions).catch(noop);\n    };\n  });\n  const resultFromInitialOptionsSignal = computed3(() => {\n    const observer = observerSignal();\n    return observer.getCurrentResult();\n  });\n  const resultFromSubscriberSignal = signal5(null);\n  effect2(() => {\n    const observer = observerSignal();\n    const observerOptions = optionsSignal();\n    untracked3(() => {\n      observer.setOptions(observerOptions);\n    });\n  }, {\n    injector\n  });\n  effect2(() => {\n    const observer = observerSignal();\n    untracked3(() => {\n      const unsubscribe = ngZone.runOutsideAngular(() => observer.subscribe(notifyManager4.batchCalls(state => {\n        ngZone.run(() => {\n          if (state.isError && shouldThrowError2(observer.options.throwOnError, [state.error])) {\n            ngZone.onError.emit(state.error);\n            throw state.error;\n          }\n          resultFromSubscriberSignal.set(state);\n        });\n      })));\n      destroyRef.onDestroy(unsubscribe);\n    });\n  }, {\n    injector\n  });\n  const resultSignal = computed3(() => {\n    const resultFromSubscriber = resultFromSubscriberSignal();\n    const resultFromInitialOptions = resultFromInitialOptionsSignal();\n    const result = resultFromSubscriber ?? resultFromInitialOptions;\n    return {\n      ...result,\n      mutate: mutateFnSignal(),\n      mutateAsync: result.mutate\n    };\n  });\n  return signalProxy(resultSignal);\n}\n\n// src/inject-mutation-state.ts\nimport { DestroyRef as DestroyRef4, Injector as Injector6, NgZone as NgZone5, assertInInjectionContext as assertInInjectionContext6, computed as computed4, inject as inject7, signal as signal6 } from \"@angular/core\";\nimport { QueryClient as QueryClient5, notifyManager as notifyManager5, replaceEqualDeep } from \"@tanstack/query-core\";\nfunction getResult(mutationCache, options) {\n  return mutationCache.findAll(options.filters).map(mutation => options.select ? options.select(mutation) : mutation.state);\n}\nfunction injectMutationState(injectMutationStateFn = () => ({}), options) {\n  !options?.injector && assertInInjectionContext6(injectMutationState);\n  const injector = options?.injector ?? inject7(Injector6);\n  const destroyRef = injector.get(DestroyRef4);\n  const ngZone = injector.get(NgZone5);\n  const queryClient = injector.get(QueryClient5);\n  const mutationCache = queryClient.getMutationCache();\n  const resultFromOptionsSignal = computed4(() => {\n    return [getResult(mutationCache, injectMutationStateFn()), performance.now()];\n  });\n  const resultFromSubscriberSignal = signal6(null);\n  const effectiveResultSignal = computed4(() => {\n    const optionsResult = resultFromOptionsSignal();\n    const subscriberResult = resultFromSubscriberSignal();\n    return subscriberResult && subscriberResult[1] > optionsResult[1] ? subscriberResult[0] : optionsResult[0];\n  });\n  const unsubscribe = ngZone.runOutsideAngular(() => mutationCache.subscribe(notifyManager5.batchCalls(() => {\n    const [lastResult] = effectiveResultSignal();\n    const nextResult = replaceEqualDeep(lastResult, getResult(mutationCache, injectMutationStateFn()));\n    if (lastResult !== nextResult) {\n      ngZone.run(() => {\n        resultFromSubscriberSignal.set([nextResult, performance.now()]);\n      });\n    }\n  })));\n  destroyRef.onDestroy(unsubscribe);\n  return effectiveResultSignal;\n}\n\n// src/inject-queries.ts\nimport { QueriesObserver, QueryClient as QueryClient6, notifyManager as notifyManager6 } from \"@tanstack/query-core\";\nimport { DestroyRef as DestroyRef5, Injector as Injector7, NgZone as NgZone6, assertInInjectionContext as assertInInjectionContext7, computed as computed5, effect as effect3, inject as inject8, runInInjectionContext as runInInjectionContext2, signal as signal7 } from \"@angular/core\";\nfunction injectQueries({\n  queries,\n  ...options\n}, injector) {\n  !injector && assertInInjectionContext7(injectQueries);\n  return runInInjectionContext2(injector ?? inject8(Injector7), () => {\n    const destroyRef = inject8(DestroyRef5);\n    const ngZone = inject8(NgZone6);\n    const queryClient = inject8(QueryClient6);\n    const isRestoring = injectIsRestoring();\n    const defaultedQueries = computed5(() => {\n      return queries().map(opts => {\n        const defaultedOptions = queryClient.defaultQueryOptions(opts);\n        defaultedOptions._optimisticResults = isRestoring() ? \"isRestoring\" : \"optimistic\";\n        return defaultedOptions;\n      });\n    });\n    const observer = new QueriesObserver(queryClient, defaultedQueries(), options);\n    effect3(() => {\n      observer.setQueries(defaultedQueries(), options);\n    });\n    const [, getCombinedResult] = observer.getOptimisticResult(defaultedQueries(), options.combine);\n    const result = signal7(getCombinedResult());\n    effect3(() => {\n      const unsubscribe = isRestoring() ? () => void 0 : ngZone.runOutsideAngular(() => observer.subscribe(notifyManager6.batchCalls(result.set)));\n      destroyRef.onDestroy(unsubscribe);\n    });\n    return result;\n  });\n}\n\n// src/inject-query.ts\nimport { QueryObserver } from \"@tanstack/query-core\";\nimport { Injector as Injector8, assertInInjectionContext as assertInInjectionContext8, inject as inject9, runInInjectionContext as runInInjectionContext3 } from \"@angular/core\";\nfunction injectQuery(injectQueryFn, options) {\n  !options?.injector && assertInInjectionContext8(injectQuery);\n  return runInInjectionContext3(options?.injector ?? inject9(Injector8), () => createBaseQuery(injectQueryFn, QueryObserver));\n}\n\n// src/inject-query-client.ts\nimport { Injector as Injector9, inject as inject10 } from \"@angular/core\";\nimport { QueryClient as QueryClient7 } from \"@tanstack/query-core\";\nfunction injectQueryClient(injectOptions = {}) {\n  return (injectOptions.injector ?? inject10(Injector9)).get(QueryClient7);\n}\n\n// src/providers.ts\nimport { DestroyRef as DestroyRef6, ENVIRONMENT_INITIALIZER, InjectionToken as InjectionToken2, PLATFORM_ID, computed as computed6, effect as effect4, inject as inject11 } from \"@angular/core\";\nimport { QueryClient as QueryClient8, onlineManager } from \"@tanstack/query-core\";\nimport { isPlatformBrowser } from \"@angular/common\";\n\n// src/util/is-dev-mode/is-dev-mode.ts\nimport { isDevMode } from \"@angular/core\";\n\n// src/providers.ts\nfunction provideQueryClient(queryClient) {\n  return {\n    provide: QueryClient8,\n    useFactory: () => {\n      const client = queryClient instanceof InjectionToken2 ? inject11(queryClient) : queryClient;\n      inject11(DestroyRef6).onDestroy(() => client.unmount());\n      client.mount();\n      return client;\n    }\n  };\n}\nfunction provideTanStackQuery(queryClient, ...features) {\n  return [provideQueryClient(queryClient), features.map(feature => feature.ɵproviders)];\n}\nfunction queryFeature(kind, providers) {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers\n  };\n}\nfunction withDevtools(withDevtoolsFn) {\n  let providers = [];\n  if (!isDevMode() && !withDevtoolsFn) {\n    providers = [];\n  } else {\n    providers = [{\n      // Do not use provideEnvironmentInitializer while Angular < v19 is supported\n      provide: ENVIRONMENT_INITIALIZER,\n      multi: true,\n      useFactory: () => {\n        if (!isPlatformBrowser(inject11(PLATFORM_ID))) return noop;\n        const injectedClient = inject11(QueryClient8, {\n          optional: true\n        });\n        const destroyRef = inject11(DestroyRef6);\n        const options = computed6(() => withDevtoolsFn?.() ?? {});\n        let devtools = null;\n        let el = null;\n        const shouldLoadToolsSignal = computed6(() => {\n          const {\n            loadDevtools\n          } = options();\n          return typeof loadDevtools === \"boolean\" ? loadDevtools : isDevMode();\n        });\n        const getResolvedQueryClient = () => {\n          const client = options().client ?? injectedClient;\n          if (!client) {\n            throw new Error(\"No QueryClient found\");\n          }\n          return client;\n        };\n        const destroyDevtools = () => {\n          devtools?.unmount();\n          el?.remove();\n          devtools = null;\n        };\n        return () => effect4(() => {\n          const shouldLoadTools = shouldLoadToolsSignal();\n          const {\n            client,\n            position,\n            errorTypes,\n            buttonPosition,\n            initialIsOpen\n          } = options();\n          if (devtools && !shouldLoadTools) {\n            destroyDevtools();\n            return;\n          } else if (devtools && shouldLoadTools) {\n            client && devtools.setClient(client);\n            position && devtools.setPosition(position);\n            errorTypes && devtools.setErrorTypes(errorTypes);\n            buttonPosition && devtools.setButtonPosition(buttonPosition);\n            initialIsOpen && devtools.setInitialIsOpen(initialIsOpen);\n            return;\n          } else if (!shouldLoadTools) {\n            return;\n          }\n          el = document.body.appendChild(document.createElement(\"div\"));\n          el.classList.add(\"tsqd-parent-container\");\n          import(\"@tanstack/query-devtools\").then(queryDevtools => {\n            devtools = new queryDevtools.TanstackQueryDevtools({\n              ...options(),\n              client: getResolvedQueryClient(),\n              queryFlavor: \"Angular Query\",\n              version: \"5\",\n              onlineManager\n            });\n            el && devtools.mount(el);\n            destroyRef.onDestroy(destroyDevtools);\n          });\n        });\n      }\n    }];\n  }\n  return queryFeature(\"DeveloperTools\", providers);\n}\nvar queryFeatures = [\"DeveloperTools\", \"PersistQueryClient\"];\nexport { infiniteQueryOptions, injectInfiniteQuery, injectIsFetching, injectIsMutating, injectIsRestoring, injectMutation, injectMutationState, injectQueries, injectQuery, injectQueryClient, mutationOptions, provideIsRestoring, provideQueryClient, provideTanStackQuery, queryFeature, queryFeatures, queryOptions, withDevtools };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAI,eAAe,MAAM;AAAA,EACvB,cAAc;AACZ,SAAK,YAA2B,oBAAI,IAAI;AACxC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AAAA,EAC3C;AAAA,EACA,UAAU,UAAU;AAClB,SAAK,UAAU,IAAI,QAAQ;AAC3B,SAAK,YAAY;AACjB,WAAO,MAAM;AACX,WAAK,UAAU,OAAO,QAAQ;AAC9B,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,eAAe;AACb,WAAO,KAAK,UAAU,OAAO;AAAA,EAC/B;AAAA,EACA,cAAc;AAAA,EAAC;AAAA,EACf,gBAAgB;AAAA,EAAC;AACnB;;;AClBA,IAAI,WAAW,OAAO,WAAW,eAAe,UAAU;AAC1D,SAAS,OAAO;AAAC;AACjB,SAAS,iBAAiB,SAAS,OAAO;AACxC,SAAO,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AAC1D;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,OAAO,UAAU,YAAY,SAAS,KAAK,UAAU;AAC9D;AACA,SAAS,eAAe,WAAW,WAAW;AAC5C,SAAO,KAAK,IAAI,aAAa,aAAa,KAAK,KAAK,IAAI,GAAG,CAAC;AAC9D;AACA,SAAS,iBAAiB,WAAW,OAAO;AAC1C,SAAO,OAAO,cAAc,aAAa,UAAU,KAAK,IAAI;AAC9D;AACA,SAAS,eAAe,SAAS,OAAO;AACtC,SAAO,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AAC1D;AACA,SAAS,WAAW,SAAS,OAAO;AAClC,QAAM;AAAA,IACJ,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,UAAU;AACZ,QAAI,OAAO;AACT,UAAI,MAAM,cAAc,sBAAsB,UAAU,MAAM,OAAO,GAAG;AACtE,eAAO;AAAA,MACT;AAAA,IACF,WAAW,CAAC,gBAAgB,MAAM,UAAU,QAAQ,GAAG;AACrD,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,SAAS,OAAO;AAClB,UAAM,WAAW,MAAM,SAAS;AAChC,QAAI,SAAS,YAAY,CAAC,UAAU;AAClC,aAAO;AAAA,IACT;AACA,QAAI,SAAS,cAAc,UAAU;AACnC,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,OAAO,UAAU,aAAa,MAAM,QAAQ,MAAM,OAAO;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,eAAe,gBAAgB,MAAM,MAAM,aAAa;AAC1D,WAAO;AAAA,EACT;AACA,MAAI,aAAa,CAAC,UAAU,KAAK,GAAG;AAClC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,cAAc,SAAS,UAAU;AACxC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,aAAa;AACf,QAAI,CAAC,SAAS,QAAQ,aAAa;AACjC,aAAO;AAAA,IACT;AACA,QAAI,OAAO;AACT,UAAI,QAAQ,SAAS,QAAQ,WAAW,MAAM,QAAQ,WAAW,GAAG;AAClE,eAAO;AAAA,MACT;AAAA,IACF,WAAW,CAAC,gBAAgB,SAAS,QAAQ,aAAa,WAAW,GAAG;AACtE,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,UAAU,SAAS,MAAM,WAAW,QAAQ;AAC9C,WAAO;AAAA,EACT;AACA,MAAI,aAAa,CAAC,UAAU,QAAQ,GAAG;AACrC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,UAAU,SAAS;AAChD,QAAM,SAAS,SAAS,kBAAkB;AAC1C,SAAO,OAAO,QAAQ;AACxB;AACA,SAAS,QAAQ,UAAU;AACzB,SAAO,KAAK,UAAU,UAAU,CAAC,GAAG,QAAQ,cAAc,GAAG,IAAI,OAAO,KAAK,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,QAAQ;AAC/G,WAAO,GAAG,IAAI,IAAI,GAAG;AACrB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC,IAAI,GAAG;AACd;AACA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,KAAK,KAAK,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AAC5D,WAAO,OAAO,KAAK,CAAC,EAAE,MAAM,SAAO,gBAAgB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;AAAA,EACpE;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,GAAG,GAAG;AAC9B,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,aAAa,CAAC,KAAK,aAAa,CAAC;AAC/C,MAAI,SAAS,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AACjD,UAAM,SAAS,QAAQ,IAAI,OAAO,KAAK,CAAC;AACxC,UAAM,QAAQ,OAAO;AACrB,UAAM,SAAS,QAAQ,IAAI,OAAO,KAAK,CAAC;AACxC,UAAM,QAAQ,OAAO;AACrB,UAAM,OAAO,QAAQ,CAAC,IAAI,CAAC;AAC3B,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,YAAM,MAAM,QAAQ,IAAI,OAAO,CAAC;AAChC,WAAK,CAAC,SAAS,OAAO,SAAS,GAAG,KAAK,UAAU,EAAE,GAAG,MAAM,UAAU,EAAE,GAAG,MAAM,QAAQ;AACvF,aAAK,GAAG,IAAI;AACZ;AAAA,MACF,OAAO;AACL,aAAK,GAAG,IAAI,iBAAiB,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAC3C,YAAI,KAAK,GAAG,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,QAAQ;AAC7C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,UAAU,SAAS,eAAe,QAAQ,IAAI;AAAA,EACvD;AACA,SAAO;AACT;AACA,SAAS,oBAAoB,GAAG,GAAG;AACjC,MAAI,CAAC,KAAK,OAAO,KAAK,CAAC,EAAE,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ;AACzD,WAAO;AAAA,EACT;AACA,aAAW,OAAO,GAAG;AACnB,QAAI,EAAE,GAAG,MAAM,EAAE,GAAG,GAAG;AACrB,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,OAAO,KAAK,KAAK,EAAE;AACrE;AACA,SAAS,cAAc,GAAG;AACxB,MAAI,CAAC,mBAAmB,CAAC,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,QAAM,OAAO,EAAE;AACf,MAAI,SAAS,QAAQ;AACnB,WAAO;AAAA,EACT;AACA,QAAM,OAAO,KAAK;AAClB,MAAI,CAAC,mBAAmB,IAAI,GAAG;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,CAAC,KAAK,eAAe,eAAe,GAAG;AACzC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,eAAe,CAAC,MAAM,OAAO,WAAW;AACjD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,GAAG;AAC7B,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AACA,SAAS,MAAM,SAAS;AACtB,SAAO,IAAI,QAAQ,aAAW;AAC5B,eAAW,SAAS,OAAO;AAAA,EAC7B,CAAC;AACH;AACA,SAAS,YAAY,UAAU,MAAM,SAAS;AAC5C,MAAI,OAAO,QAAQ,sBAAsB,YAAY;AACnD,WAAO,QAAQ,kBAAkB,UAAU,IAAI;AAAA,EACjD,WAAW,QAAQ,sBAAsB,OAAO;AAC9C,QAAI,MAAuC;AACzC,UAAI;AACF,eAAO,iBAAiB,UAAU,IAAI;AAAA,MACxC,SAAS,OAAO;AACd,gBAAQ,MAAM,0JAA0J,QAAQ,SAAS,MAAM,KAAK,EAAE;AACtM,cAAM;AAAA,MACR;AAAA,IACF;AACA,WAAO,iBAAiB,UAAU,IAAI;AAAA,EACxC;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,cAAc;AACtC,SAAO;AACT;AACA,SAAS,SAAS,OAAO,MAAM,MAAM,GAAG;AACtC,QAAM,WAAW,CAAC,GAAG,OAAO,IAAI;AAChC,SAAO,OAAO,SAAS,SAAS,MAAM,SAAS,MAAM,CAAC,IAAI;AAC5D;AACA,SAAS,WAAW,OAAO,MAAM,MAAM,GAAG;AACxC,QAAM,WAAW,CAAC,MAAM,GAAG,KAAK;AAChC,SAAO,OAAO,SAAS,SAAS,MAAM,SAAS,MAAM,GAAG,EAAE,IAAI;AAChE;AACA,IAAI,YAAY,OAAO;AACvB,SAAS,cAAc,SAAS,cAAc;AAC5C,MAAI,MAAuC;AACzC,QAAI,QAAQ,YAAY,WAAW;AACjC,cAAQ,MAAM,yGAAyG,QAAQ,SAAS,GAAG;AAAA,IAC7I;AAAA,EACF;AACA,MAAI,CAAC,QAAQ,WAAW,cAAc,gBAAgB;AACpD,WAAO,MAAM,aAAa;AAAA,EAC5B;AACA,MAAI,CAAC,QAAQ,WAAW,QAAQ,YAAY,WAAW;AACrD,WAAO,MAAM,QAAQ,OAAO,IAAI,MAAM,qBAAqB,QAAQ,SAAS,GAAG,CAAC;AAAA,EAClF;AACA,SAAO,QAAQ;AACjB;AACA,SAAS,iBAAiB,cAAc,QAAQ;AAC9C,MAAI,OAAO,iBAAiB,YAAY;AACtC,WAAO,aAAa,GAAG,MAAM;AAAA,EAC/B;AACA,SAAO,CAAC,CAAC;AACX;;;AC3NA,IAAI,eAAe,cAAc,aAAa;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM;AACN,SAAK,SAAS,aAAW;AACvB,UAAI,CAAC,YAAY,OAAO,kBAAkB;AACxC,cAAM,WAAW,MAAM,QAAQ;AAC/B,eAAO,iBAAiB,oBAAoB,UAAU,KAAK;AAC3D,eAAO,MAAM;AACX,iBAAO,oBAAoB,oBAAoB,QAAQ;AAAA,QACzD;AAAA,MACF;AACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,iBAAiB,KAAK,MAAM;AAAA,IACnC;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,WAAK,WAAW;AAChB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,WAAW,MAAM,aAAW;AAC/B,UAAI,OAAO,YAAY,WAAW;AAChC,aAAK,WAAW,OAAO;AAAA,MACzB,OAAO;AACL,aAAK,QAAQ;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,WAAW,SAAS;AAClB,UAAM,UAAU,KAAK,aAAa;AAClC,QAAI,SAAS;AACX,WAAK,WAAW;AAChB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,UAAU;AACR,UAAM,YAAY,KAAK,UAAU;AACjC,SAAK,UAAU,QAAQ,cAAY;AACjC,eAAS,SAAS;AAAA,IACpB,CAAC;AAAA,EACH;AAAA,EACA,YAAY;AACV,QAAI,OAAO,KAAK,aAAa,WAAW;AACtC,aAAO,KAAK;AAAA,IACd;AACA,WAAO,WAAW,UAAU,oBAAoB;AAAA,EAClD;AACF;AACA,IAAI,eAAe,IAAI,aAAa;;;AC3DpC,IAAI,gBAAgB,cAAc,aAAa;AAAA,EAC7C,UAAU;AAAA,EACV;AAAA,EACA;AAAA,EACA,cAAc;AACZ,UAAM;AACN,SAAK,SAAS,cAAY;AACxB,UAAI,CAAC,YAAY,OAAO,kBAAkB;AACxC,cAAM,iBAAiB,MAAM,SAAS,IAAI;AAC1C,cAAM,kBAAkB,MAAM,SAAS,KAAK;AAC5C,eAAO,iBAAiB,UAAU,gBAAgB,KAAK;AACvD,eAAO,iBAAiB,WAAW,iBAAiB,KAAK;AACzD,eAAO,MAAM;AACX,iBAAO,oBAAoB,UAAU,cAAc;AACnD,iBAAO,oBAAoB,WAAW,eAAe;AAAA,QACvD;AAAA,MACF;AACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,iBAAiB,KAAK,MAAM;AAAA,IACnC;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,WAAK,WAAW;AAChB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,iBAAiB,OAAO;AACtB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,WAAW,MAAM,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,EACjD;AAAA,EACA,UAAU,QAAQ;AAChB,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,SAAS;AACX,WAAK,UAAU;AACf,WAAK,UAAU,QAAQ,cAAY;AACjC,iBAAS,MAAM;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AACF;AACA,IAAI,gBAAgB,IAAI,cAAc;;;AClDtC,SAAS,kBAAkB;AACzB,MAAI;AACJ,MAAI;AACJ,QAAM,WAAW,IAAI,QAAQ,CAAC,UAAU,YAAY;AAClD,cAAU;AACV,aAAS;AAAA,EACX,CAAC;AACD,WAAS,SAAS;AAClB,WAAS,MAAM,MAAM;AAAA,EAAC,CAAC;AACvB,WAAS,SAAS,MAAM;AACtB,WAAO,OAAO,UAAU,IAAI;AAC5B,WAAO,SAAS;AAChB,WAAO,SAAS;AAAA,EAClB;AACA,WAAS,UAAU,WAAS;AAC1B,aAAS;AAAA,MACP,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AACD,YAAQ,KAAK;AAAA,EACf;AACA,WAAS,SAAS,YAAU;AAC1B,aAAS;AAAA,MACP,QAAQ;AAAA,MACR;AAAA,IACF,CAAC;AACD,WAAO,MAAM;AAAA,EACf;AACA,SAAO;AACT;AACA,SAAS,eAAe,SAAS;AAC/B,MAAI;AACJ,UAAQ,KAAK,YAAU;AACrB,WAAO;AACP,WAAO;AAAA,EACT,CAAC,GAAG,MAAM,IAAI;AACd,MAAI,SAAS,QAAQ;AACnB,WAAO;AAAA,MACL;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACvCA,SAAS,kBAAkB,cAAc;AACvC,SAAO,KAAK,IAAI,MAAM,KAAK,cAAc,GAAG;AAC9C;AACA,SAAS,SAAS,aAAa;AAC7B,UAAQ,eAAe,cAAc,WAAW,cAAc,SAAS,IAAI;AAC7E;AACA,IAAI,iBAAiB,cAAc,MAAM;AAAA,EACvC,YAAY,SAAS;AACnB,UAAM,gBAAgB;AACtB,SAAK,SAAS,SAAS;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AACF;AACA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,iBAAiB;AAC1B;AACA,SAAS,cAAc,QAAQ;AAC7B,MAAI,mBAAmB;AACvB,MAAI,eAAe;AACnB,MAAI,aAAa;AACjB,MAAI;AACJ,QAAM,WAAW,gBAAgB;AACjC,QAAM,SAAS,mBAAiB;AAC9B,QAAI,CAAC,YAAY;AACf,aAAO,IAAI,eAAe,aAAa,CAAC;AACxC,aAAO,QAAQ;AAAA,IACjB;AAAA,EACF;AACA,QAAM,cAAc,MAAM;AACxB,uBAAmB;AAAA,EACrB;AACA,QAAM,gBAAgB,MAAM;AAC1B,uBAAmB;AAAA,EACrB;AACA,QAAM,cAAc,MAAM,aAAa,UAAU,MAAM,OAAO,gBAAgB,YAAY,cAAc,SAAS,MAAM,OAAO,OAAO;AACrI,QAAM,WAAW,MAAM,SAAS,OAAO,WAAW,KAAK,OAAO,OAAO;AACrE,QAAM,UAAU,WAAS;AACvB,QAAI,CAAC,YAAY;AACf,mBAAa;AACb,aAAO,YAAY,KAAK;AACxB,mBAAa;AACb,eAAS,QAAQ,KAAK;AAAA,IACxB;AAAA,EACF;AACA,QAAM,SAAS,WAAS;AACtB,QAAI,CAAC,YAAY;AACf,mBAAa;AACb,aAAO,UAAU,KAAK;AACtB,mBAAa;AACb,eAAS,OAAO,KAAK;AAAA,IACvB;AAAA,EACF;AACA,QAAM,QAAQ,MAAM;AAClB,WAAO,IAAI,QAAQ,qBAAmB;AACpC,mBAAa,WAAS;AACpB,YAAI,cAAc,YAAY,GAAG;AAC/B,0BAAgB,KAAK;AAAA,QACvB;AAAA,MACF;AACA,aAAO,UAAU;AAAA,IACnB,CAAC,EAAE,KAAK,MAAM;AACZ,mBAAa;AACb,UAAI,CAAC,YAAY;AACf,eAAO,aAAa;AAAA,MACtB;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,MAAM,MAAM;AAChB,QAAI,YAAY;AACd;AAAA,IACF;AACA,QAAI;AACJ,UAAM,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;AACpE,QAAI;AACF,uBAAiB,kBAAkB,OAAO,GAAG;AAAA,IAC/C,SAAS,OAAO;AACd,uBAAiB,QAAQ,OAAO,KAAK;AAAA,IACvC;AACA,YAAQ,QAAQ,cAAc,EAAE,KAAK,OAAO,EAAE,MAAM,WAAS;AAC3D,UAAI,YAAY;AACd;AAAA,MACF;AACA,YAAM,QAAQ,OAAO,UAAU,WAAW,IAAI;AAC9C,YAAM,aAAa,OAAO,cAAc;AACxC,YAAM,QAAQ,OAAO,eAAe,aAAa,WAAW,cAAc,KAAK,IAAI;AACnF,YAAM,cAAc,UAAU,QAAQ,OAAO,UAAU,YAAY,eAAe,SAAS,OAAO,UAAU,cAAc,MAAM,cAAc,KAAK;AACnJ,UAAI,oBAAoB,CAAC,aAAa;AACpC,eAAO,KAAK;AACZ;AAAA,MACF;AACA;AACA,aAAO,SAAS,cAAc,KAAK;AACnC,YAAM,KAAK,EAAE,KAAK,MAAM;AACtB,eAAO,YAAY,IAAI,SAAS,MAAM;AAAA,MACxC,CAAC,EAAE,KAAK,MAAM;AACZ,YAAI,kBAAkB;AACpB,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,cAAI;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL,SAAS;AAAA,IACT;AAAA,IACA,UAAU,MAAM;AACd,mBAAa;AACb,aAAO;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,MAAM;AACX,UAAI,SAAS,GAAG;AACd,YAAI;AAAA,MACN,OAAO;AACL,cAAM,EAAE,KAAK,GAAG;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;AC9HA,IAAI,mBAAmB,QAAM,WAAW,IAAI,CAAC;AAC7C,SAAS,sBAAsB;AAC7B,MAAI,QAAQ,CAAC;AACb,MAAI,eAAe;AACnB,MAAI,WAAW,cAAY;AACzB,aAAS;AAAA,EACX;AACA,MAAI,gBAAgB,cAAY;AAC9B,aAAS;AAAA,EACX;AACA,MAAI,aAAa;AACjB,QAAM,WAAW,cAAY;AAC3B,QAAI,cAAc;AAChB,YAAM,KAAK,QAAQ;AAAA,IACrB,OAAO;AACL,iBAAW,MAAM;AACf,iBAAS,QAAQ;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,QAAQ,MAAM;AAClB,UAAM,gBAAgB;AACtB,YAAQ,CAAC;AACT,QAAI,cAAc,QAAQ;AACxB,iBAAW,MAAM;AACf,sBAAc,MAAM;AAClB,wBAAc,QAAQ,cAAY;AAChC,qBAAS,QAAQ;AAAA,UACnB,CAAC;AAAA,QACH,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AAAA,IACL,OAAO,cAAY;AACjB,UAAI;AACJ;AACA,UAAI;AACF,iBAAS,SAAS;AAAA,MACpB,UAAE;AACA;AACA,YAAI,CAAC,cAAc;AACjB,gBAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;AAAA;AAAA,IAIA,YAAY,cAAY;AACtB,aAAO,IAAI,SAAS;AAClB,iBAAS,MAAM;AACb,mBAAS,GAAG,IAAI;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,mBAAmB,QAAM;AACvB,iBAAW;AAAA,IACb;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,wBAAwB,QAAM;AAC5B,sBAAgB;AAAA,IAClB;AAAA,IACA,cAAc,QAAM;AAClB,mBAAa;AAAA,IACf;AAAA,EACF;AACF;AACA,IAAI,gBAAgB,oBAAoB;;;AC5ExC,IAAI,YAAY,MAAM;AAAA,EACpB;AAAA,EACA,UAAU;AACR,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,aAAa;AACX,SAAK,eAAe;AACpB,QAAI,eAAe,KAAK,MAAM,GAAG;AAC/B,WAAK,aAAa,WAAW,MAAM;AACjC,aAAK,eAAe;AAAA,MACtB,GAAG,KAAK,MAAM;AAAA,IAChB;AAAA,EACF;AAAA,EACA,aAAa,WAAW;AACtB,SAAK,SAAS,KAAK,IAAI,KAAK,UAAU,GAAG,cAAc,WAAW,WAAW,IAAI,KAAK,IAAI;AAAA,EAC5F;AAAA,EACA,iBAAiB;AACf,QAAI,KAAK,YAAY;AACnB,mBAAa,KAAK,UAAU;AAC5B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AACF;;;ACnBA,IAAI,QAAQ,cAAc,UAAU;AAAA,EAClC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,QAAQ;AAClB,UAAM;AACN,SAAK,uBAAuB;AAC5B,SAAK,kBAAkB,OAAO;AAC9B,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,YAAY,CAAC;AAClB,SAAK,UAAU,OAAO;AACtB,SAAK,SAAS,KAAK,QAAQ,cAAc;AACzC,SAAK,WAAW,OAAO;AACvB,SAAK,YAAY,OAAO;AACxB,SAAK,gBAAgB,gBAAgB,KAAK,OAAO;AACjD,SAAK,QAAQ,OAAO,SAAS,KAAK;AAClC,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,UAAU,kCACV,KAAK,kBACL;AAEL,SAAK,aAAa,KAAK,QAAQ,MAAM;AAAA,EACvC;AAAA,EACA,iBAAiB;AACf,QAAI,CAAC,KAAK,UAAU,UAAU,KAAK,MAAM,gBAAgB,QAAQ;AAC/D,WAAK,OAAO,OAAO,IAAI;AAAA,IACzB;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,UAAM,OAAO,YAAY,KAAK,MAAM,MAAM,SAAS,KAAK,OAAO;AAC/D,SAAK,UAAU;AAAA,MACb;AAAA,MACA,MAAM;AAAA,MACN,eAAe,SAAS;AAAA,MACxB,QAAQ,SAAS;AAAA,IACnB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,SAAS,OAAO,iBAAiB;AAC/B,SAAK,UAAU;AAAA,MACb,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,SAAS;AACd,UAAM,UAAU,KAAK,UAAU;AAC/B,SAAK,UAAU,OAAO,OAAO;AAC7B,WAAO,UAAU,QAAQ,KAAK,IAAI,EAAE,MAAM,IAAI,IAAI,QAAQ,QAAQ;AAAA,EACpE;AAAA,EACA,UAAU;AACR,UAAM,QAAQ;AACd,SAAK,OAAO;AAAA,MACV,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AAAA,EACA,QAAQ;AACN,SAAK,QAAQ;AACb,SAAK,SAAS,KAAK,aAAa;AAAA,EAClC;AAAA,EACA,WAAW;AACT,WAAO,KAAK,UAAU,KAAK,cAAY,eAAe,SAAS,QAAQ,SAAS,IAAI,MAAM,KAAK;AAAA,EACjG;AAAA,EACA,aAAa;AACX,QAAI,KAAK,kBAAkB,IAAI,GAAG;AAChC,aAAO,CAAC,KAAK,SAAS;AAAA,IACxB;AACA,WAAO,KAAK,QAAQ,YAAY,aAAa,KAAK,MAAM,kBAAkB,KAAK,MAAM,qBAAqB;AAAA,EAC5G;AAAA,EACA,UAAU;AACR,QAAI,KAAK,MAAM,eAAe;AAC5B,aAAO;AAAA,IACT;AACA,QAAI,KAAK,kBAAkB,IAAI,GAAG;AAChC,aAAO,KAAK,UAAU,KAAK,cAAY,SAAS,iBAAiB,EAAE,OAAO;AAAA,IAC5E;AACA,WAAO,KAAK,MAAM,SAAS;AAAA,EAC7B;AAAA,EACA,cAAc,YAAY,GAAG;AAC3B,WAAO,KAAK,MAAM,iBAAiB,KAAK,MAAM,SAAS,UAAU,CAAC,eAAe,KAAK,MAAM,eAAe,SAAS;AAAA,EACtH;AAAA,EACA,UAAU;AACR,UAAM,WAAW,KAAK,UAAU,KAAK,OAAK,EAAE,yBAAyB,CAAC;AACtE,cAAU,QAAQ;AAAA,MAChB,eAAe;AAAA,IACjB,CAAC;AACD,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA,EACA,WAAW;AACT,UAAM,WAAW,KAAK,UAAU,KAAK,OAAK,EAAE,uBAAuB,CAAC;AACpE,cAAU,QAAQ;AAAA,MAChB,eAAe;AAAA,IACjB,CAAC;AACD,SAAK,UAAU,SAAS;AAAA,EAC1B;AAAA,EACA,YAAY,UAAU;AACpB,QAAI,CAAC,KAAK,UAAU,SAAS,QAAQ,GAAG;AACtC,WAAK,UAAU,KAAK,QAAQ;AAC5B,WAAK,eAAe;AACpB,WAAK,OAAO,OAAO;AAAA,QACjB,MAAM;AAAA,QACN,OAAO;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe,UAAU;AACvB,QAAI,KAAK,UAAU,SAAS,QAAQ,GAAG;AACrC,WAAK,YAAY,KAAK,UAAU,OAAO,OAAK,MAAM,QAAQ;AAC1D,UAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,YAAI,KAAK,UAAU;AACjB,cAAI,KAAK,sBAAsB;AAC7B,iBAAK,SAAS,OAAO;AAAA,cACnB,QAAQ;AAAA,YACV,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,SAAS,YAAY;AAAA,UAC5B;AAAA,QACF;AACA,aAAK,WAAW;AAAA,MAClB;AACA,WAAK,OAAO,OAAO;AAAA,QACjB,MAAM;AAAA,QACN,OAAO;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA,EACA,aAAa;AACX,QAAI,CAAC,KAAK,MAAM,eAAe;AAC7B,WAAK,UAAU;AAAA,QACb,MAAM;AAAA,MACR,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,SAAS,cAAc;AAC3B,QAAI,KAAK,MAAM,gBAAgB,QAAQ;AACrC,UAAI,KAAK,MAAM,SAAS,UAAU,cAAc,eAAe;AAC7D,aAAK,OAAO;AAAA,UACV,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,WAAW,KAAK,UAAU;AACxB,aAAK,SAAS,cAAc;AAC5B,eAAO,KAAK,SAAS;AAAA,MACvB;AAAA,IACF;AACA,QAAI,SAAS;AACX,WAAK,WAAW,OAAO;AAAA,IACzB;AACA,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,YAAM,WAAW,KAAK,UAAU,KAAK,OAAK,EAAE,QAAQ,OAAO;AAC3D,UAAI,UAAU;AACZ,aAAK,WAAW,SAAS,OAAO;AAAA,MAClC;AAAA,IACF;AACA,QAAI,MAAuC;AACzC,UAAI,CAAC,MAAM,QAAQ,KAAK,QAAQ,QAAQ,GAAG;AACzC,gBAAQ,MAAM,qIAAqI;AAAA,MACrJ;AAAA,IACF;AACA,UAAM,kBAAkB,IAAI,gBAAgB;AAC5C,UAAM,oBAAoB,YAAU;AAClC,aAAO,eAAe,QAAQ,UAAU;AAAA,QACtC,YAAY;AAAA,QACZ,KAAK,MAAM;AACT,eAAK,uBAAuB;AAC5B,iBAAO,gBAAgB;AAAA,QACzB;AAAA,MACF,CAAC;AAAA,IACH;AACA,UAAM,UAAU,MAAM;AACpB,YAAM,UAAU,cAAc,KAAK,SAAS,YAAY;AACxD,YAAM,iBAAiB;AAAA,QACrB,QAAQ,KAAK;AAAA,QACb,UAAU,KAAK;AAAA,QACf,MAAM,KAAK;AAAA,MACb;AACA,wBAAkB,cAAc;AAChC,WAAK,uBAAuB;AAC5B,UAAI,KAAK,QAAQ,WAAW;AAC1B,eAAO,KAAK,QAAQ,UAAU,SAAS,gBAAgB,IAAI;AAAA,MAC7D;AACA,aAAO,QAAQ,cAAc;AAAA,IAC/B;AACA,UAAM,UAAU;AAAA,MACd;AAAA,MACA,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ;AAAA,IACF;AACA,sBAAkB,OAAO;AACzB,SAAK,QAAQ,UAAU,QAAQ,SAAS,IAAI;AAC5C,SAAK,eAAe,KAAK;AACzB,QAAI,KAAK,MAAM,gBAAgB,UAAU,KAAK,MAAM,cAAc,QAAQ,cAAc,MAAM;AAC5F,WAAK,UAAU;AAAA,QACb,MAAM;AAAA,QACN,MAAM,QAAQ,cAAc;AAAA,MAC9B,CAAC;AAAA,IACH;AACA,UAAM,UAAU,WAAS;AACvB,UAAI,EAAE,iBAAiB,KAAK,KAAK,MAAM,SAAS;AAC9C,aAAK,UAAU;AAAA,UACb,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,aAAK,OAAO,OAAO,UAAU,OAAO,IAAI;AACxC,aAAK,OAAO,OAAO,YAAY,KAAK,MAAM,MAAM,OAAO,IAAI;AAAA,MAC7D;AACA,WAAK,WAAW;AAAA,IAClB;AACA,SAAK,WAAW,cAAc;AAAA,MAC5B,gBAAgB,cAAc;AAAA,MAC9B,IAAI,QAAQ;AAAA,MACZ,OAAO,gBAAgB,MAAM,KAAK,eAAe;AAAA,MACjD,WAAW,UAAQ;AACjB,YAAI,SAAS,QAAQ;AACnB,cAAI,MAAuC;AACzC,oBAAQ,MAAM,yIAAyI,KAAK,SAAS,EAAE;AAAA,UACzK;AACA,kBAAQ,IAAI,MAAM,GAAG,KAAK,SAAS,oBAAoB,CAAC;AACxD;AAAA,QACF;AACA,YAAI;AACF,eAAK,QAAQ,IAAI;AAAA,QACnB,SAAS,OAAO;AACd,kBAAQ,KAAK;AACb;AAAA,QACF;AACA,aAAK,OAAO,OAAO,YAAY,MAAM,IAAI;AACzC,aAAK,OAAO,OAAO,YAAY,MAAM,KAAK,MAAM,OAAO,IAAI;AAC3D,aAAK,WAAW;AAAA,MAClB;AAAA,MACA;AAAA,MACA,QAAQ,CAAC,cAAc,UAAU;AAC/B,aAAK,UAAU;AAAA,UACb,MAAM;AAAA,UACN;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH;AAAA,MACA,SAAS,MAAM;AACb,aAAK,UAAU;AAAA,UACb,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,MACA,YAAY,MAAM;AAChB,aAAK,UAAU;AAAA,UACb,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,MACA,OAAO,QAAQ,QAAQ;AAAA,MACvB,YAAY,QAAQ,QAAQ;AAAA,MAC5B,aAAa,QAAQ,QAAQ;AAAA,MAC7B,QAAQ,MAAM;AAAA,IAChB,CAAC;AACD,WAAO,KAAK,SAAS,MAAM;AAAA,EAC7B;AAAA,EACA,UAAU,QAAQ;AAChB,UAAM,UAAU,WAAS;AACvB,cAAQ,OAAO,MAAM;AAAA,QACnB,KAAK;AACH,iBAAO,iCACF,QADE;AAAA,YAEL,mBAAmB,OAAO;AAAA,YAC1B,oBAAoB,OAAO;AAAA,UAC7B;AAAA,QACF,KAAK;AACH,iBAAO,iCACF,QADE;AAAA,YAEL,aAAa;AAAA,UACf;AAAA,QACF,KAAK;AACH,iBAAO,iCACF,QADE;AAAA,YAEL,aAAa;AAAA,UACf;AAAA,QACF,KAAK;AACH,iBAAO,gDACF,QACA,WAAW,MAAM,MAAM,KAAK,OAAO,IAFjC;AAAA,YAGL,WAAW,OAAO,QAAQ;AAAA,UAC5B;AAAA,QACF,KAAK;AACH,iBAAO,gDACF,QADE;AAAA,YAEL,MAAM,OAAO;AAAA,YACb,iBAAiB,MAAM,kBAAkB;AAAA,YACzC,eAAe,OAAO,iBAAiB,KAAK,IAAI;AAAA,YAChD,OAAO;AAAA,YACP,eAAe;AAAA,YACf,QAAQ;AAAA,cACJ,CAAC,OAAO,UAAU;AAAA,YACpB,aAAa;AAAA,YACb,mBAAmB;AAAA,YACnB,oBAAoB;AAAA,UACtB;AAAA,QAEJ,KAAK;AACH,gBAAM,QAAQ,OAAO;AACrB,cAAI,iBAAiB,KAAK,KAAK,MAAM,UAAU,KAAK,cAAc;AAChE,mBAAO,iCACF,KAAK,eADH;AAAA,cAEL,aAAa;AAAA,YACf;AAAA,UACF;AACA,iBAAO,iCACF,QADE;AAAA,YAEL;AAAA,YACA,kBAAkB,MAAM,mBAAmB;AAAA,YAC3C,gBAAgB,KAAK,IAAI;AAAA,YACzB,mBAAmB,MAAM,oBAAoB;AAAA,YAC7C,oBAAoB;AAAA,YACpB,aAAa;AAAA,YACb,QAAQ;AAAA,UACV;AAAA,QACF,KAAK;AACH,iBAAO,iCACF,QADE;AAAA,YAEL,eAAe;AAAA,UACjB;AAAA,QACF,KAAK;AACH,iBAAO,kCACF,QACA,OAAO;AAAA,MAEhB;AAAA,IACF;AACA,SAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,kBAAc,MAAM,MAAM;AACxB,WAAK,UAAU,QAAQ,cAAY;AACjC,iBAAS,cAAc;AAAA,MACzB,CAAC;AACD,WAAK,OAAO,OAAO;AAAA,QACjB,OAAO;AAAA,QACP,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,WAAW,MAAM,SAAS;AACjC,SAAO;AAAA,IACL,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,aAAa,SAAS,QAAQ,WAAW,IAAI,aAAa;AAAA,KACtD,SAAS,UAAU;AAAA,IACrB,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AAEJ;AACA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAO,OAAO,QAAQ,gBAAgB,aAAa,QAAQ,YAAY,IAAI,QAAQ;AACzF,QAAM,UAAU,SAAS;AACzB,QAAM,uBAAuB,UAAU,OAAO,QAAQ,yBAAyB,aAAa,QAAQ,qBAAqB,IAAI,QAAQ,uBAAuB;AAC5J,SAAO;AAAA,IACL;AAAA,IACA,iBAAiB;AAAA,IACjB,eAAe,UAAU,wBAAwB,KAAK,IAAI,IAAI;AAAA,IAC9D,OAAO;AAAA,IACP,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,oBAAoB;AAAA,IACpB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,QAAQ,UAAU,YAAY;AAAA,IAC9B,aAAa;AAAA,EACf;AACF;;;ACpYA,IAAI,aAAa,cAAc,aAAa;AAAA,EAC1C,YAAY,SAAS,CAAC,GAAG;AACvB,UAAM;AACN,SAAK,SAAS;AACd,SAAK,WAA0B,oBAAI,IAAI;AAAA,EACzC;AAAA,EACA;AAAA,EACA,MAAM,QAAQ,SAAS,OAAO;AAC5B,UAAM,WAAW,QAAQ;AACzB,UAAM,YAAY,QAAQ,aAAa,sBAAsB,UAAU,OAAO;AAC9E,QAAI,QAAQ,KAAK,IAAI,SAAS;AAC9B,QAAI,CAAC,OAAO;AACV,cAAQ,IAAI,MAAM;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,OAAO,oBAAoB,OAAO;AAAA,QAC3C;AAAA,QACA,gBAAgB,OAAO,iBAAiB,QAAQ;AAAA,MAClD,CAAC;AACD,WAAK,IAAI,KAAK;AAAA,IAChB;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,OAAO;AACT,QAAI,CAAC,KAAK,SAAS,IAAI,MAAM,SAAS,GAAG;AACvC,WAAK,SAAS,IAAI,MAAM,WAAW,KAAK;AACxC,WAAK,OAAO;AAAA,QACV,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,OAAO;AACZ,UAAM,aAAa,KAAK,SAAS,IAAI,MAAM,SAAS;AACpD,QAAI,YAAY;AACd,YAAM,QAAQ;AACd,UAAI,eAAe,OAAO;AACxB,aAAK,SAAS,OAAO,MAAM,SAAS;AAAA,MACtC;AACA,WAAK,OAAO;AAAA,QACV,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ;AACN,kBAAc,MAAM,MAAM;AACxB,WAAK,OAAO,EAAE,QAAQ,WAAS;AAC7B,aAAK,OAAO,KAAK;AAAA,MACnB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,SAAS,IAAI,SAAS;AAAA,EACpC;AAAA,EACA,SAAS;AACP,WAAO,CAAC,GAAG,KAAK,SAAS,OAAO,CAAC;AAAA,EACnC;AAAA,EACA,KAAK,SAAS;AACZ,UAAM,mBAAmB;AAAA,MACvB,OAAO;AAAA,OACJ;AAEL,WAAO,KAAK,OAAO,EAAE,KAAK,WAAS,WAAW,kBAAkB,KAAK,CAAC;AAAA,EACxE;AAAA,EACA,QAAQ,UAAU,CAAC,GAAG;AACpB,UAAM,UAAU,KAAK,OAAO;AAC5B,WAAO,OAAO,KAAK,OAAO,EAAE,SAAS,IAAI,QAAQ,OAAO,WAAS,WAAW,SAAS,KAAK,CAAC,IAAI;AAAA,EACjG;AAAA,EACA,OAAO,OAAO;AACZ,kBAAc,MAAM,MAAM;AACxB,WAAK,UAAU,QAAQ,cAAY;AACjC,iBAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,kBAAc,MAAM,MAAM;AACxB,WAAK,OAAO,EAAE,QAAQ,WAAS;AAC7B,cAAM,QAAQ;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,WAAW;AACT,kBAAc,MAAM,MAAM;AACxB,WAAK,OAAO,EAAE,QAAQ,WAAS;AAC7B,cAAM,SAAS;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;;;AC5FA,IAAI,WAAW,cAAc,UAAU;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,QAAQ;AAClB,UAAM;AACN,SAAK,aAAa,OAAO;AACzB,SAAK,iBAAiB,OAAO;AAC7B,SAAK,aAAa,CAAC;AACnB,SAAK,QAAQ,OAAO,SAASA,iBAAgB;AAC7C,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,WAAW,SAAS;AAClB,SAAK,UAAU;AACf,SAAK,aAAa,KAAK,QAAQ,MAAM;AAAA,EACvC;AAAA,EACA,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,YAAY,UAAU;AACpB,QAAI,CAAC,KAAK,WAAW,SAAS,QAAQ,GAAG;AACvC,WAAK,WAAW,KAAK,QAAQ;AAC7B,WAAK,eAAe;AACpB,WAAK,eAAe,OAAO;AAAA,QACzB,MAAM;AAAA,QACN,UAAU;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,eAAe,UAAU;AACvB,SAAK,aAAa,KAAK,WAAW,OAAO,OAAK,MAAM,QAAQ;AAC5D,SAAK,WAAW;AAChB,SAAK,eAAe,OAAO;AAAA,MACzB,MAAM;AAAA,MACN,UAAU;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB;AACf,QAAI,CAAC,KAAK,WAAW,QAAQ;AAC3B,UAAI,KAAK,MAAM,WAAW,WAAW;AACnC,aAAK,WAAW;AAAA,MAClB,OAAO;AACL,aAAK,eAAe,OAAO,IAAI;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,WAAO,KAAK,UAAU,SAAS;AAAA,IAE/B,KAAK,QAAQ,KAAK,MAAM,SAAS;AAAA,EACnC;AAAA,EACM,QAAQ,WAAW;AAAA;AACvB,YAAM,aAAa,MAAM;AACvB,aAAK,UAAU;AAAA,UACb,MAAM;AAAA,QACR,CAAC;AAAA,MACH;AACA,WAAK,WAAW,cAAc;AAAA,QAC5B,IAAI,MAAM;AACR,cAAI,CAAC,KAAK,QAAQ,YAAY;AAC5B,mBAAO,QAAQ,OAAO,IAAI,MAAM,qBAAqB,CAAC;AAAA,UACxD;AACA,iBAAO,KAAK,QAAQ,WAAW,SAAS;AAAA,QAC1C;AAAA,QACA,QAAQ,CAAC,cAAc,UAAU;AAC/B,eAAK,UAAU;AAAA,YACb,MAAM;AAAA,YACN;AAAA,YACA;AAAA,UACF,CAAC;AAAA,QACH;AAAA,QACA,SAAS,MAAM;AACb,eAAK,UAAU;AAAA,YACb,MAAM;AAAA,UACR,CAAC;AAAA,QACH;AAAA,QACA;AAAA,QACA,OAAO,KAAK,QAAQ,SAAS;AAAA,QAC7B,YAAY,KAAK,QAAQ;AAAA,QACzB,aAAa,KAAK,QAAQ;AAAA,QAC1B,QAAQ,MAAM,KAAK,eAAe,OAAO,IAAI;AAAA,MAC/C,CAAC;AACD,YAAM,WAAW,KAAK,MAAM,WAAW;AACvC,YAAM,WAAW,CAAC,KAAK,SAAS,SAAS;AACzC,UAAI;AACF,YAAI,UAAU;AACZ,qBAAW;AAAA,QACb,OAAO;AACL,eAAK,UAAU;AAAA,YACb,MAAM;AAAA,YACN;AAAA,YACA;AAAA,UACF,CAAC;AACD,gBAAM,KAAK,eAAe,OAAO,WAAW,WAAW,IAAI;AAC3D,gBAAM,UAAU,MAAM,KAAK,QAAQ,WAAW,SAAS;AACvD,cAAI,YAAY,KAAK,MAAM,SAAS;AAClC,iBAAK,UAAU;AAAA,cACb,MAAM;AAAA,cACN;AAAA,cACA;AAAA,cACA;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AACA,cAAM,OAAO,MAAM,KAAK,SAAS,MAAM;AACvC,cAAM,KAAK,eAAe,OAAO,YAAY,MAAM,WAAW,KAAK,MAAM,SAAS,IAAI;AACtF,cAAM,KAAK,QAAQ,YAAY,MAAM,WAAW,KAAK,MAAM,OAAO;AAClE,cAAM,KAAK,eAAe,OAAO,YAAY,MAAM,MAAM,KAAK,MAAM,WAAW,KAAK,MAAM,SAAS,IAAI;AACvG,cAAM,KAAK,QAAQ,YAAY,MAAM,MAAM,WAAW,KAAK,MAAM,OAAO;AACxE,aAAK,UAAU;AAAA,UACb,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT,SAAS,OAAO;AACd,YAAI;AACF,gBAAM,KAAK,eAAe,OAAO,UAAU,OAAO,WAAW,KAAK,MAAM,SAAS,IAAI;AACrF,gBAAM,KAAK,QAAQ,UAAU,OAAO,WAAW,KAAK,MAAM,OAAO;AACjE,gBAAM,KAAK,eAAe,OAAO,YAAY,QAAQ,OAAO,KAAK,MAAM,WAAW,KAAK,MAAM,SAAS,IAAI;AAC1G,gBAAM,KAAK,QAAQ,YAAY,QAAQ,OAAO,WAAW,KAAK,MAAM,OAAO;AAC3E,gBAAM;AAAA,QACR,UAAE;AACA,eAAK,UAAU;AAAA,YACb,MAAM;AAAA,YACN;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,UAAE;AACA,aAAK,eAAe,QAAQ,IAAI;AAAA,MAClC;AAAA,IACF;AAAA;AAAA,EACA,UAAU,QAAQ;AAChB,UAAM,UAAU,WAAS;AACvB,cAAQ,OAAO,MAAM;AAAA,QACnB,KAAK;AACH,iBAAO,iCACF,QADE;AAAA,YAEL,cAAc,OAAO;AAAA,YACrB,eAAe,OAAO;AAAA,UACxB;AAAA,QACF,KAAK;AACH,iBAAO,iCACF,QADE;AAAA,YAEL,UAAU;AAAA,UACZ;AAAA,QACF,KAAK;AACH,iBAAO,iCACF,QADE;AAAA,YAEL,UAAU;AAAA,UACZ;AAAA,QACF,KAAK;AACH,iBAAO,iCACF,QADE;AAAA,YAEL,SAAS,OAAO;AAAA,YAChB,MAAM;AAAA,YACN,cAAc;AAAA,YACd,eAAe;AAAA,YACf,OAAO;AAAA,YACP,UAAU,OAAO;AAAA,YACjB,QAAQ;AAAA,YACR,WAAW,OAAO;AAAA,YAClB,aAAa,KAAK,IAAI;AAAA,UACxB;AAAA,QACF,KAAK;AACH,iBAAO,iCACF,QADE;AAAA,YAEL,MAAM,OAAO;AAAA,YACb,cAAc;AAAA,YACd,eAAe;AAAA,YACf,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,UAAU;AAAA,UACZ;AAAA,QACF,KAAK;AACH,iBAAO,iCACF,QADE;AAAA,YAEL,MAAM;AAAA,YACN,OAAO,OAAO;AAAA,YACd,cAAc,MAAM,eAAe;AAAA,YACnC,eAAe,OAAO;AAAA,YACtB,UAAU;AAAA,YACV,QAAQ;AAAA,UACV;AAAA,MACJ;AAAA,IACF;AACA,SAAK,QAAQ,QAAQ,KAAK,KAAK;AAC/B,kBAAc,MAAM,MAAM;AACxB,WAAK,WAAW,QAAQ,cAAY;AAClC,iBAAS,iBAAiB,MAAM;AAAA,MAClC,CAAC;AACD,WAAK,eAAe,OAAO;AAAA,QACzB,UAAU;AAAA,QACV,MAAM;AAAA,QACN;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAASA,mBAAkB;AACzB,SAAO;AAAA,IACL,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,IACP,cAAc;AAAA,IACd,eAAe;AAAA,IACf,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,EACf;AACF;;;ACpNA,IAAI,gBAAgB,cAAc,aAAa;AAAA,EAC7C,YAAY,SAAS,CAAC,GAAG;AACvB,UAAM;AACN,SAAK,SAAS;AACd,SAAK,aAA4B,oBAAI,IAAI;AACzC,SAAK,UAAyB,oBAAI,IAAI;AACtC,SAAK,cAAc;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAM,QAAQ,SAAS,OAAO;AAC5B,UAAM,WAAW,IAAI,SAAS;AAAA,MAC5B,eAAe;AAAA,MACf,YAAY,EAAE,KAAK;AAAA,MACnB,SAAS,OAAO,uBAAuB,OAAO;AAAA,MAC9C;AAAA,IACF,CAAC;AACD,SAAK,IAAI,QAAQ;AACjB,WAAO;AAAA,EACT;AAAA,EACA,IAAI,UAAU;AACZ,SAAK,WAAW,IAAI,QAAQ;AAC5B,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,kBAAkB,KAAK,QAAQ,IAAI,KAAK;AAC9C,UAAI,iBAAiB;AACnB,wBAAgB,KAAK,QAAQ;AAAA,MAC/B,OAAO;AACL,aAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC;AAAA,MACpC;AAAA,IACF;AACA,SAAK,OAAO;AAAA,MACV,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,UAAU;AACf,QAAI,KAAK,WAAW,OAAO,QAAQ,GAAG;AACpC,YAAM,QAAQ,SAAS,QAAQ;AAC/B,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,kBAAkB,KAAK,QAAQ,IAAI,KAAK;AAC9C,YAAI,iBAAiB;AACnB,cAAI,gBAAgB,SAAS,GAAG;AAC9B,kBAAM,QAAQ,gBAAgB,QAAQ,QAAQ;AAC9C,gBAAI,UAAU,IAAI;AAChB,8BAAgB,OAAO,OAAO,CAAC;AAAA,YACjC;AAAA,UACF,WAAW,gBAAgB,CAAC,MAAM,UAAU;AAC1C,iBAAK,QAAQ,OAAO,KAAK;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,OAAO;AAAA,MACV,MAAM;AAAA,MACN;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO,UAAU;AACf,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,yBAAyB,KAAK,QAAQ,IAAI,KAAK;AACrD,YAAM,uBAAuB,wBAAwB,KAAK,OAAK,EAAE,MAAM,WAAW,SAAS;AAC3F,aAAO,CAAC,wBAAwB,yBAAyB;AAAA,IAC3D,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EACA,QAAQ,UAAU;AAChB,UAAM,QAAQ,SAAS,QAAQ;AAC/B,QAAI,OAAO,UAAU,UAAU;AAC7B,YAAM,gBAAgB,KAAK,QAAQ,IAAI,KAAK,GAAG,KAAK,OAAK,MAAM,YAAY,EAAE,MAAM,QAAQ;AAC3F,aAAO,eAAe,SAAS,KAAK,QAAQ,QAAQ;AAAA,IACtD,OAAO;AACL,aAAO,QAAQ,QAAQ;AAAA,IACzB;AAAA,EACF;AAAA,EACA,QAAQ;AACN,kBAAc,MAAM,MAAM;AACxB,WAAK,WAAW,QAAQ,cAAY;AAClC,aAAK,OAAO;AAAA,UACV,MAAM;AAAA,UACN;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AACD,WAAK,WAAW,MAAM;AACtB,WAAK,QAAQ,MAAM;AAAA,IACrB,CAAC;AAAA,EACH;AAAA,EACA,SAAS;AACP,WAAO,MAAM,KAAK,KAAK,UAAU;AAAA,EACnC;AAAA,EACA,KAAK,SAAS;AACZ,UAAM,mBAAmB;AAAA,MACvB,OAAO;AAAA,OACJ;AAEL,WAAO,KAAK,OAAO,EAAE,KAAK,cAAY,cAAc,kBAAkB,QAAQ,CAAC;AAAA,EACjF;AAAA,EACA,QAAQ,UAAU,CAAC,GAAG;AACpB,WAAO,KAAK,OAAO,EAAE,OAAO,cAAY,cAAc,SAAS,QAAQ,CAAC;AAAA,EAC1E;AAAA,EACA,OAAO,OAAO;AACZ,kBAAc,MAAM,MAAM;AACxB,WAAK,UAAU,QAAQ,cAAY;AACjC,iBAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,wBAAwB;AACtB,UAAM,kBAAkB,KAAK,OAAO,EAAE,OAAO,OAAK,EAAE,MAAM,QAAQ;AAClE,WAAO,cAAc,MAAM,MAAM,QAAQ,IAAI,gBAAgB,IAAI,cAAY,SAAS,SAAS,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;AAAA,EAChH;AACF;AACA,SAAS,SAAS,UAAU;AAC1B,SAAO,SAAS,QAAQ,OAAO;AACjC;;;ACxHA,SAAS,sBAAsB,OAAO;AACpC,SAAO;AAAA,IACL,SAAS,CAAC,SAAS,UAAU;AAC3B,YAAM,UAAU,QAAQ;AACxB,YAAM,YAAY,QAAQ,cAAc,MAAM,WAAW;AACzD,YAAM,WAAW,QAAQ,MAAM,MAAM,SAAS,CAAC;AAC/C,YAAM,gBAAgB,QAAQ,MAAM,MAAM,cAAc,CAAC;AACzD,UAAI,SAAS;AAAA,QACX,OAAO,CAAC;AAAA,QACR,YAAY,CAAC;AAAA,MACf;AACA,UAAI,cAAc;AAClB,YAAM,UAAU,MAAY;AAC1B,YAAI,YAAY;AAChB,cAAM,oBAAoB,YAAU;AAClC,iBAAO,eAAe,QAAQ,UAAU;AAAA,YACtC,YAAY;AAAA,YACZ,KAAK,MAAM;AACT,kBAAI,QAAQ,OAAO,SAAS;AAC1B,4BAAY;AAAA,cACd,OAAO;AACL,wBAAQ,OAAO,iBAAiB,SAAS,MAAM;AAC7C,8BAAY;AAAA,gBACd,CAAC;AAAA,cACH;AACA,qBAAO,QAAQ;AAAA,YACjB;AAAA,UACF,CAAC;AAAA,QACH;AACA,cAAM,UAAU,cAAc,QAAQ,SAAS,QAAQ,YAAY;AACnE,cAAM,YAAY,CAAO,MAAM,OAAO,aAAa;AACjD,cAAI,WAAW;AACb,mBAAO,QAAQ,OAAO;AAAA,UACxB;AACA,cAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ;AACtC,mBAAO,QAAQ,QAAQ,IAAI;AAAA,UAC7B;AACA,gBAAM,iBAAiB;AAAA,YACrB,QAAQ,QAAQ;AAAA,YAChB,UAAU,QAAQ;AAAA,YAClB,WAAW;AAAA,YACX,WAAW,WAAW,aAAa;AAAA,YACnC,MAAM,QAAQ,QAAQ;AAAA,UACxB;AACA,4BAAkB,cAAc;AAChC,gBAAM,OAAO,MAAM,QAAQ,cAAc;AACzC,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,QAAQ;AACZ,gBAAM,QAAQ,WAAW,aAAa;AACtC,iBAAO;AAAA,YACL,OAAO,MAAM,KAAK,OAAO,MAAM,QAAQ;AAAA,YACvC,YAAY,MAAM,KAAK,YAAY,OAAO,QAAQ;AAAA,UACpD;AAAA,QACF;AACA,YAAI,aAAa,SAAS,QAAQ;AAChC,gBAAM,WAAW,cAAc;AAC/B,gBAAM,cAAc,WAAW,uBAAuB;AACtD,gBAAM,UAAU;AAAA,YACd,OAAO;AAAA,YACP,YAAY;AAAA,UACd;AACA,gBAAM,QAAQ,YAAY,SAAS,OAAO;AAC1C,mBAAS,MAAM,UAAU,SAAS,OAAO,QAAQ;AAAA,QACnD,OAAO;AACL,gBAAM,iBAAiB,SAAS,SAAS;AACzC,aAAG;AACD,kBAAM,QAAQ,gBAAgB,IAAI,cAAc,CAAC,KAAK,QAAQ,mBAAmB,iBAAiB,SAAS,MAAM;AACjH,gBAAI,cAAc,KAAK,SAAS,MAAM;AACpC;AAAA,YACF;AACA,qBAAS,MAAM,UAAU,QAAQ,KAAK;AACtC;AAAA,UACF,SAAS,cAAc;AAAA,QACzB;AACA,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,QAAQ,WAAW;AAC7B,gBAAQ,UAAU,MAAM;AACtB,iBAAO,QAAQ,QAAQ,YAAY,SAAS;AAAA,YAC1C,QAAQ,QAAQ;AAAA,YAChB,UAAU,QAAQ;AAAA,YAClB,MAAM,QAAQ,QAAQ;AAAA,YACtB,QAAQ,QAAQ;AAAA,UAClB,GAAG,KAAK;AAAA,QACV;AAAA,MACF,OAAO;AACL,gBAAQ,UAAU;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,SAAS;AAAA,EACjC;AAAA,EACA;AACF,GAAG;AACD,QAAM,YAAY,MAAM,SAAS;AACjC,SAAO,MAAM,SAAS,IAAI,QAAQ,iBAAiB,MAAM,SAAS,GAAG,OAAO,WAAW,SAAS,GAAG,UAAU,IAAI;AACnH;AACA,SAAS,qBAAqB,SAAS;AAAA,EACrC;AAAA,EACA;AACF,GAAG;AACD,SAAO,MAAM,SAAS,IAAI,QAAQ,uBAAuB,MAAM,CAAC,GAAG,OAAO,WAAW,CAAC,GAAG,UAAU,IAAI;AACzG;AACA,SAAS,YAAY,SAAS,MAAM;AAClC,MAAI,CAAC,KAAM,QAAO;AAClB,SAAO,iBAAiB,SAAS,IAAI,KAAK;AAC5C;AACA,SAAS,gBAAgB,SAAS,MAAM;AACtC,MAAI,CAAC,QAAQ,CAAC,QAAQ,qBAAsB,QAAO;AACnD,SAAO,qBAAqB,SAAS,IAAI,KAAK;AAChD;;;AC1GA,IAAI,cAAc,MAAM;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,YAAY,SAAS,CAAC,GAAG;AACvB,SAAK,cAAc,OAAO,cAAc,IAAI,WAAW;AACvD,SAAK,iBAAiB,OAAO,iBAAiB,IAAI,cAAc;AAChE,SAAK,kBAAkB,OAAO,kBAAkB,CAAC;AACjD,SAAK,iBAAgC,oBAAI,IAAI;AAC7C,SAAK,oBAAmC,oBAAI,IAAI;AAChD,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,QAAQ;AACN,SAAK;AACL,QAAI,KAAK,gBAAgB,EAAG;AAC5B,SAAK,oBAAoB,aAAa,UAAU,CAAM,YAAW;AAC/D,UAAI,SAAS;AACX,cAAM,KAAK,sBAAsB;AACjC,aAAK,YAAY,QAAQ;AAAA,MAC3B;AAAA,IACF,EAAC;AACD,SAAK,qBAAqB,cAAc,UAAU,CAAM,WAAU;AAChE,UAAI,QAAQ;AACV,cAAM,KAAK,sBAAsB;AACjC,aAAK,YAAY,SAAS;AAAA,MAC5B;AAAA,IACF,EAAC;AAAA,EACH;AAAA,EACA,UAAU;AACR,SAAK;AACL,QAAI,KAAK,gBAAgB,EAAG;AAC5B,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAAA,EAC5B;AAAA,EACA,WAAW,SAAS;AAClB,WAAO,KAAK,YAAY,QAAQ,iCAC3B,UAD2B;AAAA,MAE9B,aAAa;AAAA,IACf,EAAC,EAAE;AAAA,EACL;AAAA,EACA,WAAW,SAAS;AAClB,WAAO,KAAK,eAAe,QAAQ,iCAC9B,UAD8B;AAAA,MAEjC,QAAQ;AAAA,IACV,EAAC,EAAE;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,UAAU;AACrB,UAAM,UAAU,KAAK,oBAAoB;AAAA,MACvC;AAAA,IACF,CAAC;AACD,WAAO,KAAK,YAAY,IAAI,QAAQ,SAAS,GAAG,MAAM;AAAA,EACxD;AAAA,EACA,gBAAgB,SAAS;AACvB,UAAM,mBAAmB,KAAK,oBAAoB,OAAO;AACzD,UAAM,QAAQ,KAAK,YAAY,MAAM,MAAM,gBAAgB;AAC3D,UAAM,aAAa,MAAM,MAAM;AAC/B,QAAI,eAAe,QAAQ;AACzB,aAAO,KAAK,WAAW,OAAO;AAAA,IAChC;AACA,QAAI,QAAQ,qBAAqB,MAAM,cAAc,iBAAiB,iBAAiB,WAAW,KAAK,CAAC,GAAG;AACzG,WAAK,KAAK,cAAc,gBAAgB;AAAA,IAC1C;AACA,WAAO,QAAQ,QAAQ,UAAU;AAAA,EACnC;AAAA,EACA,eAAe,SAAS;AACtB,WAAO,KAAK,YAAY,QAAQ,OAAO,EAAE,IAAI,CAAC;AAAA,MAC5C;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,OAAO,MAAM;AACnB,aAAO,CAAC,UAAU,IAAI;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EACA,aAAa,UAAU,SAAS,SAAS;AACvC,UAAM,mBAAmB,KAAK,oBAAoB;AAAA,MAChD;AAAA,IACF,CAAC;AACD,UAAM,QAAQ,KAAK,YAAY,IAAI,iBAAiB,SAAS;AAC7D,UAAM,WAAW,OAAO,MAAM;AAC9B,UAAM,OAAO,iBAAiB,SAAS,QAAQ;AAC/C,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,YAAY,MAAM,MAAM,gBAAgB,EAAE,QAAQ,MAAM,iCAC/D,UAD+D;AAAA,MAElE,QAAQ;AAAA,IACV,EAAC;AAAA,EACH;AAAA,EACA,eAAe,SAAS,SAAS,SAAS;AACxC,WAAO,cAAc,MAAM,MAAM,KAAK,YAAY,QAAQ,OAAO,EAAE,IAAI,CAAC;AAAA,MACtE;AAAA,IACF,MAAM,CAAC,UAAU,KAAK,aAAa,UAAU,SAAS,OAAO,CAAC,CAAC,CAAC;AAAA,EAClE;AAAA,EACA,cAAc,UAAU;AACtB,UAAM,UAAU,KAAK,oBAAoB;AAAA,MACvC;AAAA,IACF,CAAC;AACD,WAAO,KAAK,YAAY,IAAI,QAAQ,SAAS,GAAG;AAAA,EAClD;AAAA,EACA,cAAc,SAAS;AACrB,UAAM,aAAa,KAAK;AACxB,kBAAc,MAAM,MAAM;AACxB,iBAAW,QAAQ,OAAO,EAAE,QAAQ,WAAS;AAC3C,mBAAW,OAAO,KAAK;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,aAAa,SAAS,SAAS;AAC7B,UAAM,aAAa,KAAK;AACxB,WAAO,cAAc,MAAM,MAAM;AAC/B,iBAAW,QAAQ,OAAO,EAAE,QAAQ,WAAS;AAC3C,cAAM,MAAM;AAAA,MACd,CAAC;AACD,aAAO,KAAK,eAAe;AAAA,QACzB,MAAM;AAAA,SACH,UACF,OAAO;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS,gBAAgB,CAAC,GAAG;AACzC,UAAM,yBAAyB;AAAA,MAC7B,QAAQ;AAAA,OACL;AAEL,UAAM,WAAW,cAAc,MAAM,MAAM,KAAK,YAAY,QAAQ,OAAO,EAAE,IAAI,WAAS,MAAM,OAAO,sBAAsB,CAAC,CAAC;AAC/H,WAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,EACpD;AAAA,EACA,kBAAkB,SAAS,UAAU,CAAC,GAAG;AACvC,WAAO,cAAc,MAAM,MAAM;AAC/B,WAAK,YAAY,QAAQ,OAAO,EAAE,QAAQ,WAAS;AACjD,cAAM,WAAW;AAAA,MACnB,CAAC;AACD,UAAI,SAAS,gBAAgB,QAAQ;AACnC,eAAO,QAAQ,QAAQ;AAAA,MACzB;AACA,aAAO,KAAK,eAAe,iCACtB,UADsB;AAAA,QAEzB,MAAM,SAAS,eAAe,SAAS,QAAQ;AAAA,MACjD,IAAG,OAAO;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,eAAe,SAAS,UAAU,CAAC,GAAG;AACpC,UAAM,eAAe,iCAChB,UADgB;AAAA,MAEnB,eAAe,QAAQ,iBAAiB;AAAA,IAC1C;AACA,UAAM,WAAW,cAAc,MAAM,MAAM,KAAK,YAAY,QAAQ,OAAO,EAAE,OAAO,WAAS,CAAC,MAAM,WAAW,CAAC,EAAE,IAAI,WAAS;AAC7H,UAAI,UAAU,MAAM,MAAM,QAAQ,YAAY;AAC9C,UAAI,CAAC,aAAa,cAAc;AAC9B,kBAAU,QAAQ,MAAM,IAAI;AAAA,MAC9B;AACA,aAAO,MAAM,MAAM,gBAAgB,WAAW,QAAQ,QAAQ,IAAI;AAAA,IACpE,CAAC,CAAC;AACF,WAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,IAAI;AAAA,EACxC;AAAA,EACA,WAAW,SAAS;AAClB,UAAM,mBAAmB,KAAK,oBAAoB,OAAO;AACzD,QAAI,iBAAiB,UAAU,QAAQ;AACrC,uBAAiB,QAAQ;AAAA,IAC3B;AACA,UAAM,QAAQ,KAAK,YAAY,MAAM,MAAM,gBAAgB;AAC3D,WAAO,MAAM,cAAc,iBAAiB,iBAAiB,WAAW,KAAK,CAAC,IAAI,MAAM,MAAM,gBAAgB,IAAI,QAAQ,QAAQ,MAAM,MAAM,IAAI;AAAA,EACpJ;AAAA,EACA,cAAc,SAAS;AACrB,WAAO,KAAK,WAAW,OAAO,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,EACvD;AAAA,EACA,mBAAmB,SAAS;AAC1B,YAAQ,WAAW,sBAAsB,QAAQ,KAAK;AACtD,WAAO,KAAK,WAAW,OAAO;AAAA,EAChC;AAAA,EACA,sBAAsB,SAAS;AAC7B,WAAO,KAAK,mBAAmB,OAAO,EAAE,KAAK,IAAI,EAAE,MAAM,IAAI;AAAA,EAC/D;AAAA,EACA,wBAAwB,SAAS;AAC/B,YAAQ,WAAW,sBAAsB,QAAQ,KAAK;AACtD,WAAO,KAAK,gBAAgB,OAAO;AAAA,EACrC;AAAA,EACA,wBAAwB;AACtB,QAAI,cAAc,SAAS,GAAG;AAC5B,aAAO,KAAK,eAAe,sBAAsB;AAAA,IACnD;AACA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EACA,gBAAgB;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB,SAAS;AACzB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,iBAAiB,UAAU,SAAS;AAClC,SAAK,eAAe,IAAI,QAAQ,QAAQ,GAAG;AAAA,MACzC;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,iBAAiB,UAAU;AACzB,UAAM,WAAW,CAAC,GAAG,KAAK,eAAe,OAAO,CAAC;AACjD,UAAM,SAAS,CAAC;AAChB,aAAS,QAAQ,kBAAgB;AAC/B,UAAI,gBAAgB,UAAU,aAAa,QAAQ,GAAG;AACpD,eAAO,OAAO,QAAQ,aAAa,cAAc;AAAA,MACnD;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,aAAa,SAAS;AACxC,SAAK,kBAAkB,IAAI,QAAQ,WAAW,GAAG;AAAA,MAC/C;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EACA,oBAAoB,aAAa;AAC/B,UAAM,WAAW,CAAC,GAAG,KAAK,kBAAkB,OAAO,CAAC;AACpD,UAAM,SAAS,CAAC;AAChB,aAAS,QAAQ,kBAAgB;AAC/B,UAAI,gBAAgB,aAAa,aAAa,WAAW,GAAG;AAC1D,eAAO,OAAO,QAAQ,aAAa,cAAc;AAAA,MACnD;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,SAAS;AAC3B,QAAI,QAAQ,YAAY;AACtB,aAAO;AAAA,IACT;AACA,UAAM,mBAAmB,+DACpB,KAAK,gBAAgB,UACrB,KAAK,iBAAiB,QAAQ,QAAQ,IACtC,UAHoB;AAAA,MAIvB,YAAY;AAAA,IACd;AACA,QAAI,CAAC,iBAAiB,WAAW;AAC/B,uBAAiB,YAAY,sBAAsB,iBAAiB,UAAU,gBAAgB;AAAA,IAChG;AACA,QAAI,iBAAiB,uBAAuB,QAAQ;AAClD,uBAAiB,qBAAqB,iBAAiB,gBAAgB;AAAA,IACzE;AACA,QAAI,iBAAiB,iBAAiB,QAAQ;AAC5C,uBAAiB,eAAe,CAAC,CAAC,iBAAiB;AAAA,IACrD;AACA,QAAI,CAAC,iBAAiB,eAAe,iBAAiB,WAAW;AAC/D,uBAAiB,cAAc;AAAA,IACjC;AACA,QAAI,iBAAiB,YAAY,WAAW;AAC1C,uBAAiB,UAAU;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB,SAAS;AAC9B,QAAI,SAAS,YAAY;AACvB,aAAO;AAAA,IACT;AACA,WAAO,+DACF,KAAK,gBAAgB,YACpB,SAAS,eAAe,KAAK,oBAAoB,QAAQ,WAAW,IACrE,UAHE;AAAA,MAIL,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EACA,QAAQ;AACN,SAAK,YAAY,MAAM;AACvB,SAAK,eAAe,MAAM;AAAA,EAC5B;AACF;;;AC5RA,IAAI,gBAAgB,cAAc,aAAa;AAAA,EAC7C,YAAY,QAAQ,SAAS;AAC3B,UAAM;AACN,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,mBAAmB,gBAAgB;AACxC,QAAI,CAAC,KAAK,QAAQ,+BAA+B;AAC/C,WAAK,iBAAiB,OAAO,IAAI,MAAM,2DAA2D,CAAC;AAAA,IACrG;AACA,SAAK,YAAY;AACjB,SAAK,WAAW,OAAO;AAAA,EACzB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,4BAA4B;AAAA,EAC5B,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA,EAGA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAA+B,oBAAI,IAAI;AAAA,EACvC,cAAc;AACZ,SAAK,UAAU,KAAK,QAAQ,KAAK,IAAI;AAAA,EACvC;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,WAAK,cAAc,YAAY,IAAI;AACnC,UAAI,mBAAmB,KAAK,eAAe,KAAK,OAAO,GAAG;AACxD,aAAK,cAAc;AAAA,MACrB,OAAO;AACL,aAAK,aAAa;AAAA,MACpB;AACA,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,WAAO,cAAc,KAAK,eAAe,KAAK,SAAS,KAAK,QAAQ,kBAAkB;AAAA,EACxF;AAAA,EACA,2BAA2B;AACzB,WAAO,cAAc,KAAK,eAAe,KAAK,SAAS,KAAK,QAAQ,oBAAoB;AAAA,EAC1F;AAAA,EACA,UAAU;AACR,SAAK,YAA2B,oBAAI,IAAI;AACxC,SAAK,mBAAmB;AACxB,SAAK,sBAAsB;AAC3B,SAAK,cAAc,eAAe,IAAI;AAAA,EACxC;AAAA,EACA,WAAW,SAAS;AAClB,UAAM,cAAc,KAAK;AACzB,UAAM,YAAY,KAAK;AACvB,SAAK,UAAU,KAAK,QAAQ,oBAAoB,OAAO;AACvD,QAAI,KAAK,QAAQ,YAAY,UAAU,OAAO,KAAK,QAAQ,YAAY,aAAa,OAAO,KAAK,QAAQ,YAAY,cAAc,OAAO,eAAe,KAAK,QAAQ,SAAS,KAAK,aAAa,MAAM,WAAW;AAC/M,YAAM,IAAI,MAAM,uEAAuE;AAAA,IACzF;AACA,SAAK,aAAa;AAClB,SAAK,cAAc,WAAW,KAAK,OAAO;AAC1C,QAAI,YAAY,cAAc,CAAC,oBAAoB,KAAK,SAAS,WAAW,GAAG;AAC7E,WAAK,QAAQ,cAAc,EAAE,OAAO;AAAA,QAClC,MAAM;AAAA,QACN,OAAO,KAAK;AAAA,QACZ,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,UAAM,UAAU,KAAK,aAAa;AAClC,QAAI,WAAW,sBAAsB,KAAK,eAAe,WAAW,KAAK,SAAS,WAAW,GAAG;AAC9F,WAAK,cAAc;AAAA,IACrB;AACA,SAAK,aAAa;AAClB,QAAI,YAAY,KAAK,kBAAkB,aAAa,eAAe,KAAK,QAAQ,SAAS,KAAK,aAAa,MAAM,eAAe,YAAY,SAAS,KAAK,aAAa,KAAK,iBAAiB,KAAK,QAAQ,WAAW,KAAK,aAAa,MAAM,iBAAiB,YAAY,WAAW,KAAK,aAAa,IAAI;AACzS,WAAK,oBAAoB;AAAA,IAC3B;AACA,UAAM,sBAAsB,KAAK,wBAAwB;AACzD,QAAI,YAAY,KAAK,kBAAkB,aAAa,eAAe,KAAK,QAAQ,SAAS,KAAK,aAAa,MAAM,eAAe,YAAY,SAAS,KAAK,aAAa,KAAK,wBAAwB,KAAK,0BAA0B;AACjO,WAAK,uBAAuB,mBAAmB;AAAA,IACjD;AAAA,EACF;AAAA,EACA,oBAAoB,SAAS;AAC3B,UAAM,QAAQ,KAAK,QAAQ,cAAc,EAAE,MAAM,KAAK,SAAS,OAAO;AACtE,UAAM,SAAS,KAAK,aAAa,OAAO,OAAO;AAC/C,QAAI,sCAAsC,MAAM,MAAM,GAAG;AACvD,WAAK,iBAAiB;AACtB,WAAK,wBAAwB,KAAK;AAClC,WAAK,sBAAsB,KAAK,cAAc;AAAA,IAChD;AACA,WAAO;AAAA,EACT;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,YAAY,QAAQ,eAAe;AACjC,WAAO,IAAI,MAAM,QAAQ;AAAA,MACvB,KAAK,CAAC,QAAQ,QAAQ;AACpB,aAAK,UAAU,GAAG;AAClB,wBAAgB,GAAG;AACnB,eAAO,QAAQ,IAAI,QAAQ,GAAG;AAAA,MAChC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,UAAU,KAAK;AACb,SAAK,cAAc,IAAI,GAAG;AAAA,EAC5B;AAAA,EACA,kBAAkB;AAChB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,QAAQ,KAEJ,CAAC,GAAG;AAFA,QACH,oBADG,IACH;AAEH,WAAO,KAAK,MAAM,mBACb,QACJ;AAAA,EACH;AAAA,EACA,gBAAgB,SAAS;AACvB,UAAM,mBAAmB,KAAK,QAAQ,oBAAoB,OAAO;AACjE,UAAM,QAAQ,KAAK,QAAQ,cAAc,EAAE,MAAM,KAAK,SAAS,gBAAgB;AAC/E,WAAO,MAAM,MAAM,EAAE,KAAK,MAAM,KAAK,aAAa,OAAO,gBAAgB,CAAC;AAAA,EAC5E;AAAA,EACA,MAAM,cAAc;AAClB,WAAO,KAAK,cAAc,iCACrB,eADqB;AAAA,MAExB,eAAe,aAAa,iBAAiB;AAAA,IAC/C,EAAC,EAAE,KAAK,MAAM;AACZ,WAAK,aAAa;AAClB,aAAO,KAAK;AAAA,IACd,CAAC;AAAA,EACH;AAAA,EACA,cAAc,cAAc;AAC1B,SAAK,aAAa;AAClB,QAAI,UAAU,KAAK,cAAc,MAAM,KAAK,SAAS,YAAY;AACjE,QAAI,CAAC,cAAc,cAAc;AAC/B,gBAAU,QAAQ,MAAM,IAAI;AAAA,IAC9B;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB;AACpB,SAAK,mBAAmB;AACxB,UAAM,YAAY,iBAAiB,KAAK,QAAQ,WAAW,KAAK,aAAa;AAC7E,QAAI,YAAY,KAAK,eAAe,WAAW,CAAC,eAAe,SAAS,GAAG;AACzE;AAAA,IACF;AACA,UAAM,OAAO,eAAe,KAAK,eAAe,eAAe,SAAS;AACxE,UAAM,UAAU,OAAO;AACvB,SAAK,kBAAkB,WAAW,MAAM;AACtC,UAAI,CAAC,KAAK,eAAe,SAAS;AAChC,aAAK,aAAa;AAAA,MACpB;AAAA,IACF,GAAG,OAAO;AAAA,EACZ;AAAA,EACA,0BAA0B;AACxB,YAAQ,OAAO,KAAK,QAAQ,oBAAoB,aAAa,KAAK,QAAQ,gBAAgB,KAAK,aAAa,IAAI,KAAK,QAAQ,oBAAoB;AAAA,EACnJ;AAAA,EACA,uBAAuB,cAAc;AACnC,SAAK,sBAAsB;AAC3B,SAAK,0BAA0B;AAC/B,QAAI,YAAY,eAAe,KAAK,QAAQ,SAAS,KAAK,aAAa,MAAM,SAAS,CAAC,eAAe,KAAK,uBAAuB,KAAK,KAAK,4BAA4B,GAAG;AACzK;AAAA,IACF;AACA,SAAK,qBAAqB,YAAY,MAAM;AAC1C,UAAI,KAAK,QAAQ,+BAA+B,aAAa,UAAU,GAAG;AACxE,aAAK,cAAc;AAAA,MACrB;AAAA,IACF,GAAG,KAAK,uBAAuB;AAAA,EACjC;AAAA,EACA,gBAAgB;AACd,SAAK,oBAAoB;AACzB,SAAK,uBAAuB,KAAK,wBAAwB,CAAC;AAAA,EAC5D;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,iBAAiB;AACxB,mBAAa,KAAK,eAAe;AACjC,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,wBAAwB;AACtB,QAAI,KAAK,oBAAoB;AAC3B,oBAAc,KAAK,kBAAkB;AACrC,WAAK,qBAAqB;AAAA,IAC5B;AAAA,EACF;AAAA,EACA,aAAa,OAAO,SAAS;AAC3B,UAAM,YAAY,KAAK;AACvB,UAAM,cAAc,KAAK;AACzB,UAAM,aAAa,KAAK;AACxB,UAAM,kBAAkB,KAAK;AAC7B,UAAM,oBAAoB,KAAK;AAC/B,UAAM,cAAc,UAAU;AAC9B,UAAM,oBAAoB,cAAc,MAAM,QAAQ,KAAK;AAC3D,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,mBACV;AAEL,QAAI,oBAAoB;AACxB,QAAI;AACJ,QAAI,QAAQ,oBAAoB;AAC9B,YAAM,UAAU,KAAK,aAAa;AAClC,YAAM,eAAe,CAAC,WAAW,mBAAmB,OAAO,OAAO;AAClE,YAAM,kBAAkB,WAAW,sBAAsB,OAAO,WAAW,SAAS,WAAW;AAC/F,UAAI,gBAAgB,iBAAiB;AACnC,mBAAW,kCACN,WACA,WAAW,MAAM,MAAM,MAAM,OAAO;AAAA,MAE3C;AACA,UAAI,QAAQ,uBAAuB,eAAe;AAChD,iBAAS,cAAc;AAAA,MACzB;AAAA,IACF;AACA,QAAI;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,SAAS;AAChB,QAAI,aAAa;AACjB,QAAI,QAAQ,oBAAoB,UAAU,SAAS,UAAU,WAAW,WAAW;AACjF,UAAI;AACJ,UAAI,YAAY,qBAAqB,QAAQ,oBAAoB,mBAAmB,iBAAiB;AACnG,0BAAkB,WAAW;AAC7B,qBAAa;AAAA,MACf,OAAO;AACL,0BAAkB,OAAO,QAAQ,oBAAoB,aAAa,QAAQ,gBAAgB,KAAK,2BAA2B,MAAM,MAAM,KAAK,yBAAyB,IAAI,QAAQ;AAAA,MAClL;AACA,UAAI,oBAAoB,QAAQ;AAC9B,iBAAS;AACT,eAAO,YAAY,YAAY,MAAM,iBAAiB,OAAO;AAC7D,4BAAoB;AAAA,MACtB;AAAA,IACF;AACA,QAAI,QAAQ,UAAU,SAAS,UAAU,CAAC,YAAY;AACpD,UAAI,cAAc,SAAS,iBAAiB,QAAQ,QAAQ,WAAW,KAAK,WAAW;AACrF,eAAO,KAAK;AAAA,MACd,OAAO;AACL,YAAI;AACF,eAAK,YAAY,QAAQ;AACzB,iBAAO,QAAQ,OAAO,IAAI;AAC1B,iBAAO,YAAY,YAAY,MAAM,MAAM,OAAO;AAClD,eAAK,gBAAgB;AACrB,eAAK,eAAe;AAAA,QACtB,SAAS,aAAa;AACpB,eAAK,eAAe;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,cAAc;AACrB,cAAQ,KAAK;AACb,aAAO,KAAK;AACZ,uBAAiB,KAAK,IAAI;AAC1B,eAAS;AAAA,IACX;AACA,UAAM,aAAa,SAAS,gBAAgB;AAC5C,UAAM,YAAY,WAAW;AAC7B,UAAM,UAAU,WAAW;AAC3B,UAAM,YAAY,aAAa;AAC/B,UAAM,UAAU,SAAS;AACzB,UAAM,SAAS;AAAA,MACb;AAAA,MACA,aAAa,SAAS;AAAA,MACtB;AAAA,MACA,WAAW,WAAW;AAAA,MACtB;AAAA,MACA,kBAAkB;AAAA,MAClB;AAAA,MACA;AAAA,MACA,eAAe,SAAS;AAAA,MACxB;AAAA,MACA;AAAA,MACA,cAAc,SAAS;AAAA,MACvB,eAAe,SAAS;AAAA,MACxB,kBAAkB,SAAS;AAAA,MAC3B,WAAW,SAAS,kBAAkB,KAAK,SAAS,mBAAmB;AAAA,MACvE,qBAAqB,SAAS,kBAAkB,kBAAkB,mBAAmB,SAAS,mBAAmB,kBAAkB;AAAA,MACnI;AAAA,MACA,cAAc,cAAc,CAAC;AAAA,MAC7B,gBAAgB,WAAW,CAAC;AAAA,MAC5B,UAAU,SAAS,gBAAgB;AAAA,MACnC;AAAA,MACA,gBAAgB,WAAW;AAAA,MAC3B,SAAS,QAAQ,OAAO,OAAO;AAAA,MAC/B,SAAS,KAAK;AAAA,MACd,SAAS,KAAK;AAAA,IAChB;AACA,UAAM,aAAa;AACnB,QAAI,KAAK,QAAQ,+BAA+B;AAC9C,YAAM,6BAA6B,cAAY;AAC7C,YAAI,WAAW,WAAW,SAAS;AACjC,mBAAS,OAAO,WAAW,KAAK;AAAA,QAClC,WAAW,WAAW,SAAS,QAAQ;AACrC,mBAAS,QAAQ,WAAW,IAAI;AAAA,QAClC;AAAA,MACF;AACA,YAAM,mBAAmB,MAAM;AAC7B,cAAM,UAAU,KAAK,mBAAmB,WAAW,UAAU,gBAAgB;AAC7E,mCAA2B,OAAO;AAAA,MACpC;AACA,YAAM,eAAe,KAAK;AAC1B,cAAQ,aAAa,QAAQ;AAAA,QAC3B,KAAK;AACH,cAAI,MAAM,cAAc,UAAU,WAAW;AAC3C,uCAA2B,YAAY;AAAA,UACzC;AACA;AAAA,QACF,KAAK;AACH,cAAI,WAAW,WAAW,WAAW,WAAW,SAAS,aAAa,OAAO;AAC3E,6BAAiB;AAAA,UACnB;AACA;AAAA,QACF,KAAK;AACH,cAAI,WAAW,WAAW,WAAW,WAAW,UAAU,aAAa,QAAQ;AAC7E,6BAAiB;AAAA,UACnB;AACA;AAAA,MACJ;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,UAAM,aAAa,KAAK;AACxB,UAAM,aAAa,KAAK,aAAa,KAAK,eAAe,KAAK,OAAO;AACrE,SAAK,sBAAsB,KAAK,cAAc;AAC9C,SAAK,wBAAwB,KAAK;AAClC,QAAI,KAAK,oBAAoB,SAAS,QAAQ;AAC5C,WAAK,4BAA4B,KAAK;AAAA,IACxC;AACA,QAAI,oBAAoB,YAAY,UAAU,GAAG;AAC/C;AAAA,IACF;AACA,SAAK,iBAAiB;AACtB,UAAM,wBAAwB,MAAM;AAClC,UAAI,CAAC,YAAY;AACf,eAAO;AAAA,MACT;AACA,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,KAAK;AACT,YAAM,2BAA2B,OAAO,wBAAwB,aAAa,oBAAoB,IAAI;AACrG,UAAI,6BAA6B,SAAS,CAAC,4BAA4B,CAAC,KAAK,cAAc,MAAM;AAC/F,eAAO;AAAA,MACT;AACA,YAAM,gBAAgB,IAAI,IAAI,4BAA4B,KAAK,aAAa;AAC5E,UAAI,KAAK,QAAQ,cAAc;AAC7B,sBAAc,IAAI,OAAO;AAAA,MAC3B;AACA,aAAO,OAAO,KAAK,KAAK,cAAc,EAAE,KAAK,SAAO;AAClD,cAAM,WAAW;AACjB,cAAM,UAAU,KAAK,eAAe,QAAQ,MAAM,WAAW,QAAQ;AACrE,eAAO,WAAW,cAAc,IAAI,QAAQ;AAAA,MAC9C,CAAC;AAAA,IACH;AACA,SAAK,QAAQ;AAAA,MACX,WAAW,sBAAsB;AAAA,IACnC,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AACb,UAAM,QAAQ,KAAK,QAAQ,cAAc,EAAE,MAAM,KAAK,SAAS,KAAK,OAAO;AAC3E,QAAI,UAAU,KAAK,eAAe;AAChC;AAAA,IACF;AACA,UAAM,YAAY,KAAK;AACvB,SAAK,gBAAgB;AACrB,SAAK,4BAA4B,MAAM;AACvC,QAAI,KAAK,aAAa,GAAG;AACvB,iBAAW,eAAe,IAAI;AAC9B,YAAM,YAAY,IAAI;AAAA,IACxB;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,SAAK,aAAa;AAClB,QAAI,KAAK,aAAa,GAAG;AACvB,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AAAA,EACA,QAAQ,eAAe;AACrB,kBAAc,MAAM,MAAM;AACxB,UAAI,cAAc,WAAW;AAC3B,aAAK,UAAU,QAAQ,cAAY;AACjC,mBAAS,KAAK,cAAc;AAAA,QAC9B,CAAC;AAAA,MACH;AACA,WAAK,QAAQ,cAAc,EAAE,OAAO;AAAA,QAClC,OAAO,KAAK;AAAA,QACZ,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AACA,SAAS,kBAAkB,OAAO,SAAS;AACzC,SAAO,eAAe,QAAQ,SAAS,KAAK,MAAM,SAAS,MAAM,MAAM,SAAS,UAAU,EAAE,MAAM,MAAM,WAAW,WAAW,QAAQ,iBAAiB;AACzJ;AACA,SAAS,mBAAmB,OAAO,SAAS;AAC1C,SAAO,kBAAkB,OAAO,OAAO,KAAK,MAAM,MAAM,SAAS,UAAU,cAAc,OAAO,SAAS,QAAQ,cAAc;AACjI;AACA,SAAS,cAAc,OAAO,SAAS,OAAO;AAC5C,MAAI,eAAe,QAAQ,SAAS,KAAK,MAAM,OAAO;AACpD,UAAM,QAAQ,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AAC3D,WAAO,UAAU,YAAY,UAAU,SAAS,QAAQ,OAAO,OAAO;AAAA,EACxE;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,OAAO,WAAW,SAAS,aAAa;AACrE,UAAQ,UAAU,aAAa,eAAe,YAAY,SAAS,KAAK,MAAM,WAAW,CAAC,QAAQ,YAAY,MAAM,MAAM,WAAW,YAAY,QAAQ,OAAO,OAAO;AACzK;AACA,SAAS,QAAQ,OAAO,SAAS;AAC/B,SAAO,eAAe,QAAQ,SAAS,KAAK,MAAM,SAAS,MAAM,cAAc,iBAAiB,QAAQ,WAAW,KAAK,CAAC;AAC3H;AACA,SAAS,sCAAsC,UAAU,kBAAkB;AACzE,MAAI,CAAC,oBAAoB,SAAS,iBAAiB,GAAG,gBAAgB,GAAG;AACvE,WAAO;AAAA,EACT;AACA,SAAO;AACT;;;AC1aA,SAAS,WAAW,QAAQ,QAAQ;AAClC,SAAO,OAAO,OAAO,OAAK,CAAC,OAAO,SAAS,CAAC,CAAC;AAC/C;AACA,SAAS,UAAU,OAAO,OAAO,OAAO;AACtC,QAAM,OAAO,MAAM,MAAM,CAAC;AAC1B,OAAK,KAAK,IAAI;AACd,SAAO;AACT;AACA,IAAI,kBAAkB,cAAc,aAAa;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,mBAAmB,CAAC;AAAA,EACpB,YAAY,QAAQ,SAAS,SAAS;AACpC,UAAM;AACN,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,WAAW,CAAC;AACjB,SAAK,aAAa,CAAC;AACnB,SAAK,UAAU,CAAC;AAChB,SAAK,WAAW,OAAO;AAAA,EACzB;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,UAAU,SAAS,GAAG;AAC7B,WAAK,WAAW,QAAQ,cAAY;AAClC,iBAAS,UAAU,YAAU;AAC3B,eAAK,UAAU,UAAU,MAAM;AAAA,QACjC,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,UAAU,MAAM;AACxB,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,UAAU;AACR,SAAK,YAA2B,oBAAI,IAAI;AACxC,SAAK,WAAW,QAAQ,cAAY;AAClC,eAAS,QAAQ;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EACA,WAAW,SAAS,SAAS;AAC3B,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,QAAI,MAAuC;AACzC,YAAM,cAAc,QAAQ,IAAI,WAAS,KAAK,QAAQ,oBAAoB,KAAK,EAAE,SAAS;AAC1F,UAAI,IAAI,IAAI,WAAW,EAAE,SAAS,YAAY,QAAQ;AACpD,gBAAQ,KAAK,uFAAuF;AAAA,MACtG;AAAA,IACF;AACA,kBAAc,MAAM,MAAM;AACxB,YAAM,gBAAgB,KAAK;AAC3B,YAAM,qBAAqB,KAAK,uBAAuB,KAAK,QAAQ;AACpE,WAAK,mBAAmB;AACxB,yBAAmB,QAAQ,WAAS,MAAM,SAAS,WAAW,MAAM,qBAAqB,CAAC;AAC1F,YAAM,eAAe,mBAAmB,IAAI,WAAS,MAAM,QAAQ;AACnE,YAAM,YAAY,aAAa,IAAI,cAAY,SAAS,iBAAiB,CAAC;AAC1E,YAAM,iBAAiB,aAAa,KAAK,CAAC,UAAU,UAAU,aAAa,cAAc,KAAK,CAAC;AAC/F,UAAI,cAAc,WAAW,aAAa,UAAU,CAAC,gBAAgB;AACnE;AAAA,MACF;AACA,WAAK,aAAa;AAClB,WAAK,UAAU;AACf,UAAI,CAAC,KAAK,aAAa,GAAG;AACxB;AAAA,MACF;AACA,iBAAW,eAAe,YAAY,EAAE,QAAQ,cAAY;AAC1D,iBAAS,QAAQ;AAAA,MACnB,CAAC;AACD,iBAAW,cAAc,aAAa,EAAE,QAAQ,cAAY;AAC1D,iBAAS,UAAU,YAAU;AAC3B,eAAK,UAAU,UAAU,MAAM;AAAA,QACjC,CAAC;AAAA,MACH,CAAC;AACD,WAAK,QAAQ;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,WAAO,KAAK,WAAW,IAAI,cAAY,SAAS,gBAAgB,CAAC;AAAA,EACnE;AAAA,EACA,eAAe;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,oBAAoB,SAAS,SAAS;AACpC,UAAM,UAAU,KAAK,uBAAuB,OAAO;AACnD,UAAM,SAAS,QAAQ,IAAI,WAAS,MAAM,SAAS,oBAAoB,MAAM,qBAAqB,CAAC;AACnG,WAAO,CAAC,QAAQ,OAAK;AACnB,aAAO,KAAK,eAAe,KAAK,QAAQ,OAAO;AAAA,IACjD,GAAG,MAAM;AACP,aAAO,KAAK,aAAa,QAAQ,OAAO;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EACA,aAAa,QAAQ,SAAS;AAC5B,WAAO,QAAQ,IAAI,CAAC,OAAO,UAAU;AACnC,YAAM,iBAAiB,OAAO,KAAK;AACnC,aAAO,CAAC,MAAM,sBAAsB,sBAAsB,MAAM,SAAS,YAAY,gBAAgB,kBAAgB;AACnH,gBAAQ,QAAQ,OAAK;AACnB,YAAE,SAAS,UAAU,YAAY;AAAA,QACnC,CAAC;AAAA,MACH,CAAC,IAAI;AAAA,IACP,CAAC;AAAA,EACH;AAAA,EACA,eAAe,OAAO,SAAS;AAC7B,QAAI,SAAS;AACX,UAAI,CAAC,KAAK,mBAAmB,KAAK,YAAY,KAAK,eAAe,YAAY,KAAK,cAAc;AAC/F,aAAK,eAAe;AACpB,aAAK,cAAc,KAAK;AACxB,aAAK,kBAAkB,iBAAiB,KAAK,iBAAiB,QAAQ,KAAK,CAAC;AAAA,MAC9E;AACA,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA,EACA,uBAAuB,SAAS;AAC9B,UAAM,mBAAmB,IAAI,IAAI,KAAK,WAAW,IAAI,cAAY,CAAC,SAAS,QAAQ,WAAW,QAAQ,CAAC,CAAC;AACxG,UAAM,YAAY,CAAC;AACnB,YAAQ,QAAQ,aAAW;AACzB,YAAM,mBAAmB,KAAK,QAAQ,oBAAoB,OAAO;AACjE,YAAM,QAAQ,iBAAiB,IAAI,iBAAiB,SAAS;AAC7D,UAAI,OAAO;AACT,kBAAU,KAAK;AAAA,UACb,uBAAuB;AAAA,UACvB,UAAU;AAAA,QACZ,CAAC;AAAA,MACH,OAAO;AACL,kBAAU,KAAK;AAAA,UACb,uBAAuB;AAAA,UACvB,UAAU,IAAI,cAAc,KAAK,SAAS,gBAAgB;AAAA,QAC5D,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,UAAU,UAAU,QAAQ;AAC1B,UAAM,QAAQ,KAAK,WAAW,QAAQ,QAAQ;AAC9C,QAAI,UAAU,IAAI;AAChB,WAAK,UAAU,UAAU,KAAK,SAAS,OAAO,MAAM;AACpD,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,UAAU;AACR,QAAI,KAAK,aAAa,GAAG;AACvB,YAAM,iBAAiB,KAAK;AAC5B,YAAM,aAAa,KAAK,aAAa,KAAK,SAAS,KAAK,gBAAgB;AACxE,YAAM,YAAY,KAAK,eAAe,YAAY,KAAK,UAAU,OAAO;AACxE,UAAI,mBAAmB,WAAW;AAChC,sBAAc,MAAM,MAAM;AACxB,eAAK,UAAU,QAAQ,cAAY;AACjC,qBAAS,KAAK,OAAO;AAAA,UACvB,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;;;ACrKA,IAAI,wBAAwB,cAAc,cAAc;AAAA,EACtD,YAAY,QAAQ,SAAS;AAC3B,UAAM,QAAQ,OAAO;AAAA,EACvB;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AAAA,EAC3D;AAAA,EACA,WAAW,SAAS;AAClB,UAAM,WAAW,iCACZ,UADY;AAAA,MAEf,UAAU,sBAAsB;AAAA,IAClC,EAAC;AAAA,EACH;AAAA,EACA,oBAAoB,SAAS;AAC3B,YAAQ,WAAW,sBAAsB;AACzC,WAAO,MAAM,oBAAoB,OAAO;AAAA,EAC1C;AAAA,EACA,cAAc,SAAS;AACrB,WAAO,KAAK,MAAM,iCACb,UADa;AAAA,MAEhB,MAAM;AAAA,QACJ,WAAW;AAAA,UACT,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF,EAAC;AAAA,EACH;AAAA,EACA,kBAAkB,SAAS;AACzB,WAAO,KAAK,MAAM,iCACb,UADa;AAAA,MAEhB,MAAM;AAAA,QACJ,WAAW;AAAA,UACT,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF,EAAC;AAAA,EACH;AAAA,EACA,aAAa,OAAO,SAAS;AAC3B,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,MAAM,aAAa,OAAO,OAAO;AACtD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,iBAAiB,MAAM,WAAW,WAAW;AACnD,UAAM,uBAAuB,WAAW,mBAAmB;AAC3D,UAAM,qBAAqB,cAAc,mBAAmB;AAC5D,UAAM,2BAA2B,WAAW,mBAAmB;AAC/D,UAAM,yBAAyB,cAAc,mBAAmB;AAChE,UAAM,SAAS,iCACV,eADU;AAAA,MAEb,eAAe,KAAK;AAAA,MACpB,mBAAmB,KAAK;AAAA,MACxB,aAAa,YAAY,SAAS,MAAM,IAAI;AAAA,MAC5C,iBAAiB,gBAAgB,SAAS,MAAM,IAAI;AAAA,MACpD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,gBAAgB,kBAAkB,CAAC,wBAAwB,CAAC;AAAA,MAC5D,cAAc,gBAAgB,CAAC,sBAAsB,CAAC;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AACF;;;ACpEA,IAAI,mBAAmB,cAAc,aAAa;AAAA,EAChD;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,YAAY,QAAQ,SAAS;AAC3B,UAAM;AACN,SAAK,UAAU;AACf,SAAK,WAAW,OAAO;AACvB,SAAK,YAAY;AACjB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AACnC,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AAAA,EACnC;AAAA,EACA,WAAW,SAAS;AAClB,UAAM,cAAc,KAAK;AACzB,SAAK,UAAU,KAAK,QAAQ,uBAAuB,OAAO;AAC1D,QAAI,CAAC,oBAAoB,KAAK,SAAS,WAAW,GAAG;AACnD,WAAK,QAAQ,iBAAiB,EAAE,OAAO;AAAA,QACrC,MAAM;AAAA,QACN,UAAU,KAAK;AAAA,QACf,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,QAAI,aAAa,eAAe,KAAK,QAAQ,eAAe,QAAQ,YAAY,WAAW,MAAM,QAAQ,KAAK,QAAQ,WAAW,GAAG;AAClI,WAAK,MAAM;AAAA,IACb,WAAW,KAAK,kBAAkB,MAAM,WAAW,WAAW;AAC5D,WAAK,iBAAiB,WAAW,KAAK,OAAO;AAAA,IAC/C;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,aAAa,GAAG;AACxB,WAAK,kBAAkB,eAAe,IAAI;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,iBAAiB,QAAQ;AACvB,SAAK,cAAc;AACnB,SAAK,QAAQ,MAAM;AAAA,EACrB;AAAA,EACA,mBAAmB;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,QAAQ;AACN,SAAK,kBAAkB,eAAe,IAAI;AAC1C,SAAK,mBAAmB;AACxB,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,OAAO,WAAW,SAAS;AACzB,SAAK,iBAAiB;AACtB,SAAK,kBAAkB,eAAe,IAAI;AAC1C,SAAK,mBAAmB,KAAK,QAAQ,iBAAiB,EAAE,MAAM,KAAK,SAAS,KAAK,OAAO;AACxF,SAAK,iBAAiB,YAAY,IAAI;AACtC,WAAO,KAAK,iBAAiB,QAAQ,SAAS;AAAA,EAChD;AAAA,EACA,gBAAgB;AACd,UAAM,QAAQ,KAAK,kBAAkB,SAASC,iBAAgB;AAC9D,SAAK,iBAAiB,iCACjB,QADiB;AAAA,MAEpB,WAAW,MAAM,WAAW;AAAA,MAC5B,WAAW,MAAM,WAAW;AAAA,MAC5B,SAAS,MAAM,WAAW;AAAA,MAC1B,QAAQ,MAAM,WAAW;AAAA,MACzB,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,IACd;AAAA,EACF;AAAA,EACA,QAAQ,QAAQ;AACd,kBAAc,MAAM,MAAM;AACxB,UAAI,KAAK,kBAAkB,KAAK,aAAa,GAAG;AAC9C,cAAM,YAAY,KAAK,eAAe;AACtC,cAAM,UAAU,KAAK,eAAe;AACpC,YAAI,QAAQ,SAAS,WAAW;AAC9B,eAAK,eAAe,YAAY,OAAO,MAAM,WAAW,OAAO;AAC/D,eAAK,eAAe,YAAY,OAAO,MAAM,MAAM,WAAW,OAAO;AAAA,QACvE,WAAW,QAAQ,SAAS,SAAS;AACnC,eAAK,eAAe,UAAU,OAAO,OAAO,WAAW,OAAO;AAC9D,eAAK,eAAe,YAAY,QAAQ,OAAO,OAAO,WAAW,OAAO;AAAA,QAC1E;AAAA,MACF;AACA,WAAK,UAAU,QAAQ,cAAY;AACjC,iBAAS,KAAK,cAAc;AAAA,MAC9B,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;;;AC1FA,SAAS,qBAAqB,MAAM;AAClC,SAAO;AACT;AACA,SAAS,kBAAkB,UAAU;AACnC,SAAO;AAAA,IACL,aAAa,SAAS,QAAQ;AAAA,IAC9B,OAAO,SAAS;AAAA,KACZ,SAAS,QAAQ,SAAS;AAAA,IAC5B,OAAO,SAAS,QAAQ;AAAA,EAC1B,IACI,SAAS,QAAQ;AAAA,IACnB,MAAM,SAAS;AAAA,EACjB;AAEJ;AACA,SAAS,eAAe,OAAO,eAAe,oBAAoB;AAChE,SAAO;AAAA,IACL,cAAc,KAAK,IAAI;AAAA,IACvB,OAAO,kCACF,MAAM,QACL,MAAM,MAAM,SAAS,UAAU;AAAA,MACjC,MAAM,cAAc,MAAM,MAAM,IAAI;AAAA,IACtC;AAAA,IAEF,UAAU,MAAM;AAAA,IAChB,WAAW,MAAM;AAAA,KACb,MAAM,MAAM,WAAW,aAAa;AAAA,IACtC,SAAS,MAAM,SAAS,KAAK,aAAa,EAAE,MAAM,WAAS;AACzD,UAAI,CAAC,mBAAmB,KAAK,GAAG;AAC9B,eAAO,QAAQ,OAAO,KAAK;AAAA,MAC7B;AACA,UAAI,MAAuC;AACzC,gBAAQ,MAAM,+DAA+D,MAAM,SAAS,MAAM,KAAK,mDAAmD;AAAA,MAC5J;AACA,aAAO,QAAQ,OAAO,IAAI,MAAM,UAAU,CAAC;AAAA,IAC7C,CAAC;AAAA,EACH,IACI,MAAM,QAAQ;AAAA,IAChB,MAAM,MAAM;AAAA,EACd;AAEJ;AACA,SAAS,+BAA+B,UAAU;AAChD,SAAO,SAAS,MAAM;AACxB;AACA,SAAS,4BAA4B,OAAO;AAC1C,SAAO,MAAM,MAAM,WAAW;AAChC;AACA,SAAS,0BAA0B,GAAG;AACpC,SAAO;AACT;AACA,SAAS,UAAU,QAAQ,UAAU,CAAC,GAAG;AACvC,QAAM,iBAAiB,QAAQ,2BAA2B,OAAO,kBAAkB,EAAE,WAAW,2BAA2B;AAC3H,QAAM,YAAY,OAAO,iBAAiB,EAAE,OAAO,EAAE,QAAQ,cAAY,eAAe,QAAQ,IAAI,CAAC,kBAAkB,QAAQ,CAAC,IAAI,CAAC,CAAC;AACtI,QAAM,cAAc,QAAQ,wBAAwB,OAAO,kBAAkB,EAAE,WAAW,wBAAwB;AAClH,QAAM,qBAAqB,QAAQ,sBAAsB,OAAO,kBAAkB,EAAE,WAAW,sBAAsB;AACrH,QAAM,gBAAgB,QAAQ,iBAAiB,OAAO,kBAAkB,EAAE,WAAW,iBAAiB;AACtG,QAAM,UAAU,OAAO,cAAc,EAAE,OAAO,EAAE,QAAQ,WAAS,YAAY,KAAK,IAAI,CAAC,eAAe,OAAO,eAAe,kBAAkB,CAAC,IAAI,CAAC,CAAC;AACrJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,QAAQ,QAAQ,iBAAiB,SAAS;AACjD,MAAI,OAAO,oBAAoB,YAAY,oBAAoB,MAAM;AACnE;AAAA,EACF;AACA,QAAM,gBAAgB,OAAO,iBAAiB;AAC9C,QAAM,aAAa,OAAO,cAAc;AACxC,QAAM,kBAAkB,SAAS,gBAAgB,mBAAmB,OAAO,kBAAkB,EAAE,SAAS,mBAAmB;AAC3H,QAAM,YAAY,gBAAgB,aAAa,CAAC;AAChD,QAAM,UAAU,gBAAgB,WAAW,CAAC;AAC5C,YAAU,QAAQ,CAAC,OAGb;AAHa,iBACjB;AAAA;AAAA,IA3EJ,IA0EqB,IAEdC,mBAAA,UAFc,IAEd;AAAA,MADH;AAAA;AAGA,kBAAc,MAAM,QAAQ,iDACvB,OAAO,kBAAkB,EAAE,SAAS,YACpC,SAAS,gBAAgB,YACzBA,mBACF,KAAK;AAAA,EACV,CAAC;AACD,UAAQ,QAAQ,CAAC;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,UAAM,WAAW,UAAU,eAAe,OAAO,IAAI;AACrD,UAAM,UAAU,MAAM,SAAS,SAAS,UAAU,OAAO,MAAM;AAC/D,UAAM,OAAO,YAAY,SAAS,UAAU,gBAAgB,OAAO;AACnE,QAAI,QAAQ,WAAW,IAAI,SAAS;AACpC,UAAM,yBAAyB,OAAO,MAAM,WAAW;AACvD,UAAM,0BAA0B,OAAO,MAAM,gBAAgB;AAC7D,QAAI,OAAO;AACT,YAAM,mBAAmB;AAAA;AAAA,MAGzB,iBAAiB,UAAU,eAAe,MAAM,MAAM;AACtD,UAAI,MAAM,gBAAgB,MAAM,MAAM,iBAAiB,kBAAkB;AACvE,cAGI,YAFF;AAAA,uBAAa;AAAA,QAzGvB,IA2GY,IADC,4BACD,IADC;AAAA,UADH;AAAA;AAGF,cAAM,SAAS,iCACV,kBADU;AAAA,UAEb;AAAA,QACF,EAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,cAAQ,WAAW;AAAA,QAAM;AAAA,QAAQ,gDAC5B,OAAO,kBAAkB,EAAE,SAAS,UACpC,SAAS,gBAAgB,UAFG;AAAA,UAG/B;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA;AAAA;AAAA,QAGA,iCACK,QADL;AAAA,UAEE;AAAA,UACA,aAAa;AAAA,UACb,QAAQ,SAAS,SAAS,YAAY,MAAM;AAAA,QAC9C;AAAA,MAAC;AAAA,IACH;AACA,QAAI,WAAW,CAAC,0BAA0B,CAAC;AAAA;AAAA,KAG3C,iBAAiB,UAAU,eAAe,MAAM,MAAM,gBAAgB;AACpE,WAAK,MAAM,MAAM,QAAQ;AAAA;AAAA,QAEvB,gBAAgB,QAAQ,QAAQ,OAAO,EAAE,KAAK,eAAe;AAAA,MAC/D,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH;;;AC1IA,SAAS,cAAc;AAAA,EACrB;AAAA,EACA,cAAc;AAAA,EACd;AACF,GAAG;AACD,SAAO,CAAM,YAAW;AACtB,UAAM,QAAQ,QAAQ,OAAO,cAAc,EAAE,KAAK;AAAA,MAChD,UAAU,QAAQ;AAAA,MAClB,OAAO;AAAA,IACT,CAAC;AACD,UAAM,YAAY,CAAC,CAAC,SAAS,MAAM,MAAM,SAAS;AAClD,QAAI,aAAa,gBAAgB,SAAS;AACxC,YAAM,SAAS;AAAA,QACb,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,OAAO;AAAA,QACP,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AACA,QAAI,SAAS,CAAC;AACd,UAAM,SAAS,MAAM,QAAQ,OAAO;AACpC;AAAA,iCAA0B,SAA1B,0EAAkC;AAAvB,cAAM,QAAjB;AACE,YAAI,QAAQ,OAAO,SAAS;AAC1B;AAAA,QACF;AACA,YAAI,CAAC,aAAa,gBAAgB,WAAW;AAC3C,kBAAQ,OAAO,aAAa,QAAQ,UAAU,CAAC,OAAO,CAAC,MAAM;AAC3D,mBAAO,SAAS,MAAM,OAAO,SAAS;AAAA,UACxC,CAAC;AAAA,QACH;AACA,iBAAS,SAAS,QAAQ,OAAO,SAAS;AAAA,MAC5C;AAAA,aAVA,MAvBJ;AAuBI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA,QAAI,aAAa,gBAAgB,aAAa,CAAC,QAAQ,OAAO,SAAS;AACrE,cAAQ,OAAO,aAAa,QAAQ,UAAU,MAAM;AAAA,IACtD;AACA,WAAO,QAAQ,OAAO,aAAa,QAAQ,QAAQ;AAAA,EACrD;AACF;;;ACtCA,IAAI,gBAAgB,OAAO,eAAe;AAC1C,IAAI,qBAAqB,OAAO,oBAAoB;AACpD,IAAI,cAAc,OAAO,aAAa;;;ACCtC,SAAS,aAAa,SAAS;AAC7B,SAAO;AACT;AAGA,SAAS,gBAAgB,SAAS;AAChC,SAAO;AACT;AAGA,SAAS,qBAAqB,SAAS;AACrC,SAAO;AACT;AAYA,SAAS,YAAY,aAAa;AAChC,QAAM,gBAAgB,CAAC;AACvB,SAAO,IAAI,MAAM,eAAe;AAAA,IAC9B,IAAI,QAAQ,MAAM;AAChB,YAAM,gBAAgB,OAAO,IAAI;AACjC,UAAI,cAAe,QAAO;AAC1B,YAAM,cAAc,UAAU,WAAW,EAAE,IAAI;AAC/C,UAAI,OAAO,gBAAgB,WAAY,QAAO;AAC9C,aAAO,OAAO,IAAI,IAAI,SAAS,MAAM,YAAY,EAAE,IAAI,CAAC;AAAA,IAC1D;AAAA,IACA,IAAI,GAAG,MAAM;AACX,aAAO,CAAC,CAAC,UAAU,WAAW,EAAE,IAAI;AAAA,IACtC;AAAA,IACA,UAAU;AACR,aAAO,QAAQ,QAAQ,UAAU,WAAW,CAAC;AAAA,IAC/C;AAAA,IACA,2BAA2B;AACzB,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,cAAc;AAAA,MAChB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAIA,IAAI,eAAe,IAAI,eAAe,OAAO,cAAc,eAAe,YAAY,gCAAgC,IAAI;AAAA;AAAA,EAExH,SAAS,MAAM,OAAO,KAAK,EAAE,WAAW;AAC1C,CAAC;AACD,SAAS,kBAAkB,SAAS;AAClC,GAAC,SAAS,YAAY,yBAAyB,iBAAiB;AAChE,QAAM,WAAW,SAAS,YAAY,OAAO,QAAQ;AACrD,SAAO,SAAS,IAAI,YAAY;AAClC;AACA,SAAS,mBAAmB,aAAa;AACvC,SAAO;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,EACZ;AACF;AAGA,SAAS,gBAAgB,WAAW,UAAU;AAC5C,QAAM,SAAS,OAAQ,MAAM;AAC7B,QAAM,cAAc,OAAQ,WAAW;AACvC,QAAM,cAAc,kBAAkB;AACtC,QAAM,yBAAyB,SAAU,MAAM;AAC7C,UAAM,mBAAmB,YAAY,oBAAoB,UAAU,CAAC;AACpE,qBAAiB,qBAAqB,YAAY,IAAI,gBAAgB;AACtE,WAAO;AAAA,EACT,CAAC;AACD,QAAM,kBAAkB,MAAM;AAC5B,QAAI,WAAW;AACf,WAAO,SAAU,MAAM;AACrB,aAAO,aAAa,IAAI,SAAS,aAAa,uBAAuB,CAAC;AAAA,IACxE,CAAC;AAAA,EACH,GAAG;AACH,QAAM,yBAAyB,SAAU,MAAM,eAAe,EAAE,oBAAoB,uBAAuB,CAAC,CAAC;AAC7G,QAAM,6BAA6B,OAAQ,IAAI;AAC/C,SAAO,eAAa;AAClB,UAAM,WAAW,eAAe;AAChC,UAAM,mBAAmB,uBAAuB;AAChD,cAAW,MAAM;AACf,eAAS,WAAW,gBAAgB;AAAA,IACtC,CAAC;AACD,cAAU,MAAM;AACd,aAAO,IAAI,MAAM,2BAA2B,IAAI,IAAI,CAAC;AAAA,IACvD,CAAC;AAAA,EACH,GAAG;AAAA;AAAA;AAAA,IAGD,mBAAmB,QAAQ,QAAQ,QAAQ;AAAA,EAC7C,CAAC;AACD,SAAO,eAAa;AAClB,UAAM,WAAW,eAAe;AAChC,UAAM,cAAc,YAAY,IAAI,MAAM,SAAS,UAAW,MAAM,OAAO,kBAAkB,MAAM,SAAS,UAAU,cAAc,WAAW,WAAS;AACtJ,aAAO,IAAI,MAAM;AACf,YAAI,MAAM,WAAW,CAAC,MAAM,cAAc,iBAAiB,SAAS,QAAQ,cAAc,CAAC,MAAM,OAAO,SAAS,gBAAgB,CAAC,CAAC,GAAG;AACpI,iBAAO,QAAQ,KAAK,MAAM,KAAK;AAC/B,gBAAM,MAAM;AAAA,QACd;AACA,mCAA2B,IAAI,KAAK;AAAA,MACtC,CAAC;AAAA,IACH,CAAC,CAAC,CAAC,CAAC;AACJ,cAAU,WAAW;AAAA,EACvB,CAAC;AACD,SAAO,YAAY,SAAU,MAAM;AACjC,UAAM,mBAAmB,2BAA2B;AACpD,UAAM,mBAAmB,uBAAuB;AAChD,WAAO,oBAAoB;AAAA,EAC7B,CAAC,CAAC;AACJ;AAGA,SAAS,oBAAoB,uBAAuB,SAAS;AAC3D,GAAC,SAAS,YAAY,yBAA0B,mBAAmB;AACnE,QAAM,WAAW,SAAS,YAAY,OAAQ,QAAS;AACvD,SAAO,sBAAsB,UAAU,MAAM,gBAAgB,uBAAuB,qBAAqB,CAAC;AAC5G;AAKA,SAAS,iBAAiB,SAAS,SAAS;AAC1C,GAAC,SAAS,YAAY,yBAA0B,gBAAgB;AAChE,QAAM,WAAW,SAAS,YAAY,OAAQ,QAAS;AACvD,QAAM,aAAa,SAAS,IAAI,UAAU;AAC1C,QAAM,SAAS,SAAS,IAAI,MAAO;AACnC,QAAM,cAAc,SAAS,IAAI,WAAY;AAC7C,QAAM,QAAQ,YAAY,cAAc;AACxC,MAAI,aAAa,YAAY,WAAW,OAAO;AAC/C,QAAM,SAAS,OAAQ,UAAU;AACjC,QAAM,cAAc,OAAO,kBAAkB,MAAM,MAAM,UAAU,cAAe,WAAW,MAAM;AACjG,UAAM,gBAAgB,YAAY,WAAW,OAAO;AACpD,QAAI,eAAe,eAAe;AAChC,mBAAa;AACb,aAAO,IAAI,MAAM;AACf,eAAO,IAAI,UAAU;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC,CAAC;AACH,aAAW,UAAU,WAAW;AAChC,SAAO;AACT;AAKA,SAAS,iBAAiB,SAAS,SAAS;AAC1C,GAAC,SAAS,YAAY,yBAA0B,gBAAgB;AAChE,QAAM,WAAW,SAAS,YAAY,OAAQ,QAAS;AACvD,QAAM,aAAa,SAAS,IAAI,UAAW;AAC3C,QAAM,SAAS,SAAS,IAAI,MAAO;AACnC,QAAM,cAAc,SAAS,IAAI,WAAY;AAC7C,QAAM,QAAQ,YAAY,iBAAiB;AAC3C,MAAI,aAAa,YAAY,WAAW,OAAO;AAC/C,QAAM,SAAS,OAAQ,UAAU;AACjC,QAAM,cAAc,OAAO,kBAAkB,MAAM,MAAM,UAAU,cAAe,WAAW,MAAM;AACjG,UAAM,gBAAgB,YAAY,WAAW,OAAO;AACpD,QAAI,eAAe,eAAe;AAChC,mBAAa;AACb,aAAO,IAAI,MAAM;AACf,eAAO,IAAI,UAAU;AAAA,MACvB,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC,CAAC;AACH,aAAW,UAAU,WAAW;AAChC,SAAO;AACT;AAOA,SAASC,QAAO;AAAC;AAGjB,SAAS,eAAe,kBAAkB,SAAS;AACjD,GAAC,SAAS,YAAY,yBAA0B,cAAc;AAC9D,QAAM,WAAW,SAAS,YAAY,OAAQ,QAAS;AACvD,QAAM,aAAa,SAAS,IAAI,UAAW;AAC3C,QAAM,SAAS,SAAS,IAAI,MAAO;AACnC,QAAM,cAAc,SAAS,IAAI,WAAY;AAC7C,QAAM,gBAAgB,SAAU,gBAAgB;AAChD,QAAM,kBAAkB,MAAM;AAC5B,QAAI,WAAW;AACf,WAAO,SAAU,MAAM;AACrB,aAAO,aAAa,IAAI,iBAAiB,aAAa,cAAc,CAAC;AAAA,IACvE,CAAC;AAAA,EACH,GAAG;AACH,QAAM,iBAAiB,SAAU,MAAM;AACrC,UAAM,WAAW,eAAe;AAChC,WAAO,CAAC,WAAW,kBAAkB;AACnC,eAAS,OAAO,WAAW,aAAa,EAAE,MAAMA,KAAI;AAAA,IACtD;AAAA,EACF,CAAC;AACD,QAAM,iCAAiC,SAAU,MAAM;AACrD,UAAM,WAAW,eAAe;AAChC,WAAO,SAAS,iBAAiB;AAAA,EACnC,CAAC;AACD,QAAM,6BAA6B,OAAQ,IAAI;AAC/C,SAAQ,MAAM;AACZ,UAAM,WAAW,eAAe;AAChC,UAAM,kBAAkB,cAAc;AACtC,cAAW,MAAM;AACf,eAAS,WAAW,eAAe;AAAA,IACrC,CAAC;AAAA,EACH,GAAG;AAAA,IACD;AAAA,EACF,CAAC;AACD,SAAQ,MAAM;AACZ,UAAM,WAAW,eAAe;AAChC,cAAW,MAAM;AACf,YAAM,cAAc,OAAO,kBAAkB,MAAM,SAAS,UAAU,cAAe,WAAW,WAAS;AACvG,eAAO,IAAI,MAAM;AACf,cAAI,MAAM,WAAW,iBAAkB,SAAS,QAAQ,cAAc,CAAC,MAAM,KAAK,CAAC,GAAG;AACpF,mBAAO,QAAQ,KAAK,MAAM,KAAK;AAC/B,kBAAM,MAAM;AAAA,UACd;AACA,qCAA2B,IAAI,KAAK;AAAA,QACtC,CAAC;AAAA,MACH,CAAC,CAAC,CAAC;AACH,iBAAW,UAAU,WAAW;AAAA,IAClC,CAAC;AAAA,EACH,GAAG;AAAA,IACD;AAAA,EACF,CAAC;AACD,QAAM,eAAe,SAAU,MAAM;AACnC,UAAM,uBAAuB,2BAA2B;AACxD,UAAM,2BAA2B,+BAA+B;AAChE,UAAM,SAAS,wBAAwB;AACvC,WAAO,iCACF,SADE;AAAA,MAEL,QAAQ,eAAe;AAAA,MACvB,aAAa,OAAO;AAAA,IACtB;AAAA,EACF,CAAC;AACD,SAAO,YAAY,YAAY;AACjC;AAKA,SAAS,UAAU,eAAe,SAAS;AACzC,SAAO,cAAc,QAAQ,QAAQ,OAAO,EAAE,IAAI,cAAY,QAAQ,SAAS,QAAQ,OAAO,QAAQ,IAAI,SAAS,KAAK;AAC1H;AACA,SAAS,oBAAoB,wBAAwB,OAAO,CAAC,IAAI,SAAS;AACxE,GAAC,SAAS,YAAY,yBAA0B,mBAAmB;AACnE,QAAM,WAAW,SAAS,YAAY,OAAQ,QAAS;AACvD,QAAM,aAAa,SAAS,IAAI,UAAW;AAC3C,QAAM,SAAS,SAAS,IAAI,MAAO;AACnC,QAAM,cAAc,SAAS,IAAI,WAAY;AAC7C,QAAM,gBAAgB,YAAY,iBAAiB;AACnD,QAAM,0BAA0B,SAAU,MAAM;AAC9C,WAAO,CAAC,UAAU,eAAe,sBAAsB,CAAC,GAAG,YAAY,IAAI,CAAC;AAAA,EAC9E,CAAC;AACD,QAAM,6BAA6B,OAAQ,IAAI;AAC/C,QAAM,wBAAwB,SAAU,MAAM;AAC5C,UAAM,gBAAgB,wBAAwB;AAC9C,UAAM,mBAAmB,2BAA2B;AACpD,WAAO,oBAAoB,iBAAiB,CAAC,IAAI,cAAc,CAAC,IAAI,iBAAiB,CAAC,IAAI,cAAc,CAAC;AAAA,EAC3G,CAAC;AACD,QAAM,cAAc,OAAO,kBAAkB,MAAM,cAAc,UAAU,cAAe,WAAW,MAAM;AACzG,UAAM,CAAC,UAAU,IAAI,sBAAsB;AAC3C,UAAM,aAAa,iBAAiB,YAAY,UAAU,eAAe,sBAAsB,CAAC,CAAC;AACjG,QAAI,eAAe,YAAY;AAC7B,aAAO,IAAI,MAAM;AACf,mCAA2B,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,CAAC;AAAA,MAChE,CAAC;AAAA,IACH;AAAA,EACF,CAAC,CAAC,CAAC;AACH,aAAW,UAAU,WAAW;AAChC,SAAO;AACT;AAKA,SAAS,cAAc,IAGpB,UAAU;AAHU,eACrB;AAAA;AAAA,EAlSF,IAiSuB,IAElB,oBAFkB,IAElB;AAAA,IADH;AAAA;AAGA,GAAC,YAAY,yBAA0B,aAAa;AACpD,SAAO,sBAAuB,YAAY,OAAQ,QAAS,GAAG,MAAM;AAClE,UAAM,aAAa,OAAQ,UAAW;AACtC,UAAM,SAAS,OAAQ,MAAO;AAC9B,UAAM,cAAc,OAAQ,WAAY;AACxC,UAAM,cAAc,kBAAkB;AACtC,UAAM,mBAAmB,SAAU,MAAM;AACvC,aAAO,QAAQ,EAAE,IAAI,UAAQ;AAC3B,cAAM,mBAAmB,YAAY,oBAAoB,IAAI;AAC7D,yBAAiB,qBAAqB,YAAY,IAAI,gBAAgB;AACtE,eAAO;AAAA,MACT,CAAC;AAAA,IACH,CAAC;AACD,UAAM,WAAW,IAAI,gBAAgB,aAAa,iBAAiB,GAAG,OAAO;AAC7E,WAAQ,MAAM;AACZ,eAAS,WAAW,iBAAiB,GAAG,OAAO;AAAA,IACjD,CAAC;AACD,UAAM,CAAC,EAAE,iBAAiB,IAAI,SAAS,oBAAoB,iBAAiB,GAAG,QAAQ,OAAO;AAC9F,UAAM,SAAS,OAAQ,kBAAkB,CAAC;AAC1C,WAAQ,MAAM;AACZ,YAAM,cAAc,YAAY,IAAI,MAAM,SAAS,OAAO,kBAAkB,MAAM,SAAS,UAAU,cAAe,WAAW,OAAO,GAAG,CAAC,CAAC;AAC3I,iBAAW,UAAU,WAAW;AAAA,IAClC,CAAC;AACD,WAAO;AAAA,EACT,CAAC;AACH;AAKA,SAAS,YAAY,eAAe,SAAS;AAC3C,GAAC,SAAS,YAAY,yBAA0B,WAAW;AAC3D,SAAO,sBAAuB,SAAS,YAAY,OAAQ,QAAS,GAAG,MAAM,gBAAgB,eAAe,aAAa,CAAC;AAC5H;AAKA,SAAS,kBAAkB,gBAAgB,CAAC,GAAG;AAC7C,UAAQ,cAAc,YAAY,OAAS,QAAS,GAAG,IAAI,WAAY;AACzE;AAWA,SAAS,mBAAmB,aAAa;AACvC,SAAO;AAAA,IACL,SAAS;AAAA,IACT,YAAY,MAAM;AAChB,YAAM,SAAS,uBAAuB,iBAAkB,OAAS,WAAW,IAAI;AAChF,aAAS,UAAW,EAAE,UAAU,MAAM,OAAO,QAAQ,CAAC;AACtD,aAAO,MAAM;AACb,aAAO;AAAA,IACT;AAAA,EACF;AACF;AACA,SAAS,qBAAqB,gBAAgB,UAAU;AACtD,SAAO,CAAC,mBAAmB,WAAW,GAAG,SAAS,IAAI,aAAW,QAAQ,UAAU,CAAC;AACtF;AACA,SAAS,aAAa,MAAM,WAAW;AACrC,SAAO;AAAA,IACL,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AACF;AACA,SAAS,aAAa,gBAAgB;AACpC,MAAI,YAAY,CAAC;AACjB,MAAI,CAAC,UAAU,KAAK,CAAC,gBAAgB;AACnC,gBAAY,CAAC;AAAA,EACf,OAAO;AACL,gBAAY,CAAC;AAAA;AAAA,MAEX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,YAAY,MAAM;AAChB,YAAI,CAAC,kBAAkB,OAAS,WAAW,CAAC,EAAG,QAAOA;AACtD,cAAM,iBAAiB,OAAS,aAAc;AAAA,UAC5C,UAAU;AAAA,QACZ,CAAC;AACD,cAAM,aAAa,OAAS,UAAW;AACvC,cAAM,UAAU,SAAU,MAAM,iBAAiB,KAAK,CAAC,CAAC;AACxD,YAAI,WAAW;AACf,YAAI,KAAK;AACT,cAAM,wBAAwB,SAAU,MAAM;AAC5C,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI,QAAQ;AACZ,iBAAO,OAAO,iBAAiB,YAAY,eAAe,UAAU;AAAA,QACtE,CAAC;AACD,cAAM,yBAAyB,MAAM;AACnC,gBAAM,SAAS,QAAQ,EAAE,UAAU;AACnC,cAAI,CAAC,QAAQ;AACX,kBAAM,IAAI,MAAM,sBAAsB;AAAA,UACxC;AACA,iBAAO;AAAA,QACT;AACA,cAAM,kBAAkB,MAAM;AAC5B,oBAAU,QAAQ;AAClB,cAAI,OAAO;AACX,qBAAW;AAAA,QACb;AACA,eAAO,MAAM,OAAQ,MAAM;AACzB,gBAAM,kBAAkB,sBAAsB;AAC9C,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI,QAAQ;AACZ,cAAI,YAAY,CAAC,iBAAiB;AAChC,4BAAgB;AAChB;AAAA,UACF,WAAW,YAAY,iBAAiB;AACtC,sBAAU,SAAS,UAAU,MAAM;AACnC,wBAAY,SAAS,YAAY,QAAQ;AACzC,0BAAc,SAAS,cAAc,UAAU;AAC/C,8BAAkB,SAAS,kBAAkB,cAAc;AAC3D,6BAAiB,SAAS,iBAAiB,aAAa;AACxD;AAAA,UACF,WAAW,CAAC,iBAAiB;AAC3B;AAAA,UACF;AACA,eAAK,SAAS,KAAK,YAAY,SAAS,cAAc,KAAK,CAAC;AAC5D,aAAG,UAAU,IAAI,uBAAuB;AACxC,iBAAO,mBAA0B,EAAE,KAAK,mBAAiB;AACvD,uBAAW,IAAI,cAAc,sBAAsB,iCAC9C,QAAQ,IADsC;AAAA,cAEjD,QAAQ,uBAAuB;AAAA,cAC/B,aAAa;AAAA,cACb,SAAS;AAAA,cACT;AAAA,YACF,EAAC;AACD,kBAAM,SAAS,MAAM,EAAE;AACvB,uBAAW,UAAU,eAAe;AAAA,UACtC,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO,aAAa,kBAAkB,SAAS;AACjD;AACA,IAAI,gBAAgB,CAAC,kBAAkB,oBAAoB;", "names": ["getDefaultState", "getDefaultState", "mutationOptions", "noop"]}