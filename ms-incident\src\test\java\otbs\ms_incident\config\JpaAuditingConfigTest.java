package otbs.ms_incident.config;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.data.domain.AuditorAware;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Collections;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

class JpaAuditingConfigTest {

    private AuditorAware<String> auditorProvider;

    @BeforeEach
    void setUp() {
        // Clear security context before each test
        SecurityContextHolder.clearContext();
        JpaAuditingConfig config = new JpaAuditingConfig();
        auditorProvider = config.auditorProvider();
    }

    @AfterEach
    void tearDown() {
        // Clean up the security context after each test
        SecurityContextHolder.clearContext();
    }

    @Test
    void auditorProvider_shouldReturnSystemAsDefaultAuditor() {
        // When
        Optional<String> auditor = auditorProvider.getCurrentAuditor();
        
        // Then
        assertThat(auditor).isPresent();
        assertThat(auditor.get()).contains("system");
    }
    
    @Test
    void auditorProvider_shouldReturnAuthenticatedUsername() {
        // Given
        String username = "testuser";
        Authentication authentication = new UsernamePasswordAuthenticationToken(
            username, 
            "password", 
            Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"))
        );
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // When
        Optional<String> auditor = auditorProvider.getCurrentAuditor();
        
        // Then
        assertThat(auditor).isPresent();
        assertThat(auditor.get()).contains(username);
    }
    
    @Test
    void auditorProvider_withNonAuthenticatedUser_shouldReturnSystem() {
        // Given
        Authentication authentication = new UsernamePasswordAuthenticationToken(
            "testuser", 
            "password", 
            null
        );
        authentication.setAuthenticated(false);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        // When
        Optional<String> auditor = auditorProvider.getCurrentAuditor();
        
        // Then
        assertThat(auditor).isPresent();
        assertThat(auditor.get()).contains("system");
    }
} 