package otbs.ms_incident.commun;

import org.junit.jupiter.api.Test;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;

class PageUtilTest {

    @Test
    void toPageResponse_shouldConvertPageToPageResponse() {
        // Given
        List<String> content = Arrays.asList("Item1", "Item2", "Item3");
        Page<String> page = new PageImpl<>(content, PageRequest.of(0, 10), 3);

        // When
        PageResponse<String> response = PageUtil.toPageResponse(page);

        // Then
        assertNotNull(response);
        assertEquals(content, response.getContent());
        assertEquals(0, response.getNumber());
        assertEquals(10, response.getSize());
        assertEquals(3, response.getTotalElements());
        assertEquals(1, response.getTotalPages());
        assertTrue(response.isFirst());
        assertTrue(response.isLast());
        assertFalse(response.isEmpty());
    }

    @Test
    void toPageResponse_withMapper_shouldConvertAndMapContent() {
        // Given
        List<Integer> content = Arrays.asList(1, 2, 3);
        Page<Integer> page = new PageImpl<>(content, PageRequest.of(0, 10), 3);
        Function<Integer, String> mapper = i -> "Number: " + i;

        // When
        PageResponse<String> response = PageUtil.toPageResponse(page, mapper);

        // Then
        assertNotNull(response);
        assertEquals(Arrays.asList("Number: 1", "Number: 2", "Number: 3"), response.getContent());
        assertEquals(0, response.getNumber());
        assertEquals(10, response.getSize());
        assertEquals(3, response.getTotalElements());
        assertEquals(1, response.getTotalPages());
        assertTrue(response.isFirst());
        assertTrue(response.isLast());
        assertFalse(response.isEmpty());
    }

    @Test
    void toPageResponse_withEmptyPage_shouldReturnEmptyPageResponse() {
        // Given
        List<String> content = List.of();
        Page<String> page = new PageImpl<>(content, PageRequest.of(0, 10), 0);

        // When
        PageResponse<String> response = PageUtil.toPageResponse(page);

        // Then
        assertNotNull(response);
        assertTrue(response.getContent().isEmpty());
        assertEquals(0, response.getNumber());
        assertEquals(10, response.getSize());
        assertEquals(0, response.getTotalElements());
        assertEquals(0, response.getTotalPages());
        assertTrue(response.isFirst());
        assertTrue(response.isLast());
        assertTrue(response.isEmpty());
    }
}
