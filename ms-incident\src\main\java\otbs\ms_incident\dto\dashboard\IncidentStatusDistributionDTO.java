package otbs.ms_incident.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO pour la répartition des incidents par statut
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncidentStatusDistributionDTO {
    private long openIncidents;
    private long inProgressIncidents;
    private long resolvedIncidents;
}
