package otbs.ms_incident.commun;

import org.springframework.data.domain.Page;

import java.util.List;
import java.util.function.Function;

/**
 * Classe utilitaire pour la gestion des pages et la conversion entre Page et PageResponse
 */
public class PageUtil {

    private PageUtil() {
        // Constructeur privé pour empêcher l'instanciation
    }

    /**
     * Convertit un objet Page de Spring Data en PageResponse
     *
     * @param page L'objet Page à convertir
     * @param <T> Le type d'objet contenu dans la page
     * @return Un objet PageResponse contenant les mêmes données que la page
     */
    public static <T> PageResponse<T> toPageResponse(Page<T> page) {
        PageResponse<T> response = new PageResponse<>();
        response.setContent(page.getContent());
        response.setNumber(page.getNumber());
        response.setSize(page.getSize());
        response.setTotalElements(page.getTotalElements());
        response.setTotalPages(page.getTotalPages());
        response.setFirst(page.isFirst());
        response.setLast(page.isLast());
        response.setEmpty(page.isEmpty());
        return response;
    }

    /**
     * Convertit un objet Page de Spring Data en PageResponse avec transformation des éléments
     *
     * @param page L'objet Page à convertir
     * @param mapper Fonction de transformation des éléments
     * @param <T> Le type d'objet contenu dans la page source
     * @param <R> Le type d'objet contenu dans la page résultante
     * @return Un objet PageResponse contenant les données transformées
     */
    public static <T, R> PageResponse<R> toPageResponse(Page<T> page, Function<T, R> mapper) {
        List<R> content = page.getContent().stream()
                .map(mapper)
                .toList();

        PageResponse<R> response = new PageResponse<>();
        response.setContent(content);
        response.setNumber(page.getNumber());
        response.setSize(page.getSize());
        response.setTotalElements(page.getTotalElements());
        response.setTotalPages(page.getTotalPages());
        response.setFirst(page.isFirst());
        response.setLast(page.isLast());
        response.setEmpty(page.isEmpty());
        return response;
    }
}
