package otbs.ms_incident.config;

import feign.Logger;
import feign.RequestInterceptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.util.Collections;

/**
 * Configuration centralisée pour les clients HTTP (Feign et RestTemplate).
 * Cette classe configure les clients utilisés pour communiquer avec d'autres services.
 */
@Configuration
public class FeignConfig {

    /**
     * URL du service vehicule récupérée depuis les propriétés de configuration.
     */
    @Value("${vehicule.service.url}")
    private String vehiculeServiceUrl;

    /**
     * Configure le niveau de journalisation pour les clients Feign.
     *
     * @return Le niveau de journalisation pour les clients Feign
     */
    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }

    /**
     * Configure l'intercepteur pour propager le token JWT aux services appelés via Feign.
     *
     * @return L'intercepteur Feign pour propager le token JWT
     */
    @Bean
    public RequestInterceptor feignRequestInterceptor() {
        return new FeignClientInterceptor();
    }

    /**
     * Configure RestTemplate avec un intercepteur pour propager le token JWT.
     *
     * @return RestTemplate configuré
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setInterceptors(Collections.singletonList(new JwtTokenInterceptor()));
        return restTemplate;
    }

    /**
     * Intercepteur pour ajouter le token JWT aux requêtes RestTemplate.
     */
    private static class JwtTokenInterceptor implements ClientHttpRequestInterceptor {

        @Override
        public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution) throws IOException {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

            if (authentication instanceof JwtAuthenticationToken jwtAuthenticationToken) {
                String tokenValue = jwtAuthenticationToken.getToken().getTokenValue();

                request.getHeaders().add("Authorization", "Bearer " + tokenValue);
            }

            return execution.execute(request, body);
        }
    }
}
