package otbs.ms_incident.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Statuts possibles pour un incident")
public enum StatusIncident {
    @Schema(description = "Incident signalé, pas encore pris en charge")
    A_TRAITER,

    @Schema(description = "Analyse ou intervention en cours")
    EN_COURS_TRAITEMENT,

    @Schema(description = "Incident résolu et clôturé")
    RESOLU
} 