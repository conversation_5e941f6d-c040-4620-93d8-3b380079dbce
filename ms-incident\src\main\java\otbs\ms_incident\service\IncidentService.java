package otbs.ms_incident.service;

import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.commun.PageResponse;

import org.springframework.data.domain.Pageable;
import java.time.LocalDate;
import java.util.List;

/**
 * Interface définissant les services de gestion des incidents.
 * Cette interface spécifie toutes les opérations disponibles pour manipuler
 * les incidents dans le système ParcAuto, incluant la création, la lecture,
 * la mise à jour, la suppression et diverses méthodes de recherche.
 *
 * @version 0.0.1-SNAPSHOT
 */
public interface IncidentService {
    /**
     * Crée un nouvel incident à partir des données fournies.
     *
     * @param requestDTO Les données nécessaires pour créer l'incident
     * @return L'incident créé avec son ID généré
     */
    IncidentResponseDTO createIncident(IncidentRequestDTO requestDTO);

    /**
     * Récupère un incident par son identifiant.
     *
     * @param id L'identifiant de l'incident à récupérer
     * @return L'incident correspondant à l'ID fourni
     */
    IncidentResponseDTO getIncidentById(Long id);

    /**
     * Récupère tous les incidents enregistrés.
     *
     * @return La liste de tous les incidents
     */
    List<IncidentResponseDTO> getAllIncidents();

    /**
     * Met à jour un incident existant.
     *
     * @param id L'identifiant de l'incident à mettre à jour
     * @param requestDTO Les nouvelles données de l'incident
     * @return L'incident mis à jour
     */
    IncidentResponseDTO updateIncident(Long id, IncidentRequestDTO requestDTO);

    /**
     * Supprime un incident par son identifiant.
     *
     * @param id L'identifiant de l'incident à supprimer
     */
    void deleteIncident(Long id);



    /**
     * Récupère les incidents sur une période donnée.
     *
     * @param debut La date de début de la période
     * @param fin La date de fin de la période
     * @return La liste des incidents survenus durant cette période
     */
    List<IncidentResponseDTO> getIncidentsByDateRange(LocalDate debut, LocalDate fin);

    /**
     * Récupère les incidents par lieu.
     *
     * @param lieu Le lieu ou une partie du lieu à rechercher
     * @return La liste des incidents correspondant au lieu spécifié
     */
    List<IncidentResponseDTO> getIncidentsByLieu(String lieu);

    /**
     * Ajoute une réparation à un incident.
     *
     * @param incidentId L'identifiant de l'incident
     * @param reparationId L'identifiant de la réparation à associer
     * @return L'incident mis à jour avec la nouvelle réparation
     */
    IncidentResponseDTO addReparationToIncident(Long incidentId, Long reparationId);

    /**
     * Retire une réparation d'un incident.
     *
     * @param incidentId L'identifiant de l'incident
     * @param reparationId L'identifiant de la réparation à dissocier
     * @return L'incident mis à jour
     */
    IncidentResponseDTO removeReparationFromIncident(Long incidentId, Long reparationId);

    /**
     * Récupère directement l'entité Incident.
     * Cette méthode ne retourne pas un DTO mais l'entité elle-même,
     * à utiliser uniquement dans les services internes.
     *
     * @param id L'identifiant de l'incident à récupérer
     * @return L'entité Incident
     */
    otbs.ms_incident.entity.Incident getIncidentEntityById(Long id);

    /**
     * Récupère les incidents associés à un véhicule spécifique.
     *
     * @param vehiculeId L'identifiant du véhicule
     * @return La liste des incidents associés au véhicule
     */
    List<IncidentResponseDTO> getIncidentsByVehiculeId(Long vehiculeId);

    /**
     * Récupère les incidents survenus aujourd'hui.
     *
     * @return La liste des incidents survenus aujourd'hui
     */
    List<IncidentResponseDTO> getIncidentsForToday();

    /**
     * Récupère les incidents survenus au cours du dernier mois.
     *
     * @return La liste des incidents survenus au cours du dernier mois
     */
    List<IncidentResponseDTO> getIncidentsForLastMonth();

    /**
     * Récupère les incidents survenus au cours du dernier semestre.
     *
     * @return La liste des incidents survenus au cours du dernier semestre
     */
    List<IncidentResponseDTO> getIncidentsForLastSemester();

    /**
     * Récupère les incidents survenus au cours de la dernière année.
     *
     * @return La liste des incidents survenus au cours de la dernière année
     */
    List<IncidentResponseDTO> getIncidentsForLastYear();

    /**
     * Compte le nombre d'incidents associés à un véhicule spécifique.
     *
     * @param vehiculeId L'identifiant du véhicule
     * @return Le nombre d'incidents associés au véhicule
     */
    Long countIncidentsByVehiculeId(Long vehiculeId);

    /**
     * Récupère tous les incidents avec pagination.
     *
     * @param pageable Les informations de pagination (page, taille, tri)
     * @return Une page d'incidents
     */
    PageResponse<IncidentResponseDTO> getAllIncidents(Pageable pageable);



    /**
     * Récupère les incidents sur une période donnée avec pagination.
     *
     * @param debut La date de début de la période
     * @param fin La date de fin de la période
     * @param pageable Les informations de pagination (page, taille, tri)
     * @return Une page d'incidents survenus durant cette période
     */
    PageResponse<IncidentResponseDTO> getIncidentsByDateRange(LocalDate debut, LocalDate fin, Pageable pageable);

    /**
     * Récupère les incidents par lieu avec pagination.
     *
     * @param lieu Le lieu ou une partie du lieu à rechercher
     * @param pageable Les informations de pagination (page, taille, tri)
     * @return Une page d'incidents correspondant au lieu spécifié
     */
    PageResponse<IncidentResponseDTO> getIncidentsByLieu(String lieu, Pageable pageable);



    /**
     * Récupère les incidents associés à un véhicule spécifique avec pagination.
     *
     * @param vehiculeId L'identifiant du véhicule
     * @param pageable Les informations de pagination (page, taille, tri)
     * @return Une page d'incidents associés au véhicule spécifié
     */
    PageResponse<IncidentResponseDTO> getIncidentsByVehiculeId(Long vehiculeId, Pageable pageable);



    /**
     * Met à jour le statut d'un incident.
     *
     * @param id L'identifiant de l'incident à mettre à jour
     * @param status Le nouveau statut de l'incident
     * @return L'incident mis à jour
     */
    IncidentResponseDTO updateIncidentStatus(Long id, StatusIncident status);

    /**
     * Met à jour la priorité d'un incident.
     *
     * @param id L'identifiant de l'incident à mettre à jour
     * @param priority La nouvelle priorité de l'incident
     * @return L'incident mis à jour
     */
    IncidentResponseDTO updateIncidentPriority(Long id, NiveauPrioriteIncident priority);
}