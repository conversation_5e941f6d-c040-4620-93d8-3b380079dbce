package otbs.ms_incident.dto;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import otbs.ms_incident.enums.StatusReparation;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class ReparationRequestDTOTest {

    private Validator validator;
    private ReparationRequestDTO reparationRequestDTO;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();

        // Setup valid DTO
        reparationRequestDTO = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.now())
                .description("Réparation du pare-choc")
                .cout(new BigDecimal("1500.00"))
                .garage("Garage Central")
                .incidentId(1L)
                .build();
    }

    @Test
    void testValidReparationRequestDTO() {
        // When
        Set<ConstraintViolation<ReparationRequestDTO>> violations = validator.validate(reparationRequestDTO);

        // Then
        assertTrue(violations.isEmpty());
    }

    @Test
    void testNullDateReparation() {
        // Given
        reparationRequestDTO.setDateReparation(null);

        // When
        Set<ConstraintViolation<ReparationRequestDTO>> violations = validator.validate(reparationRequestDTO);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("La date de réparation est obligatoire", violations.iterator().next().getMessage());
    }

    @Test
    void testFutureDateReparation() {
        // Given
        reparationRequestDTO.setDateReparation(LocalDate.now().plusDays(10));

        // When
        Set<ConstraintViolation<ReparationRequestDTO>> violations = validator.validate(reparationRequestDTO);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("La date de réparation doit être dans le passé ou le présent", violations.iterator().next().getMessage());
    }

    @Test
    void testEmptyDescription() {
        // Given
        reparationRequestDTO.setDescription("");

        // When
        Set<ConstraintViolation<ReparationRequestDTO>> violations = validator.validate(reparationRequestDTO);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("La description est obligatoire", violations.iterator().next().getMessage());
    }

    @Test
    void testNullDescription() {
        // Given
        reparationRequestDTO.setDescription(null);

        // When
        Set<ConstraintViolation<ReparationRequestDTO>> violations = validator.validate(reparationRequestDTO);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("La description est obligatoire", violations.iterator().next().getMessage());
    }

    @Test
    void testNegativeCout() {
        // Given
        reparationRequestDTO.setCout(new BigDecimal("-100.00"));

        // When
        Set<ConstraintViolation<ReparationRequestDTO>> violations = validator.validate(reparationRequestDTO);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("Le coût doit être positif", violations.iterator().next().getMessage());
    }

    @Test
    void testNullCout() {
        // Given
        reparationRequestDTO.setCout(null);

        // When
        Set<ConstraintViolation<ReparationRequestDTO>> violations = validator.validate(reparationRequestDTO);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("Le coût est obligatoire", violations.iterator().next().getMessage());
    }

    @Test
    void testEmptyGarage() {
        // Given
        reparationRequestDTO.setGarage("");

        // When
        Set<ConstraintViolation<ReparationRequestDTO>> violations = validator.validate(reparationRequestDTO);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("Le nom du garage est obligatoire", violations.iterator().next().getMessage());
    }

    @Test
    void testNullGarage() {
        // Given
        reparationRequestDTO.setGarage(null);

        // When
        Set<ConstraintViolation<ReparationRequestDTO>> violations = validator.validate(reparationRequestDTO);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("Le nom du garage est obligatoire", violations.iterator().next().getMessage());
    }

    @Test
    void testBuilderAndGetters() {
        // Given
        LocalDate dateReparation = LocalDate.now();
        String description = "Réparation de la portière";
        BigDecimal cout = new BigDecimal("750.50");
        String garage = "Garage Express";
        Long incidentId = 2L;

        // When
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .build();

        // Then
        assertEquals(dateReparation, dto.getDateReparation());
        assertEquals(description, dto.getDescription());
        assertEquals(cout, dto.getCout());
        assertEquals(garage, dto.getGarage());
        assertEquals(incidentId, dto.getIncidentId());
    }

    @Test
    void testNoArgsConstructor() {
        // When
        ReparationRequestDTO dto = new ReparationRequestDTO();

        // Then
        assertNull(dto.getDateReparation());
        assertNull(dto.getDescription());
        assertNull(dto.getCout());
        assertNull(dto.getGarage());
        assertNull(dto.getIncidentId());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        LocalDate dateReparation = LocalDate.now();
        String description = "Réparation complète";
        BigDecimal cout = new BigDecimal("2000.00");
        String garage = "Garage Premium";
        Long incidentId = 3L;

        // When
        ReparationRequestDTO dto = new ReparationRequestDTO(
                dateReparation, StatusReparation.EN_COURS, description, cout, garage, incidentId, false, BigDecimal.ZERO);

        // Then
        assertEquals(dateReparation, dto.getDateReparation());
        assertEquals(description, dto.getDescription());
        assertEquals(cout, dto.getCout());
        assertEquals(garage, dto.getGarage());
        assertEquals(incidentId, dto.getIncidentId());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given - create identical DTOs
        LocalDate dateReparation = LocalDate.now();
        ReparationRequestDTO dto1 = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .description("Réparation moteur")
                .cout(new BigDecimal("3000.00"))
                .garage("Garage Test")
                .incidentId(5L)
                .build();

        ReparationRequestDTO dto2 = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .description("Réparation moteur")
                .cout(new BigDecimal("3000.00"))
                .garage("Garage Test")
                .incidentId(5L)
                .build();

        ReparationRequestDTO dto3 = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .description("Réparation différente")
                .cout(new BigDecimal("3000.00"))
                .garage("Garage Test")
                .incidentId(5L)
                .build();

        // Then
        // Test reflexivity
        assertEquals(dto1, dto1);

        // Test symmetry
        assertEquals(dto1, dto2);
        assertEquals(dto2, dto1);

        // Test hashCode consistency
        assertEquals(dto1.hashCode(), dto2.hashCode());

        // Test inequality
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());

        // Test against null and different class
        assertNotEquals(null, dto1);
        assertNotEquals("String",dto1 );
    }

    @Test
    void testToString() {
        // When
        String dtoString = reparationRequestDTO.toString();

        // Then
        assertTrue(dtoString.contains("dateReparation="));
        assertTrue(dtoString.contains("description=Réparation du pare-choc"));
        assertTrue(dtoString.contains("cout=1500.00"));
        assertTrue(dtoString.contains("garage=Garage Central"));
        assertTrue(dtoString.contains("incidentId=1"));
    }

    @Test
    void testSettersAndGetters() {
        // Given
        ReparationRequestDTO dto = new ReparationRequestDTO();
        LocalDate dateReparation = LocalDate.now();
        String description = "Nouvelle description";
        BigDecimal cout = new BigDecimal("4500.00");
        String garage = "Nouveau Garage";
        Long incidentId = 10L;

        // When
        dto.setDateReparation(dateReparation);
        dto.setDescription(description);
        dto.setCout(cout);
        dto.setGarage(garage);
        dto.setIncidentId(incidentId);

        // Then
        assertEquals(dateReparation, dto.getDateReparation());
        assertEquals(description, dto.getDescription());
        assertEquals(cout, dto.getCout());
        assertEquals(garage, dto.getGarage());
        assertEquals(incidentId, dto.getIncidentId());
    }

    @Test
    void testEqualsWithNull() {
        // Given
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.now())
                .description("Réparation moteur")
                .cout(new BigDecimal("3000.00"))
                .garage("Garage Test")
                .incidentId(5L)
                .build();

        // Then
        assertNotEquals(null,dto );
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Given
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.now())
                .description("Réparation moteur")
                .cout(new BigDecimal("3000.00"))
                .garage("Garage Test")
                .incidentId(5L)
                .build();

        // Then
        assertNotEquals("Not a DTO",dto );
    }

    @Test
    void testEqualsWithExtensiveCoverage() {
        // Given
        LocalDate dateReparation = LocalDate.now();
        String description = "Réparation moteur";
        BigDecimal cout = new BigDecimal("3000.00");
        String garage = "Garage Test";
        Long incidentId = 5L;

        // Create base DTO
        ReparationRequestDTO base = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .build();

        // Create identical DTO
        ReparationRequestDTO same = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .build();

        // Create DTOs with different fields
        ReparationRequestDTO differentDate = ReparationRequestDTO.builder()
                .dateReparation(dateReparation.minusDays(1))
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .build();

        ReparationRequestDTO differentDescription = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .description("Different description")
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .build();

        ReparationRequestDTO differentCout = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .description(description)
                .cout(new BigDecimal("4000.00"))
                .garage(garage)
                .incidentId(incidentId)
                .build();

        ReparationRequestDTO differentGarage = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage("Different Garage")
                .incidentId(incidentId)
                .build();

        ReparationRequestDTO differentIncidentId = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(6L)
                .build();

        // Then - test reflexivity and symmetry
        assertEquals(base, base);
        assertEquals(base, same);
        assertEquals(same, base);

        // Test transitivity
        assertNotEquals(base, differentDate);
        assertNotEquals(base, differentDescription);
        assertNotEquals(base, differentCout);
        assertNotEquals(base, differentGarage);
        assertNotEquals(base, differentIncidentId);

        // Test hashCode
        assertEquals(base.hashCode(), same.hashCode());
        assertNotEquals(base.hashCode(), differentDate.hashCode());
        assertNotEquals(base.hashCode(), differentDescription.hashCode());
        assertNotEquals(base.hashCode(), differentCout.hashCode());
        assertNotEquals(base.hashCode(), differentGarage.hashCode());
        assertNotEquals(base.hashCode(), differentIncidentId.hashCode());
    }

    @Test
    void testEqualsAndHashCodeWithNullFields() {
        // Given
        ReparationRequestDTO dtoWithNulls = ReparationRequestDTO.builder()
                .dateReparation(null)
                .description(null)
                .cout(null)
                .garage(null)
                .incidentId(null)
                .build();

        ReparationRequestDTO anotherDtoWithNulls = ReparationRequestDTO.builder()
                .dateReparation(null)
                .description(null)
                .cout(null)
                .garage(null)
                .incidentId(null)
                .build();

        ReparationRequestDTO dtoWithValues = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.now())
                .description("Réparation")
                .cout(new BigDecimal("1000.00"))
                .garage("Garage")
                .incidentId(1L)
                .build();

        // Then
        assertEquals(dtoWithNulls, anotherDtoWithNulls);
        assertEquals(dtoWithNulls.hashCode(), anotherDtoWithNulls.hashCode());
        assertNotEquals(dtoWithNulls, dtoWithValues);
        assertNotEquals(dtoWithNulls.hashCode(), dtoWithValues.hashCode());
    }
}