package otbs.ms_incident.dto;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class IncidentRequestDTOValidationTest {

    private Validator validator;
    private IncidentRequestDTO validDto;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();

        // Create a valid DTO
        validDto = new IncidentRequestDTO();
        validDto.setDate(LocalDate.now());
        validDto.setType(TypeIncident.ACCIDENT);
        validDto.setStatus(StatusIncident.A_TRAITER);
        validDto.setPriorite(NiveauPrioriteIncident.MOYEN);
        validDto.setLieu("Paris");
        validDto.setDescription("Description de l'incident");
        validDto.setVehiculeId(1L);
        validDto.setPhotos(new ArrayList<>());
        validDto.setReparationIds(new ArrayList<>());
    }

    @Test
    void testValidDTO() {
        // When
        Set<ConstraintViolation<IncidentRequestDTO>> violations = validator.validate(validDto);

        // Then
        assertTrue(violations.isEmpty());
    }

    @Test
    void testDateNotNull() {
        // Given
        validDto.setDate(null);

        // When
        Set<ConstraintViolation<IncidentRequestDTO>> violations = validator.validate(validDto);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("date", violations.iterator().next().getPropertyPath().toString());
    }

    @Test
    void testTypeNotNull() {
        // Given
        validDto.setType(null);

        // When
        Set<ConstraintViolation<IncidentRequestDTO>> violations = validator.validate(validDto);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("type", violations.iterator().next().getPropertyPath().toString());
    }

    @Test
    void testLieuNotBlank() {
        // Given
        validDto.setLieu("");

        // When
        Set<ConstraintViolation<IncidentRequestDTO>> violations = validator.validate(validDto);

        // Then
        assertFalse(violations.isEmpty());
        assertEquals(1, violations.size());
        assertEquals("lieu", violations.iterator().next().getPropertyPath().toString());
    }

    @Test
    void testDescriptionCanBeEmpty() {
        // Given
        validDto.setDescription("");

        // When
        Set<ConstraintViolation<IncidentRequestDTO>> violations = validator.validate(validDto);

        // Then
        // Description doesn't have @NotBlank, so it can be empty
        assertTrue(violations.isEmpty());
    }

    @Test
    void testVehiculeIdCanBeNull() {
        // Given
        validDto.setVehiculeId(null);

        // When
        Set<ConstraintViolation<IncidentRequestDTO>> violations = validator.validate(validDto);

        // Then
        // VehiculeId doesn't have @NotNull, so it can be null
        assertTrue(violations.isEmpty());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Long vehiculeId = 1L;
        LocalDate date = LocalDate.now();
        TypeIncident type = TypeIncident.PANNE;
        StatusIncident status = StatusIncident.EN_COURS_TRAITEMENT;
        NiveauPrioriteIncident priorite = NiveauPrioriteIncident.FAIBLE;
        String lieu = "Lyon";
        String description = "Panne moteur";
        String constat = "constat.pdf";
        List<String> photos = List.of("photo1.jpg");
        List<Long> reparationIds = List.of(1L, 2L);

        // When
        IncidentRequestDTO dto = new IncidentRequestDTO(
                vehiculeId, date, type, status, priorite, lieu, description, constat, photos, reparationIds
        );

        // Then
        assertEquals(vehiculeId, dto.getVehiculeId());
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertEquals(status, dto.getStatus());
        assertEquals(priorite, dto.getPriorite());
        assertEquals(lieu, dto.getLieu());
        assertEquals(description, dto.getDescription());
        assertEquals(constat, dto.getConstat());
        assertEquals(photos, dto.getPhotos());
        assertEquals(reparationIds, dto.getReparationIds());
    }

    @Test
    void testBuilder() {
        // When
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .vehiculeId(2L)
                .date(LocalDate.of(2023, 6, 15))
                .type(TypeIncident.ACCIDENT)
                .status(StatusIncident.RESOLU)
                .priorite(NiveauPrioriteIncident.CRITIQUE)
                .lieu("Marseille")
                .description("Accident grave")
                .constat("rapport.pdf")
                .photos(List.of("photo1.jpg"))
                .reparationIds(List.of(3L, 4L))
                .build();

        // Then
        assertEquals(2L, dto.getVehiculeId());
        assertEquals(LocalDate.of(2023, 6, 15), dto.getDate());
        assertEquals(TypeIncident.ACCIDENT, dto.getType());
        assertEquals(StatusIncident.RESOLU, dto.getStatus());
        assertEquals(NiveauPrioriteIncident.CRITIQUE, dto.getPriorite());
        assertEquals("Marseille", dto.getLieu());
        assertEquals("Accident grave", dto.getDescription());
        assertEquals("rapport.pdf", dto.getConstat());
        assertEquals(List.of("photo1.jpg"), dto.getPhotos());
        assertEquals(List.of(3L, 4L), dto.getReparationIds());
    }
}
