package otbs.ms_incident.entity;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

class IncidentAdvancedTest {

    private Incident incident;

    @BeforeEach
    void setUp() {
        incident = new Incident();
        incident.setId(1L);
        incident.setVehiculeId(100L);
        incident.setDate(LocalDate.of(2023, 5, 15));
        incident.setType(TypeIncident.ACCIDENT);
        incident.setStatus(StatusIncident.A_TRAITER);
        incident.setPriorite(NiveauPrioriteIncident.FAIBLE);
        incident.setLieu("Paris");
        incident.setDescription("Accident test");
    }

    @Test
    void hasConstat_shouldReturnFalse_whenConstatIsNull() {
        // Given
        incident.setConstat(null);

        // When
        boolean result = incident.hasConstat();

        // Then
        assertFalse(result);
    }

    @Test
    void hasConstat_shouldReturnFalse_whenConstatIsEmpty() {
        // Given
        incident.setConstat("");

        // When
        boolean result = incident.hasConstat();

        // Then
        assertFalse(result);
    }

    @Test
    void hasConstat_shouldReturnTrue_whenConstatIsNotEmpty() {
        // Given
        incident.setConstat("path/to/constat.pdf");

        // When
        boolean result = incident.hasConstat();

        // Then
        assertTrue(result);
    }

    @Test
    void hasPhotos_shouldReturnFalse_whenPhotosIsNull() {
        // Given
        incident.setPhotos(null);

        // When
        boolean result = incident.hasPhotos();

        // Then
        assertFalse(result);
    }

    @Test
    void hasPhotos_shouldReturnFalse_whenPhotosIsEmpty() {
        // Given
        incident.setPhotos(new ArrayList<>());

        // When
        boolean result = incident.hasPhotos();

        // Then
        assertFalse(result);
    }

    @Test
    void hasPhotos_shouldReturnTrue_whenPhotosIsNotEmpty() {
        // Given
        incident.setPhotos(Collections.singletonList("path/to/photo.jpg"));

        // When
        boolean result = incident.hasPhotos();

        // Then
        assertTrue(result);
    }

    @Test
    void addPhoto_shouldAddPhotoToList_whenPhotosIsNotNull() {
        // Given
        List<String> photos = new ArrayList<>();
        incident.setPhotos(photos);
        String photoPath = "path/to/photo.jpg";

        // When
        incident.addPhoto(photoPath);

        // Then
        assertEquals(1, incident.getPhotos().size());
        assertEquals(photoPath, incident.getPhotos().get(0));
    }

    @Test
    void addPhoto_shouldCreateNewListAndAddPhoto_whenPhotosIsNull() {
        // Given
        incident.setPhotos(null);
        String photoPath = "path/to/photo.jpg";

        // When
        incident.addPhoto(photoPath);

        // Then
        assertNotNull(incident.getPhotos());
        assertEquals(1, incident.getPhotos().size());
        assertEquals(photoPath, incident.getPhotos().get(0));
    }

    @Test
    void removePhoto_shouldReturnFalse_whenPhotosIsNull() {
        // Given
        incident.setPhotos(null);
        String photoPath = "path/to/photo.jpg";

        // When
        boolean result = incident.removePhoto(photoPath);

        // Then
        assertFalse(result);
    }

    @Test
    void removePhoto_shouldReturnFalse_whenPhotoDoesNotExist() {
        // Given
        incident.setPhotos(Collections.singletonList("path/to/photo1.jpg"));
        String photoPath = "path/to/photo2.jpg";

        // When
        boolean result = incident.removePhoto(photoPath);

        // Then
        assertFalse(result);
        assertEquals(1, incident.getPhotos().size());
    }

    @Test
    void removePhoto_shouldReturnTrue_whenPhotoExists() {
        // Given
        String photoPath = "path/to/photo.jpg";
        List<String> photos = new ArrayList<>();
        photos.add(photoPath);
        incident.setPhotos(photos);

        // When
        boolean result = incident.removePhoto(photoPath);

        // Then
        assertTrue(result);
        assertTrue(incident.getPhotos().isEmpty());
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {" ", "  \t  "})
    void hasConstat_shouldReturnFalse_forNullEmptyOrBlankConstat(String constat) {
        // Given
        incident.setConstat(constat);

        // When
        boolean result = incident.hasConstat();

        // Then
        assertFalse(result);
    }

    @ParameterizedTest
    @MethodSource("providePhotosLists")
    void hasPhotos_shouldReturnExpectedResult(List<String> photos, boolean expected) {
        // Given
        incident.setPhotos(photos);

        // When
        boolean result = incident.hasPhotos();

        // Then
        assertEquals(expected, result);
    }

    @Test
    void defaultValues_shouldBeSetCorrectly() {
        // Given
        Incident newIncident = new Incident();

        // Then
        assertEquals(StatusIncident.A_TRAITER, newIncident.getStatus());
        assertEquals(NiveauPrioriteIncident.FAIBLE, newIncident.getPriorite());
        assertNotNull(newIncident.getPhotos());
        assertTrue(newIncident.getPhotos().isEmpty());
        assertNotNull(newIncident.getReparations());
        assertTrue(newIncident.getReparations().isEmpty());
    }

    @Test
    void allArgsConstructor_shouldSetAllFields() {
        // Given
        Long id = 2L;
        Long vehiculeId = 200L;
        LocalDate date = LocalDate.of(2023, 6, 20);
        TypeIncident type = TypeIncident.PANNE;
        StatusIncident status = StatusIncident.EN_COURS_TRAITEMENT;
        NiveauPrioriteIncident priorite = NiveauPrioriteIncident.CRITIQUE;
        String lieu = "Lyon";
        String constat = "path/to/constat.pdf";
        List<String> photos = Arrays.asList("photo1.jpg", "photo2.jpg");
        String description = "Panne test";
        List<Reparation> reparations = new ArrayList<>();

        // When
        Incident newIncident = new Incident(id, vehiculeId, date, type, status, priorite, lieu, constat, photos, description, reparations);

        // Then
        assertEquals(id, newIncident.getId());
        assertEquals(vehiculeId, newIncident.getVehiculeId());
        assertEquals(date, newIncident.getDate());
        assertEquals(type, newIncident.getType());
        assertEquals(status, newIncident.getStatus());
        assertEquals(priorite, newIncident.getPriorite());
        assertEquals(lieu, newIncident.getLieu());
        assertEquals(constat, newIncident.getConstat());
        assertEquals(photos, newIncident.getPhotos());
        assertEquals(description, newIncident.getDescription());
        assertEquals(reparations, newIncident.getReparations());
    }

    @Test
    void settersAndGetters_shouldWorkCorrectly() {
        // Given
        Long id = 3L;
        Long vehiculeId = 300L;
        LocalDate date = LocalDate.of(2023, 7, 25);
        TypeIncident type = TypeIncident.PANNE;
        StatusIncident status = StatusIncident.RESOLU;
        NiveauPrioriteIncident priorite = NiveauPrioriteIncident.MOYEN;
        String lieu = "Marseille";
        String constat = "path/to/another/constat.pdf";
        List<String> photos = Arrays.asList("photo3.jpg", "photo4.jpg");
        String description = "Another test";
        List<Reparation> reparations = new ArrayList<>();
        reparations.add(new Reparation());

        // When
        Incident testIncident = new Incident();
        testIncident.setId(id);
        testIncident.setVehiculeId(vehiculeId);
        testIncident.setDate(date);
        testIncident.setType(type);
        testIncident.setStatus(status);
        testIncident.setPriorite(priorite);
        testIncident.setLieu(lieu);
        testIncident.setConstat(constat);
        testIncident.setPhotos(photos);
        testIncident.setDescription(description);
        testIncident.setReparations(reparations);

        // Then
        assertEquals(id, testIncident.getId());
        assertEquals(vehiculeId, testIncident.getVehiculeId());
        assertEquals(date, testIncident.getDate());
        assertEquals(type, testIncident.getType());
        assertEquals(status, testIncident.getStatus());
        assertEquals(priorite, testIncident.getPriorite());
        assertEquals(lieu, testIncident.getLieu());
        assertEquals(constat, testIncident.getConstat());
        assertEquals(photos, testIncident.getPhotos());
        assertEquals(description, testIncident.getDescription());
        assertEquals(reparations, testIncident.getReparations());
    }

    // Helper method to provide test data for hasPhotos parameterized test
    private static Stream<Arguments> providePhotosLists() {
        return Stream.of(
                Arguments.of(null, false),
                Arguments.of(Collections.emptyList(), false),
                Arguments.of(Collections.singletonList("photo.jpg"), true),
                Arguments.of(Arrays.asList("photo1.jpg", "photo2.jpg"), true)
        );
    }
}
