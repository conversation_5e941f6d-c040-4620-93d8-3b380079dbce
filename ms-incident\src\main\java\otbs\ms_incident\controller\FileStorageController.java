package otbs.ms_incident.controller;



import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import otbs.ms_incident.service.FileStorageService;
import otbs.ms_incident.service.IncidentFileManager;
import otbs.ms_incident.service.IncidentService;
import otbs.ms_incident.util.ControllerUtils;

import java.io.IOException;
import java.net.MalformedURLException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.Files;
import org.springframework.core.io.UrlResource;

/**
 * Contrôleur REST pour la gestion des fichiers liés aux incidents
 */
@RestController
@RequestMapping("/api/files")
public class FileStorageController {
    // Constants for repeated literals
    private static final String FILENAME_PARAM = "filename";

    private final FileStorageService fileStorageService;
    private final IncidentFileManager incidentFileManager;
    private final IncidentService incidentService;

    public FileStorageController(FileStorageService fileStorageService, IncidentFileManager incidentFileManager, IncidentService incidentService) {
        this.fileStorageService = fileStorageService;
        this.incidentFileManager = incidentFileManager;
        this.incidentService = incidentService;
    }

    /**
     * Vérifie si l'incident existe avant de procéder à des opérations sur ses fichiers
     * Si l'incident n'existe pas, tente de le créer automatiquement
     *
     * @param incidentId ID de l'incident à vérifier
     * @return true si l'incident existe ou a été créé, false sinon
     */
    private boolean ensureIncidentExists(Long incidentId) {
        try {
            // Vérifier si l'incident existe déjà
            incidentFileManager.validateIncidentExists(incidentId);
            return true;
        } catch (Exception e) {

            return createIncidentAutomatically();
        }
    }

    /**
     * Creates an incident automatically with default values
     *
     * @return true if creation was successful, false otherwise
     */
    private boolean createIncidentAutomatically() {
        try {
            // Créer un incident vide avec l'ID spécifié
            otbs.ms_incident.dto.IncidentRequestDTO newIncident = new otbs.ms_incident.dto.IncidentRequestDTO();
            newIncident.setDate(java.time.LocalDate.now());
            newIncident.setType(otbs.ms_incident.enums.TypeIncident.ACCIDENT); // Type par défaut
            newIncident.setLieu("Lieu temporaire - Ajouté automatiquement"); // Lieu obligatoire
            newIncident.setDescription("Incident créé automatiquement lors du téléchargement d'un fichier");

            // Créer l'incident
            incidentService.createIncident(newIncident);
            return true;
        } catch (Exception ex) {

            return false;
        }
    }

    /**
     * Vérifie si l'incident existe avant de procéder à des opérations sur ses fichiers
     *
     * @param incidentId ID de l'incident à vérifier
     * @return true si l'incident existe, false sinon
     */
    private boolean incidentExists(Long incidentId) {
        try {
            // Utilisation du service pour vérifier l'existence de l'incident
            incidentFileManager.validateIncidentExists(incidentId);
            return true;
        } catch (Exception e) {

            return false;
        }
    }

    /**
     * Creates error response map for API errors
     *
     * @param errorMessage The error message
     * @return Map containing error details
     */
    private Map<String, Object> createErrorResponse(String errorMessage) {
        return ControllerUtils.createErrorResponse(errorMessage);
    }

    /**
     * Télécharge un fichier pour un incident spécifique
     *
     * @param file Fichier à télécharger
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param overwrite Écraser si existe déjà
     * @return Informations sur le fichier téléchargé
     */
    @PostMapping("/incident/{incidentId}")
    public ResponseEntity<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @PathVariable Long incidentId,
            @RequestParam(value = "fileType", defaultValue = "PHOTO") String fileType,
            @RequestParam(value = "overwrite", defaultValue = "false") boolean overwrite) {



        // Vérifier si l'incident existe, sinon le créer automatiquement
        if (!ensureIncidentExists(incidentId)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(createErrorResponse("L'incident avec l'ID " + incidentId +
                      " n'existe pas et n'a pas pu être créé automatiquement"));
        }

        try {
            Map<String, Object> result = incidentFileManager.uploadFile(
                file, incidentId, otbs.ms_incident.enums.FileType.valueOf(fileType), overwrite);
            return ResponseEntity.ok(result);
        } catch (Exception e) {

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(createErrorResponse(e.getMessage()));
        }
    }

    /**
     * Récupère un fichier pour un incident spécifique
     *
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param filename Nom du fichier
     * @return Fichier pour téléchargement
     */
    @GetMapping("/incident/{incidentId}/{fileType}/{filename:.+}")
    public ResponseEntity<Resource> getIncidentFile(
            @PathVariable Long incidentId,
            @PathVariable String fileType,
            @PathVariable(FILENAME_PARAM) String filename) {



        return getFileResponse(incidentId, fileType, filename);
    }

    /**
     * Méthode commune pour récupérer un fichier et construire la réponse HTTP
     * Utilisée par getIncidentFile et downloadIncidentFile
     */
    private ResponseEntity<Resource> getFileResponse(Long incidentId, String fileType, String filename) {
        // Vérifier si l'incident existe
        if (!incidentExists(incidentId)) {
            return ResponseEntity.notFound().build();
        }

        try {
            // Construire le chemin du fichier et récupérer le fichier
            Resource resource = loadIncidentFile(incidentId, fileType, filename);

            // Déterminer le type de contenu du fichier
            String contentType = incidentFileManager.determineContentType(filename);


            return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
        } catch (Exception e) {

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Méthode supprimée: determineFolderName

    /**
     * Loads a file for an incident, handling path construction and validation
     *
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param filename Nom du fichier
     * @return Resource representing the file
     * @throws IOException If the file cannot be loaded
     */
    private Resource loadIncidentFile(Long incidentId, String fileType, String filename) throws IOException {
        // Construire le chemin du fichier - directement dans le dossier principal
        Path basePath = Paths.get(fileStorageService.getStoragePath());

        // Essayer d'abord avec le nom de fichier exact
        Path filePath = basePath.resolve(filename);

        // Si le fichier n'existe pas, essayer avec le préfixe de l'incident et du type
        if (!Files.exists(filePath)) {


            // Déterminer le préfixe en fonction du type de fichier
            String prefix = fileType.equalsIgnoreCase("PHOTO") ? "photo_" : "constat_";

            // Rechercher tous les fichiers qui correspondent au modèle
            try {
                final String searchPattern = prefix + incidentId + "_";
                final String filenameToMatch = filename;

                // Rechercher les fichiers qui correspondent au modèle
                Optional<Path> matchingFile;
                try (var pathStream = Files.list(basePath)) {
                    matchingFile = pathStream
                        .filter(Files::isRegularFile)
                        .filter(path -> {
                            String name = path.getFileName().toString();
                            // Vérifier si le nom du fichier contient le préfixe et l'ID de l'incident
                            // et si le nom du fichier se termine par le nom de fichier demandé
                            return name.startsWith(searchPattern) &&
                                   (name.endsWith(filenameToMatch) || name.equals(filenameToMatch));
                        })
                        .findFirst();
                }

                if (matchingFile.isPresent()) {
                    filePath = matchingFile.get();

                } else {
                    String errorMessage = String.format("Aucun fichier correspondant trouvé pour l'incident %d avec pattern: %s*%s",
                        incidentId, searchPattern, filenameToMatch);

                    throw new otbs.ms_incident.exception.FileNotFoundStorageException(errorMessage);
                }
            } catch (IOException e) {
                String errorMessage = String.format("Erreur lors de la recherche de fichiers pour l'incident %d: %s",
                    incidentId, e.getMessage());

                throw new otbs.ms_incident.exception.FileStorageException(
                    errorMessage,
                    e,
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "FILE_SEARCH_ERROR"
                );
            }
        }

        if (!Files.exists(filePath)) {
            String errorMessage = String.format("Fichier non trouvé après recherche pour l'incident %d, type %s: %s",
                incidentId, fileType, filePath);

            throw new otbs.ms_incident.exception.FileNotFoundStorageException(errorMessage);
        }

        try {
            Resource resource = new UrlResource(filePath.toUri());

            if (!resource.exists()) {
                String errorMessage = String.format("Resource n'existe pas pour l'incident %d, type %s: %s",
                    incidentId, fileType, filePath);

                throw new otbs.ms_incident.exception.FileNotFoundStorageException(errorMessage);
            }
            return resource;
        } catch (MalformedURLException e) {
            String errorMessage = String.format("URL malformée pour le fichier de l'incident %d, type %s, chemin %s: %s",
                incidentId, fileType, filePath, e.getMessage());

            throw new otbs.ms_incident.exception.FileStorageException(
                errorMessage,
                e,
                HttpStatus.INTERNAL_SERVER_ERROR,
                "MALFORMED_URL_ERROR"
            );
        }


    }

    /**
     * Supprime un fichier pour un incident spécifique
     *
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param filename Nom du fichier
     * @return Informations sur la suppression
     */
    @DeleteMapping("/incident/{incidentId}/{fileType}/{filename:.+}")
    public ResponseEntity<Map<String, Object>> deleteIncidentFile(
            @PathVariable Long incidentId,
            @PathVariable String fileType,
            @PathVariable(FILENAME_PARAM) String filename) {



        // Vérifier si l'incident existe
        if (!incidentExists(incidentId)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(createErrorResponse("L'incident avec l'ID " + incidentId + " n'existe pas"));
        }

        try {
            Map<String, Object> result = incidentFileManager.deleteFile(
                incidentId, otbs.ms_incident.enums.FileType.valueOf(fileType), filename);
            return ResponseEntity.ok(result);
        } catch (Exception e) {

            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(createErrorResponse(e.getMessage()));
        }
    }

    /**
     * Liste tous les fichiers pour un incident spécifique
     *
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier optionnel (PHOTO ou CONSTAT)
     * @return Liste des fichiers avec métadonnées
     */
    @GetMapping("/list/incident/{incidentId}")
    public ResponseEntity<List<Map<String, Object>>> listIncidentFiles(
            @PathVariable Long incidentId,
            @RequestParam(value = "fileType", required = false) String fileType) {



        // Vérifier si l'incident existe
        if (!incidentExists(incidentId)) {
            return ResponseEntity.notFound().build();
        }

        otbs.ms_incident.enums.FileType fileTypeEnum = null;
        if (fileType != null && !fileType.isEmpty()) {
            fileTypeEnum = otbs.ms_incident.enums.FileType.valueOf(fileType);
        }

        List<Map<String, Object>> files = incidentFileManager.listFiles(incidentId, fileTypeEnum);
        return ResponseEntity.ok(files);
    }

    /**
     * Télécharge un fichier pour un incident spécifique (endpoint pour le téléchargement général)
     *
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param filename Nom du fichier
     * @return Fichier à télécharger
     */
    @GetMapping("/download/incident/{incidentId}/{fileType}/{filename:.+}")
    public ResponseEntity<Resource> downloadIncidentFile(
            @PathVariable Long incidentId,
            @PathVariable String fileType,
            @PathVariable(FILENAME_PARAM) String filename) {



        // Réutiliser la même logique que getIncidentFile
        return getFileResponse(incidentId, fileType, filename);
    }

    /**
     * Supprime un fichier pour un incident spécifique (endpoint général)
     *
     * @param incidentId ID de l'incident
     * @param fileType Type de fichier (PHOTO ou CONSTAT)
     * @param filename Nom du fichier
     * @return Informations sur la suppression
     */
    @DeleteMapping("/delete/incident/{incidentId}/{fileType}/{filename:.+}")
    public ResponseEntity<Map<String, Object>> removeIncidentFile(
            @PathVariable Long incidentId,
            @PathVariable String fileType,
            @PathVariable(FILENAME_PARAM) String filename) {

        return deleteIncidentFile(incidentId, fileType, filename);
    }

    // Endpoint /upload supprimé pour unifier les endpoints d'upload
}