<!-- astreinte-reservation-to-class.component.html - Code complet identique aux missions -->
<div class="reservation-container">
  <app-nav-guide [currentPage]="'astreintes_à_finaliser'"></app-nav-guide>

  <header class="page-header">
    <h1>Gestion des astreintes à finaliser</h1>
  </header>

  <!-- Tableau pour desktop et tablette -->
  <div class="vehicule-list">
    <table>
      <thead>
        <tr>
          <th>Ordre d'astreinte</th>
          <th>Date Début</th>
          <th>Date Fin</th>
          <th>Véhicule réservé</th>
          <th>C<PERSON>s Re<PERSON></th>
          <th>Cartes Carburant</th>
          <th>
            <div class="nom_colonne">
              <p>Badge</p><p>Télépéage</p>
            </div>
          </th>
          <th>
            <div class="nom_colonne">
              <p>Nombre</p><p>Tickets Resto</p>
            </div>
          </th>
          <th>
            <div class="nom_colonne">
              <p>Frais</p><p>Carburant</p>
            </div>
          </th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let reservation of dataSource">
          <td style="width: 70px;">{{ reservation.astreinteIds.join(' et ') }}</td>
          <td>{{ reservation.dateDebut | date:'dd/MM/yyyy' }}</td>
          <td>{{ reservation.dateFin | date:'dd/MM/yyyy' }}</td>
          <td >
            <div style="display: grid; gap: 5px; justify-items: center;">
              <div *ngIf="reservation.immatriculation" class="immatricule-container">
                <app-immatricule [immatriculation]="reservation.immatriculation"></app-immatricule>
              </div>
            </div>
          </td>
          <td>
            <input
              type="checkbox"
              [checked]="true"
              class="checkbox-large"
              aria-label="Clé remise"
            >
          </td>
          <!-- Cellule Consommations reformulée -->
          <td>
            <div style="justify-items: center;">
              <div *ngIf="!hasConsommations(reservation.reservationId)" class="no-consommation">
                Aucune consommation
              </div>
              <div *ngIf="hasConsommations(reservation.reservationId)" class="consommation-badges">
                <div *ngFor="let item of getConsommationItems(reservation.reservationId)" class="consommation-container">
                  <div class="consommation-badge"
                      [ngClass]="getTypeClass(item.type)">
                    <!-- Affichage simplifié pour correspondre à la capture d'écran -->
                    <ng-container *ngIf="item.type === 'Carte'">Carte {{ item.value }}</ng-container>
                  </div>
                    <button *ngIf="hasConsommations(reservation.reservationId)" class="btn-action btn-edit" title="Ajouter carte carburant" (click)="goToConsommationCarburant(reservation)">
                      <i class="fa-regular fa-edit"></i>
                    </button>
                </div>
              </div>
            </div>
          </td>
          <td>
            <div style="display: grid; gap: 5px; justify-items: center;">
                <p *ngIf="reservation.numeroBadge && reservation.reservationId" class="consommation-badge badge-cash" > N° {{ reservation.numeroBadge }}</p>
              <div *ngIf="!reservation.numeroBadge" class="no-consommation">
                Aucun badge
              </div>
              <button *ngIf="reservation.numeroBadge" class="btn-action btn-edit" title="Editer un vehicule" (click)="addConsommationTelepeage(reservation)">
                <i class="fa-regular fa-edit"></i>
              </button>
            </div>
          </td>
          <td>{{ reservation.nombreTicketResto }}</td>
          <td>
            <div style="text-align: center;">
              <span *ngIf="reservation.frais > 0" class="frais-amount">
                {{ formatCurrency(reservation.frais) }}
              </span>
              <span *ngIf="reservation.frais === 0" class="no-frais">
                Aucun frais
              </span>
            </div>
          </td>

          <!-- Actions dans une seule cellule avec alignement horizontal -->
          <td >
            <div class="actions-cell">
              <button class="btn-action btn-view" title="Voir les détails">
                <i class="fas fa-eye"></i>
              </button>
              <button class="btn-action btn-fuel" title="Consommation Carburant" (click)="goToConsommationCarburant(reservation)">
                <i class="fas fa-gas-pump"></i>
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- Cartes pour mobile -->
    <div class="mobile-cards">
      <div class="vehicule-card" *ngFor="let reservation of dataSource">
        <div class="card-header">
          <span class="status-badge">
            {{ reservation.astreinteIds }}
          </span>
        </div>

        <div class="card-row">
          <div class="label"><i class="fas fa-users fa-fw"></i> Consultants:</div>
          <div class="value">{{ reservation.consultantNames ? reservation.consultantNames.join(', ') : 'Non assigné' }}</div>
        </div>

        <div class="card-row">
          <div class="label"><i class="fas fa-calendar-alt fa-fw"></i> Date Début:</div>
          <div class="value">{{ reservation.dateDebut | date:'dd/MM/yyyy' }}</div>
        </div>

        <div class="card-row">
          <div class="label"><i class="fas fa-cogs fa-fw"></i> Date Fin:</div>
          <div class="value">
            <span class="badge">
              {{ reservation.dateFin | date:'dd/MM/yyyy' }}
            </span>
          </div>
        </div>

        <div class="card-row">
          <div class="label"><i class="fas fa-gas-pump fa-fw"></i> Véhicule:</div>
          <div class="value">
            <span class="badge">
              {{ reservation.immatriculation }}
            </span>
          </div>
        </div>

        <div class="card-row">
          <div class="label"><i class="fas fa-credit-card fa-fw"></i> Consommations:</div>
          <div class="value">
            <!-- Affichage reformulé pour mobile -->
            <div class="consommation-container mobile">
              <div *ngIf="!hasConsommations(reservation.reservationId)" class="no-consommation">
                Aucune consommation
              </div>

              <div *ngIf="hasConsommations(reservation.reservationId)" class="consommation-badges">
                <div *ngFor="let item of getConsommationItems(reservation.reservationId)"
                     class="consommation-badge"
                     [ngClass]="getTypeClass(item.type)">
                  <!-- Même format simplifié que pour desktop -->
                  <ng-container *ngIf="item.type === 'Carte'">Carte {{ item.value }}</ng-container>
                  <ng-container *ngIf="item.type === 'Frais'">Frais #{{ item.id }}</ng-container>
                  <ng-container *ngIf="item.type === 'Cash'">Cash #{{ item.id }}</ng-container>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="card-row">
          <div class="label">Badge Télépéage:</div>
          <div class="value">
            {{ reservation.numeroBadge}}
          </div>
        </div>

        <div class="card-actions">
          <button class="btn-action btn-view" title="Voir les détails">
            <i class="fas fa-eye"></i>
          </button>
          <button class="btn-action btn-fuel" title="Consommation Carburant" (click)="goToConsommationCarburant(reservation)">
            <i class="fas fa-gas-pump"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
