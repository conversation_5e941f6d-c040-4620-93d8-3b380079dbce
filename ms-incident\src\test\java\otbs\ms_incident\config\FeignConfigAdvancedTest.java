package otbs.ms_incident.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FeignConfigAdvancedTest {

    private FeignConfig config;

    @Mock
    private HttpRequest httpRequest;

    @Mock
    private ClientHttpRequestExecution execution;

    @Mock
    private ClientHttpResponse clientHttpResponse;

    @Mock
    private HttpHeaders httpHeaders;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private JwtAuthenticationToken jwtAuthenticationToken;

    @Mock
    private Jwt jwt;

    @Mock
    private RequestTemplate requestTemplate;

    @BeforeEach
    void setUp() {
        config = new FeignConfig();
    }

    @Test
    void feignClientInterceptor_shouldAddAuthorizationHeader_whenJwtAuthenticationTokenIsPresent() {
        // Given
        RequestInterceptor interceptor = config.feignRequestInterceptor();

        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            // Setup mocks
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(jwtAuthenticationToken);
            when(jwtAuthenticationToken.getToken()).thenReturn(jwt);
            when(jwt.getTokenValue()).thenReturn("test-token");

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate).header("Authorization", "Bearer test-token");
        }
    }

    @Test
    void feignClientInterceptor_shouldNotAddAuthorizationHeader_whenAuthenticationIsNull() {
        // Given
        RequestInterceptor interceptor = config.feignRequestInterceptor();

        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            // Setup mocks
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(null);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate, never()).header(eq("Authorization"), anyString());
        }
    }

    @Test
    void feignClientInterceptor_shouldNotAddAuthorizationHeader_whenAuthenticationIsNotJwtAuthenticationToken() {
        // Given
        RequestInterceptor interceptor = config.feignRequestInterceptor();
        Authentication nonJwtAuthentication = mock(Authentication.class);

        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            // Setup mocks
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(nonJwtAuthentication);

            // When
            interceptor.apply(requestTemplate);

            // Then
            verify(requestTemplate, never()).header(eq("Authorization"), anyString());
        }
    }

    @Test
    void restTemplate_shouldHaveCorrectInterceptors() {
        // When
        RestTemplate restTemplate = config.restTemplate();

        // Then
        assertNotNull(restTemplate);
        assertEquals(1, restTemplate.getInterceptors().size());
        assertTrue(restTemplate.getInterceptors().get(0) instanceof ClientHttpRequestInterceptor);
    }

    @Test
    void jwtTokenInterceptor_shouldExecuteRequestWithoutModification_whenNoAuthentication() throws Exception {
        // Given
        RestTemplate restTemplate = config.restTemplate();
        ClientHttpRequestInterceptor interceptor = restTemplate.getInterceptors().get(0);
        byte[] body = "test".getBytes();

        try (MockedStatic<SecurityContextHolder> mockedStatic = Mockito.mockStatic(SecurityContextHolder.class)) {
            // Setup mocks
            mockedStatic.when(SecurityContextHolder::getContext).thenReturn(securityContext);
            when(securityContext.getAuthentication()).thenReturn(null);
            when(httpRequest.getHeaders()).thenReturn(httpHeaders);
            when(execution.execute(httpRequest, body)).thenReturn(clientHttpResponse);

            // When
            Method interceptMethod = interceptor.getClass().getDeclaredMethod("intercept",
                    HttpRequest.class, byte[].class, ClientHttpRequestExecution.class);
            interceptMethod.setAccessible(true);
            ClientHttpResponse result = (ClientHttpResponse) interceptMethod.invoke(interceptor, httpRequest, body, execution);

            // Then
            assertSame(clientHttpResponse, result);
            verify(httpHeaders, never()).add(eq("Authorization"), anyString());
            verify(execution).execute(httpRequest, body);
        }
    }

    @Test
    void vehiculeServiceUrl_shouldBeInjectedCorrectly() throws Exception {
        // Given
        String testUrl = "http://test-vehicule-service";
        Field urlField = FeignConfig.class.getDeclaredField("vehiculeServiceUrl");
        urlField.setAccessible(true);
        urlField.set(config, testUrl);

        // Then
        assertEquals(testUrl, urlField.get(config));
    }

    @Test
    void feignRequestInterceptor_shouldReturnFeignClientInterceptorInstance() {
        // When
        RequestInterceptor interceptor = config.feignRequestInterceptor();

        // Then
        assertNotNull(interceptor);
        assertTrue(interceptor instanceof FeignClientInterceptor);
    }
}
