package otbs.reservation.service;

import org.springframework.stereotype.Service;
import otbs.reservation.dto.ConsommationTelepeageDto;
import otbs.reservation.model.ConsommationTelepeage;
import otbs.reservation.model.Reservation;
import otbs.reservation.model.Telepeage;
import otbs.reservation.repository.ConsommationTelepeageRepo;
import otbs.reservation.repository.TelepeageRepo;

@Service
public class ConsommationTelepeageService {

    private final ConsommationTelepeageRepo consommationTelepeageRepo;
    private final TelepeageService telepeageService;
    private final TelepeageRepo telepeageRepo;
    private final ReservationService reservationService;

    public ConsommationTelepeageService(ConsommationTelepeageRepo consommationTelepeageRepo,
                                        TelepeageService telepeageService,
                                        ReservationService reservationService,
                                        TelepeageRepo telepeageRepo) {
        this.consommationTelepeageRepo = consommationTelepeageRepo;
        this.telepeageService = telepeageService;
        this.telepeageRepo = telepeageRepo;
        this.reservationService = reservationService;
    }


    private static ConsommationTelepeageDto entityToDto(ConsommationTelepeage consommationTelepeage) {
        ConsommationTelepeageDto consommationDto = new ConsommationTelepeageDto();
        consommationDto.setId(consommationTelepeage.getId());
        if(consommationTelepeage.getMontant()!= null){
            consommationDto.setMontant(consommationTelepeage.getMontant());
        }
        consommationDto.setTelepeageId(consommationTelepeage.getTelepeage().getTelepeageId());
        consommationDto.setReservationId(consommationTelepeage.getReservation().getReservationId());

        return  consommationDto;
    }

    private void setAttributes(ConsommationTelepeageDto consommationTelepeageDto, ConsommationTelepeage consommationTelepeage) {
        consommationTelepeage.setTelepeage(telepeageService.findTelepeage(consommationTelepeageDto.getTelepeageId()));
        consommationTelepeage.setReservation(reservationService.findReservation(consommationTelepeageDto.getReservationId()));
    }

    private ConsommationTelepeage findConsommationTelepeage(Long id) {
        return consommationTelepeageRepo.findById(id)
                .orElseThrow(() -> new RuntimeException("ConsommationTelepeage non trouvée"));
    }

    public ConsommationTelepeageDto attributionBadgeTelepeage(ConsommationTelepeageDto consommationTelepeageDto) {
        ConsommationTelepeage consommationTelepeage = new ConsommationTelepeage();
        setAttributes( consommationTelepeageDto, consommationTelepeage);
        return entityToDto(consommationTelepeageRepo.save(consommationTelepeage));
    }

    public ConsommationTelepeageDto addConsommationTelepeage(ConsommationTelepeageDto consommationTelepeageDto) {
        ConsommationTelepeage consommationTelepeage = new ConsommationTelepeage();
        setAttributes(consommationTelepeageDto, consommationTelepeage);
        consommationTelepeage.setMontant(consommationTelepeageDto.getMontant());
        return entityToDto(consommationTelepeageRepo.save(consommationTelepeage));
    }


    public double getConsommationParReservation(Long reservationId) {
        var reservation = reservationService.findReservation(reservationId);
        var consommation = consommationTelepeageRepo.findByReservation(reservation);

        if (consommation != null) {
            return consommation.getMontant();
        } else {
            return 0.0;
        }
    }

    public double getConsommationParMission(Long idMission) {
        return reservationService.findReservationsByMission(idMission).stream()
                .mapToDouble(reservation -> getConsommationParReservation(reservation.getReservationId()))
                .sum();
    }

    public ConsommationTelepeageDto updateAttributedBadgeTelepeage(Long reservationId,
                                                                   ConsommationTelepeageDto consommationTelepeageDto) {
        ConsommationTelepeage consommationTelepeage = consommationTelepeageRepo.findByReservation(
                reservationService.findReservation(reservationId));
        if(consommationTelepeageDto.getTelepeageId() != null){
            consommationTelepeage.setTelepeage(telepeageService.findTelepeage(consommationTelepeageDto.getTelepeageId()));
            return entityToDto(consommationTelepeageRepo.save(consommationTelepeage));
        }else {
            consommationTelepeageRepo.delete(consommationTelepeage);
            return new ConsommationTelepeageDto();
        }
    }

    public ConsommationTelepeageDto getConsommationTelepeageParReservation(Long reservationId) {
        ConsommationTelepeage consommationTelepeage = consommationTelepeageRepo.findByReservation(
                reservationService.findReservation(reservationId));
        return entityToDto(consommationTelepeage);
    }

    public ConsommationTelepeageDto addConsommationTelepeageByReservation(ConsommationTelepeageDto consommationTelepeageDto) {
        Reservation reservation = reservationService.findReservation(consommationTelepeageDto.getReservationId());
        ConsommationTelepeage consommationTelepeage = consommationTelepeageRepo.findByReservation(reservation);
        consommationTelepeage.setMontant(consommationTelepeageDto.getMontant());

        Telepeage telepeage = telepeageService.findTelepeage(consommationTelepeageDto.getTelepeageId());
        telepeage.setSolde(telepeage.getSolde() - consommationTelepeageDto.getMontant());
        telepeageRepo.save(telepeage);
        return entityToDto(consommationTelepeageRepo.save(consommationTelepeage));
    }
}
