package otbs.ms_astreint.dto.notification;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class AstreinteEvent extends NotificationEvent implements Serializable {

    private Long astreinteId;
    private String descriptionAstreinte;
    private List<String> consultantsAssignes;

    public AstreinteEvent(String eventType, Long astreinteId, List<String> destinataires,
                         String titre, String message) {
        super(eventType, "ms-astreint", destinataires, titre, message);
        this.astreinteId = astreinteId;
        this.setEntiteLieeId(astreinteId);
        this.setEntiteLieeType("astreinte");
        this.setPriorite("NORMALE");
    }

    public AstreinteEvent(String eventType, Long astreinteId, String destinataire,
                         String titre, String message) {
        super(eventType, "ms-astreint", destinataire, titre, message);
        this.astreinteId = astreinteId;
        this.setEntiteLieeId(astreinteId);
        this.setEntiteLieeType("astreinte");
        this.setPriorite("NORMALE");
    }
} 