spring.application.name=vehicule

# Server port
server.port=9003

# Eureka Client Configuration
eureka.client.service-url.defaultZone=http://*************:9102/eureka/
#<PERSON> will automatically detect the hostname of the machine where the service is running

# DataSource Configuration
spring.datasource.url=*********************************************
spring.datasource.username=parcauto
spring.datasource.password=parcauto
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database=postgresql

# Springdoc OpenAPI Configuration
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true

# OAuth2 Security Configuration for the REST API
spring.security.oauth2.resourceserver.jwt.issuer-uri: http://*************:8080/realms/parc-auto
spring.security.oauth2.resourceserver.jwt.jwk-set-uri: ${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs
jwt.auth.converter.resource-id= ms_vehicule
jwt.auth.converter.principle-attribute= preferred_username

# OAuth2 Parameters Configuration for Swagger UI
springdoc.swagger-ui.oauth.client-id=ms_vehicule
springdoc.swagger-ui.oauth.client-secret=WMNFDttbGHmkiJQsl7VsPzqHmp5G47yn

# NFS File Storage Path Configuration
storage.path=/app/storage

vehicule.notification.delai-maj-initial=7
vehicule.notification.delai-avertissement1=1
vehicule.notification.delai-avertissement2=3
vehicule.notification.delai-avertissement3=7
vehicule.notification.seuil-vidange-urgent=0
vehicule.notification.seuil-avertissement1-min=1
vehicule.notification.seuil-avertissement1-max=500
vehicule.notification.seuil-avertissement2-min=501
vehicule.notification.seuil-avertissement2-max=1000
vehicule.notification.seuil-avertissement3-min=1001
vehicule.notification.seuil-avertissement3-max=2000
vehicule.notification.seuil-vehicule-neuf=10000
vehicule.notification.destinataire-role=RESPONSABLE_SERVICE_GENERAUX

spring.rabbitmq.host=${RABBITMQ_HOST:*************}
spring.rabbitmq.port=${RABBITMQ_PORT:5672}
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}
spring.rabbitmq.virtual-host=${RABBITMQ_VHOST:/}
spring.rabbitmq.connection-timeout=${RABBITMQ_CONNECTION_TIMEOUT:10000}
spring.rabbitmq.requested-heartbeat=${RABBITMQ_HEARTBEAT:30}
spring.rabbitmq.publisher-confirm-type=correlated
spring.rabbitmq.publisher-returns=true
spring.rabbitmq.template.mandatory=true
spring.rabbitmq.template.retry.enabled=true
spring.rabbitmq.template.retry.initial-interval=1000
spring.rabbitmq.template.retry.max-attempts=3


