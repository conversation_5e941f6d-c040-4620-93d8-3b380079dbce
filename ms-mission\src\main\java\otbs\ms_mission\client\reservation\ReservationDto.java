package otbs.ms_mission.client.reservation;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * DTO pour représenter une réservation provenant du microservice ms-reservation.
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ReservationDto {
    private Long reservationId;
    private Date dateReservation;
    private Long idVehicule;
    private Long idMission;
}
