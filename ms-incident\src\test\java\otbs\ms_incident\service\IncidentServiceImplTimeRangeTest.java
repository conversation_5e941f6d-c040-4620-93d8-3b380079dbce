package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import otbs.ms_incident.commun.PageResponse;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;
import otbs.ms_incident.exception.BadRequestException;
import otbs.ms_incident.mapper.IncidentMapper;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IncidentServiceImplTimeRangeTest {

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentMapper incidentMapper;

    @InjectMocks
    private IncidentServiceImpl incidentService;

    private Incident incident1, incident2;
    private IncidentResponseDTO responseDTO1, responseDTO2;
    private List<Incident> incidents;
    private List<IncidentResponseDTO> responseDTOs;

    @BeforeEach
    void setUp() {
        // Setup test incidents
        incident1 = new Incident();
        incident1.setId(1L);
        incident1.setDate(LocalDate.of(2023, 5, 15));
        incident1.setType(TypeIncident.ACCIDENT);
        incident1.setStatus(StatusIncident.A_TRAITER);
        incident1.setPriorite(NiveauPrioriteIncident.CRITIQUE);
        incident1.setLieu("Paris");
        incident1.setDescription("Accident 1");
        incident1.setVehiculeId(100L);

        incident2 = new Incident();
        incident2.setId(2L);
        incident2.setDate(LocalDate.of(2023, 6, 20));
        incident2.setType(TypeIncident.PANNE);
        incident2.setStatus(StatusIncident.EN_COURS_TRAITEMENT);
        incident2.setPriorite(NiveauPrioriteIncident.MOYEN);
        incident2.setLieu("Lyon");
        incident2.setDescription("Panne 1");
        incident2.setVehiculeId(200L);

        incidents = Arrays.asList(incident1, incident2);

        // Setup test DTOs
        responseDTO1 = new IncidentResponseDTO();
        responseDTO1.setId(1L);
        responseDTO1.setVehiculeId(100L);

        responseDTO2 = new IncidentResponseDTO();
        responseDTO2.setId(2L);
        responseDTO2.setVehiculeId(200L);

        responseDTOs = Arrays.asList(responseDTO1, responseDTO2);
    }

    @Test
    void getIncidentsForToday_shouldReturnTodaysIncidents() {
        // Given
        LocalDate today = LocalDate.now();
        when(incidentRepository.findByDateBetween(today, today)).thenReturn(incidents);
        when(incidentMapper.toResponseDTOList(incidents)).thenReturn(responseDTOs);

        // When
        List<IncidentResponseDTO> result = incidentService.getIncidentsForToday();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(incidentRepository).findByDateBetween(today, today);
        verify(incidentMapper).toResponseDTOList(incidents);
    }

    @Test
    void getIncidentsForLastMonth_shouldReturnLastMonthIncidents() {
        // Given
        LocalDate today = LocalDate.now();
        LocalDate oneMonthAgo = today.minusMonths(1);
        when(incidentRepository.findByDateBetween(oneMonthAgo, today)).thenReturn(incidents);
        when(incidentMapper.toResponseDTOList(incidents)).thenReturn(responseDTOs);

        // When
        List<IncidentResponseDTO> result = incidentService.getIncidentsForLastMonth();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(incidentRepository).findByDateBetween(oneMonthAgo, today);
        verify(incidentMapper).toResponseDTOList(incidents);
    }

    @Test
    void getIncidentsForLastSemester_shouldReturnLastSixMonthsIncidents() {
        // Given
        LocalDate today = LocalDate.now();
        LocalDate sixMonthsAgo = today.minusMonths(6);
        when(incidentRepository.findByDateBetween(sixMonthsAgo, today)).thenReturn(incidents);
        when(incidentMapper.toResponseDTOList(incidents)).thenReturn(responseDTOs);

        // When
        List<IncidentResponseDTO> result = incidentService.getIncidentsForLastSemester();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(incidentRepository).findByDateBetween(sixMonthsAgo, today);
        verify(incidentMapper).toResponseDTOList(incidents);
    }

    @Test
    void getIncidentsForLastYear_shouldReturnLastYearIncidents() {
        // Given
        LocalDate today = LocalDate.now();
        LocalDate oneYearAgo = today.minusYears(1);
        when(incidentRepository.findByDateBetween(oneYearAgo, today)).thenReturn(incidents);
        when(incidentMapper.toResponseDTOList(incidents)).thenReturn(responseDTOs);

        // When
        List<IncidentResponseDTO> result = incidentService.getIncidentsForLastYear();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(incidentRepository).findByDateBetween(oneYearAgo, today);
        verify(incidentMapper).toResponseDTOList(incidents);
    }

    @Test
    void getIncidentsByDateRange_withPagination_shouldReturnPagedIncidents() {
        // Given
        LocalDate debut = LocalDate.of(2023, 5, 1);
        LocalDate fin = LocalDate.of(2023, 6, 30);
        Pageable pageable = PageRequest.of(0, 10);
        Page<Incident> page = new PageImpl<>(incidents, pageable, 2);

        when(incidentRepository.findByDateBetween(debut, fin, pageable)).thenReturn(page);
        when(incidentMapper.toResponseDTO(incident1)).thenReturn(responseDTO1);
        when(incidentMapper.toResponseDTO(incident2)).thenReturn(responseDTO2);

        // When
        PageResponse<IncidentResponseDTO> result = incidentService.getIncidentsByDateRange(debut, fin, pageable);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        assertEquals(2, result.getTotalElements());
        assertEquals(1, result.getTotalPages());
        verify(incidentRepository).findByDateBetween(debut, fin, pageable);
    }

    @Test
    void getIncidentsByDateRange_withPagination_shouldThrowBadRequestException_whenDebutIsAfterFin() {
        // Given
        LocalDate debut = LocalDate.of(2023, 6, 30);
        LocalDate fin = LocalDate.of(2023, 5, 1);
        Pageable pageable = PageRequest.of(0, 10);

        // When & Then
        assertThrows(BadRequestException.class, () -> incidentService.getIncidentsByDateRange(debut, fin, pageable));
        verify(incidentRepository, never()).findByDateBetween(any(LocalDate.class), any(LocalDate.class), any(Pageable.class));
    }

    @Test
    void getIncidentsByLieu_withPagination_shouldReturnPagedIncidents() {
        // Given
        String lieu = "Paris";
        Pageable pageable = PageRequest.of(0, 10);
        Page<Incident> page = new PageImpl<>(incidents, pageable, 2);

        when(incidentRepository.findByLieuContaining(lieu, pageable)).thenReturn(page);
        when(incidentMapper.toResponseDTO(incident1)).thenReturn(responseDTO1);
        when(incidentMapper.toResponseDTO(incident2)).thenReturn(responseDTO2);

        // When
        PageResponse<IncidentResponseDTO> result = incidentService.getIncidentsByLieu(lieu, pageable);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        assertEquals(2, result.getTotalElements());
        assertEquals(1, result.getTotalPages());
        verify(incidentRepository).findByLieuContaining(lieu, pageable);
    }

    @Test
    void getIncidentsByVehiculeId_withPagination_shouldReturnPagedIncidents() {
        // Given
        Long vehiculeId = 100L;
        Pageable pageable = PageRequest.of(0, 10);
        Page<Incident> page = new PageImpl<>(incidents, pageable, 2);

        when(incidentRepository.findByVehiculeId(vehiculeId, pageable)).thenReturn(page);
        when(incidentMapper.toResponseDTO(incident1)).thenReturn(responseDTO1);
        when(incidentMapper.toResponseDTO(incident2)).thenReturn(responseDTO2);

        // When
        PageResponse<IncidentResponseDTO> result = incidentService.getIncidentsByVehiculeId(vehiculeId, pageable);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        assertEquals(2, result.getTotalElements());
        assertEquals(1, result.getTotalPages());
        verify(incidentRepository).findByVehiculeId(vehiculeId, pageable);
    }
}
