package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import otbs.ms_incident.commun.PageResponse;
import otbs.ms_incident.dto.ReparationRequestDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.enums.StatusReparation;
import otbs.ms_incident.exception.BadRequestException;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.ReparationMapper;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReparationServiceEdgeCasesTest {

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationMapper reparationMapper;

    @InjectMocks
    private ReparationServiceImpl reparationService;

    private Incident incident;
    private Reparation reparation;
    private ReparationRequestDTO requestDTO;
    private ReparationResponseDTO responseDTO;

    @BeforeEach
    void setUp() {
        incident = new Incident();
        incident.setId(1L);

        reparation = new Reparation();
        reparation.setId(1L);
        reparation.setDateReparation(LocalDate.of(2023, 5, 15));
        reparation.setStatus(StatusReparation.EN_COURS);
        reparation.setDescription("Réparation test");
        reparation.setCout(new BigDecimal("1000.00"));
        reparation.setGarage("Garage Test");
        reparation.setIncident(incident);

        requestDTO = new ReparationRequestDTO();
        requestDTO.setDateReparation(LocalDate.of(2023, 5, 15));
        requestDTO.setStatus(StatusReparation.EN_COURS);
        requestDTO.setDescription("Réparation test");
        requestDTO.setCout(new BigDecimal("1000.00"));
        requestDTO.setGarage("Garage Test");
        requestDTO.setIncidentId(1L);

        responseDTO = new ReparationResponseDTO();
        responseDTO.setId(1L);
        responseDTO.setDateReparation(LocalDate.of(2023, 5, 15));
        responseDTO.setStatus(StatusReparation.EN_COURS);
        responseDTO.setDescription("Réparation test");
        responseDTO.setCout(new BigDecimal("1000.00"));
        responseDTO.setGarage("Garage Test");
        responseDTO.setIncidentId(1L);
    }

    @Test
    void createReparation_shouldThrowException_whenIncidentNotFound() {
        // Given
        when(incidentRepository.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.createReparation(requestDTO)
        );
    }

    @Test
    void createReparation_shouldFail_withFutureDate() {
        // Given
        requestDTO.setDateReparation(LocalDate.now().plusDays(10));
        // Pas besoin de mock car la validation échoue avant d'appeler le repository

        // When/Then
        assertThrows(BadRequestException.class, () ->
            reparationService.createReparation(requestDTO)
        );
    }

    @Test
    void createReparation_shouldFail_withNegativeCost() {
        // Given
        requestDTO.setCout(new BigDecimal("-100.00"));
        // Pas besoin de mock car la validation échoue avant d'appeler le repository

        // When/Then
        assertThrows(BadRequestException.class, () ->
            reparationService.createReparation(requestDTO)
        );
    }

    @Test
    void updateReparation_shouldThrowException_whenReparationNotFound() {
        // Given
        when(reparationRepository.findById(999L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.updateReparation(999L, requestDTO)
        );
    }

    @Test
    void updateReparation_shouldSucceed_whenIncidentIdNotChanged() {
        // Given
        when(reparationRepository.findById(1L)).thenReturn(Optional.of(reparation));
        when(reparationRepository.save(any(Reparation.class))).thenReturn(reparation);
        when(reparationMapper.toDTO(reparation)).thenReturn(responseDTO);

        // When
        ReparationResponseDTO result = reparationService.updateReparation(1L, requestDTO);

        // Then
        assertNotNull(result);
        assertEquals(responseDTO, result);
    }

    @Test
    void getReparationsByIncidentId_shouldReturnEmptyList_whenNoReparationsFound() {
        // Given
        when(incidentRepository.existsById(1L)).thenReturn(true);
        when(reparationRepository.findByIncidentId(1L)).thenReturn(Collections.emptyList());

        // When
        List<ReparationResponseDTO> result = reparationService.getReparationsByIncidentId(1L);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getReparationsByIncidentIdPaginated_shouldReturnEmptyPage_whenNoReparationsFound() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<Reparation> emptyPage = new PageImpl<>(Collections.emptyList(), pageable, 0);

        when(incidentRepository.existsById(1L)).thenReturn(true);
        when(reparationRepository.findByIncidentId(1L, pageable)).thenReturn(emptyPage);

        // When
        PageResponse<ReparationResponseDTO> result = reparationService.getReparationsByIncidentIdPaginated(1L, pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getContent().isEmpty());
        assertEquals(0, result.getTotalElements());
    }

    @Test
    void deleteReparation_shouldThrowException_whenReparationNotFound() {
        // Given
        when(reparationRepository.existsById(999L)).thenReturn(false);

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.deleteReparation(999L)
        );
    }

    @Test
    void getReparationsByStatusAndIncidentId_shouldReturnEmptyList_whenNoReparationsFound() {
        // Given
        when(incidentRepository.existsById(1L)).thenReturn(true);
        when(reparationRepository.findByStatusAndIncidentId(StatusReparation.TERMINEE, 1L))
            .thenReturn(Collections.emptyList());

        // When
        List<ReparationResponseDTO> result = reparationService.getReparationsByStatusAndIncidentId(StatusReparation.TERMINEE, 1L);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getAverageCost_shouldReturnZero_whenNoReparations() {
        // Given
        when(reparationRepository.findAll()).thenReturn(Collections.emptyList());

        // When
        BigDecimal result = reparationService.getAverageCost();

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void getRepairCountByStatus_shouldReturnEmptyMap_whenNoReparations() {
        // Given
        when(reparationRepository.findAll()).thenReturn(Collections.emptyList());

        // When
        var result = reparationService.getRepairCountByStatus();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
