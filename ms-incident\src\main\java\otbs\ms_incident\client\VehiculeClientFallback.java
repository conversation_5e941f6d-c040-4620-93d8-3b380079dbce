package otbs.ms_incident.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Implémentation de fallback pour le client Feign VehiculeClient.
 * Cette classe fournit des réponses par défaut lorsque le service vehicule est indisponible.
 */
@Component
@Slf4j
public class VehiculeClientFallback implements VehiculeClient {

    private static final String INDISPONIBLE = "Indisponible";

    @Override
    public VehiculeDto getVehiculeById(Long id) {
        log.warn("Fallback activé pour getVehiculeById avec id: {}", id);
        // Retourner un objet par défaut avec des informations minimales
        return createDefaultVehicule(id, null);
    }

    @Override
    public VehiculeDto getVehiculeByImmatriculation(String immatriculation) {
        log.warn("Fallback activé pour getVehiculeByImmatriculation avec immatriculation: {}", immatriculation);
        // Retourner un objet par défaut avec des informations minimales
        return createDefaultVehicule(null, immatriculation);
    }

    /**
     * Crée un objet VehiculeDto par défaut avec des informations minimales.
     *

     * @return Un objet VehiculeDto par défaut
     */
    @Override
    public List<VehiculeDto> getAllVehicules() {
        log.warn("Fallback activé pour getAllVehicules");
        return new ArrayList<>();
    }



    /**
     * Crée un objet VehiculeDto par défaut avec des informations minimales.
     *
     * @param id L'ID du véhicule (peut être null)
     * @param immatriculation L'immatriculation du véhicule (peut être null)
     * @return Un objet VehiculeDto par défaut avec tous les attributs initialisés
     */
    private VehiculeDto createDefaultVehicule(Long id, String immatriculation) {
        VehiculeDto vehicule = new VehiculeDto();
        vehicule.setIdVehicule(id);
        vehicule.setImmatriculation(immatriculation != null ? immatriculation : INDISPONIBLE);
        vehicule.setMarque(INDISPONIBLE);
        vehicule.setModele(INDISPONIBLE);
        vehicule.setEtat("INDISPONIBLE");
        vehicule.setCategorie(INDISPONIBLE);
        vehicule.setKilometrage(0);
        vehicule.setDatePrelevementKilometrage(new java.util.Date()); // Date actuelle
        vehicule.setTypeCarburant(INDISPONIBLE);
        vehicule.setNombreDePlaces(0);
        vehicule.setRemisCles(false);
        vehicule.setAstreinte(false);
        return vehicule;
    }
}
