package otbs.ms_incident.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.dto.dashboard.*;
import otbs.ms_incident.service.DashboardService;

import java.time.LocalDate;
import java.util.List;

/**
 * Contrôleur pour les endpoints du dashboard
 */
@RestController
@RequestMapping("/api/dashboard")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Dashboard", description = "API pour le dashboard des incidents et réparations")
public class DashboardController {

    private final DashboardService dashboardService;


    @GetMapping("/summary")
    @Operation(summary = "Obtenir les statistiques générales du dashboard",
               description = "Retourne les statistiques générales pour le dashboard")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistiques récupérées avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    public ResponseEntity<DashboardSummaryDTO> getDashboardSummary(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin,
            @Parameter(description = "ID du véhicule (optionnel)")
            @RequestParam(required = false) Long vehiculeId) {

        log.info("REST request to get dashboard summary stats for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        DashboardSummaryDTO result = dashboardService.getDashboardSummary(debut, fin, vehiculeId);

        return ResponseEntity.ok(result);
    }

    @GetMapping("/recent-incidents")
    @Operation(summary = "Obtenir les incidents récents",
               description = "Retourne les 5 incidents les plus récents pour une plage de dates donnée")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Incidents récupérés avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @CircuitBreaker(name = "vehiculeService")
    @Retry(name = "vehiculeService")
    public ResponseEntity<List<IncidentResponseDTO>> getRecentIncidents(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin,
            @Parameter(description = "ID du véhicule (optionnel)")
            @RequestParam(required = false) Long vehiculeId) {

        log.info("REST request to get recent incidents for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        List<IncidentResponseDTO> incidents = dashboardService.getRecentIncidents(debut, fin, vehiculeId);

        return ResponseEntity.ok(incidents);
    }

    @GetMapping("/recent-repairs")
    @Operation(summary = "Obtenir les réparations récentes",
               description = "Retourne les 5 réparations les plus récentes pour une plage de dates donnée")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Réparations récupérées avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @CircuitBreaker(name = "vehiculeService")
    @Retry(name = "vehiculeService")
    public ResponseEntity<List<ReparationResponseDTO>> getRecentRepairs(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin,
            @Parameter(description = "ID du véhicule (optionnel)")
            @RequestParam(required = false) Long vehiculeId) {

        log.info("REST request to get recent repairs for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        List<ReparationResponseDTO> repairs = dashboardService.getRecentRepairs(debut, fin, vehiculeId);

        return ResponseEntity.ok(repairs);
    }



    @GetMapping("/incidents/status-distribution")
    @Operation(summary = "Obtenir la répartition des incidents par statut",
               description = "Retourne la répartition des incidents par statut pour une plage de dates donnée")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistiques récupérées avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    public ResponseEntity<IncidentStatusDistributionDTO> getIncidentStatusDistribution(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin,
            @Parameter(description = "ID du véhicule (optionnel)")
            @RequestParam(required = false) Long vehiculeId) {

        log.info("REST request to get incident status distribution for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        IncidentStatusDistributionDTO result = dashboardService.getIncidentStatusDistribution(debut, fin, vehiculeId);

        return ResponseEntity.ok(result);
    }

    @GetMapping("/incidents/type-distribution")
    @Operation(summary = "Obtenir la répartition des incidents par type",
               description = "Retourne la répartition des incidents par type pour une plage de dates donnée")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistiques récupérées avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    public ResponseEntity<IncidentTypeDistributionDTO> getIncidentTypeDistribution(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin,
            @Parameter(description = "ID du véhicule (optionnel)")
            @RequestParam(required = false) Long vehiculeId) {

        log.info("REST request to get incident type distribution for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        IncidentTypeDistributionDTO result = dashboardService.getIncidentTypeDistribution(debut, fin, vehiculeId);

        return ResponseEntity.ok(result);
    }

    @GetMapping("/incidents/vehicle-distribution")
    @Operation(summary = "Obtenir la répartition des incidents par véhicule",
               description = "Retourne la répartition des incidents par véhicule pour une plage de dates donnée")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistiques récupérées avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @CircuitBreaker(name = "vehiculeService")
    @Retry(name = "vehiculeService")
    public ResponseEntity<VehicleIncidentDistributionDTO> getVehicleIncidentDistribution(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin,
            @Parameter(description = "ID du véhicule (optionnel)")
            @RequestParam(required = false) Long vehiculeId) {

        log.info("REST request to get vehicle incident distribution for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        VehicleIncidentDistributionDTO result = dashboardService.getVehicleIncidentDistribution(debut, fin, vehiculeId);

        return ResponseEntity.ok(result);
    }

    @GetMapping("/incidents/vehicle-type-distribution")
    @Operation(summary = "Obtenir la répartition des incidents par véhicule et par type",
               description = "Retourne la répartition des incidents par véhicule et par type pour une plage de dates donnée")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistiques récupérées avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @CircuitBreaker(name = "vehiculeService")
    @Retry(name = "vehiculeService")
    public ResponseEntity<VehicleIncidentTypeDistributionDTO> getVehicleIncidentTypeDistribution(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin,
            @Parameter(description = "ID du véhicule (optionnel)")
            @RequestParam(required = false) Long vehiculeId) {

        log.info("REST request to get vehicle incident type distribution for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        VehicleIncidentTypeDistributionDTO result = dashboardService.getVehicleIncidentTypeDistribution(debut, fin, vehiculeId);

        return ResponseEntity.ok(result);
    }

    @GetMapping("/repairs/cost-by-vehicle")
    @Operation(summary = "Obtenir les coûts de réparation par véhicule et par statut",
               description = "Retourne les coûts de réparation par véhicule et par statut pour une plage de dates donnée")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistiques récupérées avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @CircuitBreaker(name = "vehiculeService")
    @Retry(name = "vehiculeService")
    public ResponseEntity<RepairCostByVehicleDTO> getRepairCostByVehicle(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin,
            @Parameter(description = "ID du véhicule (optionnel)")
            @RequestParam(required = false) Long vehiculeId) {

        log.info("REST request to get repair cost by vehicle for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        RepairCostByVehicleDTO result = dashboardService.getRepairCostByVehicle(debut, fin, vehiculeId);

        return ResponseEntity.ok(result);
    }

    @GetMapping("/repairs/insurance-coverage")
    @Operation(summary = "Obtenir l'analyse de la couverture d'assurance pour les accidents",
               description = "Retourne l'analyse de la couverture d'assurance pour les réparations liées aux accidents")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistiques récupérées avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @CircuitBreaker(name = "vehiculeService")
    @Retry(name = "vehiculeService")
    public ResponseEntity<InsuranceCoverageDTO> getInsuranceCoverage(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin,
            @Parameter(description = "ID du véhicule (optionnel)")
            @RequestParam(required = false) Long vehiculeId) {

        log.info("REST request to get insurance coverage for accidents for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        InsuranceCoverageDTO result = dashboardService.getInsuranceCoverage(debut, fin, vehiculeId);

        return ResponseEntity.ok(result);
    }
}
