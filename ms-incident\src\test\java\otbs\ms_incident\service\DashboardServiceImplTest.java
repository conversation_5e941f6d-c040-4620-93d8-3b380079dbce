package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import otbs.ms_incident.client.VehiculeClient;
import otbs.ms_incident.client.VehiculeDto;
import otbs.ms_incident.dto.dashboard.DashboardSummaryDTO;
import otbs.ms_incident.dto.dashboard.IncidentStatusDistributionDTO;
import otbs.ms_incident.dto.dashboard.IncidentTypeDistributionDTO;
import otbs.ms_incident.dto.dashboard.VehicleIncidentDistributionDTO;
import otbs.ms_incident.mapper.IncidentMapper;
import otbs.ms_incident.mapper.ReparationMapper;
import otbs.ms_incident.repository.DashboardRepository;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import otbs.ms_incident.enums.TypeIncident;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DashboardServiceImplTest {

    @Mock
    private DashboardRepository dashboardRepository;

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentMapper incidentMapper;

    @Mock
    private ReparationMapper reparationMapper;

    @Mock
    private VehiculeClient vehiculeClient;

    @InjectMocks
    private DashboardServiceImpl dashboardService;

    private LocalDate debut;
    private LocalDate fin;
    private Long vehiculeId;
    private DashboardSummaryDTO currentPeriodDTO;
    private DashboardSummaryDTO previousPeriodDTO;

    @BeforeEach
    void setUp() {
        debut = LocalDate.of(2025, 5, 1);
        fin = LocalDate.of(2025, 5, 31);
        vehiculeId = 1L;

        // Initialiser les DTOs pour les tests
        currentPeriodDTO = new DashboardSummaryDTO();
        currentPeriodDTO.setTotalIncidents(10);
        currentPeriodDTO.setOpenIncidents(5);
        currentPeriodDTO.setInProgressIncidents(3);
        currentPeriodDTO.setResolvedIncidents(2);
        currentPeriodDTO.setHighPriorityIncidents(2);
        currentPeriodDTO.setTotalReparations(8);
        currentPeriodDTO.setInProgressReparations(5);
        currentPeriodDTO.setCompletedReparations(3);
        currentPeriodDTO.setTotalCost(BigDecimal.valueOf(1000));
        currentPeriodDTO.setAverageCost(BigDecimal.valueOf(125));

        previousPeriodDTO = new DashboardSummaryDTO();
        previousPeriodDTO.setTotalIncidents(5);
        previousPeriodDTO.setOpenIncidents(2);
        previousPeriodDTO.setInProgressIncidents(2);
        previousPeriodDTO.setResolvedIncidents(1);
        previousPeriodDTO.setHighPriorityIncidents(1);
        previousPeriodDTO.setTotalReparations(4);
        previousPeriodDTO.setInProgressReparations(3);
        previousPeriodDTO.setCompletedReparations(1);
        previousPeriodDTO.setTotalCost(BigDecimal.valueOf(500));
        previousPeriodDTO.setAverageCost(BigDecimal.valueOf(125));
    }

    @Test
    void getDashboardSummary_Success() {
        // Arrange
        LocalDate previousPeriodEnd = debut.minusDays(1);
        LocalDate previousPeriodStart = previousPeriodEnd.minusDays(30);

        when(dashboardRepository.getDashboardSummary(debut, fin, vehiculeId)).thenReturn(currentPeriodDTO);
        when(dashboardRepository.getDashboardSummary(previousPeriodStart, previousPeriodEnd, vehiculeId)).thenReturn(previousPeriodDTO);

        // Act
        DashboardSummaryDTO result = dashboardService.getDashboardSummary(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(10, result.getTotalIncidents());
        assertEquals(5, result.getOpenIncidents());
        assertEquals(8, result.getTotalReparations());
        assertEquals(BigDecimal.valueOf(1000), result.getTotalCost());

        // Vérifier les variations
        assertEquals(100.0, result.getTotalIncidentsChange(), 0.01);
        assertEquals(150.0, result.getOpenIncidentsChange(), 0.01);
        assertEquals(100.0, result.getTotalReparationsChange(), 0.01);
        assertEquals(100.0, result.getTotalCostChange(), 0.01);

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getDashboardSummary(debut, fin, vehiculeId);
        verify(dashboardRepository).getDashboardSummary(previousPeriodStart, previousPeriodEnd, vehiculeId);
    }

    @Test
    void getDashboardSummary_CurrentPeriodNull() {
        // Arrange
        when(dashboardRepository.getDashboardSummary(debut, fin, vehiculeId)).thenReturn(null);

        // Act
        DashboardSummaryDTO result = dashboardService.getDashboardSummary(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getTotalIncidents());
        assertEquals(0, result.getOpenIncidents());
        assertEquals(0, result.getTotalReparations());
        assertEquals(BigDecimal.ZERO, result.getTotalCost());
        assertEquals(0.0, result.getTotalIncidentsChange(), 0.01);

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getDashboardSummary(debut, fin, vehiculeId);
    }

    @Test
    void getDashboardSummary_PreviousPeriodNull() {
        // Arrange
        LocalDate previousPeriodEnd = debut.minusDays(1);
        LocalDate previousPeriodStart = previousPeriodEnd.minusDays(30);

        when(dashboardRepository.getDashboardSummary(debut, fin, vehiculeId)).thenReturn(currentPeriodDTO);
        when(dashboardRepository.getDashboardSummary(previousPeriodStart, previousPeriodEnd, vehiculeId)).thenReturn(null);

        // Act
        DashboardSummaryDTO result = dashboardService.getDashboardSummary(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(10, result.getTotalIncidents());
        assertEquals(5, result.getOpenIncidents());
        assertEquals(8, result.getTotalReparations());
        assertEquals(BigDecimal.valueOf(1000), result.getTotalCost());

        // Vérifier que les variations sont à 0
        assertEquals(0.0, result.getTotalIncidentsChange(), 0.01);
        assertEquals(0.0, result.getOpenIncidentsChange(), 0.01);
        assertEquals(0.0, result.getTotalReparationsChange(), 0.01);
        assertEquals(0.0, result.getTotalCostChange(), 0.01);

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getDashboardSummary(debut, fin, vehiculeId);
        verify(dashboardRepository).getDashboardSummary(previousPeriodStart, previousPeriodEnd, vehiculeId);
    }

    @Test
    void getDashboardSummary_CurrentPeriodException() {
        // Arrange
        when(dashboardRepository.getDashboardSummary(debut, fin, vehiculeId)).thenThrow(new RuntimeException("Test exception"));

        // Act
        DashboardSummaryDTO result = dashboardService.getDashboardSummary(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getTotalIncidents());
        assertEquals(0, result.getOpenIncidents());
        assertEquals(0, result.getTotalReparations());
        assertEquals(BigDecimal.ZERO, result.getTotalCost());
        assertEquals(0.0, result.getTotalIncidentsChange(), 0.01);

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getDashboardSummary(debut, fin, vehiculeId);
    }

    @Test
    void getDashboardSummary_PreviousPeriodException() {
        // Arrange
        LocalDate previousPeriodEnd = debut.minusDays(1);
        LocalDate previousPeriodStart = previousPeriodEnd.minusDays(30);

        when(dashboardRepository.getDashboardSummary(debut, fin, vehiculeId)).thenReturn(currentPeriodDTO);
        when(dashboardRepository.getDashboardSummary(previousPeriodStart, previousPeriodEnd, vehiculeId))
                .thenThrow(new RuntimeException("Test exception"));

        // Act
        DashboardSummaryDTO result = dashboardService.getDashboardSummary(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(10, result.getTotalIncidents());
        assertEquals(5, result.getOpenIncidents());
        assertEquals(8, result.getTotalReparations());
        assertEquals(BigDecimal.valueOf(1000), result.getTotalCost());

        // Vérifier que les variations sont à 0
        assertEquals(0.0, result.getTotalIncidentsChange(), 0.01);
        assertEquals(0.0, result.getOpenIncidentsChange(), 0.01);
        assertEquals(0.0, result.getTotalReparationsChange(), 0.01);
        assertEquals(0.0, result.getTotalCostChange(), 0.01);

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getDashboardSummary(debut, fin, vehiculeId);
        verify(dashboardRepository).getDashboardSummary(previousPeriodStart, previousPeriodEnd, vehiculeId);
    }

    @Test
    void getDashboardSummary_GlobalException() {
        // Arrange
        DashboardSummaryDTO mockDTO = mock(DashboardSummaryDTO.class);
        when(dashboardRepository.getDashboardSummary(debut, fin, vehiculeId)).thenReturn(mockDTO);
        doThrow(new RuntimeException("Test exception")).when(mockDTO).ensureNonNullValues();

        // Act
        DashboardSummaryDTO result = dashboardService.getDashboardSummary(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getTotalIncidents());
        assertEquals(0, result.getOpenIncidents());
        assertEquals(0, result.getTotalReparations());
        assertEquals(BigDecimal.ZERO, result.getTotalCost());
        assertEquals(0.0, result.getTotalIncidentsChange(), 0.01);

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getDashboardSummary(debut, fin, vehiculeId);
    }

    @Test
    void getIncidentStatusDistribution_Success() {
        // Arrange
        IncidentStatusDistributionDTO expectedDTO = new IncidentStatusDistributionDTO(5, 3, 2);
        when(dashboardRepository.getIncidentStatusDistribution(debut, fin, vehiculeId)).thenReturn(expectedDTO);

        // Act
        IncidentStatusDistributionDTO result = dashboardService.getIncidentStatusDistribution(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(5, result.getOpenIncidents());
        assertEquals(3, result.getInProgressIncidents());
        assertEquals(2, result.getResolvedIncidents());

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getIncidentStatusDistribution(debut, fin, vehiculeId);
    }

    @Test
    void getIncidentTypeDistribution_Success() {
        // Arrange
        List<Object[]> mockResults = Arrays.asList(
                new Object[]{TypeIncident.ACCIDENT, 5L},
                new Object[]{TypeIncident.PANNE, 3L}
        );
        when(dashboardRepository.getIncidentTypeDistribution(debut, fin, vehiculeId)).thenReturn(mockResults);

        // Act
        IncidentTypeDistributionDTO result = dashboardService.getIncidentTypeDistribution(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getCountByType().size());
        assertEquals(5L, result.getCountByType().get("ACCIDENT"));
        assertEquals(3L, result.getCountByType().get("PANNE"));

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getIncidentTypeDistribution(debut, fin, vehiculeId);
    }

    @Test
    void getVehicleIncidentDistribution_Success() {
        // Arrange
        List<Object[]> mockResults = Arrays.asList(
                new Object[]{1L, 5L},
                new Object[]{2L, 3L}
        );
        when(dashboardRepository.getIncidentCountByVehicle(debut, fin, vehiculeId)).thenReturn(mockResults);

        List<VehiculeDto> mockVehicules = new ArrayList<>();
        VehiculeDto vehicule1 = new VehiculeDto();
        vehicule1.setIdVehicule(1L);
        vehicule1.setImmatriculation("ABC123");

        VehiculeDto vehicule2 = new VehiculeDto();
        vehicule2.setIdVehicule(2L);
        vehicule2.setImmatriculation("DEF456");

        mockVehicules.add(vehicule1);
        mockVehicules.add(vehicule2);
        when(vehiculeClient.getAllVehicules()).thenReturn(mockVehicules);

        // Act
        VehicleIncidentDistributionDTO result = dashboardService.getVehicleIncidentDistribution(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getCountByVehicle().size());
        assertEquals(5L, result.getCountByVehicle().get(1L));
        assertEquals(3L, result.getCountByVehicle().get(2L));
        assertEquals("ABC123", result.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", result.getVehicleImmatriculations().get(2L));

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getIncidentCountByVehicle(debut, fin, vehiculeId);
        verify(vehiculeClient).getAllVehicules();
    }

    @Test
    void getVehicleIncidentDistribution_VehiculeClientException() {
        // Arrange
        List<Object[]> mockResults = Arrays.asList(
                new Object[]{1L, 5L},
                new Object[]{2L, 3L}
        );
        when(dashboardRepository.getIncidentCountByVehicle(debut, fin, vehiculeId)).thenReturn(mockResults);
        when(vehiculeClient.getAllVehicules()).thenThrow(new RuntimeException("Test exception"));

        // Act
        VehicleIncidentDistributionDTO result = dashboardService.getVehicleIncidentDistribution(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getCountByVehicle().size());
        assertEquals(5L, result.getCountByVehicle().get(1L));
        assertEquals(3L, result.getCountByVehicle().get(2L));
        assertEquals("123TU4567", result.getVehicleImmatriculations().get(1L));
        assertEquals("123TU4567", result.getVehicleImmatriculations().get(2L));

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getIncidentCountByVehicle(debut, fin, vehiculeId);
        verify(vehiculeClient).getAllVehicules();
    }

    static Stream<Arguments> percentageChangeTestCases() {
        return Stream.of(
            // oldValue, newValue, expectedResult
            Arguments.of(100.0, 150.0, 50.0),      // Normal case
            Arguments.of(0.0, 150.0, 100.0),       // Old value zero
            Arguments.of(0.0, 0.0, 0.0),           // Both values zero
            Arguments.of(100.0, 50.0, -50.0)       // Decrease
        );
    }

    @ParameterizedTest
    @MethodSource("percentageChangeTestCases")
    void calculatePercentageChange_VariousScenarios(double oldValue, double newValue, double expectedResult) {
        // Act
        double result = invokeCalculatePercentageChange(oldValue, newValue);

        // Assert
        assertEquals(expectedResult, result, 0.01);
    }

    @Test
    void calculatePercentageChange_ExtremeIncrease() {
        // Arrange
        double oldValue = 1.0;
        double newValue = 1000000.0;

        // Act
        double result = invokeCalculatePercentageChange(oldValue, newValue);

        // Assert
        assertEquals(1000.0, result, 0.01); // Limité à 1000%
    }

    @Test
    void calculatePercentageChange_ExtremeDecrease() {
        // Arrange
        double oldValue = 1000000.0;
        double newValue = 1.0;

        // Act
        double result = invokeCalculatePercentageChange(oldValue, newValue);

        // Assert
        assertEquals(-99.9999, result, 0.01); // Presque -100%
    }

    @Test
    void calculatePercentageChange_Exception() {
        // Arrange
        double oldValue = Double.NaN;
        double newValue = 150.0;

        // Act
        double result = invokeCalculatePercentageChange(oldValue, newValue);

        // Assert
        assertEquals(0.0, result, 0.01);
    }

    // Méthode utilitaire pour invoquer la méthode privée calculatePercentageChange
    private double invokeCalculatePercentageChange(double oldValue, double newValue) {
        try {
            java.lang.reflect.Method method = DashboardServiceImpl.class.getDeclaredMethod("calculatePercentageChange", double.class, double.class);
            method.setAccessible(true);
            return (double) method.invoke(dashboardService, oldValue, newValue);
        } catch (Exception e) {
            fail("Failed to invoke calculatePercentageChange method: " + e.getMessage());
            return 0.0;
        }
    }
}
