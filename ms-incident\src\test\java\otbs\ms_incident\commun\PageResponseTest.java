package otbs.ms_incident.commun;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class PageResponseTest {

    @Test
    void noArgsConstructor_shouldCreateEmptyPageResponse() {
        // When
        PageResponse<String> response = new PageResponse<>();

        // Then
        assertNull(response.getContent());
        assertEquals(0, response.getNumber());
        assertEquals(0, response.getSize());
        assertEquals(0, response.getTotalElements());
        assertEquals(0, response.getTotalPages());
        assertFalse(response.isFirst());
        assertFalse(response.isLast());
        assertFalse(response.isEmpty());
    }

    @Test
    void allArgsConstructor_shouldCreatePopulatedPageResponse() {
        // Given
        List<String> content = Arrays.asList("Item1", "Item2");
        int number = 1;
        int size = 10;
        long totalElements = 15;
        int totalPages = 2;
        boolean first = false;
        boolean last = false;
        boolean empty = false;

        // When
        PageResponse<String> response = new PageResponse<>(content, number, size, totalElements, totalPages, first, last, empty);

        // Then
        assertEquals(content, response.getContent());
        assertEquals(number, response.getNumber());
        assertEquals(size, response.getSize());
        assertEquals(totalElements, response.getTotalElements());
        assertEquals(totalPages, response.getTotalPages());
        assertEquals(first, response.isFirst());
        assertEquals(last, response.isLast());
        assertEquals(empty, response.isEmpty());
    }

    @Test
    void settersAndGetters_shouldWorkCorrectly() {
        // Given
        PageResponse<Integer> response = new PageResponse<>();
        List<Integer> content = Arrays.asList(1, 2, 3);
        int number = 2;
        int size = 5;
        long totalElements = 20;
        int totalPages = 4;
        boolean first = false;
        boolean last = true;
        boolean empty = false;

        // When
        response.setContent(content);
        response.setNumber(number);
        response.setSize(size);
        response.setTotalElements(totalElements);
        response.setTotalPages(totalPages);
        response.setFirst(first);
        response.setLast(last);
        response.setEmpty(empty);

        // Then
        assertEquals(content, response.getContent());
        assertEquals(number, response.getNumber());
        assertEquals(size, response.getSize());
        assertEquals(totalElements, response.getTotalElements());
        assertEquals(totalPages, response.getTotalPages());
        assertEquals(first, response.isFirst());
        assertEquals(last, response.isLast());
        assertEquals(empty, response.isEmpty());
    }

    @Test
    void equalsAndHashCode_shouldWorkCorrectly() {
        // Given
        List<String> content = Arrays.asList("Item1", "Item2");
        PageResponse<String> response1 = new PageResponse<>(content, 1, 10, 15, 2, false, false, false);
        PageResponse<String> response2 = new PageResponse<>(content, 1, 10, 15, 2, false, false, false);
        PageResponse<String> response3 = new PageResponse<>(content, 2, 10, 15, 2, false, false, false);

        // Then
        assertEquals(response1, response2);
        assertNotEquals(response1, response3);
        assertEquals(response1.hashCode(), response2.hashCode());
        assertNotEquals(response1.hashCode(), response3.hashCode());
    }
}
