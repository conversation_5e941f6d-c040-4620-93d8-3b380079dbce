package otbs.ms_astreint.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import otbs.ms_astreint.model.ConsultantAstreinte;
import otbs.ms_astreint.model.NiveauAstreinte;

import java.util.List;

@Repository
public interface ConsultantAstreinteRepository extends JpaRepository<ConsultantAstreinte, Long> {
    List<ConsultantAstreinte> findByAstreinte_IdAstreinte(Long astreinteId);
    List<ConsultantAstreinte> findByNiveauAstreinte(NiveauAstreinte niveau);
} 