package otbs.ms_incident.dto.dashboard;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test for DashboardSummaryDTO.IncidentStats
 */
class DashboardSummaryDTOIncidentStatsTest {

    @Test
    void testBuilder_withAllFields() {
        // Given
        long totalIncidents = 100;
        long openIncidents = 30;
        long inProgressIncidents = 40;
        long resolvedIncidents = 30;
        long highPriorityIncidents = 20;

        // When
        DashboardSummaryDTO.IncidentStats stats = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(totalIncidents)
                .openIncidents(openIncidents)
                .inProgressIncidents(inProgressIncidents)
                .resolvedIncidents(resolvedIncidents)
                .highPriorityIncidents(highPriorityIncidents)
                .build();

        // Then
        assertEquals(totalIncidents, stats.getTotalIncidents());
        assertEquals(openIncidents, stats.getOpenIncidents());
        assertEquals(inProgressIncidents, stats.getInProgressIncidents());
        assertEquals(resolvedIncidents, stats.getResolvedIncidents());
        assertEquals(highPriorityIncidents, stats.getHighPriorityIncidents());
    }

    @Test
    void testBuilder_withMinimalFields() {
        // When
        DashboardSummaryDTO.IncidentStats stats = DashboardSummaryDTO.IncidentStats.builder()
                .build();

        // Then
        assertEquals(0, stats.getTotalIncidents());
        assertEquals(0, stats.getOpenIncidents());
        assertEquals(0, stats.getInProgressIncidents());
        assertEquals(0, stats.getResolvedIncidents());
        assertEquals(0, stats.getHighPriorityIncidents());
    }

    @Test
    void testBuilder_withPartialFields() {
        // Given
        long totalIncidents = 100;
        long openIncidents = 30;

        // When
        DashboardSummaryDTO.IncidentStats stats = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(totalIncidents)
                .openIncidents(openIncidents)
                .build();

        // Then
        assertEquals(totalIncidents, stats.getTotalIncidents());
        assertEquals(openIncidents, stats.getOpenIncidents());
        assertEquals(0, stats.getInProgressIncidents());
        assertEquals(0, stats.getResolvedIncidents());
        assertEquals(0, stats.getHighPriorityIncidents());
    }

    @Test
    void testToString_shouldNotThrowException() {
        // Given
        DashboardSummaryDTO.IncidentStats stats = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(100)
                .openIncidents(30)
                .inProgressIncidents(40)
                .resolvedIncidents(30)
                .build();

        // When & Then
        assertDoesNotThrow(() -> {
            String result = stats.toString();
            assertNotNull(result);
            assertFalse(result.isEmpty());
        });
    }

    @Test
    void testEquals_withSameObject() {
        // Given
        DashboardSummaryDTO.IncidentStats stats = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(100)
                .openIncidents(30)
                .inProgressIncidents(40)
                .resolvedIncidents(30)
                .build();

        // When & Then
        assertEquals(stats, stats);
    }

    @Test
    void testEquals_withEqualObject() {
        // Given
        DashboardSummaryDTO.IncidentStats stats1 = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(100)
                .openIncidents(30)
                .inProgressIncidents(40)
                .resolvedIncidents(30)
                .build();

        DashboardSummaryDTO.IncidentStats stats2 = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(100)
                .openIncidents(30)
                .inProgressIncidents(40)
                .resolvedIncidents(30)
                .build();

        // When & Then
        assertEquals(stats1, stats2);
        assertEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testEquals_withDifferentObject() {
        // Given
        DashboardSummaryDTO.IncidentStats stats1 = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(100)
                .openIncidents(30)
                .inProgressIncidents(40)
                .resolvedIncidents(30)
                .build();

        DashboardSummaryDTO.IncidentStats stats2 = DashboardSummaryDTO.IncidentStats.builder()
                .totalIncidents(120)
                .openIncidents(40)
                .inProgressIncidents(50)
                .resolvedIncidents(30)
                .build();

        // When & Then
        assertNotEquals(stats1, stats2);
        assertNotEquals(stats1.hashCode(), stats2.hashCode());
    }

    @Test
    void testGettersAndSetters() {
        // Given
        DashboardSummaryDTO.IncidentStats stats = DashboardSummaryDTO.IncidentStats.builder().build();

        // When
        stats.setTotalIncidents(100);
        stats.setOpenIncidents(30);
        stats.setInProgressIncidents(40);
        stats.setResolvedIncidents(30);
        stats.setHighPriorityIncidents(20);

        // Then
        assertEquals(100, stats.getTotalIncidents());
        assertEquals(30, stats.getOpenIncidents());
        assertEquals(40, stats.getInProgressIncidents());
        assertEquals(30, stats.getResolvedIncidents());
        assertEquals(20, stats.getHighPriorityIncidents());
    }
}
