package otbs.ms_incident.enums;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

class FileTypeTest {

    @Test
    void testEnumValues() {
        // Vérifier que l'enum a exactement deux valeurs
        assertEquals(2, FileType.values().length);

        // Vérifier que les valeurs sont celles attendues
        assertEquals(FileType.PHOTO, FileType.valueOf("PHOTO"));
        assertEquals(FileType.CONSTAT, FileType.valueOf("CONSTAT"));
    }

    @Test
    void testFromStringValid() {
        // Tester les conversions valides
        assertEquals(FileType.PHOTO, FileType.fromString("PHOTO"));
        assertEquals(FileType.PHOTO, FileType.fromString("photo"));
        assertEquals(FileType.CONSTAT, FileType.fromString("CONSTAT"));
        assertEquals(FileType.CONSTAT, FileType.fromString("constat"));
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"invalid", "PHOTOS", "pdf", "123"})
    void testFromStringInvalid(String value) {
        // Tester les conversions invalides
        assertNull(FileType.fromString(value));
    }

    @Test
    void testGetStorageFolder() {
        // Vérifier que les dossiers de stockage sont corrects
        assertEquals("photos", FileType.PHOTO.getStorageFolder());
        assertEquals("constats", FileType.CONSTAT.getStorageFolder());
    }

    @ParameterizedTest
    @CsvSource({
        "jpg,PHOTO,true",
        "jpeg,PHOTO,true",
        "png,PHOTO,true",
        "gif,PHOTO,true",
        "pdf,PHOTO,false",
        "doc,PHOTO,false",
        "pdf,CONSTAT,true",
        "jpg,CONSTAT,false",
        "doc,CONSTAT,false"
    })
    void testIsValidExtension(String extension, FileType fileType, boolean expected) {
        // Vérifier la validation des extensions
        assertEquals(expected, fileType.isValidExtension(extension));
    }

    @Test
    void testConstants() {
        // Vérifier les constantes
        assertEquals("photos", FileType.PHOTOS_FOLDER);
        assertEquals("constats", FileType.CONSTATS_FOLDER);
    }

    @ParameterizedTest
    @NullAndEmptySource
    void testIsValidExtensionWithNullOrEmpty(String extension) {
        // Aucune extension ne devrait être valide quand elle est null ou vide
        assertFalse(FileType.PHOTO.isValidExtension(extension));
        assertFalse(FileType.CONSTAT.isValidExtension(extension));
    }

    @Test
    void testSwitchCases() {
        // Vérifier que tous les cas sont couverts dans le switch
        for (FileType type : FileType.values()) {
            // Vérifier que chaque type d'enum est soit PHOTO soit CONSTAT
            assertTrue(type == FileType.PHOTO || type == FileType.CONSTAT,
                    "Tous les types d'enum doivent être gérés dans le switch");
        }

        // Vérifier que les extensions sont correctement validées pour chaque type
        assertTrue(FileType.PHOTO.isValidExtension("jpg"));
        assertTrue(FileType.PHOTO.isValidExtension("jpeg"));
        assertTrue(FileType.PHOTO.isValidExtension("png"));
        assertTrue(FileType.PHOTO.isValidExtension("gif"));
        assertFalse(FileType.PHOTO.isValidExtension("pdf"));

        assertTrue(FileType.CONSTAT.isValidExtension("pdf"));
        assertFalse(FileType.CONSTAT.isValidExtension("jpg"));
        assertFalse(FileType.CONSTAT.isValidExtension("png"));
    }
}