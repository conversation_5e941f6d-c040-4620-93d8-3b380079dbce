package otbs.ms_incident.exception;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ErrorResponseTest {

    @Test
    void testErrorResponseBuilder() {
        // Arrange
        LocalDateTime timestamp = LocalDateTime.now();
        int status = 404;
        String error = "Not Found";
        String message = "Resource not found";
        String path = "/api/incidents/123";
        String code = "RESOURCE_NOT_FOUND";
        String traceId = "abc-123-xyz";
        
        // Act
        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(status)
                .error(error)
                .message(message)
                .path(path)
                .code(code)
                .traceId(traceId)
                .build();
        
        // Assert
        assertEquals(timestamp, errorResponse.getTimestamp());
        assertEquals(status, errorResponse.getStatus());
        assertEquals(error, errorResponse.getError());
        assertEquals(message, errorResponse.getMessage());
        assertEquals(path, errorResponse.getPath());
        assertEquals(code, errorResponse.getCode());
        assertEquals(traceId, errorResponse.getTraceId());
        assertNotNull(errorResponse.getFieldErrors());
        assertTrue(errorResponse.getFieldErrors().isEmpty());
    }
    
    @Test
    void testErrorResponseWithFieldErrors() {
        // Arrange
        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setTimestamp(LocalDateTime.now());
        errorResponse.setStatus(400);
        errorResponse.setError("Bad Request");
        errorResponse.setMessage("Validation failed");
        errorResponse.setPath("/api/incidents");
        
        ErrorResponse.ValidationError validationError1 = new ErrorResponse.ValidationError("title", "Title is required");
        ErrorResponse.ValidationError validationError2 = new ErrorResponse.ValidationError("description", "Description must not be empty");
        
        List<ErrorResponse.ValidationError> fieldErrors = new ArrayList<>();
        fieldErrors.add(validationError1);
        fieldErrors.add(validationError2);
        
        // Act
        errorResponse.setFieldErrors(fieldErrors);
        
        // Assert
        assertEquals(2, errorResponse.getFieldErrors().size());
        assertEquals("title", errorResponse.getFieldErrors().get(0).getField());
        assertEquals("Title is required", errorResponse.getFieldErrors().get(0).getMessage());
        assertEquals("description", errorResponse.getFieldErrors().get(1).getField());
        assertEquals("Description must not be empty", errorResponse.getFieldErrors().get(1).getMessage());
    }
    
    @Test
    void testValidationErrorBuilder() {
        // Arrange
        String field = "firstName";
        String message = "First name must not be empty";
        
        // Act
        ErrorResponse.ValidationError validationError = ErrorResponse.ValidationError.builder()
                .field(field)
                .message(message)
                .build();
        
        // Assert
        assertEquals(field, validationError.getField());
        assertEquals(message, validationError.getMessage());
    }
    
    @Test
    void testDefaultConstructor() {
        // Act
        ErrorResponse errorResponse = new ErrorResponse();
        
        // Assert
        assertNotNull(errorResponse);
        assertNotNull(errorResponse.getFieldErrors());
        assertTrue(errorResponse.getFieldErrors().isEmpty());
    }
    
    @Test
    void testAllArgsConstructor() {
        // Arrange
        LocalDateTime timestamp = LocalDateTime.now();
        int status = 500;
        String error = "Internal Server Error";
        String message = "An error occurred";
        String path = "/api/incidents";
        String code = "SERVER_ERROR";
        String traceId = "xyz-789";
        List<ErrorResponse.ValidationError> fieldErrors = new ArrayList<>();
        
        // Act
        ErrorResponse errorResponse = new ErrorResponse(timestamp, status, error, message, path, code, traceId, fieldErrors);
        
        // Assert
        assertEquals(timestamp, errorResponse.getTimestamp());
        assertEquals(status, errorResponse.getStatus());
        assertEquals(error, errorResponse.getError());
        assertEquals(message, errorResponse.getMessage());
        assertEquals(path, errorResponse.getPath());
        assertEquals(code, errorResponse.getCode());
        assertEquals(traceId, errorResponse.getTraceId());
        assertSame(fieldErrors, errorResponse.getFieldErrors());
    }
    
    @Test
    void testSettersAndGetters() {
        // Arrange
        ErrorResponse errorResponse = new ErrorResponse();
        LocalDateTime timestamp = LocalDateTime.now();
        int status = 400;
        String error = "Bad Request";
        String message = "Invalid input";
        String path = "/api/users";
        String code = "VALIDATION_ERROR";
        String traceId = "123-abc";
        
        // Act
        errorResponse.setTimestamp(timestamp);
        errorResponse.setStatus(status);
        errorResponse.setError(error);
        errorResponse.setMessage(message);
        errorResponse.setPath(path);
        errorResponse.setCode(code);
        errorResponse.setTraceId(traceId);
        
        // Assert
        assertEquals(timestamp, errorResponse.getTimestamp());
        assertEquals(status, errorResponse.getStatus());
        assertEquals(error, errorResponse.getError());
        assertEquals(message, errorResponse.getMessage());
        assertEquals(path, errorResponse.getPath());
        assertEquals(code, errorResponse.getCode());
        assertEquals(traceId, errorResponse.getTraceId());
    }
    
    @Test
    void testValidationErrorDefaultConstructor() {
        // Act
        ErrorResponse.ValidationError validationError = new ErrorResponse.ValidationError();
        
        // Assert
        assertNotNull(validationError);
        assertNull(validationError.getField());
        assertNull(validationError.getMessage());
    }
    
    @Test
    void testValidationErrorAllArgsConstructor() {
        // Arrange
        String field = "email";
        String message = "Must be a valid email";
        
        // Act
        ErrorResponse.ValidationError validationError = new ErrorResponse.ValidationError(field, message);
        
        // Assert
        assertEquals(field, validationError.getField());
        assertEquals(message, validationError.getMessage());
    }
    
    @Test
    void testValidationErrorSettersAndGetters() {
        // Arrange
        ErrorResponse.ValidationError validationError = new ErrorResponse.ValidationError();
        String field = "phoneNumber";
        String message = "Must be a valid phone number";
        
        // Act
        validationError.setField(field);
        validationError.setMessage(message);
        
        // Assert
        assertEquals(field, validationError.getField());
        assertEquals(message, validationError.getMessage());
    }
    
    @Test
    void testEqualsAndHashCode() {
        // Arrange
        LocalDateTime timestamp = LocalDateTime.now();
        ErrorResponse response1 = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(404)
                .error("Not Found")
                .message("Resource not found")
                .path("/api/test")
                .code("NOT_FOUND")
                .traceId("123")
                .build();
        
        ErrorResponse response2 = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(404)
                .error("Not Found")
                .message("Resource not found")
                .path("/api/test")
                .code("NOT_FOUND")
                .traceId("123")
                .build();
        
        ErrorResponse response3 = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(500)
                .error("Server Error")
                .message("Internal error")
                .path("/api/other")
                .code("SERVER_ERROR")
                .traceId("456")
                .build();
        
        // Assert
        assertEquals(response1, response2);
        assertEquals(response1.hashCode(), response2.hashCode());
        assertNotEquals(response1, response3);
        assertNotEquals(response1.hashCode(), response3.hashCode());
    }
    
    @Test
    void testValidationErrorEqualsAndHashCode() {
        // Arrange
        ErrorResponse.ValidationError error1 = new ErrorResponse.ValidationError("name", "Name is required");
        ErrorResponse.ValidationError error2 = new ErrorResponse.ValidationError("name", "Name is required");
        ErrorResponse.ValidationError error3 = new ErrorResponse.ValidationError("age", "Age must be positive");
        
        // Assert
        assertEquals(error1, error2);
        assertEquals(error1.hashCode(), error2.hashCode());
        assertNotEquals(error1, error3);
        assertNotEquals(error1.hashCode(), error3.hashCode());
    }
    
    @Test
    void testToString() {
        // Arrange
        LocalDateTime timestamp = LocalDateTime.now();
        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(400)
                .error("Bad Request")
                .message("Invalid input")
                .path("/api/incidents")
                .code("VALIDATION_ERROR")
                .traceId("abc123")
                .build();
        
        // Act
        String toString = errorResponse.toString();
        
        // Assert
        assertTrue(toString.contains("timestamp=" + timestamp.toString()));
        assertTrue(toString.contains("status=400"));
        assertTrue(toString.contains("error=Bad Request"));
        assertTrue(toString.contains("message=Invalid input"));
        assertTrue(toString.contains("path=/api/incidents"));
        assertTrue(toString.contains("code=VALIDATION_ERROR"));
        assertTrue(toString.contains("traceId=abc123"));
    }
    
    @Test
    void testValidationErrorToString() {
        // Arrange
        ErrorResponse.ValidationError validationError = new ErrorResponse.ValidationError("email", "Invalid email format");
        
        // Act
        String toString = validationError.toString();
        
        // Assert
        assertTrue(toString.contains("field=email"));
        assertTrue(toString.contains("message=Invalid email format"));
    }
    
    @Test
    void testEqualsWithNull() {
        // Arrange
        ErrorResponse response = ErrorResponse.builder()
                .status(404)
                .error("Not Found")
                .message("Resource not found")
                .build();
        
        // Assert
        assertNotEquals(null,response );
    }
    
    @Test
    void testEqualsWithDifferentClass() {
        // Arrange
        ErrorResponse response = ErrorResponse.builder()
                .status(404)
                .error("Not Found")
                .message("Resource not found")
                .build();
        
        // Assert
        assertNotEquals("Not an ErrorResponse",response );
    }
    
    @Test
    void testEqualsWithDifferentFieldValues() {
        // Arrange
        LocalDateTime timestamp = LocalDateTime.now();
        ErrorResponse base = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(404)
                .error("Not Found")
                .message("Resource not found")
                .path("/api/test")
                .code("NOT_FOUND")
                .traceId("123")
                .build();
        
        ErrorResponse differentTimestamp = ErrorResponse.builder()
                .timestamp(timestamp.plusDays(1))
                .status(404)
                .error("Not Found")
                .message("Resource not found")
                .path("/api/test")
                .code("NOT_FOUND")
                .traceId("123")
                .build();
        
        ErrorResponse differentStatus = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(500)
                .error("Not Found")
                .message("Resource not found")
                .path("/api/test")
                .code("NOT_FOUND")
                .traceId("123")
                .build();
        
        ErrorResponse differentError = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(404)
                .error("Server Error")
                .message("Resource not found")
                .path("/api/test")
                .code("NOT_FOUND")
                .traceId("123")
                .build();
        
        ErrorResponse differentMessage = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(404)
                .error("Not Found")
                .message("Different message")
                .path("/api/test")
                .code("NOT_FOUND")
                .traceId("123")
                .build();
        
        ErrorResponse differentPath = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(404)
                .error("Not Found")
                .message("Resource not found")
                .path("/api/different")
                .code("NOT_FOUND")
                .traceId("123")
                .build();
        
        ErrorResponse differentCode = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(404)
                .error("Not Found")
                .message("Resource not found")
                .path("/api/test")
                .code("DIFFERENT_CODE")
                .traceId("123")
                .build();
        
        ErrorResponse differentTraceId = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(404)
                .error("Not Found")
                .message("Resource not found")
                .path("/api/test")
                .code("NOT_FOUND")
                .traceId("different")
                .build();
        
        ErrorResponse differentFieldErrors = ErrorResponse.builder()
                .timestamp(timestamp)
                .status(404)
                .error("Not Found")
                .message("Resource not found")
                .path("/api/test")
                .code("NOT_FOUND")
                .traceId("123")
                .fieldErrors(List.of(new ErrorResponse.ValidationError("field", "message")))
                .build();
        
        // Assert
        assertNotEquals(base, differentTimestamp);
        assertNotEquals(base, differentStatus);
        assertNotEquals(base, differentError);
        assertNotEquals(base, differentMessage);
        assertNotEquals(base, differentPath);
        assertNotEquals(base, differentCode);
        assertNotEquals(base, differentTraceId);
        assertNotEquals(base, differentFieldErrors);
    }
    
    @Test
    void testValidationErrorEqualsWithNull() {
        // Arrange
        ErrorResponse.ValidationError error = new ErrorResponse.ValidationError("field", "message");
        
        // Assert
        assertNotEquals(null,error );
    }
    
    @Test
    void testValidationErrorEqualsWithDifferentClass() {
        // Arrange
        ErrorResponse.ValidationError error = new ErrorResponse.ValidationError("field", "message");
        
        // Assert
        assertNotEquals("Not a ValidationError",error );
    }
    
    @Test
    void testValidationErrorEqualsWithDifferentFieldValues() {
        // Arrange
        ErrorResponse.ValidationError base = new ErrorResponse.ValidationError("field", "message");
        ErrorResponse.ValidationError differentField = new ErrorResponse.ValidationError("different", "message");
        ErrorResponse.ValidationError differentMessage = new ErrorResponse.ValidationError("field", "different");
        
        // Assert
        assertNotEquals(base, differentField);
        assertNotEquals(base, differentMessage);
    }
} 