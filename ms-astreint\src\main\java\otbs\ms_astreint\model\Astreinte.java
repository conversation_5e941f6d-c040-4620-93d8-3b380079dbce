package otbs.ms_astreint.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "astreintes")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Astreinte extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_astreinte")
    private Long idAstreinte;

    @NotNull(message = "La date de début est obligatoire")
    @Column(name = "date_debut", nullable = false)
    private LocalDateTime dateDebut;

    @NotNull(message = "La date de fin est obligatoire")
    @Column(name = "date_fin", nullable = false)
    private LocalDateTime dateFin;

    @Column(name = "description", length = 500)
    private String description;

    @OneToMany(mappedBy = "astreinte", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ConsultantAstreinte> consultants = new ArrayList<>();

    public Astreinte(LocalDateTime dateDebut, LocalDateTime dateFin) {
        this.dateDebut = dateDebut;
        this.dateFin = dateFin;
        this.consultants = new ArrayList<>();
    }

    public Astreinte(LocalDateTime dateDebut, LocalDateTime dateFin, String description) {
        this(dateDebut, dateFin);
        this.description = description;
    }

    public void ajouterConsultant(String consultant, NiveauAstreinte niveauAstreinte) {
        ConsultantAstreinte consultantAstreinte = new ConsultantAstreinte(this, consultant, niveauAstreinte);
        this.consultants.add(consultantAstreinte);
    }

    public String getConsultantPourNiveau(NiveauAstreinte niveau) {
        return this.consultants.stream()
                .filter(c -> c.getNiveauAstreinte() == niveau)
                .map(ConsultantAstreinte::getConsultant)
                .findFirst()
                .orElse(null);
    }

    @Override
    public String toString() {
        return "Astreinte{" +
                "idAstreinte=" + idAstreinte +
                ", dateDebut=" + dateDebut +
                ", dateFin=" + dateFin +
                ", consultantsCount=" + (consultants != null ? consultants.size() : 0) +
                '}';
    }
}
