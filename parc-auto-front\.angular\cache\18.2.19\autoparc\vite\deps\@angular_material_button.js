import {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>utt<PERSON>,
  MatButtonModule,
  MatFabAnchor,
  <PERSON><PERSON><PERSON>Button,
  <PERSON><PERSON>conAnch<PERSON>,
  <PERSON><PERSON><PERSON>Butt<PERSON>,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
} from "./chunk-QR2A2EMK.js";
import "./chunk-YQKFE6SP.js";
import "./chunk-U2YTM3FN.js";
import "./chunk-L57JFFAX.js";
import "./chunk-WPM5VTLQ.js";
import "./chunk-PEBH6BBU.js";
import "./chunk-4S3KYZTJ.js";
import "./chunk-TF6TRJEI.js";
export {
  MAT_BUTTON_CONFIG,
  MAT_FAB_DEFAULT_OPTIONS,
  MAT_FAB_DEFAULT_OPTIONS_FACTORY,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ButtonModule,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>I<PERSON>Anch<PERSON>,
  <PERSON><PERSON><PERSON>B<PERSON><PERSON>,
  Mat<PERSON>iniFabAnchor,
  MatMiniFabButton
};
//# sourceMappingURL=@angular_material_button.js.map
