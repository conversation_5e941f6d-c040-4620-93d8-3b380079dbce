# Server configuration
spring.application.name=ms-notification
server.port=9004



# Configuration de la DB
spring.datasource.url=${DB_URL:*********************************************}
spring.datasource.username=${DB_USERNAME:parcauto}
spring.datasource.password=${DB_PASSWORD:parcauto}
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Hikari Connection Pool
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=30000



# Eureka client configuration
eureka.client.service-url.defaultZone=${Eureka-url:http://*************:9102/eureka}
eureka.client.enabled=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true

# Logging configuration
logging.level.root=INFO
logging.level.otbs.ms_notification=DEBUG
logging.level.com.netflix.discovery=DEBUG
logging.level.com.netflix.eureka=DEBUG
logging.level.org.springframework.security=DEBUG

# Actuator configuration
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always

# Configuration OAuth2 - Resource Server (JWT)
spring.security.oauth2.resourceserver.jwt.issuer-uri=${ISSUER_URI:http://10.112.62.189:8080/realms/parc-auto}
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${JWK_SET_URI:${spring.security.oauth2.resourceserver.jwt.issuer-uri}/protocol/openid-connect/certs}

# Configuration OAuth2 - Client Registration pour ms-notification
spring.security.oauth2.client.registration.keycloak.client-id=${CLIENT_ID:ms-notification-client}
spring.security.oauth2.client.registration.keycloak.authorization-grant-type=${AUTH_GRANT_TYPE:client_credentials}
spring.security.oauth2.client.registration.keycloak.scope=${SCOPE:openid}

# Configuration OAuth2 - Provider (Keycloak)
spring.security.oauth2.client.provider.keycloak.issuer-uri=${PROVIDER_URI:${spring.security.oauth2.resourceserver.jwt.issuer-uri}}
spring.security.oauth2.client.provider.keycloak.user-name-attribute=${USERNAME_ATTRIBUTE:preferred_username}



spring.security.oauth2.client.registration.keycloak.client-secret=${CLIENT_SECRET:kXo0aXujOuWKdGYvqLKHkX95hTqt4ts2}


# Configuration de Feign pour activer les fallbacks
feign.circuitbreaker.enabled=true
feign.client.config.default.connectTimeout=5000
feign.client.config.default.readTimeout=5000

# Configuration RabbitMQ 
spring.rabbitmq.host=${RABBITMQ_HOST:*************}
spring.rabbitmq.port=${RABBITMQ_PORT:5672}
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}
spring.rabbitmq.virtual-host=${RABBITMQ_VHOST:/}

# Configuration de connexion
spring.rabbitmq.connection-timeout=${RABBITMQ_CONNECTION_TIMEOUT:10000}
spring.rabbitmq.requested-heartbeat=${RABBITMQ_HEARTBEAT:30}

# Configuration du consumer
spring.rabbitmq.listener.simple.acknowledge-mode=manual
spring.rabbitmq.listener.simple.prefetch=10
spring.rabbitmq.listener.simple.retry.enabled=true
spring.rabbitmq.listener.simple.retry.initial-interval=1000
spring.rabbitmq.listener.simple.retry.max-attempts=3

spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:<EMAIL>}
spring.mail.password=${MAIL_PASSWORD:jgmp ojdn foaz jqzx}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000
spring.mail.default-encoding=UTF-8

# Configuration des emails de notification
notification.email.sender=${MAIL_SENDER:<EMAIL>}
notification.email.enabled=${MAIL_ENABLED:true}
notification.email.subject-prefix=[Parc Auto]


# Cache Configuration
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=1000,expireAfterWrite=3600s

# Configuration des destinataires de notifications - INCIDENTS
notification.incident.created.roles=RESPONSABLE_SERVICE_GENERAUX,PROJECT_MANAGER
notification.incident.resolved.roles=RESPONSABLE_SERVICE_GENERAUX

# Configuration des destinataires de notifications - MISSIONS
notification.mission.created.roles=PROJECT_MANAGER,RESPONSABLE_BUREAU_ORDRE
notification.mission.updated.roles=PROJECT_MANAGER,RESPONSABLE_BUREAU_ORDRE
notification.mission.assigned.roles=PROJECT_MANAGER,RESPONSABLE_BUREAU_ORDRE
notification.mission.completed.roles=PROJECT_MANAGER,RESPONSABLE_BUREAU_ORDRE
notification.mission.accompanist-assigned.roles=PROJECT_MANAGER,RESPONSABLE_BUREAU_ORDRE

# Configuration des alias de rôles pour gérer les incohérences
notification.role-aliases.RESPONSABLE_SERVICES_GENERAUX=RESPONSABLE_SERVICE_GENERAUX

# Activer la notification du supérieur hiérarchique
notification.hierarchy.enabled=true
notification.hierarchy.CONSULTANT=DIRECTEUR_TECHNIQUE
notification.hierarchy.events=MISSION_CREATED