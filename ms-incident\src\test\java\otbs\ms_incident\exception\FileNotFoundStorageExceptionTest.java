package otbs.ms_incident.exception;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import static org.junit.jupiter.api.Assertions.*;

class FileNotFoundStorageExceptionTest {

    @Test
    void testConstructorWithMessage() {
        // Arrange
        String message = "File not found";
        
        // Act
        FileNotFoundStorageException exception = new FileNotFoundStorageException(message);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("FILE_NOT_FOUND", exception.getErrorCode());
    }
    
    @Test
    void testConstructorWithMessageAndCause() {
        // Arrange
        String message = "File not found";
        Throwable cause = new RuntimeException("Root cause");
        
        // Act
        FileNotFoundStorageException exception = new FileNotFoundStorageException(message, cause);
        
        // Assert
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertEquals(HttpStatus.NOT_FOUND, exception.getStatus());
        assertEquals("FILE_NOT_FOUND", exception.getErrorCode());
    }
} 