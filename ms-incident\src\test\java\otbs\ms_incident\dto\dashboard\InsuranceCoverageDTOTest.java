package otbs.ms_incident.dto.dashboard;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class InsuranceCoverageDTOTest {

    @Test
    void testNoArgsConstructor() {
        // When
        InsuranceCoverageDTO dto = new InsuranceCoverageDTO();

        // Then
        assertNull(dto.getInsuranceCoverageByVehicle());
        assertNull(dto.getVehicleImmatriculations());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        vehicle1Coverage.put("COVERED_AMOUNT", BigDecimal.valueOf(800));
        Map<String, BigDecimal> vehicle2Coverage = new HashMap<>();
        vehicle2Coverage.put("TOTAL_COST", BigDecimal.valueOf(1500));
        vehicle2Coverage.put("COVERED_AMOUNT", BigDecimal.valueOf(1200));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);
        insuranceCoverageByVehicle.put(2L, vehicle2Coverage);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        // When
        InsuranceCoverageDTO dto = new InsuranceCoverageDTO(insuranceCoverageByVehicle, vehicleImmatriculations);

        // Then
        assertEquals(insuranceCoverageByVehicle, dto.getInsuranceCoverageByVehicle());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
        assertEquals(BigDecimal.valueOf(1000), dto.getInsuranceCoverageByVehicle().get(1L).get("TOTAL_COST"));
        assertEquals(BigDecimal.valueOf(800), dto.getInsuranceCoverageByVehicle().get(1L).get("COVERED_AMOUNT"));
        assertEquals(BigDecimal.valueOf(1500), dto.getInsuranceCoverageByVehicle().get(2L).get("TOTAL_COST"));
        assertEquals(BigDecimal.valueOf(1200), dto.getInsuranceCoverageByVehicle().get(2L).get("COVERED_AMOUNT"));
        assertEquals("ABC123", dto.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", dto.getVehicleImmatriculations().get(2L));
    }

    @Test
    void testBuilder() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        vehicle1Coverage.put("COVERED_AMOUNT", BigDecimal.valueOf(800));
        Map<String, BigDecimal> vehicle2Coverage = new HashMap<>();
        vehicle2Coverage.put("TOTAL_COST", BigDecimal.valueOf(1500));
        vehicle2Coverage.put("COVERED_AMOUNT", BigDecimal.valueOf(1200));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);
        insuranceCoverageByVehicle.put(2L, vehicle2Coverage);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        // When
        InsuranceCoverageDTO dto = InsuranceCoverageDTO.builder()
                .insuranceCoverageByVehicle(insuranceCoverageByVehicle)
                .vehicleImmatriculations(vehicleImmatriculations)
                .build();

        // Then
        assertEquals(insuranceCoverageByVehicle, dto.getInsuranceCoverageByVehicle());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
        assertEquals(BigDecimal.valueOf(1000), dto.getInsuranceCoverageByVehicle().get(1L).get("TOTAL_COST"));
        assertEquals(BigDecimal.valueOf(800), dto.getInsuranceCoverageByVehicle().get(1L).get("COVERED_AMOUNT"));
        assertEquals(BigDecimal.valueOf(1500), dto.getInsuranceCoverageByVehicle().get(2L).get("TOTAL_COST"));
        assertEquals(BigDecimal.valueOf(1200), dto.getInsuranceCoverageByVehicle().get(2L).get("COVERED_AMOUNT"));
        assertEquals("ABC123", dto.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", dto.getVehicleImmatriculations().get(2L));
    }

    @Test
    void testSettersAndGetters() {
        // Given
        InsuranceCoverageDTO dto = new InsuranceCoverageDTO();
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        vehicle1Coverage.put("COVERED_AMOUNT", BigDecimal.valueOf(800));
        Map<String, BigDecimal> vehicle2Coverage = new HashMap<>();
        vehicle2Coverage.put("TOTAL_COST", BigDecimal.valueOf(1500));
        vehicle2Coverage.put("COVERED_AMOUNT", BigDecimal.valueOf(1200));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);
        insuranceCoverageByVehicle.put(2L, vehicle2Coverage);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        // When
        dto.setInsuranceCoverageByVehicle(insuranceCoverageByVehicle);
        dto.setVehicleImmatriculations(vehicleImmatriculations);

        // Then
        assertEquals(insuranceCoverageByVehicle, dto.getInsuranceCoverageByVehicle());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
        assertEquals(BigDecimal.valueOf(1000), dto.getInsuranceCoverageByVehicle().get(1L).get("TOTAL_COST"));
        assertEquals(BigDecimal.valueOf(800), dto.getInsuranceCoverageByVehicle().get(1L).get("COVERED_AMOUNT"));
        assertEquals(BigDecimal.valueOf(1500), dto.getInsuranceCoverageByVehicle().get(2L).get("TOTAL_COST"));
        assertEquals(BigDecimal.valueOf(1200), dto.getInsuranceCoverageByVehicle().get(2L).get("COVERED_AMOUNT"));
        assertEquals("ABC123", dto.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", dto.getVehicleImmatriculations().get(2L));
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle1 = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage1 = new HashMap<>();
        vehicle1Coverage1.put("TOTAL_COST", BigDecimal.valueOf(1000));
        vehicle1Coverage1.put("COVERED_AMOUNT", BigDecimal.valueOf(800));
        insuranceCoverageByVehicle1.put(1L, vehicle1Coverage1);

        Map<Long, String> vehicleImmatriculations1 = new HashMap<>();
        vehicleImmatriculations1.put(1L, "ABC123");

        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle2 = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage2 = new HashMap<>();
        vehicle1Coverage2.put("TOTAL_COST", BigDecimal.valueOf(1000));
        vehicle1Coverage2.put("COVERED_AMOUNT", BigDecimal.valueOf(800));
        insuranceCoverageByVehicle2.put(1L, vehicle1Coverage2);

        Map<Long, String> vehicleImmatriculations2 = new HashMap<>();
        vehicleImmatriculations2.put(1L, "ABC123");

        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle3 = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage3 = new HashMap<>();
        vehicle1Coverage3.put("TOTAL_COST", BigDecimal.valueOf(2000));
        vehicle1Coverage3.put("COVERED_AMOUNT", BigDecimal.valueOf(1600));
        insuranceCoverageByVehicle3.put(1L, vehicle1Coverage3);

        Map<Long, String> vehicleImmatriculations3 = new HashMap<>();
        vehicleImmatriculations3.put(1L, "GHI789");

        InsuranceCoverageDTO dto1 = new InsuranceCoverageDTO(insuranceCoverageByVehicle1, vehicleImmatriculations1);
        InsuranceCoverageDTO dto2 = new InsuranceCoverageDTO(insuranceCoverageByVehicle2, vehicleImmatriculations2);
        InsuranceCoverageDTO dto3 = new InsuranceCoverageDTO(insuranceCoverageByVehicle3, vehicleImmatriculations3);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testEqualsWithDifferentInsuranceCoverageByVehicle() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle1 = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage1 = new HashMap<>();
        vehicle1Coverage1.put("TOTAL_COST", BigDecimal.valueOf(1000));
        insuranceCoverageByVehicle1.put(1L, vehicle1Coverage1);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle2 = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage2 = new HashMap<>();
        vehicle1Coverage2.put("TOTAL_COST", BigDecimal.valueOf(2000));
        insuranceCoverageByVehicle2.put(1L, vehicle1Coverage2);

        InsuranceCoverageDTO dto1 = new InsuranceCoverageDTO(insuranceCoverageByVehicle1, vehicleImmatriculations);
        InsuranceCoverageDTO dto2 = new InsuranceCoverageDTO(insuranceCoverageByVehicle2, vehicleImmatriculations);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentVehicleImmatriculations() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);

        Map<Long, String> vehicleImmatriculations1 = new HashMap<>();
        vehicleImmatriculations1.put(1L, "ABC123");

        Map<Long, String> vehicleImmatriculations2 = new HashMap<>();
        vehicleImmatriculations2.put(1L, "DEF456");

        InsuranceCoverageDTO dto1 = new InsuranceCoverageDTO(insuranceCoverageByVehicle, vehicleImmatriculations1);
        InsuranceCoverageDTO dto2 = new InsuranceCoverageDTO(insuranceCoverageByVehicle, vehicleImmatriculations2);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithOneNullInsuranceCoverageByVehicle() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        InsuranceCoverageDTO dto1 = new InsuranceCoverageDTO(insuranceCoverageByVehicle, vehicleImmatriculations);
        InsuranceCoverageDTO dto2 = new InsuranceCoverageDTO(null, vehicleImmatriculations);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithOneNullVehicleImmatriculations() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        InsuranceCoverageDTO dto1 = new InsuranceCoverageDTO(insuranceCoverageByVehicle, vehicleImmatriculations);
        InsuranceCoverageDTO dto2 = new InsuranceCoverageDTO(insuranceCoverageByVehicle, null);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithBothNullMaps() {
        // Given
        InsuranceCoverageDTO dto1 = new InsuranceCoverageDTO(null, null);
        InsuranceCoverageDTO dto2 = new InsuranceCoverageDTO(null, null);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testHashCodeWithNullMaps() {
        // Given
        InsuranceCoverageDTO dto = new InsuranceCoverageDTO(null, null);

        // When & Then
        assertDoesNotThrow(dto::hashCode);
    }

    @Test
    void testToString() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        vehicle1Coverage.put("COVERED_AMOUNT", BigDecimal.valueOf(800));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        InsuranceCoverageDTO dto = new InsuranceCoverageDTO(insuranceCoverageByVehicle, vehicleImmatriculations);

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("insuranceCoverageByVehicle="));
        assertTrue(result.contains("vehicleImmatriculations="));
    }

    @Test
    void testEqualsWithSameObject() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        InsuranceCoverageDTO dto = new InsuranceCoverageDTO(insuranceCoverageByVehicle, vehicleImmatriculations);

        // Then
        assertEquals(dto, dto);
    }

    @Test
    void testEqualsWithNull() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        InsuranceCoverageDTO dto = new InsuranceCoverageDTO(insuranceCoverageByVehicle, vehicleImmatriculations);

        // Then
        assertNotEquals(null, dto);
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Given
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();
        Map<String, BigDecimal> vehicle1Coverage = new HashMap<>();
        vehicle1Coverage.put("TOTAL_COST", BigDecimal.valueOf(1000));
        insuranceCoverageByVehicle.put(1L, vehicle1Coverage);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        InsuranceCoverageDTO dto = new InsuranceCoverageDTO(insuranceCoverageByVehicle, vehicleImmatriculations);

        // Then
        assertNotEquals("Not a DTO", dto);
    }

    @Test
    void testBuilderWithNullMaps() {
        // When
        InsuranceCoverageDTO dto = InsuranceCoverageDTO.builder()
                .insuranceCoverageByVehicle(null)
                .vehicleImmatriculations(null)
                .build();

        // Then
        assertNull(dto.getInsuranceCoverageByVehicle());
        assertNull(dto.getVehicleImmatriculations());
    }
}
