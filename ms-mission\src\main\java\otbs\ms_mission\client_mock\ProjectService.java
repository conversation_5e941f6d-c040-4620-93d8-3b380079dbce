package otbs.ms_mission.client_mock;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import otbs.ms_mission.exception.ResourceNotFoundException;

import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * Service pour la gestion des projets.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ProjectService {

    private final ProjectRepository projectRepository;
    private final ProjectMapper projectMapper;

    /**
     * Récupère tous les projets.
     *
     * @return Liste de tous les projets
     */
    @Transactional(readOnly = true)
    public List<ProjectDTO> getAllProjects() {
        log.debug("Service request to get all projects");
        List<Project> projects = projectRepository.findAll();
        return projectMapper.toDtoList(projects);
    }

    /**
     * Récupère un projet par son ID.
     *
     * @param id L'ID du projet
     * @return Le projet correspondant
     * @throws ResourceNotFoundException si le projet n'existe pas
     */
    @Transactional(readOnly = true)
    public ProjectDTO getProjectById(Integer id) {
        log.debug("Service request to get Project by id: {}", id);
        return projectRepository.findById(id)
                .map(projectMapper::toDto)
                .orElseThrow(() -> new ResourceNotFoundException("Project", "id", id));
    }

    /**
     * Récupère une entité Project par son ID.
     *
     * @param id L'ID du projet
     * @return L'entité Project correspondante
     * @throws ResourceNotFoundException si le projet n'existe pas
     */
    @Transactional(readOnly = true)
    public Project getProjectEntityById(Integer id) {
        return projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project", "id", id));
    }

    /**
     * Récupère les projets filtrés par préfixes CT et/ou AF avec recherche optionnelle.
     *
     * @param prefixes Liste des préfixes à inclure ("CT", "AF", ou les deux)
     * @param searchTerm Chaîne de caractères à rechercher dans le nom (optionnel)
     * @return Liste des projets correspondants
     */
    @Transactional(readOnly = true)
    public List<ProjectDTO> getProjectsByPrefixes(List<String> prefixes, String searchTerm) {
        log.debug("Service request to get projects by prefixes: {} and search term: {}", prefixes, searchTerm);

        // Si aucun préfixe n'est spécifié mais qu'il y a un terme de recherche
        if ((prefixes == null || prefixes.isEmpty()) && searchTerm != null && !searchTerm.trim().isEmpty()) {
            List<Project> projects = projectRepository.findProjectsByNameContaining(searchTerm.trim());
            return projectMapper.toDtoList(projects);
        }

        // Si aucun préfixe et aucun terme de recherche
        if (prefixes == null || prefixes.isEmpty()) {
            return new ArrayList<>();
        }

        boolean includeCT = prefixes.contains("CT");
        boolean includeAF = prefixes.contains("AF");

        if (!includeCT && !includeAF) {
            return new ArrayList<>();
        }

        List<Project> projects;

        // Si un terme de recherche est fourni, utiliser la méthode combinée
        if (searchTerm != null && !searchTerm.trim().isEmpty()) {
            projects = projectRepository.findProjectsByPrefixesAndSearch(includeCT, includeAF, searchTerm.trim());
        } else {
            // Sinon, utiliser la méthode simple par préfixes
            projects = projectRepository.findProjectsByPrefixes(includeCT, includeAF);
        }

        return projectMapper.toDtoList(projects);
    }


}
