package otbs.ms_incident.entity;

import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "incidents")
public class Incident extends BaseEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(description = "Identifiant unique de l'incident", example = "1")
    private Long id;

    @Column(name = "vehicule_id")
    @Schema(description = "Identifiant du véhicule associé à cet incident", example = "1")
    private Long vehiculeId;

    @Schema(description = "Date à laquelle l'incident s'est produit", example = "2023-05-15")
    @Column(nullable = false)
    private LocalDate date;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Schema(description = "Type d'incident (ACCIDENT ou PANNE)")
    private TypeIncident type;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Schema(description = "Statut de l'incident")
    private StatusIncident status = StatusIncident.A_TRAITER;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    @Schema(description = "Niveau de priorité de l'incident")
    private NiveauPrioriteIncident priorite = NiveauPrioriteIncident.FAIBLE;

    @Schema(description = "Lieu où s'est produit l'incident", example = "Avenue des Champs-Élysées, Paris")
    @Column(length = 255, nullable = false)
    private String lieu;

    @Schema(description = "Chemin vers le fichier du constat d'accident ou rapport technique",
           example = "incidents/123/constats/constat_abc123.pdf")
    @Column(length = 512)
    private String constat;

    @ElementCollection
    @CollectionTable(
        name = "incident_photos",
        joinColumns = @JoinColumn(name = "incident_id", referencedColumnName = "id")
    )
    @Column(name = "photo_path", length = 512)
    @Schema(description = "Liste des chemins vers les photos de l'incident",
           example = "[\"incidents/123/photos/photo_abc123.jpg\"]")
    private List<String> photos = new ArrayList<>();

    @Column(length = 1000)
    @Schema(description = "Description détaillée de l'incident", example = "Collision latérale avec un autre véhicule")
    private String description;

    @OneToMany(mappedBy = "incident", cascade = CascadeType.ALL, orphanRemoval = true)
    @Schema(description = "Liste des réparations associées à cet incident")
    private List<Reparation> reparations = new ArrayList<>();

    /**
     * Vérifie si cet incident possède un constat
     * @return true si un constat est associé à l'incident
     */
    @Transient
    public boolean hasConstat() {
        return constat != null && !constat.trim().isEmpty();
    }

    /**
     * Vérifie si cet incident possède des photos
     * @return true si des photos sont associées à l'incident
     */
    @Transient
    public boolean hasPhotos() {
        return photos != null && !photos.isEmpty();
    }

    /**
     * Ajoute une photo à l'incident
     * @param photoPath chemin de la photo à ajouter
     */
    public void addPhoto(String photoPath) {
        if (photos == null) {
            photos = new ArrayList<>();
        }
        photos.add(photoPath);
    }

    /**
     * Supprime une photo de l'incident
     * @param photoPath chemin de la photo à supprimer
     * @return true si la photo a été trouvée et supprimée
     */
    public boolean removePhoto(String photoPath) {
        if (photos == null) {
            return false;
        }
        return photos.remove(photoPath);
    }
}