package otbs.ms_incident.config;

import org.junit.jupiter.api.Test;
import org.springframework.context.MessageSource;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

import static org.junit.jupiter.api.Assertions.*;

class MessageConfigTest {

    @Test
    void localeResolver_ShouldReturnAcceptHeaderLocaleResolver() {
        // Arrange
        MessageConfig config = new MessageConfig();
        
        // Act
        LocaleResolver resolver = config.localeResolver();
        
        // Assert
        assertNotNull(resolver, "LocaleResolver should not be null");
        assertTrue(resolver instanceof AcceptHeaderLocaleResolver, 
                "Should return an AcceptHeaderLocaleResolver");
    }
    
    @Test
    void messageSource_ShouldReturnResourceBundleMessageSource() {
        // Arrange
        MessageConfig config = new MessageConfig();
        
        // Act
        MessageSource source = config.messageSource();
        
        // Assert
        assertNotNull(source, "MessageSource should not be null");
        assertTrue(source instanceof ResourceBundleMessageSource, 
                "Should return a ResourceBundleMessageSource");
    }
} 