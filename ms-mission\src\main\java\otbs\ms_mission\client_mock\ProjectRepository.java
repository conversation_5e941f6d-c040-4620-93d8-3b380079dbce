package otbs.ms_mission.client_mock;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository pour l'entité Project avec méthodes optimisées.
 */
@Repository
public interface ProjectRepository extends JpaRepository<Project, Integer> {

    /**
     * Recherche un projet par son identifiant.
     *
     * @param identifier L'identifiant du projet
     * @return Le projet correspondant, s'il existe
     */
    Optional<Project> findByIdentifier(String identifier);

    /**
     * Vérifie si un projet existe avec l'identifiant spécifié.
     *
     * @param identifier L'identifiant du projet
     * @return true si le projet existe, false sinon
     */
    boolean existsByIdentifier(String identifier);



    /**
     * Récupère les projets filtrés par préfixes de manière flexible.
     * CT/AF peuvent être n'importe où dans le nom, mais doivent être suivis d'un chiffre.
     *
     * @param includeCT Inclure les projets CT
     * @param includeAF Inclure les projets AF
     * @return Liste des projets correspondants
     */
    @Query("SELECT p FROM Project p WHERE " +
           "(:includeCT = true AND (p.name LIKE '%CT0%' OR p.name LIKE '%CT1%' OR p.name LIKE '%CT2%' OR p.name LIKE '%CT3%' OR p.name LIKE '%CT4%' OR " +
           "p.name LIKE '%CT5%' OR p.name LIKE '%CT6%' OR p.name LIKE '%CT7%' OR p.name LIKE '%CT8%' OR p.name LIKE '%CT9%')) OR " +
           "(:includeAF = true AND (p.name LIKE '%AF0%' OR p.name LIKE '%AF1%' OR p.name LIKE '%AF2%' OR p.name LIKE '%AF3%' OR p.name LIKE '%AF4%' OR " +
           "p.name LIKE '%AF5%' OR p.name LIKE '%AF6%' OR p.name LIKE '%AF7%' OR p.name LIKE '%AF8%' OR p.name LIKE '%AF9%'))")
    List<Project> findProjectsByPrefixes(@Param("includeCT") boolean includeCT,
                                       @Param("includeAF") boolean includeAF);

    /**
     * Récupère les projets filtrés par préfixes et contenant une chaîne de caractères.
     * CT/AF peuvent être n'importe où dans le nom, mais doivent être suivis d'un chiffre.
     *
     * @param includeCT Inclure les projets CT
     * @param includeAF Inclure les projets AF
     * @param searchTerm Chaîne de caractères à rechercher dans le nom (optionnel)
     * @return Liste des projets correspondants
     */
    @Query("SELECT p FROM Project p WHERE " +
           "((:includeCT = true AND (p.name LIKE '%CT0%' OR p.name LIKE '%CT1%' OR p.name LIKE '%CT2%' OR p.name LIKE '%CT3%' OR p.name LIKE '%CT4%' OR " +
           "p.name LIKE '%CT5%' OR p.name LIKE '%CT6%' OR p.name LIKE '%CT7%' OR p.name LIKE '%CT8%' OR p.name LIKE '%CT9%')) OR " +
           "(:includeAF = true AND (p.name LIKE '%AF0%' OR p.name LIKE '%AF1%' OR p.name LIKE '%AF2%' OR p.name LIKE '%AF3%' OR p.name LIKE '%AF4%' OR " +
           "p.name LIKE '%AF5%' OR p.name LIKE '%AF6%' OR p.name LIKE '%AF7%' OR p.name LIKE '%AF8%' OR p.name LIKE '%AF9%'))) " +
           "AND (:searchTerm IS NULL OR :searchTerm = '' OR UPPER(p.name) LIKE UPPER(CONCAT('%', :searchTerm, '%')))")
    List<Project> findProjectsByPrefixesAndSearch(@Param("includeCT") boolean includeCT,
                                                 @Param("includeAF") boolean includeAF,
                                                 @Param("searchTerm") String searchTerm);

    /**
     * Recherche des projets par chaîne de caractères dans le nom (sans filtre de préfixe).
     *
     * @param searchTerm Chaîne de caractères à rechercher
     * @return Liste des projets correspondants
     */
    @Query("SELECT p FROM Project p WHERE UPPER(p.name) LIKE UPPER(CONCAT('%', :searchTerm, '%'))")
    List<Project> findProjectsByNameContaining(@Param("searchTerm") String searchTerm);


}
