package otbs.ms_incident.dto.dashboard;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class RepairCostByVehicleDTOTest {

    @Test
    void testNoArgsConstructor() {
        // Act
        RepairCostByVehicleDTO dto = new RepairCostByVehicleDTO();

        // Assert
        assertNull(dto.getCostByVehicleAndStatus());
        assertNull(dto.getVehicleImmatriculations());
    }

    @Test
    void testAllArgsConstructor() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costByVehicleAndStatus = new HashMap<>();
        Map<String, BigDecimal> costs = new HashMap<>();
        costs.put("EN_COURS", new BigDecimal("1000.00"));
        costByVehicleAndStatus.put(1L, costs);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC-123");

        // Act
        RepairCostByVehicleDTO dto = new RepairCostByVehicleDTO(costByVehicleAndStatus, vehicleImmatriculations);

        // Assert
        assertEquals(costByVehicleAndStatus, dto.getCostByVehicleAndStatus());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
    }

    @Test
    void testBuilder() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costByVehicleAndStatus = new HashMap<>();
        Map<String, BigDecimal> costs = new HashMap<>();
        costs.put("TERMINEE", new BigDecimal("2000.00"));
        costByVehicleAndStatus.put(2L, costs);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(2L, "XYZ-789");

        // Act
        RepairCostByVehicleDTO dto = RepairCostByVehicleDTO.builder()
                .costByVehicleAndStatus(costByVehicleAndStatus)
                .vehicleImmatriculations(vehicleImmatriculations)
                .build();

        // Assert
        assertEquals(costByVehicleAndStatus, dto.getCostByVehicleAndStatus());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
    }

    @Test
    void testSettersAndGetters() {
        // Arrange
        RepairCostByVehicleDTO dto = new RepairCostByVehicleDTO();

        Map<Long, Map<String, BigDecimal>> costByVehicleAndStatus = new HashMap<>();
        Map<String, BigDecimal> costs = new HashMap<>();
        costs.put("EN_COURS", new BigDecimal("1500.00"));
        costByVehicleAndStatus.put(3L, costs);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(3L, "DEF-456");

        // Act
        dto.setCostByVehicleAndStatus(costByVehicleAndStatus);
        dto.setVehicleImmatriculations(vehicleImmatriculations);

        // Assert
        assertEquals(costByVehicleAndStatus, dto.getCostByVehicleAndStatus());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
    }

    @Test
    void testEqualsAndHashCode() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costs1 = new HashMap<>();
        Map<String, BigDecimal> statusCosts1 = new HashMap<>();
        statusCosts1.put("EN_COURS", new BigDecimal("1000.00"));
        costs1.put(1L, statusCosts1);

        Map<Long, String> immat1 = new HashMap<>();
        immat1.put(1L, "ABC-123");

        RepairCostByVehicleDTO dto1 = new RepairCostByVehicleDTO(costs1, immat1);
        RepairCostByVehicleDTO dto2 = new RepairCostByVehicleDTO(costs1, immat1);
        RepairCostByVehicleDTO dto3 = new RepairCostByVehicleDTO(new HashMap<>(), new HashMap<>());

        // Assert
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testEqualsWithSameObject() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costs = new HashMap<>();
        Map<String, BigDecimal> statusCosts = new HashMap<>();
        statusCosts.put("EN_COURS", new BigDecimal("1000.00"));
        costs.put(1L, statusCosts);

        Map<Long, String> immat = new HashMap<>();
        immat.put(1L, "ABC-123");

        RepairCostByVehicleDTO dto = new RepairCostByVehicleDTO(costs, immat);

        // Assert
        assertEquals(dto, dto);
    }

    @Test
    void testEqualsWithNull() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costs = new HashMap<>();
        Map<String, BigDecimal> statusCosts = new HashMap<>();
        statusCosts.put("EN_COURS", new BigDecimal("1000.00"));
        costs.put(1L, statusCosts);

        Map<Long, String> immat = new HashMap<>();
        immat.put(1L, "ABC-123");

        RepairCostByVehicleDTO dto = new RepairCostByVehicleDTO(costs, immat);

        // Assert
        assertNotEquals(null, dto);
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costs = new HashMap<>();
        Map<String, BigDecimal> statusCosts = new HashMap<>();
        statusCosts.put("EN_COURS", new BigDecimal("1000.00"));
        costs.put(1L, statusCosts);

        Map<Long, String> immat = new HashMap<>();
        immat.put(1L, "ABC-123");

        RepairCostByVehicleDTO dto = new RepairCostByVehicleDTO(costs, immat);

        // Assert
        assertNotEquals("Not a DTO", dto);
    }

    @Test
    void testEqualsWithDifferentCostByVehicleAndStatus() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costs1 = new HashMap<>();
        Map<String, BigDecimal> statusCosts1 = new HashMap<>();
        statusCosts1.put("EN_COURS", new BigDecimal("1000.00"));
        costs1.put(1L, statusCosts1);

        Map<Long, Map<String, BigDecimal>> costs2 = new HashMap<>();
        Map<String, BigDecimal> statusCosts2 = new HashMap<>();
        statusCosts2.put("EN_COURS", new BigDecimal("2000.00"));
        costs2.put(1L, statusCosts2);

        Map<Long, String> immat = new HashMap<>();
        immat.put(1L, "ABC-123");

        RepairCostByVehicleDTO dto1 = new RepairCostByVehicleDTO(costs1, immat);
        RepairCostByVehicleDTO dto2 = new RepairCostByVehicleDTO(costs2, immat);

        // Assert
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentVehicleImmatriculations() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costs = new HashMap<>();
        Map<String, BigDecimal> statusCosts = new HashMap<>();
        statusCosts.put("EN_COURS", new BigDecimal("1000.00"));
        costs.put(1L, statusCosts);

        Map<Long, String> immat1 = new HashMap<>();
        immat1.put(1L, "ABC-123");

        Map<Long, String> immat2 = new HashMap<>();
        immat2.put(1L, "DEF-456");

        RepairCostByVehicleDTO dto1 = new RepairCostByVehicleDTO(costs, immat1);
        RepairCostByVehicleDTO dto2 = new RepairCostByVehicleDTO(costs, immat2);

        // Assert
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithOneNullCostByVehicleAndStatus() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costs = new HashMap<>();
        Map<String, BigDecimal> statusCosts = new HashMap<>();
        statusCosts.put("EN_COURS", new BigDecimal("1000.00"));
        costs.put(1L, statusCosts);

        Map<Long, String> immat = new HashMap<>();
        immat.put(1L, "ABC-123");

        RepairCostByVehicleDTO dto1 = new RepairCostByVehicleDTO(costs, immat);
        RepairCostByVehicleDTO dto2 = new RepairCostByVehicleDTO(null, immat);

        // Assert
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithOneNullVehicleImmatriculations() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costs = new HashMap<>();
        Map<String, BigDecimal> statusCosts = new HashMap<>();
        statusCosts.put("EN_COURS", new BigDecimal("1000.00"));
        costs.put(1L, statusCosts);

        Map<Long, String> immat = new HashMap<>();
        immat.put(1L, "ABC-123");

        RepairCostByVehicleDTO dto1 = new RepairCostByVehicleDTO(costs, immat);
        RepairCostByVehicleDTO dto2 = new RepairCostByVehicleDTO(costs, null);

        // Assert
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithBothNullMaps() {
        // Arrange
        RepairCostByVehicleDTO dto1 = new RepairCostByVehicleDTO(null, null);
        RepairCostByVehicleDTO dto2 = new RepairCostByVehicleDTO(null, null);

        // Assert
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testHashCodeWithNullMaps() {
        // Arrange
        RepairCostByVehicleDTO dto = new RepairCostByVehicleDTO(null, null);

        // Act & Assert
        assertDoesNotThrow(dto::hashCode);
    }

    @Test
    void testToString() {
        // Arrange
        Map<Long, Map<String, BigDecimal>> costByVehicleAndStatus = new HashMap<>();
        Map<String, BigDecimal> costs = new HashMap<>();
        costs.put("EN_COURS", new BigDecimal("1000.00"));
        costByVehicleAndStatus.put(1L, costs);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC-123");

        RepairCostByVehicleDTO dto = new RepairCostByVehicleDTO(costByVehicleAndStatus, vehicleImmatriculations);

        // Act
        String toString = dto.toString();

        // Assert
        assertTrue(toString.contains("costByVehicleAndStatus"));
        assertTrue(toString.contains("vehicleImmatriculations"));
        assertTrue(toString.contains("ABC-123"));
    }
}
