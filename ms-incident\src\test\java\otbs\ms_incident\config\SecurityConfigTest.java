package otbs.ms_incident.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.DefaultSecurityFilterChain;


import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class SecurityConfigTest {

    private SecurityConfig securityConfig;
    private HttpSecurity httpSecurity;
    private DefaultSecurityFilterChain mockFilterChain;

    @BeforeEach
    void setUp() throws Exception {
        securityConfig = new SecurityConfig();
        httpSecurity = mock(HttpSecurity.class, RETURNS_SELF);
        mockFilterChain = mock(DefaultSecurityFilterChain.class);
        when(httpSecurity.build()).thenReturn(mockFilterChain);
    }

    @Test
    void securityFilterChain_ShouldConfigureHttpSecurity() throws Exception {
        // Arrange
        when(httpSecurity.csrf(any())).thenReturn(httpSecurity);
        when(httpSecurity.authorizeHttpRequests(any())).thenReturn(httpSecurity);
        when(httpSecurity.oauth2ResourceServer(any())).thenReturn(httpSecurity);
        when(httpSecurity.sessionManagement(any())).thenReturn(httpSecurity);

        // Act
        SecurityFilterChain filterChain = securityConfig.securityFilterChain(httpSecurity);

        // Assert
        assertNotNull(filterChain, "Security filter chain should not be null");
        verify(httpSecurity).csrf(any());
        verify(httpSecurity).authorizeHttpRequests(any());
        verify(httpSecurity).oauth2ResourceServer(any());
        verify(httpSecurity).sessionManagement(any());
        verify(httpSecurity).build();
    }

    @Test
    void securityFilterChain_ShouldConfigureSessionManagement() throws Exception {
        // Arrange
        when(httpSecurity.csrf(any())).thenReturn(httpSecurity);
        when(httpSecurity.authorizeHttpRequests(any())).thenReturn(httpSecurity);
        when(httpSecurity.oauth2ResourceServer(any())).thenReturn(httpSecurity);
        when(httpSecurity.sessionManagement(any())).thenReturn(httpSecurity);

        // Act
        securityConfig.securityFilterChain(httpSecurity);

        // Assert
        verify(httpSecurity).sessionManagement(any());
    }

    @Test
    void securityFilterChain_ShouldConfigureOAuth2ResourceServer() throws Exception {
        // Arrange
        when(httpSecurity.csrf(any())).thenReturn(httpSecurity);
        when(httpSecurity.authorizeHttpRequests(any())).thenReturn(httpSecurity);
        when(httpSecurity.oauth2ResourceServer(any())).thenReturn(httpSecurity);
        when(httpSecurity.sessionManagement(any())).thenReturn(httpSecurity);

        // Act
        securityConfig.securityFilterChain(httpSecurity);

        // Assert
        verify(httpSecurity).oauth2ResourceServer(any());
    }

    @Test
    void keycloakRealmRoleConverter_ShouldConvertRolesToAuthorities() {
        // Arrange
        SecurityConfig.KeycloakRealmRoleConverter converter = new SecurityConfig.KeycloakRealmRoleConverter();

        Map<String, Object> claims = new HashMap<>();
        Map<String, Object> realmAccess = new HashMap<>();
        realmAccess.put("roles", List.of("user", "admin"));
        claims.put("realm_access", realmAccess);

        Jwt jwt = jwt(claims);

        // Act
        Collection<?> authorities = converter.convert(jwt);

        // Assert
        assertEquals(2, authorities.size(), "Should return 2 authorities");
        assertTrue(authorities.contains(new SimpleGrantedAuthority("ROLE_USER")),
                "Should contain ROLE_USER authority");
        assertTrue(authorities.contains(new SimpleGrantedAuthority("ROLE_ADMIN")),
                "Should contain ROLE_ADMIN authority");
    }

    @Test
    void keycloakRealmRoleConverter_ShouldHandleEmptyRoles() {
        // Arrange
        SecurityConfig.KeycloakRealmRoleConverter converter = new SecurityConfig.KeycloakRealmRoleConverter();

        Map<String, Object> claims = new HashMap<>();
        Map<String, Object> realmAccess = new HashMap<>();
        realmAccess.put("roles", List.of());
        claims.put("realm_access", realmAccess);

        Jwt jwt = jwt(claims);

        // Act
        Collection<?> authorities = converter.convert(jwt);

        // Assert
        assertTrue(authorities.isEmpty(), "Should return empty authorities list");
    }

    @Test
    void keycloakRealmRoleConverter_ShouldHandleNullRealmAccess() {
        // Arrange
        SecurityConfig.KeycloakRealmRoleConverter converter = new SecurityConfig.KeycloakRealmRoleConverter();

        Map<String, Object> claims = new HashMap<>();
        claims.put("sub", "user123");

        Jwt jwt = jwt(claims);

        // Act
        Collection<?> authorities = converter.convert(jwt);

        // Assert
        assertTrue(authorities.isEmpty(), "Should return empty authorities list");
    }

    @Test
    void keycloakRealmRoleConverter_ShouldHandleEmptyRealmAccess() {
        // Arrange
        SecurityConfig.KeycloakRealmRoleConverter converter = new SecurityConfig.KeycloakRealmRoleConverter();

        Map<String, Object> claims = new HashMap<>();
        claims.put("realm_access", new HashMap<>());
        claims.put("sub", "user123");

        Jwt jwt = jwt(claims);

        // Act
        Collection<?> authorities = converter.convert(jwt);

        // Assert
        assertTrue(authorities.isEmpty(), "Should return empty authorities list");
    }

    @Test
    void keycloakRealmRoleConverter_ShouldHandleNullRoles() {
        // Arrange
        SecurityConfig.KeycloakRealmRoleConverter converter = new SecurityConfig.KeycloakRealmRoleConverter();

        Map<String, Object> claims = new HashMap<>();
        Map<String, Object> realmAccess = new HashMap<>();
        realmAccess.put("roles", null);
        claims.put("realm_access", realmAccess);
        claims.put("sub", "user123");

        Jwt jwt = jwt(claims);

        // Act & Assert
        assertThrows(NullPointerException.class, () -> converter.convert(jwt),
                "Should throw NullPointerException when roles is null");
    }

    @Test
    void keycloakRealmRoleConverter_ShouldHandleSpecialCharactersInRoles() {
        // Arrange
        SecurityConfig.KeycloakRealmRoleConverter converter = new SecurityConfig.KeycloakRealmRoleConverter();

        Map<String, Object> claims = new HashMap<>();
        Map<String, Object> realmAccess = new HashMap<>();
        realmAccess.put("roles", List.of("user-admin", "SUPER_USER", "role.with.dots"));
        claims.put("realm_access", realmAccess);

        Jwt jwt = jwt(claims);

        // Act
        Collection<?> authorities = converter.convert(jwt);

        // Assert
        assertEquals(3, authorities.size(), "Should return 3 authorities");
        assertTrue(authorities.contains(new SimpleGrantedAuthority("ROLE_USER-ADMIN")));
        assertTrue(authorities.contains(new SimpleGrantedAuthority("ROLE_SUPER_USER")));
        assertTrue(authorities.contains(new SimpleGrantedAuthority("ROLE_ROLE.WITH.DOTS")));
    }

    @Test
    void keycloakRealmRoleConverter_ShouldHandleMixedCaseRoles() {
        // Arrange
        SecurityConfig.KeycloakRealmRoleConverter converter = new SecurityConfig.KeycloakRealmRoleConverter();

        Map<String, Object> claims = new HashMap<>();
        Map<String, Object> realmAccess = new HashMap<>();
        realmAccess.put("roles", List.of("User", "ADMIN", "superUser"));
        claims.put("realm_access", realmAccess);

        Jwt jwt = jwt(claims);

        // Act
        Collection<?> authorities = converter.convert(jwt);

        // Assert
        assertEquals(3, authorities.size(), "Should return 3 authorities");
        assertTrue(authorities.contains(new SimpleGrantedAuthority("ROLE_USER")));
        assertTrue(authorities.contains(new SimpleGrantedAuthority("ROLE_ADMIN")));
        assertTrue(authorities.contains(new SimpleGrantedAuthority("ROLE_SUPERUSER")));
    }

    @Test
    void corsConfigurationSource_ShouldReturnConfiguredCorsSource() {
        // Act
        var corsSource = securityConfig.corsConfigurationSource();

        // Assert
        assertNotNull(corsSource, "CORS configuration source should not be null");
    }

    @Test
    void jwtAuthenticationConverter_ShouldReturnConfiguredConverter() throws Exception {
        // This test uses reflection to access the private method
        var method = SecurityConfig.class.getDeclaredMethod("jwtAuthenticationConverter");
        method.setAccessible(true);

        // Act
        var converter = method.invoke(securityConfig);

        // Assert
        assertNotNull(converter, "JWT authentication converter should not be null");
    }

    // Helper method to create a JWT
    private Jwt jwt(Map<String, Object> claims) {
        return new Jwt(
            "token",
            Instant.now(),
            Instant.now().plusSeconds(300),
            Map.of("alg", "none"),
            claims
        );
    }
}