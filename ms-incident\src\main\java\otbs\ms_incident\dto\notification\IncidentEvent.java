package otbs.ms_incident.dto.notification;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * Événement spécifique aux incidents.
 * Hérite de NotificationEvent et ajoute des informations spécifiques aux incidents.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class IncidentEvent extends NotificationEvent {
    private Long incidentId;
    private String typeIncident;
    private String statusIncident;
    private Long vehiculeId;
    private String immatriculation;
    private String createurIncident;

    public IncidentEvent(String eventType, Long incidentId, List<String> destinataires,
                        String titre, String message) {
        super(eventType, "ms-incident", destinataires, titre, message);
        this.incidentId = incidentId;
        this.setEntiteLieeId(incidentId);
        this.setEntiteLieeType("INCIDENT");
        this.setUrlAction("/incidents/" + incidentId);
    }

    public IncidentEvent(String eventType, Long incidentId, String destinataire,
                        String titre, String message) {
        super(eventType, "ms-incident", destinataire, titre, message);
        this.incidentId = incidentId;
        this.setEntiteLieeId(incidentId);
        this.setEntiteLieeType("INCIDENT");
        this.setUrlAction("/incidents/" + incidentId);
    }
}
