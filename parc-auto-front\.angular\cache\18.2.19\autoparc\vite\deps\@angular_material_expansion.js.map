{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/accordion.mjs", "../../../../../../node_modules/@angular/material/fesm2022/expansion.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, booleanAttribute, Directive, Input, EventEmitter, Optional, Inject, SkipSelf, Output, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/collections';\nimport { Subject, Subscription } from 'rxjs';\n\n/** Used to generate unique ID for each accordion. */\nlet nextId$1 = 0;\n/**\n * Injection token that can be used to reference instances of `CdkAccordion`. It serves\n * as alternative token to the actual `CdkAccordion` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst CDK_ACCORDION = new InjectionToken('CdkAccordion');\n/**\n * Directive whose purpose is to manage the expanded state of CdkAccordionItem children.\n */\nclass CdkAccordion {\n  constructor() {\n    /** Emits when the state of the accordion changes */\n    this._stateChanges = new Subject();\n    /** Stream that emits true/false when openAll/closeAll is triggered. */\n    this._openCloseAllActions = new Subject();\n    /** A readonly id value to use for unique selection coordination. */\n    this.id = `cdk-accordion-${nextId$1++}`;\n    /** Whether the accordion should allow multiple expanded accordion items simultaneously. */\n    this.multi = false;\n  }\n  /** Opens all enabled accordion items in an accordion where multi is enabled. */\n  openAll() {\n    if (this.multi) {\n      this._openCloseAllActions.next(true);\n    }\n  }\n  /** Closes all enabled accordion items. */\n  closeAll() {\n    this._openCloseAllActions.next(false);\n  }\n  ngOnChanges(changes) {\n    this._stateChanges.next(changes);\n  }\n  ngOnDestroy() {\n    this._stateChanges.complete();\n    this._openCloseAllActions.complete();\n  }\n  static {\n    this.ɵfac = function CdkAccordion_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAccordion)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkAccordion,\n      selectors: [[\"cdk-accordion\"], [\"\", \"cdkAccordion\", \"\"]],\n      inputs: {\n        multi: [2, \"multi\", \"multi\", booleanAttribute]\n      },\n      exportAs: [\"cdkAccordion\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CDK_ACCORDION,\n        useExisting: CdkAccordion\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-accordion, [cdkAccordion]',\n      exportAs: 'cdkAccordion',\n      providers: [{\n        provide: CDK_ACCORDION,\n        useExisting: CdkAccordion\n      }],\n      standalone: true\n    }]\n  }], null, {\n    multi: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/** Used to generate unique ID for each accordion item. */\nlet nextId = 0;\n/**\n * A basic directive expected to be extended and decorated as a component.  Sets up all\n * events and attributes needed to be managed by a CdkAccordion parent.\n */\nclass CdkAccordionItem {\n  /** Whether the AccordionItem is expanded. */\n  get expanded() {\n    return this._expanded;\n  }\n  set expanded(expanded) {\n    // Only emit events and update the internal value if the value changes.\n    if (this._expanded !== expanded) {\n      this._expanded = expanded;\n      this.expandedChange.emit(expanded);\n      if (expanded) {\n        this.opened.emit();\n        /**\n         * In the unique selection dispatcher, the id parameter is the id of the CdkAccordionItem,\n         * the name value is the id of the accordion.\n         */\n        const accordionId = this.accordion ? this.accordion.id : this.id;\n        this._expansionDispatcher.notify(this.id, accordionId);\n      } else {\n        this.closed.emit();\n      }\n      // Ensures that the animation will run when the value is set outside of an `@Input`.\n      // This includes cases like the open, close and toggle methods.\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  constructor(accordion, _changeDetectorRef, _expansionDispatcher) {\n    this.accordion = accordion;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._expansionDispatcher = _expansionDispatcher;\n    /** Subscription to openAll/closeAll events. */\n    this._openCloseAllSubscription = Subscription.EMPTY;\n    /** Event emitted every time the AccordionItem is closed. */\n    this.closed = new EventEmitter();\n    /** Event emitted every time the AccordionItem is opened. */\n    this.opened = new EventEmitter();\n    /** Event emitted when the AccordionItem is destroyed. */\n    this.destroyed = new EventEmitter();\n    /**\n     * Emits whenever the expanded state of the accordion changes.\n     * Primarily used to facilitate two-way binding.\n     * @docs-private\n     */\n    this.expandedChange = new EventEmitter();\n    /** The unique AccordionItem id. */\n    this.id = `cdk-accordion-child-${nextId++}`;\n    this._expanded = false;\n    /** Whether the AccordionItem is disabled. */\n    this.disabled = false;\n    /** Unregister function for _expansionDispatcher. */\n    this._removeUniqueSelectionListener = () => {};\n    this._removeUniqueSelectionListener = _expansionDispatcher.listen((id, accordionId) => {\n      if (this.accordion && !this.accordion.multi && this.accordion.id === accordionId && this.id !== id) {\n        this.expanded = false;\n      }\n    });\n    // When an accordion item is hosted in an accordion, subscribe to open/close events.\n    if (this.accordion) {\n      this._openCloseAllSubscription = this._subscribeToOpenCloseAllActions();\n    }\n  }\n  /** Emits an event for the accordion item being destroyed. */\n  ngOnDestroy() {\n    this.opened.complete();\n    this.closed.complete();\n    this.destroyed.emit();\n    this.destroyed.complete();\n    this._removeUniqueSelectionListener();\n    this._openCloseAllSubscription.unsubscribe();\n  }\n  /** Toggles the expanded state of the accordion item. */\n  toggle() {\n    if (!this.disabled) {\n      this.expanded = !this.expanded;\n    }\n  }\n  /** Sets the expanded state of the accordion item to false. */\n  close() {\n    if (!this.disabled) {\n      this.expanded = false;\n    }\n  }\n  /** Sets the expanded state of the accordion item to true. */\n  open() {\n    if (!this.disabled) {\n      this.expanded = true;\n    }\n  }\n  _subscribeToOpenCloseAllActions() {\n    return this.accordion._openCloseAllActions.subscribe(expanded => {\n      // Only change expanded state if item is enabled\n      if (!this.disabled) {\n        this.expanded = expanded;\n      }\n    });\n  }\n  static {\n    this.ɵfac = function CdkAccordionItem_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAccordionItem)(i0.ɵɵdirectiveInject(CDK_ACCORDION, 12), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UniqueSelectionDispatcher));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkAccordionItem,\n      selectors: [[\"cdk-accordion-item\"], [\"\", \"cdkAccordionItem\", \"\"]],\n      inputs: {\n        expanded: [2, \"expanded\", \"expanded\", booleanAttribute],\n        disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n      },\n      outputs: {\n        closed: \"closed\",\n        opened: \"opened\",\n        destroyed: \"destroyed\",\n        expandedChange: \"expandedChange\"\n      },\n      exportAs: [\"cdkAccordionItem\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([\n      // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n      // registering to the same accordion.\n      {\n        provide: CDK_ACCORDION,\n        useValue: undefined\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordionItem, [{\n    type: Directive,\n    args: [{\n      selector: 'cdk-accordion-item, [cdkAccordionItem]',\n      exportAs: 'cdkAccordionItem',\n      providers: [\n      // Provide `CDK_ACCORDION` as undefined to prevent nested accordion items from\n      // registering to the same accordion.\n      {\n        provide: CDK_ACCORDION,\n        useValue: undefined\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkAccordion,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDK_ACCORDION]\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.UniqueSelectionDispatcher\n  }], {\n    closed: [{\n      type: Output\n    }],\n    opened: [{\n      type: Output\n    }],\n    destroyed: [{\n      type: Output\n    }],\n    expandedChange: [{\n      type: Output\n    }],\n    expanded: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass CdkAccordionModule {\n  static {\n    this.ɵfac = function CdkAccordionModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CdkAccordionModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkAccordionModule,\n      imports: [CdkAccordion, CdkAccordionItem],\n      exports: [CdkAccordion, CdkAccordionItem]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkAccordionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkAccordion, CdkAccordionItem],\n      exports: [CdkAccordion, CdkAccordionItem]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_ACCORDION, CdkAccordion, CdkAccordionItem, CdkAccordionModule };\n", "import { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport { TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, EventEmitter, ANIMATION_MODULE_TYPE, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, SkipSelf, Input, Output, ContentChild, ViewChild, numberAttribute, Host, Attribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/cdk/collections';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst _c0 = [\"body\"];\nconst _c1 = [[[\"mat-expansion-panel-header\"]], \"*\", [[\"mat-action-row\"]]];\nconst _c2 = [\"mat-expansion-panel-header\", \"*\", \"mat-action-row\"];\nfunction MatExpansionPanel_ng_template_5_Template(rf, ctx) {}\nconst _c3 = [[[\"mat-panel-title\"]], [[\"mat-panel-description\"]], \"*\"];\nconst _c4 = [\"mat-panel-title\", \"mat-panel-description\", \"*\"];\nfunction MatExpansionPanelHeader_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 2);\n    i0.ɵɵelement(2, \"path\", 3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@indicatorRotate\", ctx_r0._getExpandedState());\n  }\n}\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/** Time and timing curve for expansion panel animations. */\n// Note: Keep this in sync with the Sass variable for the panel header animation.\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM. This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n */\nconst matExpansionAnimations = {\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: trigger('indicatorRotate', [state('collapsed, void', style({\n    transform: 'rotate(0deg)'\n  })), state('expanded', style({\n    transform: 'rotate(180deg)'\n  })), transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING))]),\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: trigger('bodyExpansion', [state('collapsed, void', style({\n    height: '0px',\n    visibility: 'hidden'\n  })),\n  // Clear the `visibility` while open, otherwise the content will be visible when placed in\n  // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n  // that have a `visibility` of their own (see #27436).\n  state('expanded', style({\n    height: '*',\n    visibility: ''\n  })), transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING))])\n};\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n  constructor(_template, _expansionPanel) {\n    this._template = _template;\n    this._expansionPanel = _expansionPanel;\n  }\n  static {\n    this.ɵfac = function MatExpansionPanelContent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelContent)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelContent,\n      selectors: [[\"ng-template\", \"matExpansionPanelContent\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matExpansionPanelContent]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_EXPANSION_PANEL]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n\n/** Counter for generating unique element ids. */\nlet uniqueId = 0;\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n  /** Whether the toggle indicator should be hidden. */\n  get hideToggle() {\n    return this._hideToggle || this.accordion && this.accordion.hideToggle;\n  }\n  set hideToggle(value) {\n    this._hideToggle = value;\n  }\n  /** The position of the expansion indicator. */\n  get togglePosition() {\n    return this._togglePosition || this.accordion && this.accordion.togglePosition;\n  }\n  set togglePosition(value) {\n    this._togglePosition = value;\n  }\n  constructor(accordion, _changeDetectorRef, _uniqueSelectionDispatcher, _viewContainerRef, _document, _animationMode, defaultOptions) {\n    super(accordion, _changeDetectorRef, _uniqueSelectionDispatcher);\n    this._viewContainerRef = _viewContainerRef;\n    this._animationMode = _animationMode;\n    this._hideToggle = false;\n    /** An event emitted after the body's expansion animation happens. */\n    this.afterExpand = new EventEmitter();\n    /** An event emitted after the body's collapse animation happens. */\n    this.afterCollapse = new EventEmitter();\n    /** Stream that emits for changes in `@Input` properties. */\n    this._inputChanges = new Subject();\n    /** ID for the associated header element. Used for a11y labelling. */\n    this._headerId = `mat-expansion-panel-header-${uniqueId++}`;\n    this.accordion = accordion;\n    this._document = _document;\n    this._animationsDisabled = _animationMode === 'NoopAnimations';\n    if (defaultOptions) {\n      this.hideToggle = defaultOptions.hideToggle;\n    }\n  }\n  /** Determines whether the expansion panel should have spacing between it and its siblings. */\n  _hasSpacing() {\n    if (this.accordion) {\n      return this.expanded && this.accordion.displayMode === 'default';\n    }\n    return false;\n  }\n  /** Gets the expanded state string. */\n  _getExpandedState() {\n    return this.expanded ? 'expanded' : 'collapsed';\n  }\n  /** Toggles the expanded state of the expansion panel. */\n  toggle() {\n    this.expanded = !this.expanded;\n  }\n  /** Sets the expanded state of the expansion panel to false. */\n  close() {\n    this.expanded = false;\n  }\n  /** Sets the expanded state of the expansion panel to true. */\n  open() {\n    this.expanded = true;\n  }\n  ngAfterContentInit() {\n    if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n      // Render the content as soon as the panel becomes open.\n      this.opened.pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1)).subscribe(() => {\n        this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n      });\n    }\n  }\n  ngOnChanges(changes) {\n    this._inputChanges.next(changes);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._inputChanges.complete();\n  }\n  /** Checks whether the expansion panel's content contains the currently-focused element. */\n  _containsFocus() {\n    if (this._body) {\n      const focusedElement = this._document.activeElement;\n      const bodyElement = this._body.nativeElement;\n      return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n    }\n    return false;\n  }\n  /** Called when the expansion animation has started. */\n  _animationStarted(event) {\n    if (!isInitialAnimation(event) && !this._animationsDisabled && this._body) {\n      // Prevent the user from tabbing into the content while it's animating.\n      // TODO(crisbeto): maybe use `inert` to prevent focus from entering while closed as well\n      // instead of `visibility`? Will allow us to clean up some code but needs more testing.\n      this._body?.nativeElement.setAttribute('inert', '');\n    }\n  }\n  /** Called when the expansion animation has finished. */\n  _animationDone(event) {\n    if (!isInitialAnimation(event)) {\n      if (event.toState === 'expanded') {\n        this.afterExpand.emit();\n      } else if (event.toState === 'collapsed') {\n        this.afterCollapse.emit();\n      }\n      // Re-enable tabbing once the animation is finished.\n      if (!this._animationsDisabled && this._body) {\n        this._body.nativeElement.removeAttribute('inert');\n      }\n    }\n  }\n  static {\n    this.ɵfac = function MatExpansionPanel_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanel)(i0.ɵɵdirectiveInject(MAT_ACCORDION, 12), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UniqueSelectionDispatcher), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatExpansionPanel,\n      selectors: [[\"mat-expansion-panel\"]],\n      contentQueries: function MatExpansionPanel_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelContent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n        }\n      },\n      viewQuery: function MatExpansionPanel_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._body = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-expansion-panel\"],\n      hostVars: 6,\n      hostBindings: function MatExpansionPanel_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-expanded\", ctx.expanded)(\"_mat-animation-noopable\", ctx._animationsDisabled)(\"mat-expansion-panel-spacing\", ctx._hasSpacing());\n        }\n      },\n      inputs: {\n        hideToggle: [2, \"hideToggle\", \"hideToggle\", booleanAttribute],\n        togglePosition: \"togglePosition\"\n      },\n      outputs: {\n        afterExpand: \"afterExpand\",\n        afterCollapse: \"afterCollapse\"\n      },\n      exportAs: [\"matExpansionPanel\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([\n      // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n      // to the same accordion.\n      {\n        provide: MAT_ACCORDION,\n        useValue: undefined\n      }, {\n        provide: MAT_EXPANSION_PANEL,\n        useExisting: MatExpansionPanel\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 7,\n      vars: 4,\n      consts: [[\"body\", \"\"], [\"role\", \"region\", 1, \"mat-expansion-panel-content\", 3, \"id\"], [1, \"mat-expansion-panel-body\"], [3, \"cdkPortalOutlet\"]],\n      template: function MatExpansionPanel_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 1, 0);\n          i0.ɵɵlistener(\"@bodyExpansion.start\", function MatExpansionPanel_Template_div_animation_bodyExpansion_start_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._animationStarted($event));\n          })(\"@bodyExpansion.done\", function MatExpansionPanel_Template_div_animation_bodyExpansion_done_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._animationDone($event));\n          });\n          i0.ɵɵelementStart(3, \"div\", 2);\n          i0.ɵɵprojection(4, 1);\n          i0.ɵɵtemplate(5, MatExpansionPanel_ng_template_5_Template, 0, 0, \"ng-template\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(6, 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"@bodyExpansion\", ctx._getExpandedState())(\"id\", ctx.id);\n          i0.ɵɵattribute(\"aria-labelledby\", ctx._headerId);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._portal);\n        }\n      },\n      dependencies: [CdkPortalOutlet],\n      styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative;background:var(--mat-expansion-container-background-color, var(--mat-app-surface));color:var(--mat-expansion-container-text-color, var(--mat-app-on-surface));border-radius:var(--mat-expansion-container-shape)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape);border-top-left-radius:var(--mat-expansion-container-shape)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape);border-bottom-left-radius:var(--mat-expansion-container-shape)}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;font-family:var(--mat-expansion-container-text-font, var(--mat-app-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-app-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-app-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-app-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-app-body-large-tracking))}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-app-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matExpansionAnimations.bodyExpansion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel',\n      exportAs: 'matExpansionPanel',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [matExpansionAnimations.bodyExpansion],\n      providers: [\n      // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n      // to the same accordion.\n      {\n        provide: MAT_ACCORDION,\n        useValue: undefined\n      }, {\n        provide: MAT_EXPANSION_PANEL,\n        useExisting: MatExpansionPanel\n      }],\n      host: {\n        'class': 'mat-expansion-panel',\n        '[class.mat-expanded]': 'expanded',\n        '[class._mat-animation-noopable]': '_animationsDisabled',\n        '[class.mat-expansion-panel-spacing]': '_hasSpacing()'\n      },\n      standalone: true,\n      imports: [CdkPortalOutlet],\n      template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.start)=\\\"_animationStarted($event)\\\"\\n     (@bodyExpansion.done)=\\\"_animationDone($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\",\n      styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative;background:var(--mat-expansion-container-background-color, var(--mat-app-surface));color:var(--mat-expansion-container-text-color, var(--mat-app-on-surface));border-radius:var(--mat-expansion-container-shape)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape);border-top-left-radius:var(--mat-expansion-container-shape)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape);border-bottom-left-radius:var(--mat-expansion-container-shape)}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;font-family:var(--mat-expansion-container-text-font, var(--mat-app-body-large-font));font-size:var(--mat-expansion-container-text-size, var(--mat-app-body-large-size));font-weight:var(--mat-expansion-container-text-weight, var(--mat-app-body-large-weight));line-height:var(--mat-expansion-container-text-line-height, var(--mat-app-body-large-line-height));letter-spacing:var(--mat-expansion-container-text-tracking, var(--mat-app-body-large-tracking))}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color, var(--mat-app-outline))}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }, {\n      type: Inject,\n      args: [MAT_ACCORDION]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.UniqueSelectionDispatcher\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }], {\n    hideToggle: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    togglePosition: [{\n      type: Input\n    }],\n    afterExpand: [{\n      type: Output\n    }],\n    afterCollapse: [{\n      type: Output\n    }],\n    _lazyContent: [{\n      type: ContentChild,\n      args: [MatExpansionPanelContent]\n    }],\n    _body: [{\n      type: ViewChild,\n      args: ['body']\n    }]\n  });\n})();\n/** Checks whether an animation is the initial setup animation. */\nfunction isInitialAnimation(event) {\n  return event.fromState === 'void';\n}\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {\n  static {\n    this.ɵfac = function MatExpansionPanelActionRow_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelActionRow)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelActionRow,\n      selectors: [[\"mat-action-row\"]],\n      hostAttrs: [1, \"mat-action-row\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelActionRow, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-action-row',\n      host: {\n        class: 'mat-action-row'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader {\n  constructor(panel, _element, _focusMonitor, _changeDetectorRef, defaultOptions, _animationMode, tabIndex) {\n    this.panel = panel;\n    this._element = _element;\n    this._focusMonitor = _focusMonitor;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    this._parentChangeSubscription = Subscription.EMPTY;\n    /** Tab index of the header. */\n    this.tabIndex = 0;\n    const accordionHideToggleChange = panel.accordion ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition']))) : EMPTY;\n    this.tabIndex = parseInt(tabIndex || '') || 0;\n    // Since the toggle state depends on an @Input on the panel, we\n    // need to subscribe and trigger change detection manually.\n    this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n      return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n    }))).subscribe(() => this._changeDetectorRef.markForCheck());\n    // Avoids focus being lost if the panel contained the focused element and was closed.\n    panel.closed.pipe(filter(() => panel._containsFocus())).subscribe(() => _focusMonitor.focusVia(_element, 'program'));\n    if (defaultOptions) {\n      this.expandedHeight = defaultOptions.expandedHeight;\n      this.collapsedHeight = defaultOptions.collapsedHeight;\n    }\n  }\n  /**\n   * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n   * @docs-private\n   */\n  get disabled() {\n    return this.panel.disabled;\n  }\n  /** Toggles the expanded state of the panel. */\n  _toggle() {\n    if (!this.disabled) {\n      this.panel.toggle();\n    }\n  }\n  /** Gets whether the panel is expanded. */\n  _isExpanded() {\n    return this.panel.expanded;\n  }\n  /** Gets the expanded state string of the panel. */\n  _getExpandedState() {\n    return this.panel._getExpandedState();\n  }\n  /** Gets the panel id. */\n  _getPanelId() {\n    return this.panel.id;\n  }\n  /** Gets the toggle position for the header. */\n  _getTogglePosition() {\n    return this.panel.togglePosition;\n  }\n  /** Gets whether the expand indicator should be shown. */\n  _showToggle() {\n    return !this.panel.hideToggle && !this.panel.disabled;\n  }\n  /**\n   * Gets the current height of the header. Null if no custom height has been\n   * specified, and if the default height from the stylesheet should be used.\n   */\n  _getHeaderHeight() {\n    const isExpanded = this._isExpanded();\n    if (isExpanded && this.expandedHeight) {\n      return this.expandedHeight;\n    } else if (!isExpanded && this.collapsedHeight) {\n      return this.collapsedHeight;\n    }\n    return null;\n  }\n  /** Handle keydown event calling to toggle() if appropriate. */\n  _keydown(event) {\n    switch (event.keyCode) {\n      // Toggle for space and enter keys.\n      case SPACE:\n      case ENTER:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this._toggle();\n        }\n        break;\n      default:\n        if (this.panel.accordion) {\n          this.panel.accordion._handleHeaderKeydown(event);\n        }\n        return;\n    }\n  }\n  /**\n   * Focuses the panel header. Implemented as a part of `FocusableOption`.\n   * @param origin Origin of the action that triggered the focus.\n   * @docs-private\n   */\n  focus(origin, options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._element).subscribe(origin => {\n      if (origin && this.panel.accordion) {\n        this.panel.accordion._handleHeaderFocus(this);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._parentChangeSubscription.unsubscribe();\n    this._focusMonitor.stopMonitoring(this._element);\n  }\n  static {\n    this.ɵfac = function MatExpansionPanelHeader_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelHeader)(i0.ɵɵdirectiveInject(MatExpansionPanel, 1), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵinjectAttribute('tabindex'));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatExpansionPanelHeader,\n      selectors: [[\"mat-expansion-panel-header\"]],\n      hostAttrs: [\"role\", \"button\", 1, \"mat-expansion-panel-header\", \"mat-focus-indicator\"],\n      hostVars: 15,\n      hostBindings: function MatExpansionPanelHeader_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatExpansionPanelHeader_click_HostBindingHandler() {\n            return ctx._toggle();\n          })(\"keydown\", function MatExpansionPanelHeader_keydown_HostBindingHandler($event) {\n            return ctx._keydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.panel._headerId)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx._getPanelId())(\"aria-expanded\", ctx._isExpanded())(\"aria-disabled\", ctx.panel.disabled);\n          i0.ɵɵstyleProp(\"height\", ctx._getHeaderHeight());\n          i0.ɵɵclassProp(\"mat-expanded\", ctx._isExpanded())(\"mat-expansion-toggle-indicator-after\", ctx._getTogglePosition() === \"after\")(\"mat-expansion-toggle-indicator-before\", ctx._getTogglePosition() === \"before\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n        }\n      },\n      inputs: {\n        expandedHeight: \"expandedHeight\",\n        collapsedHeight: \"collapsedHeight\",\n        tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c4,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"mat-content\"], [1, \"mat-expansion-indicator\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 -960 960 960\", \"aria-hidden\", \"true\", \"focusable\", \"false\"], [\"d\", \"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\"]],\n      template: function MatExpansionPanelHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵprojection(3, 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, MatExpansionPanelHeader_Conditional_4_Template, 3, 1, \"span\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-content-hide-toggle\", !ctx._showToggle());\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(ctx._showToggle() ? 4 : -1);\n        }\n      },\n      styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-expansion-header-collapsed-state-height);font-family:var(--mat-expansion-header-text-font, var(--mat-app-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-app-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-app-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-app-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-app-title-medium-tracking))}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color)}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color)}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-app-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-app-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-app-on-surface-variant))}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-app-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, inline-block)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-app-on-surface-variant));display:var(--mat-expansion-header-indicator-display, none)}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matExpansionAnimations.indicatorRotate]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [matExpansionAnimations.indicatorRotate],\n      host: {\n        'class': 'mat-expansion-panel-header mat-focus-indicator',\n        'role': 'button',\n        '[attr.id]': 'panel._headerId',\n        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n        '[attr.aria-controls]': '_getPanelId()',\n        '[attr.aria-expanded]': '_isExpanded()',\n        '[attr.aria-disabled]': 'panel.disabled',\n        '[class.mat-expanded]': '_isExpanded()',\n        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        '[style.height]': '_getHeaderHeight()',\n        '(click)': '_toggle()',\n        '(keydown)': '_keydown($event)'\n      },\n      standalone: true,\n      template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n\\n@if (_showToggle()) {\\n  <span [@indicatorRotate]=\\\"_getExpandedState()\\\" class=\\\"mat-expansion-indicator\\\">\\n    <svg\\n      xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      aria-hidden=\\\"true\\\"\\n      focusable=\\\"false\\\">\\n      <path d=\\\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\\\"/>\\n    </svg>\\n  </span>\\n}\\n\",\n      styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-expansion-header-collapsed-state-height);font-family:var(--mat-expansion-header-text-font, var(--mat-app-title-medium-font));font-size:var(--mat-expansion-header-text-size, var(--mat-app-title-medium-size));font-weight:var(--mat-expansion-header-text-weight, var(--mat-app-title-medium-weight));line-height:var(--mat-expansion-header-text-line-height, var(--mat-app-title-medium-line-height));letter-spacing:var(--mat-expansion-header-text-tracking, var(--mat-app-title-medium-tracking))}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color)}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color)}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color, var(--mat-app-surface))}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color, var(--mat-app-on-surface))}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color, var(--mat-app-on-surface-variant))}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color, var(--mat-app-on-surface-variant));display:var(--mat-expansion-legacy-header-indicator-display, inline-block)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color, var(--mat-app-on-surface-variant));display:var(--mat-expansion-header-indicator-display, none)}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"]\n    }]\n  }], () => [{\n    type: MatExpansionPanel,\n    decorators: [{\n      type: Host\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }], {\n    expandedHeight: [{\n      type: Input\n    }],\n    collapsedHeight: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }]\n  });\n})();\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {\n  static {\n    this.ɵfac = function MatExpansionPanelDescription_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelDescription)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelDescription,\n      selectors: [[\"mat-panel-description\"]],\n      hostAttrs: [1, \"mat-expansion-panel-header-description\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelDescription, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-description',\n      host: {\n        class: 'mat-expansion-panel-header-description'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {\n  static {\n    this.ɵfac = function MatExpansionPanelTitle_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionPanelTitle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelTitle,\n      selectors: [[\"mat-panel-title\"]],\n      hostAttrs: [1, \"mat-expansion-panel-header-title\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelTitle, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-title',\n      host: {\n        class: 'mat-expansion-panel-header-title'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n  constructor() {\n    super(...arguments);\n    /** Headers belonging to this accordion. */\n    this._ownHeaders = new QueryList();\n    /** Whether the expansion indicator should be hidden. */\n    this.hideToggle = false;\n    /**\n     * Display mode used for all expansion panels in the accordion. Currently two display\n     * modes exist:\n     *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n     *     panel at a different elevation from the rest of the accordion.\n     *  flat - no spacing is placed around expanded panels, showing all panels at the same\n     *     elevation.\n     */\n    this.displayMode = 'default';\n    /** The position of the expansion indicator. */\n    this.togglePosition = 'after';\n  }\n  ngAfterContentInit() {\n    this._headers.changes.pipe(startWith(this._headers)).subscribe(headers => {\n      this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n      this._ownHeaders.notifyOnChanges();\n    });\n    this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n  }\n  /** Handles keyboard events coming in from the panel headers. */\n  _handleHeaderKeydown(event) {\n    this._keyManager.onKeydown(event);\n  }\n  _handleHeaderFocus(header) {\n    this._keyManager.updateActiveItem(header);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._keyManager?.destroy();\n    this._ownHeaders.destroy();\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatAccordion_BaseFactory;\n      return function MatAccordion_Factory(__ngFactoryType__) {\n        return (ɵMatAccordion_BaseFactory || (ɵMatAccordion_BaseFactory = i0.ɵɵgetInheritedFactory(MatAccordion)))(__ngFactoryType__ || MatAccordion);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatAccordion,\n      selectors: [[\"mat-accordion\"]],\n      contentQueries: function MatAccordion_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelHeader, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headers = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-accordion\"],\n      hostVars: 2,\n      hostBindings: function MatAccordion_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-accordion-multi\", ctx.multi);\n        }\n      },\n      inputs: {\n        hideToggle: [2, \"hideToggle\", \"hideToggle\", booleanAttribute],\n        displayMode: \"displayMode\",\n        togglePosition: \"togglePosition\"\n      },\n      exportAs: [\"matAccordion\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_ACCORDION,\n        useExisting: MatAccordion\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-accordion',\n      exportAs: 'matAccordion',\n      providers: [{\n        provide: MAT_ACCORDION,\n        useExisting: MatAccordion\n      }],\n      host: {\n        class: 'mat-accordion',\n        // Class binding which is only used by the test harness as there is no other\n        // way for the harness to detect if multiple panel support is enabled.\n        '[class.mat-accordion-multi]': 'this.multi'\n      },\n      standalone: true\n    }]\n  }], null, {\n    _headers: [{\n      type: ContentChildren,\n      args: [MatExpansionPanelHeader, {\n        descendants: true\n      }]\n    }],\n    hideToggle: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    displayMode: [{\n      type: Input\n    }],\n    togglePosition: [{\n      type: Input\n    }]\n  });\n})();\nclass MatExpansionModule {\n  static {\n    this.ɵfac = function MatExpansionModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatExpansionModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatExpansionModule,\n      imports: [MatCommonModule, CdkAccordionModule, PortalModule, MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent],\n      exports: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CdkAccordionModule, PortalModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CdkAccordionModule, PortalModule, MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent],\n      exports: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAI,WAAW;AAMf,IAAM,gBAAgB,IAAI,eAAe,cAAc;AAIvD,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,cAAc;AAEZ,SAAK,gBAAgB,IAAI,QAAQ;AAEjC,SAAK,uBAAuB,IAAI,QAAQ;AAExC,SAAK,KAAK,iBAAiB,UAAU;AAErC,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,KAAK,OAAO;AACd,WAAK,qBAAqB,KAAK,IAAI;AAAA,IACrC;AAAA,EACF;AAAA;AAAA,EAEA,WAAW;AACT,SAAK,qBAAqB,KAAK,KAAK;AAAA,EACtC;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,cAAc,KAAK,OAAO;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,SAAS;AAC5B,SAAK,qBAAqB,SAAS;AAAA,EACrC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,GAAG,CAAC,IAAI,gBAAgB,EAAE,CAAC;AAAA,MACvD,QAAQ;AAAA,QACN,OAAO,CAAC,GAAG,SAAS,SAAS,gBAAgB;AAAA,MAC/C;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA6B,oBAAoB;AAAA,IAC3D,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAGH,IAAI,SAAS;AAKb,IAAM,mBAAN,MAAM,kBAAiB;AAAA;AAAA,EAErB,IAAI,WAAW;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,SAAS,UAAU;AAErB,QAAI,KAAK,cAAc,UAAU;AAC/B,WAAK,YAAY;AACjB,WAAK,eAAe,KAAK,QAAQ;AACjC,UAAI,UAAU;AACZ,aAAK,OAAO,KAAK;AAKjB,cAAM,cAAc,KAAK,YAAY,KAAK,UAAU,KAAK,KAAK;AAC9D,aAAK,qBAAqB,OAAO,KAAK,IAAI,WAAW;AAAA,MACvD,OAAO;AACL,aAAK,OAAO,KAAK;AAAA,MACnB;AAGA,WAAK,mBAAmB,aAAa;AAAA,IACvC;AAAA,EACF;AAAA,EACA,YAAY,WAAW,oBAAoB,sBAAsB;AAC/D,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAC1B,SAAK,uBAAuB;AAE5B,SAAK,4BAA4B,aAAa;AAE9C,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,SAAS,IAAI,aAAa;AAE/B,SAAK,YAAY,IAAI,aAAa;AAMlC,SAAK,iBAAiB,IAAI,aAAa;AAEvC,SAAK,KAAK,uBAAuB,QAAQ;AACzC,SAAK,YAAY;AAEjB,SAAK,WAAW;AAEhB,SAAK,iCAAiC,MAAM;AAAA,IAAC;AAC7C,SAAK,iCAAiC,qBAAqB,OAAO,CAAC,IAAI,gBAAgB;AACrF,UAAI,KAAK,aAAa,CAAC,KAAK,UAAU,SAAS,KAAK,UAAU,OAAO,eAAe,KAAK,OAAO,IAAI;AAClG,aAAK,WAAW;AAAA,MAClB;AAAA,IACF,CAAC;AAED,QAAI,KAAK,WAAW;AAClB,WAAK,4BAA4B,KAAK,gCAAgC;AAAA,IACxE;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,SAAK,OAAO,SAAS;AACrB,SAAK,OAAO,SAAS;AACrB,SAAK,UAAU,KAAK;AACpB,SAAK,UAAU,SAAS;AACxB,SAAK,+BAA+B;AACpC,SAAK,0BAA0B,YAAY;AAAA,EAC7C;AAAA;AAAA,EAEA,SAAS;AACP,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW,CAAC,KAAK;AAAA,IACxB;AAAA,EACF;AAAA;AAAA,EAEA,QAAQ;AACN,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA,EAEA,OAAO;AACL,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA,EACA,kCAAkC;AAChC,WAAO,KAAK,UAAU,qBAAqB,UAAU,cAAY;AAE/D,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,WAAW;AAAA,MAClB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,mBAAmB;AAC/D,aAAO,KAAK,qBAAqB,mBAAqB,kBAAkB,eAAe,EAAE,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,yBAAyB,CAAC;AAAA,IAC5L;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,GAAG,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MAChE,QAAQ;AAAA,QACN,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,QACtD,UAAU,CAAC,GAAG,YAAY,YAAY,gBAAgB;AAAA,MACxD;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,gBAAgB;AAAA,MAClB;AAAA,MACA,UAAU,CAAC,kBAAkB;AAAA,MAC7B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA;AAAA;AAAA,QAGjC;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MAAC,CAAC,GAAM,wBAAwB;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA;AAAA;AAAA,QAGX;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,MAAC;AAAA,MACD,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,cAAc,gBAAgB;AAAA,MACxC,SAAS,CAAC,cAAc,gBAAgB;AAAA,IAC1C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,cAAc,gBAAgB;AAAA,MACxC,SAAS,CAAC,cAAc,gBAAgB;AAAA,IAC1C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3RH,IAAM,MAAM,CAAC,MAAM;AACnB,IAAM,MAAM,CAAC,CAAC,CAAC,4BAA4B,CAAC,GAAG,KAAK,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACxE,IAAM,MAAM,CAAC,8BAA8B,KAAK,gBAAgB;AAChE,SAAS,yCAAyC,IAAI,KAAK;AAAC;AAC5D,IAAM,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,uBAAuB,CAAC,GAAG,GAAG;AACpE,IAAM,MAAM,CAAC,mBAAmB,yBAAyB,GAAG;AAC5D,SAAS,+CAA+C,IAAI,KAAK;AAC/D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,eAAe;AAClB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,kBAAkB,CAAC;AAAA,EAC9D;AACF;AACA,IAAM,gBAAgB,IAAI,eAAe,eAAe;AAIxD,IAAM,mCAAmC;AAuBzC,IAAM,yBAAyB;AAAA;AAAA,EAE7B,iBAAiB,QAAQ,mBAAmB,CAAC,MAAM,mBAAmB,MAAM;AAAA,IAC1E,WAAW;AAAA,EACb,CAAC,CAAC,GAAG,MAAM,YAAY,MAAM;AAAA,IAC3B,WAAW;AAAA,EACb,CAAC,CAAC,GAAG,WAAW,6CAA6C,QAAQ,gCAAgC,CAAC,CAAC,CAAC;AAAA;AAAA,EAExG,eAAe,QAAQ,iBAAiB;AAAA,IAAC,MAAM,mBAAmB,MAAM;AAAA,MACtE,QAAQ;AAAA,MACR,YAAY;AAAA,IACd,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA,IAIF,MAAM,YAAY,MAAM;AAAA,MACtB,QAAQ;AAAA,MACR,YAAY;AAAA,IACd,CAAC,CAAC;AAAA,IAAG,WAAW,6CAA6C,QAAQ,gCAAgC,CAAC;AAAA,EAAC,CAAC;AAC1G;AAMA,IAAM,sBAAsB,IAAI,eAAe,qBAAqB;AAMpE,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,WAAW,iBAAiB;AACtC,SAAK,YAAY;AACjB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,kBAAqB,WAAW,GAAM,kBAAkB,qBAAqB,CAAC,CAAC;AAAA,IAC/I;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,4BAA4B,EAAE,CAAC;AAAA,MAC3D,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,IAAI,WAAW;AAKf,IAAM,sCAAsC,IAAI,eAAe,qCAAqC;AAKpG,IAAM,oBAAN,MAAM,2BAA0B,iBAAiB;AAAA;AAAA,EAE/C,IAAI,aAAa;AACf,WAAO,KAAK,eAAe,KAAK,aAAa,KAAK,UAAU;AAAA,EAC9D;AAAA,EACA,IAAI,WAAW,OAAO;AACpB,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA,EAEA,IAAI,iBAAiB;AACnB,WAAO,KAAK,mBAAmB,KAAK,aAAa,KAAK,UAAU;AAAA,EAClE;AAAA,EACA,IAAI,eAAe,OAAO;AACxB,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,YAAY,WAAW,oBAAoB,4BAA4B,mBAAmB,WAAW,gBAAgB,gBAAgB;AACnI,UAAM,WAAW,oBAAoB,0BAA0B;AAC/D,SAAK,oBAAoB;AACzB,SAAK,iBAAiB;AACtB,SAAK,cAAc;AAEnB,SAAK,cAAc,IAAI,aAAa;AAEpC,SAAK,gBAAgB,IAAI,aAAa;AAEtC,SAAK,gBAAgB,IAAI,QAAQ;AAEjC,SAAK,YAAY,8BAA8B,UAAU;AACzD,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,sBAAsB,mBAAmB;AAC9C,QAAI,gBAAgB;AAClB,WAAK,aAAa,eAAe;AAAA,IACnC;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,QAAI,KAAK,WAAW;AAClB,aAAO,KAAK,YAAY,KAAK,UAAU,gBAAgB;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,WAAW,aAAa;AAAA,EACtC;AAAA;AAAA,EAEA,SAAS;AACP,SAAK,WAAW,CAAC,KAAK;AAAA,EACxB;AAAA;AAAA,EAEA,QAAQ;AACN,SAAK,WAAW;AAAA,EAClB;AAAA;AAAA,EAEA,OAAO;AACL,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,gBAAgB,KAAK,aAAa,oBAAoB,MAAM;AAEnE,WAAK,OAAO,KAAK,UAAU,IAAI,GAAG,OAAO,MAAM,KAAK,YAAY,CAAC,KAAK,OAAO,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACvG,aAAK,UAAU,IAAI,eAAe,KAAK,aAAa,WAAW,KAAK,iBAAiB;AAAA,MACvF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,cAAc,KAAK,OAAO;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,cAAc,SAAS;AAAA,EAC9B;AAAA;AAAA,EAEA,iBAAiB;AACf,QAAI,KAAK,OAAO;AACd,YAAM,iBAAiB,KAAK,UAAU;AACtC,YAAM,cAAc,KAAK,MAAM;AAC/B,aAAO,mBAAmB,eAAe,YAAY,SAAS,cAAc;AAAA,IAC9E;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,kBAAkB,OAAO;AACvB,QAAI,CAAC,mBAAmB,KAAK,KAAK,CAAC,KAAK,uBAAuB,KAAK,OAAO;AAIzE,WAAK,OAAO,cAAc,aAAa,SAAS,EAAE;AAAA,IACpD;AAAA,EACF;AAAA;AAAA,EAEA,eAAe,OAAO;AACpB,QAAI,CAAC,mBAAmB,KAAK,GAAG;AAC9B,UAAI,MAAM,YAAY,YAAY;AAChC,aAAK,YAAY,KAAK;AAAA,MACxB,WAAW,MAAM,YAAY,aAAa;AACxC,aAAK,cAAc,KAAK;AAAA,MAC1B;AAEA,UAAI,CAAC,KAAK,uBAAuB,KAAK,OAAO;AAC3C,aAAK,MAAM,cAAc,gBAAgB,OAAO;AAAA,MAClD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,kBAAkB,eAAe,EAAE,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,yBAAyB,GAAM,kBAAqB,gBAAgB,GAAM,kBAAkB,QAAQ,GAAM,kBAAkB,uBAAuB,CAAC,GAAM,kBAAkB,qCAAqC,CAAC,CAAC;AAAA,IACtX;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,gBAAgB,SAAS,iCAAiC,IAAI,KAAK,UAAU;AAC3E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,0BAA0B,CAAC;AAAA,QACzD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,QACrE;AAAA,MACF;AAAA,MACA,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,QAAQ,GAAG;AAAA,QAC9D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,qBAAqB;AAAA,MACpC,UAAU;AAAA,MACV,cAAc,SAAS,+BAA+B,IAAI,KAAK;AAC7D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,gBAAgB,IAAI,QAAQ,EAAE,2BAA2B,IAAI,mBAAmB,EAAE,+BAA+B,IAAI,YAAY,CAAC;AAAA,QACnJ;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,QAC5D,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS;AAAA,QACP,aAAa;AAAA,QACb,eAAe;AAAA,MACjB;AAAA,MACA,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA;AAAA;AAAA,QAGjC;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QAAG;AAAA,UACD,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MAAC,CAAC,GAAM,0BAA6B,4BAA+B,sBAAyB,mBAAmB;AAAA,MAChH,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,QAAQ,UAAU,GAAG,+BAA+B,GAAG,IAAI,GAAG,CAAC,GAAG,0BAA0B,GAAG,CAAC,GAAG,iBAAiB,CAAC;AAAA,MAC7I,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB,GAAG;AACtB,UAAG,aAAa,CAAC;AACjB,UAAG,eAAe,GAAG,OAAO,GAAG,CAAC;AAChC,UAAG,WAAW,wBAAwB,SAAS,wEAAwE,QAAQ;AAC7H,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,kBAAkB,MAAM,CAAC;AAAA,UACrD,CAAC,EAAE,uBAAuB,SAAS,uEAAuE,QAAQ;AAChH,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,UAClD,CAAC;AACD,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,aAAa,GAAG,CAAC;AACpB,UAAG,WAAW,GAAG,0CAA0C,GAAG,GAAG,eAAe,CAAC;AACjF,UAAG,aAAa;AAChB,UAAG,aAAa,GAAG,CAAC;AACpB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,kBAAkB,IAAI,kBAAkB,CAAC,EAAE,MAAM,IAAI,EAAE;AACrE,UAAG,YAAY,mBAAmB,IAAI,SAAS;AAC/C,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,mBAAmB,IAAI,OAAO;AAAA,QAC9C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,eAAe;AAAA,MAC9B,QAAQ,CAAC,6qFAA+qF;AAAA,MACxrF,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,uBAAuB,aAAa;AAAA,MAClD;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY,CAAC,uBAAuB,aAAa;AAAA,MACjD,WAAW;AAAA;AAAA;AAAA,QAGX;AAAA,UACE,SAAS;AAAA,UACT,UAAU;AAAA,QACZ;AAAA,QAAG;AAAA,UACD,SAAS;AAAA,UACT,aAAa;AAAA,QACf;AAAA,MAAC;AAAA,MACD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,wBAAwB;AAAA,QACxB,mCAAmC;AAAA,QACnC,uCAAuC;AAAA,MACzC;AAAA,MACA,YAAY;AAAA,MACZ,SAAS,CAAC,eAAe;AAAA,MACzB,UAAU;AAAA,MACV,QAAQ,CAAC,6qFAA+qF;AAAA,IAC1rF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mCAAmC;AAAA,IAC5C,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAEH,SAAS,mBAAmB,OAAO;AACjC,SAAO,MAAM,cAAc;AAC7B;AAIA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA4B;AAAA,IAC/D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,MAC9B,WAAW,CAAC,GAAG,gBAAgB;AAAA,MAC/B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,YAAY,OAAO,UAAU,eAAe,oBAAoB,gBAAgB,gBAAgB,UAAU;AACxG,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,gBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,iBAAiB;AACtB,SAAK,4BAA4B,aAAa;AAE9C,SAAK,WAAW;AAChB,UAAM,4BAA4B,MAAM,YAAY,MAAM,UAAU,cAAc,KAAK,OAAO,aAAW,CAAC,EAAE,QAAQ,YAAY,KAAK,QAAQ,gBAAgB,EAAE,CAAC,IAAI;AACpK,SAAK,WAAW,SAAS,YAAY,EAAE,KAAK;AAG5C,SAAK,4BAA4B,MAAM,MAAM,QAAQ,MAAM,QAAQ,2BAA2B,MAAM,cAAc,KAAK,OAAO,aAAW;AACvI,aAAO,CAAC,EAAE,QAAQ,YAAY,KAAK,QAAQ,UAAU,KAAK,QAAQ,gBAAgB;AAAA,IACpF,CAAC,CAAC,CAAC,EAAE,UAAU,MAAM,KAAK,mBAAmB,aAAa,CAAC;AAE3D,UAAM,OAAO,KAAK,OAAO,MAAM,MAAM,eAAe,CAAC,CAAC,EAAE,UAAU,MAAM,cAAc,SAAS,UAAU,SAAS,CAAC;AACnH,QAAI,gBAAgB;AAClB,WAAK,iBAAiB,eAAe;AACrC,WAAK,kBAAkB,eAAe;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,WAAW;AACb,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,UAAU;AACR,QAAI,CAAC,KAAK,UAAU;AAClB,WAAK,MAAM,OAAO;AAAA,IACpB;AAAA,EACF;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,oBAAoB;AAClB,WAAO,KAAK,MAAM,kBAAkB;AAAA,EACtC;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,qBAAqB;AACnB,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO,CAAC,KAAK,MAAM,cAAc,CAAC,KAAK,MAAM;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAM,aAAa,KAAK,YAAY;AACpC,QAAI,cAAc,KAAK,gBAAgB;AACrC,aAAO,KAAK;AAAA,IACd,WAAW,CAAC,cAAc,KAAK,iBAAiB;AAC9C,aAAO,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,SAAS,OAAO;AACd,YAAQ,MAAM,SAAS;AAAA,MAErB,KAAK;AAAA,MACL,KAAK;AACH,YAAI,CAAC,eAAe,KAAK,GAAG;AAC1B,gBAAM,eAAe;AACrB,eAAK,QAAQ;AAAA,QACf;AACA;AAAA,MACF;AACE,YAAI,KAAK,MAAM,WAAW;AACxB,eAAK,MAAM,UAAU,qBAAqB,KAAK;AAAA,QACjD;AACA;AAAA,IACJ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ,SAAS;AACrB,QAAI,QAAQ;AACV,WAAK,cAAc,SAAS,KAAK,UAAU,QAAQ,OAAO;AAAA,IAC5D,OAAO;AACL,WAAK,SAAS,cAAc,MAAM,OAAO;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,kBAAkB;AAChB,SAAK,cAAc,QAAQ,KAAK,QAAQ,EAAE,UAAU,YAAU;AAC5D,UAAI,UAAU,KAAK,MAAM,WAAW;AAClC,aAAK,MAAM,UAAU,mBAAmB,IAAI;AAAA,MAC9C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,SAAK,0BAA0B,YAAY;AAC3C,SAAK,cAAc,eAAe,KAAK,QAAQ;AAAA,EACjD;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,kBAAkB,mBAAmB,CAAC,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,iBAAiB,GAAM,kBAAkB,qCAAqC,CAAC,GAAM,kBAAkB,uBAAuB,CAAC,GAAM,kBAAkB,UAAU,CAAC;AAAA,IAC9W;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,MAC1C,WAAW,CAAC,QAAQ,UAAU,GAAG,8BAA8B,qBAAqB;AAAA,MACpF,UAAU;AAAA,MACV,cAAc,SAAS,qCAAqC,IAAI,KAAK;AACnE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,mDAAmD;AACjF,mBAAO,IAAI,QAAQ;AAAA,UACrB,CAAC,EAAE,WAAW,SAAS,mDAAmD,QAAQ;AAChF,mBAAO,IAAI,SAAS,MAAM;AAAA,UAC5B,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,MAAM,IAAI,MAAM,SAAS,EAAE,YAAY,IAAI,WAAW,KAAK,IAAI,QAAQ,EAAE,iBAAiB,IAAI,YAAY,CAAC,EAAE,iBAAiB,IAAI,YAAY,CAAC,EAAE,iBAAiB,IAAI,MAAM,QAAQ;AACnM,UAAG,YAAY,UAAU,IAAI,iBAAiB,CAAC;AAC/C,UAAG,YAAY,gBAAgB,IAAI,YAAY,CAAC,EAAE,wCAAwC,IAAI,mBAAmB,MAAM,OAAO,EAAE,yCAAyC,IAAI,mBAAmB,MAAM,QAAQ,EAAE,2BAA2B,IAAI,mBAAmB,gBAAgB;AAAA,QACpR;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,gBAAgB;AAAA,QAChB,iBAAiB;AAAA,QACjB,UAAU,CAAC,GAAG,YAAY,YAAY,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK,CAAC;AAAA,MAC3F;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA6B,mBAAmB;AAAA,MAC9D,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,SAAS,8BAA8B,WAAW,kBAAkB,eAAe,QAAQ,aAAa,OAAO,GAAG,CAAC,KAAK,uDAAuD,CAAC;AAAA,MAC9O,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB,GAAG;AACtB,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa,GAAG,CAAC;AACpB,UAAG,aAAa,GAAG,CAAC;AACpB,UAAG,aAAa;AAChB,UAAG,WAAW,GAAG,gDAAgD,GAAG,GAAG,QAAQ,CAAC;AAAA,QAClF;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,2BAA2B,CAAC,IAAI,YAAY,CAAC;AAC5D,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,IAAI,YAAY,IAAI,IAAI,EAAE;AAAA,QAC7C;AAAA,MACF;AAAA,MACA,QAAQ,CAAC,k4HAAo4H;AAAA,MAC74H,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,uBAAuB,eAAe;AAAA,MACpD;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,YAAY,CAAC,uBAAuB,eAAe;AAAA,MACnD,MAAM;AAAA,QACJ,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,mBAAmB;AAAA,QACnB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,wBAAwB;AAAA,QACxB,gDAAgD;AAAA,QAChD,iDAAiD;AAAA,QACjD,mCAAmC;AAAA,QACnC,kBAAkB;AAAA,QAClB,WAAW;AAAA,QACX,aAAa;AAAA,MACf;AAAA,MACA,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ,CAAC,k4HAAo4H;AAAA,IAC/4H,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mCAAmC;AAAA,IAC5C,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,qBAAqB;AAAA,IAC9B,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,UAAU;AAAA,IACnB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW,WAAS,SAAS,OAAO,IAAI,gBAAgB,KAAK;AAAA,MAC/D,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAIH,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAA8B;AAAA,IACjE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,uBAAuB,CAAC;AAAA,MACrC,WAAW,CAAC,GAAG,wCAAwC;AAAA,MACvD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAAwB;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,kCAAkC;AAAA,MACjD,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAKH,IAAM,eAAN,MAAM,sBAAqB,aAAa;AAAA,EACtC,cAAc;AACZ,UAAM,GAAG,SAAS;AAElB,SAAK,cAAc,IAAI,UAAU;AAEjC,SAAK,aAAa;AASlB,SAAK,cAAc;AAEnB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,qBAAqB;AACnB,SAAK,SAAS,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,aAAW;AACxE,WAAK,YAAY,MAAM,QAAQ,OAAO,YAAU,OAAO,MAAM,cAAc,IAAI,CAAC;AAChF,WAAK,YAAY,gBAAgB;AAAA,IACnC,CAAC;AACD,SAAK,cAAc,IAAI,gBAAgB,KAAK,WAAW,EAAE,SAAS,EAAE,eAAe;AAAA,EACrF;AAAA;AAAA,EAEA,qBAAqB,OAAO;AAC1B,SAAK,YAAY,UAAU,KAAK;AAAA,EAClC;AAAA,EACA,mBAAmB,QAAQ;AACzB,SAAK,YAAY,iBAAiB,MAAM;AAAA,EAC1C;AAAA,EACA,cAAc;AACZ,UAAM,YAAY;AAClB,SAAK,aAAa,QAAQ;AAC1B,SAAK,YAAY,QAAQ;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAuB,uBAAM;AAChC,UAAI;AACJ,aAAO,SAAS,qBAAqB,mBAAmB;AACtD,gBAAQ,8BAA8B,4BAA+B,sBAAsB,aAAY,IAAI,qBAAqB,aAAY;AAAA,MAC9I;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,gBAAgB,SAAS,4BAA4B,IAAI,KAAK,UAAU;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,yBAAyB,CAAC;AAAA,QACxD;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,WAAW;AAAA,QAC9D;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,eAAe;AAAA,MAC9B,UAAU;AAAA,MACV,cAAc,SAAS,0BAA0B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,uBAAuB,IAAI,KAAK;AAAA,QACjD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY,CAAC,GAAG,cAAc,cAAc,gBAAgB;AAAA,QAC5D,aAAa;AAAA,QACb,gBAAgB;AAAA,MAClB;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC,CAAC,GAAM,0BAA6B,0BAA0B;AAAA,IACjE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA,MACf,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,OAAO;AAAA;AAAA;AAAA,QAGP,+BAA+B;AAAA,MACjC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,yBAAyB;AAAA,QAC9B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,oBAAoB,cAAc,cAAc,mBAAmB,4BAA4B,yBAAyB,wBAAwB,8BAA8B,wBAAwB;AAAA,MACjO,SAAS,CAAC,cAAc,mBAAmB,4BAA4B,yBAAyB,wBAAwB,8BAA8B,wBAAwB;AAAA,IAChL,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,iBAAiB,oBAAoB,YAAY;AAAA,IAC7D,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,oBAAoB,cAAc,cAAc,mBAAmB,4BAA4B,yBAAyB,wBAAwB,8BAA8B,wBAAwB;AAAA,MACjO,SAAS,CAAC,cAAc,mBAAmB,4BAA4B,yBAAyB,wBAAwB,8BAA8B,wBAAwB;AAAA,IAChL,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}