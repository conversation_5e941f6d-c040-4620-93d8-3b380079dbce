package otbs.ms_incident.dto;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import otbs.ms_incident.enums.StatusReparation;

import java.math.BigDecimal;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test for ReparationRequestDTO builder
 */
class ReparationRequestDTOBuilderTest {

    @Test
    void testBuilder_withAllFields() {
        // Given
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        StatusReparation status = StatusReparation.EN_COURS;
        String description = "Réparation du pare-choc";
        BigDecimal cout = new BigDecimal("1500.00");
        String garage = "Garage Central";
        Long incidentId = 1L;
        Boolean rembourse = true;
        BigDecimal montantCouverture = new BigDecimal("1000.00");

        // When
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .status(status)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .rembourse(rembourse)
                .montantCouverture(montantCouverture)
                .build();

        // Then
        assertEquals(dateReparation, dto.getDateReparation());
        assertEquals(status, dto.getStatus());
        assertEquals(description, dto.getDescription());
        assertEquals(cout, dto.getCout());
        assertEquals(garage, dto.getGarage());
        assertEquals(incidentId, dto.getIncidentId());
        assertEquals(rembourse, dto.getRembourse());
        assertEquals(montantCouverture, dto.getMontantCouverture());
    }

    @Test
    void testBuilder_withMinimalRequiredFields() {
        // Given
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = new BigDecimal("1500.00");
        Long incidentId = 1L;

        // When
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .cout(cout)
                .incidentId(incidentId)
                .build();

        // Then
        assertEquals(dateReparation, dto.getDateReparation());
        assertNull(dto.getStatus());
        assertNull(dto.getDescription());
        assertEquals(cout, dto.getCout());
        assertNull(dto.getGarage());
        assertEquals(incidentId, dto.getIncidentId());
        assertNull(dto.getRembourse());
        assertNull(dto.getMontantCouverture());
    }

    @ParameterizedTest
    @EnumSource(StatusReparation.class)
    void testBuilder_withDifferentStatusValues(StatusReparation status) {
        // Given
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = new BigDecimal("1500.00");
        Long incidentId = 1L;

        // When
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .status(status)
                .cout(cout)
                .incidentId(incidentId)
                .build();

        // Then
        assertEquals(status, dto.getStatus());
    }

    @Test
    void testBuilder_withNegativeCost() {
        // Given
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = new BigDecimal("-1500.00");
        Long incidentId = 1L;

        // When
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .cout(cout)
                .incidentId(incidentId)
                .build();

        // Then
        assertEquals(cout, dto.getCout());
    }

    @Test
    void testBuilder_withZeroCost() {
        // Given
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = BigDecimal.ZERO;
        Long incidentId = 1L;

        // When
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .cout(cout)
                .incidentId(incidentId)
                .build();

        // Then
        assertEquals(cout, dto.getCout());
    }

    @Test
    void testBuilder_withRembourseTrue() {
        // Given
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = new BigDecimal("1500.00");
        Long incidentId = 1L;
        Boolean rembourse = true;

        // When
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .cout(cout)
                .incidentId(incidentId)
                .rembourse(rembourse)
                .build();

        // Then
        assertTrue(dto.getRembourse());
    }

    @Test
    void testBuilder_withRembourseNull() {
        // Given
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = new BigDecimal("1500.00");
        Long incidentId = 1L;
        Boolean rembourse = null;

        // When
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(dateReparation)
                .cout(cout)
                .incidentId(incidentId)
                .rembourse(rembourse)
                .build();

        // Then
        assertNull(dto.getRembourse());
    }

    @Test
    void testToString_shouldNotThrowException() {
        // Given
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .incidentId(1L)
                .build();

        // When & Then
        assertDoesNotThrow(() -> {
            String result = dto.toString();
            assertNotNull(result);
            assertFalse(result.isEmpty());
        });
    }

    @Test
    void testEquals_withSameObject() {
        // Given
        ReparationRequestDTO dto = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .incidentId(1L)
                .build();

        // When & Then
        assertEquals(dto, dto);
    }

    @Test
    void testEquals_withEqualObject() {
        // Given
        ReparationRequestDTO dto1 = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .incidentId(1L)
                .build();

        ReparationRequestDTO dto2 = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .incidentId(1L)
                .build();

        // When & Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEquals_withDifferentObject() {
        // Given
        ReparationRequestDTO dto1 = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .incidentId(1L)
                .build();

        ReparationRequestDTO dto2 = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("2000.00"))
                .incidentId(1L)
                .build();

        // When & Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }
}
