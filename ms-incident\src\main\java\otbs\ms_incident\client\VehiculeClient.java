package otbs.ms_incident.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(name = "vehicule", url = "${vehicule.service.url:http://10.112.63.191:9003}", fallback = VehiculeClientFallback.class)
public interface VehiculeClient {

    @GetMapping("/api/v1/vehicule/{idVehicule}")
    VehiculeDto getVehiculeById(@PathVariable("idVehicule") Long id);

    @GetMapping("/api/v1/vehicule/findByImmatriculation/{immatriculation}")
    VehiculeDto getVehiculeByImmatriculation(@PathVariable("immatriculation") String immatriculation);

    @GetMapping("/api/v1/vehicule/allVehicule")
    List<VehiculeDto> getAllVehicules();
}
