package otbs.ms_incident.config;

import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

import java.util.Collections;
import java.util.List;
import java.util.Locale;

/**
 * Configuration pour l'internationalisation des messages
 */
@Configuration
public class MessageConfig {

    /**
     * Configure le résolveur de locale basé sur l'en-tête Accept-Language
     * 
     * @return LocaleResolver configuré
     */
    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver resolver = new AcceptHeaderLocaleResolver();
        
        // Définition du français comme langue par défaut
        resolver.setDefaultLocale(Locale.FRENCH);
        
        // Définition du français comme seule langue supportée
        List<Locale> supportedLocales = Collections.singletonList(Locale.FRENCH);
        resolver.setSupportedLocales(supportedLocales);
        
        return resolver;
    }

    /**
     * Configure la source des messages pour l'internationalisation
     * 
     * @return MessageSource configuré
     */
    @Bean
    public MessageSource messageSource() {
        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
        messageSource.setBasenames("messages");
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setFallbackToSystemLocale(false); // Ne pas utiliser la locale système comme fallback
        messageSource.setUseCodeAsDefaultMessage(true); // Utiliser le code comme message par défaut si non trouvé
        return messageSource;
    }
} 