package otbs.ms_incident.client;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class VehiculeClientFallbackTest {

    @InjectMocks
    private VehiculeClientFallback fallback;

    @BeforeEach
    void setUp() {
        fallback = new VehiculeClientFallback();
    }

    @Test
    void getVehiculeById_shouldReturnDefaultVehicule() {
        // Given
        Long id = 123L;

        // When
        VehiculeDto result = fallback.getVehiculeById(id);

        // Then
        assertNotNull(result);
        assertEquals(id, result.getIdVehicule());
        assertEquals("Indisponible", result.getMarque());
        assertEquals("Indisponible", result.getModele());
        assertEquals("INDISPONIBLE", result.getEtat());
        assertEquals("Indisponible", result.getCategorie());
        assertEquals(0, result.getKilometrage());
        assertNotNull(result.getDatePrelevementKilometrage());
        assertEquals("Indisponible", result.getTypeCarburant());
        assertEquals(0, result.getNombreDePlaces());
        assertFalse(result.isRemisCles());
        assertFalse(result.isAstreinte());
    }

    @Test
    void getVehiculeById_withNullId_shouldReturnDefaultVehicule() {
        // Given
        Long id = null;

        // When
        VehiculeDto result = fallback.getVehiculeById(id);

        // Then
        assertNotNull(result);
        assertNull(result.getIdVehicule());
        assertEquals("Indisponible", result.getImmatriculation());
        assertEquals("Indisponible", result.getMarque());
        assertEquals("Indisponible", result.getModele());
        assertEquals("INDISPONIBLE", result.getEtat());
    }

    @Test
    void getVehiculeByImmatriculation_shouldReturnDefaultVehicule() {
        // Given
        String immatriculation = "AB-123-CD";

        // When
        VehiculeDto result = fallback.getVehiculeByImmatriculation(immatriculation);

        // Then
        assertNotNull(result);
        assertNull(result.getIdVehicule());
        assertEquals(immatriculation, result.getImmatriculation());
        assertEquals("Indisponible", result.getMarque());
        assertEquals("Indisponible", result.getModele());
        assertEquals("INDISPONIBLE", result.getEtat());
        assertEquals("Indisponible", result.getCategorie());
        assertEquals(0, result.getKilometrage());
        assertNotNull(result.getDatePrelevementKilometrage());
        assertEquals("Indisponible", result.getTypeCarburant());
        assertEquals(0, result.getNombreDePlaces());
        assertFalse(result.isRemisCles());
        assertFalse(result.isAstreinte());
    }

    @Test
    void getVehiculeByImmatriculation_withNullImmatriculation_shouldReturnDefaultVehicule() {
        // Given
        String immatriculation = null;

        // When
        VehiculeDto result = fallback.getVehiculeByImmatriculation(immatriculation);

        // Then
        assertNotNull(result);
        assertNull(result.getIdVehicule());
        assertEquals("Indisponible", result.getImmatriculation());
        assertEquals("Indisponible", result.getMarque());
        assertEquals("Indisponible", result.getModele());
        assertEquals("INDISPONIBLE", result.getEtat());
    }

    @Test
    void getAllVehicules_shouldReturnEmptyList() {
        // When
        List<VehiculeDto> result = fallback.getAllVehicules();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void createDefaultVehicule_shouldSetAllFields() {
        // Given
        Long id = 123L;

        // When
        VehiculeDto result = fallback.getVehiculeById(id);
        Date datePrelevement = result.getDatePrelevementKilometrage();

        // Then
        assertNotNull(result);
        assertEquals(id, result.getIdVehicule());
        assertEquals("Indisponible", result.getImmatriculation()); // Immatriculation is set to default when using getVehiculeById
        assertEquals("Indisponible", result.getMarque());
        assertEquals("Indisponible", result.getModele());
        assertEquals("INDISPONIBLE", result.getEtat());
        assertEquals("Indisponible", result.getCategorie());
        assertEquals(0, result.getKilometrage());
        assertNotNull(datePrelevement);
        assertTrue(datePrelevement.getTime() <= new Date().getTime()); // Date should be now or in the past
        assertEquals("Indisponible", result.getTypeCarburant());
        assertEquals(0, result.getNombreDePlaces());
        assertFalse(result.isRemisCles());
        assertFalse(result.isAstreinte());
    }
}
