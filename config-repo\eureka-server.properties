#-----------------Applications---------------#
server.port=9102
#-----------------Eureka---------------------#

eureka.datacenter=${EUREKA_DC:Main}

eureka.environment=${EUREKA_ENV:prod}

eureka.instance.hostname=${EUREKA_HOSTNAME:parc-auto}

eureka.client.serviceUrl.defaultZone=${EUREKA_SERVICE:http://*************:9102/eureka/}

eureka.client.register-with-eureka=false

eureka.client.fetch-registry=false


#-----------------log info-------------------#

logging.level.com.netflix.eureka=OFF

logging.level.com.netflix.discovery=OFF

eureka.server.renewalPercentThreshold=0.5
