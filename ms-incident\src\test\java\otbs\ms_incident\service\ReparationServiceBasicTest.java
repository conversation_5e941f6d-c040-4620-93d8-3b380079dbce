package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.enums.StatusReparation;
import otbs.ms_incident.mapper.ReparationMapper;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
 class ReparationServiceBasicTest {

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationMapper reparationMapper;

    @InjectMocks
    private ReparationServiceImpl reparationService;

    private Reparation reparation1, reparation2, reparation3;
    private ReparationResponseDTO responseDTO1, responseDTO2, responseDTO3;

    @BeforeEach
    void setUp() {
        Incident incident = new Incident();
        incident.setId(1L);

        // Setup test reparations
        reparation1 = new Reparation();
        reparation1.setId(1L);
        reparation1.setCout(new BigDecimal("1000.00"));
        reparation1.setStatus(StatusReparation.EN_COURS);
        reparation1.setGarage("Garage A");
        reparation1.setDateReparation(LocalDate.of(2023, 5, 15));
        reparation1.setIncident(incident);

        reparation2 = new Reparation();
        reparation2.setId(2L);
        reparation2.setCout(new BigDecimal("2000.00"));
        reparation2.setStatus(StatusReparation.TERMINEE);
        reparation2.setGarage("Garage B");
        reparation2.setDateReparation(LocalDate.of(2023, 6, 20));
        reparation2.setIncident(incident);

        reparation3 = new Reparation();
        reparation3.setId(3L);
        reparation3.setCout(new BigDecimal("3000.00"));
        reparation3.setStatus(StatusReparation.TERMINEE);
        reparation3.setGarage("Garage C");
        reparation3.setDateReparation(LocalDate.of(2023, 7, 10));
        reparation3.setIncident(incident);

        // Setup response DTOs
        responseDTO1 = new ReparationResponseDTO();
        responseDTO1.setId(1L);
        responseDTO1.setCout(new BigDecimal("1000.00"));
        responseDTO1.setStatus(StatusReparation.EN_COURS);
        responseDTO1.setGarage("Garage A");
        responseDTO1.setIncidentId(1L);

        responseDTO2 = new ReparationResponseDTO();
        responseDTO2.setId(2L);
        responseDTO2.setCout(new BigDecimal("2000.00"));
        responseDTO2.setStatus(StatusReparation.TERMINEE);
        responseDTO2.setGarage("Garage B");
        responseDTO2.setIncidentId(1L);

        responseDTO3 = new ReparationResponseDTO();
        responseDTO3.setId(3L);
        responseDTO3.setCout(new BigDecimal("3000.00"));
        responseDTO3.setStatus(StatusReparation.TERMINEE);
        responseDTO3.setGarage("Garage C");
        responseDTO3.setIncidentId(1L);
    }

    @Test
    void getReparationsByStatus_shouldReturnFilteredReparations() {
        // Given
        List<Reparation> reparations = List.of(reparation1);
        when(reparationRepository.findByStatus(StatusReparation.EN_COURS)).thenReturn(reparations);
        when(reparationMapper.toDTOList(reparations)).thenReturn(List.of(responseDTO1));

        // When
        List<ReparationResponseDTO> result = reparationService.getReparationsByStatus(StatusReparation.EN_COURS);

        // Then
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
    }

    @Test
    void getReparationsByIncidentId_shouldReturnFilteredReparations() {
        // Given
        List<Reparation> reparations = Arrays.asList(reparation1, reparation2);
        when(incidentRepository.existsById(1L)).thenReturn(true);
        when(reparationRepository.findByIncidentId(1L)).thenReturn(reparations);
        when(reparationMapper.toDTOList(reparations)).thenReturn(Arrays.asList(responseDTO1, responseDTO2));

        // When
        List<ReparationResponseDTO> result = reparationService.getReparationsByIncidentId(1L);

        // Then
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0).getId());
        assertEquals(2L, result.get(1).getId());
    }

    @Test
    void getAverageCost_shouldCalculateCorrectAverage() {
        // Given
        List<Reparation> reparations = Arrays.asList(reparation1, reparation2, reparation3);
        when(reparationRepository.findAll()).thenReturn(reparations);

        // When
        BigDecimal result = reparationService.getAverageCost();

        // Then
        assertEquals(new BigDecimal("2000.00"), result);
    }

    @Test
    void getAverageCost_shouldReturnZero_whenNoReparations() {
        // Given
        when(reparationRepository.findAll()).thenReturn(List.of());

        // When
        BigDecimal result = reparationService.getAverageCost();

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void getRepairCountByStatus_shouldReturnCorrectCounts() {
        // Given
        List<Reparation> reparations = Arrays.asList(reparation1, reparation2, reparation3);
        when(reparationRepository.findAll()).thenReturn(reparations);

        // When
        Map<String, Long> result = reparationService.getRepairCountByStatus();

        // Then
        assertEquals(2, result.size());
        assertEquals(1L, result.get("EN_COURS"));
        assertEquals(2L, result.get("TERMINEE"));
    }

    @Test
    void getRepairCountByStatus_shouldReturnEmptyMap_whenNoReparations() {
        // Given
        when(reparationRepository.findAll()).thenReturn(List.of());

        // When
        Map<String, Long> result = reparationService.getRepairCountByStatus();

        // Then
        assertTrue(result.isEmpty());
    }
}
