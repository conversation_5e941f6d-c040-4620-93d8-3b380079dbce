package otbs.ms_astreint.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import otbs.ms_astreint.client.keycloak.KeycloakService;
import otbs.ms_astreint.config.RabbitMQPublisherConfig;
import otbs.ms_astreint.dto.notification.AstreinteEvent;
import otbs.ms_astreint.model.Astreinte;
import otbs.ms_astreint.model.ConsultantAstreinte;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationEventPublisher {

    private final RabbitTemplate rabbitTemplate;
    private final KeycloakService keycloakService;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");

    public void publishAstreinteCreated(Astreinte astreinte) {
        try {
            log.info("Publication événement astreinte créée: {}", astreinte.getIdAstreinte());

            // Récupérer les IDs des consultants assignés
            List<String> consultantsIds = getConsultantsIds(astreinte);
            
            if (consultantsIds.isEmpty()) {
                log.warn("Aucun consultant assigné à l'astreinte {}, aucune notification envoyée", astreinte.getIdAstreinte());
                return;
            }

            List<String> consultantsNoms = getConsultantsNames(consultantsIds);

            AstreinteEvent event = new AstreinteEvent(
                "ASTREINTE_CREATED",
                astreinte.getIdAstreinte(),
                consultantsIds,
                "Nouvelle astreinte assignée",
                String.format("Vous avez été assigné(e) à une nouvelle astreinte du %s au %s. " +
                             "Description: %s",
                             astreinte.getDateDebut().format(DATE_FORMATTER),
                             astreinte.getDateFin().format(DATE_FORMATTER),
                             astreinte.getDescription() != null ? astreinte.getDescription() : "Aucune description")
            );

            enrichirEvenementAstreinte(event, astreinte, consultantsNoms);

            rabbitTemplate.convertAndSend(
                RabbitMQPublisherConfig.NOTIFICATIONS_EXCHANGE,
                RabbitMQPublisherConfig.ASTREINTE_CREATED_ROUTING_KEY,
                event
            );

            log.info("Événement astreinte créée publié avec succès: {} pour {} consultants: {}",
                    event.getEventId(), consultantsIds.size(), consultantsIds);

        } catch (Exception e) {
            log.error("Erreur lors de la publication de l'événement astreinte créée: {}", astreinte.getIdAstreinte(), e);
        }
    }


    public void publishAstreinteUpdated(Astreinte astreinte) {
        try {
            log.info("Publication événement astreinte mise à jour: {}", astreinte.getIdAstreinte());

            List<String> consultantsIds = getConsultantsIds(astreinte);
            
            if (consultantsIds.isEmpty()) {
                log.warn("Aucun consultant assigné à l'astreinte {}, aucune notification envoyée", astreinte.getIdAstreinte());
                return;
            }

            List<String> consultantsNoms = getConsultantsNames(consultantsIds);

            AstreinteEvent event = new AstreinteEvent(
                "ASTREINTE_UPDATED",
                astreinte.getIdAstreinte(),
                consultantsIds,
                "Astreinte modifiée",
                String.format("Votre astreinte du %s au %s a été modifiée. " +
                             "Description: %s",
                             astreinte.getDateDebut().format(DATE_FORMATTER),
                             astreinte.getDateFin().format(DATE_FORMATTER),
                             astreinte.getDescription() != null ? astreinte.getDescription() : "Aucune description")
            );

            enrichirEvenementAstreinte(event, astreinte, consultantsNoms);

            rabbitTemplate.convertAndSend(
                RabbitMQPublisherConfig.NOTIFICATIONS_EXCHANGE,
                RabbitMQPublisherConfig.ASTREINTE_UPDATED_ROUTING_KEY,
                event
            );

            log.info("Événement astreinte mise à jour publié avec succès: {} pour {} consultants: {}",
                    event.getEventId(), consultantsIds.size(), consultantsIds);

        } catch (Exception e) {
            log.error("Erreur lors de la publication de l'événement astreinte mise à jour: {}", astreinte.getIdAstreinte(), e);
        }
    }

    public void publishAstreinteDeleted(Astreinte astreinte) {
        try {
            log.info("Publication événement astreinte supprimée: {}", astreinte.getIdAstreinte());

            List<String> consultantsIds = getConsultantsIds(astreinte);
            
            if (consultantsIds.isEmpty()) {
                log.info("Aucun consultant assigné à l'astreinte supprimée {}, aucune notification envoyée", astreinte.getIdAstreinte());
                return;
            }

            List<String> consultantsNoms = getConsultantsNames(consultantsIds);

            AstreinteEvent event = new AstreinteEvent(
                "ASTREINTE_DELETED",
                astreinte.getIdAstreinte(),
                consultantsIds,
                "Astreinte annulée",
                String.format("Votre astreinte du %s au %s a été annulée. " +
                             "Description: %s",
                             astreinte.getDateDebut().format(DATE_FORMATTER),
                             astreinte.getDateFin().format(DATE_FORMATTER),
                             astreinte.getDescription() != null ? astreinte.getDescription() : "Aucune description")
            );

            enrichirEvenementAstreinte(event, astreinte, consultantsNoms);

            rabbitTemplate.convertAndSend(
                RabbitMQPublisherConfig.NOTIFICATIONS_EXCHANGE,
                RabbitMQPublisherConfig.ASTREINTE_DELETED_ROUTING_KEY,
                event
            );

            log.info("Événement astreinte supprimée publié avec succès: {} pour {} consultants: {}",
                    event.getEventId(), consultantsIds.size(), consultantsIds);

        } catch (Exception e) {
            log.error("Erreur lors de la publication de l'événement astreinte supprimée: {}", astreinte.getIdAstreinte(), e);
        }
    }

    private void enrichirEvenementAstreinte(AstreinteEvent event, Astreinte astreinte, List<String> consultantsNoms) {
        event.setAstreinteId(astreinte.getIdAstreinte());
        event.setDescriptionAstreinte(astreinte.getDescription());
        
        event.setConsultantsAssignes(getConsultantsIds(astreinte));

        event.getMetadata().put("reference", astreinte.getIdAstreinte());
        event.getMetadata().put("description", astreinte.getDescription());
        
        if (astreinte.getDateDebut() != null) {
            event.getMetadata().put("dateDebut", astreinte.getDateDebut().format(DATE_FORMATTER));
            event.getMetadata().put("dateDebutObj", astreinte.getDateDebut());
        }
        if (astreinte.getDateFin() != null) {
            event.getMetadata().put("dateFin", astreinte.getDateFin().format(DATE_FORMATTER));
            event.getMetadata().put("dateFinObj", astreinte.getDateFin());
        }
        
        event.getMetadata().put("consultantsNoms", consultantsNoms);
        event.getMetadata().put("nombreConsultants", consultantsNoms.size());
        
        List<String> niveaux = astreinte.getConsultants().stream()
                .map(c -> c.getNiveauAstreinte().toString())
                .collect(Collectors.toList());
        event.getMetadata().put("niveauxAstreinte", niveaux);
    }

    
    private List<String> getConsultantsIds(Astreinte astreinte) {
        if (astreinte.getConsultants() == null) {
            return new ArrayList<>();
        }
        
        return astreinte.getConsultants().stream()
                .map(ConsultantAstreinte::getConsultant)
                .collect(Collectors.toList());
    }

   
    private List<String> getConsultantsNames(List<String> consultantsIds) {
        return consultantsIds.stream()
                .map(id -> {
                    try {
                        return keycloakService.getFullNameFromUserId(id);
                    } catch (Exception e) {
                        log.warn("Impossible de récupérer le nom pour l'utilisateur {}: {}", id, e.getMessage());
                        return id; // Fallback sur l'ID
                    }
                })
                .collect(Collectors.toList());
    }
} 