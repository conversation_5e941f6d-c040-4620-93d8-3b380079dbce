package otbs.ms_incident.dto;

import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "DTO pour la création ou mise à jour d'un incident")
public class IncidentRequestDTO {
    @Schema(description = "Identifiant du véhicule associé à cet incident", example = "1")
    private Long vehiculeId;

    @Schema(description = "Date à laquelle l'incident s'est produit", example = "2023-05-15", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "La date est obligatoire")
    private LocalDate date;

    @Schema(description = "Type d'incident", example = "ACCIDENT", allowableValues = {"ACCIDENT", "PANNE"}, requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @NotNull(message = "Le type d'incident est obligatoire")
    private TypeIncident type;

    @Schema(description = "Statut de l'incident", example = "A_TRAITER", allowableValues = {"A_TRAITER", "EN_COURS_TRAITEMENT", "RESOLU"})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private StatusIncident status;

    @Schema(description = "Niveau de priorité de l'incident", example = "FAIBLE", allowableValues = {"CRITIQUE", "MOYEN", "FAIBLE"})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Builder.Default
    private NiveauPrioriteIncident priorite = NiveauPrioriteIncident.FAIBLE;

    @Schema(description = "Lieu où l'incident s'est produit", example = "Rue de la Paix, Paris", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "Le lieu est obligatoire")
    @Size(max = 255, message = "Le lieu ne peut pas dépasser 255 caractères")
    private String lieu;

    @Schema(description = "Description détaillée de l'incident", example = "Collision latérale avec un autre véhicule")
    @Size(max = 1000, message = "La description ne peut pas dépasser 1000 caractères")
    private String description;

    @Schema(description = "Chemin vers le constat d'accident ou rapport technique", example = "incidents/123/constats/constat_abc123.pdf")
    private String constat;

    @Schema(description = "Liste des chemins vers les photos de l'incident", example = "[\"incidents/123/photos/photo_abc123.jpg\"]")
    @Builder.Default
    private List<String> photos = new ArrayList<>();

    @Schema(description = "Liste des identifiants des réparations associées à cet incident", example = "[1, 2, 3]")
    @Builder.Default
    private List<Long> reparationIds = new ArrayList<>();
}