package otbs.ms_incident.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import otbs.ms_incident.client.VehiculeClient;
import otbs.ms_incident.client.VehiculeDto;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.dto.dashboard.*;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.enums.TypeIncident;
import otbs.ms_incident.mapper.IncidentMapper;
import otbs.ms_incident.mapper.ReparationMapper;
import otbs.ms_incident.repository.DashboardRepository;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Implémentation des services pour le dashboard
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DashboardServiceImpl implements DashboardService {

    private static final String ERROR_VEHICLE_IMMATRICULATION = "Erreur lors de la récupération des immatriculations des véhicules";
    private static final String DEFAULT_IMMATRICULATION = "N/A";
    private static final String DEFAULT_FALLBACK_IMMATRICULATION = "123TU4567";
    private static final int RECENT_ITEMS_LIMIT = 5;

    // Constantes pour le dashboard

    private final DashboardRepository dashboardRepository;
    private final IncidentRepository incidentRepository;
    private final ReparationRepository reparationRepository;
    private final IncidentMapper incidentMapper;
    private final ReparationMapper reparationMapper;
    private final VehiculeClient vehiculeClient;

    @Override
    @Transactional(readOnly = true)
    public DashboardSummaryDTO getDashboardSummary(LocalDate debut, LocalDate fin, Long vehiculeId) {
        log.info("Service request to get dashboard summary for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        try {
            // Calculer la période précédente de même durée pour les comparaisons
            long daysBetween = ChronoUnit.DAYS.between(debut, fin) + 1;
            LocalDate previousPeriodEnd = debut.minusDays(1);
            LocalDate previousPeriodStart = previousPeriodEnd.minusDays(daysBetween - 1);

            // Récupérer les statistiques pour la période actuelle
            DashboardSummaryDTO currentPeriod = fetchCurrentPeriodData(debut, fin, vehiculeId);

            // S'assurer que les valeurs BigDecimal ne sont pas nulles
            currentPeriod.ensureNonNullValues();

            // Calculer les variations avec la période précédente
            calculatePeriodChanges(currentPeriod, previousPeriodStart, previousPeriodEnd, vehiculeId);

            return currentPeriod;
        } catch (Exception e) {
            log.error("Error retrieving dashboard summary: {}", e.getMessage(), e);
            return createEmptyDashboardSummary();
        }
    }

    /**
     * Récupère les données pour la période actuelle
     */
    private DashboardSummaryDTO fetchCurrentPeriodData(LocalDate debut, LocalDate fin, Long vehiculeId) {
        try {
            DashboardSummaryDTO currentPeriod = dashboardRepository.getDashboardSummary(debut, fin, vehiculeId);
            if (currentPeriod == null) {
                log.warn("Query returned null for current period, creating empty DTO");
                return createEmptyDashboardSummary();
            }
            return currentPeriod;
        } catch (Exception e) {
            log.error("Error executing query for current period: {}", e.getMessage(), e);
            return createEmptyDashboardSummary();
        }
    }

    /**
     * Calcule les variations entre la période actuelle et la période précédente
     */
    private void calculatePeriodChanges(DashboardSummaryDTO currentPeriod, LocalDate previousPeriodStart,
                                       LocalDate previousPeriodEnd, Long vehiculeId) {
        try {
            // Récupérer les statistiques pour la période précédente
            DashboardSummaryDTO previousPeriod = dashboardRepository.getDashboardSummary(previousPeriodStart, previousPeriodEnd, vehiculeId);

            // Si la requête retourne null, créer un DTO vide
            if (previousPeriod == null) {
                log.warn("Query returned null for previous period, setting changes to 0");
                setZeroChanges(currentPeriod);
                return;
            }

            // S'assurer que les valeurs BigDecimal ne sont pas nulles
            previousPeriod.ensureNonNullValues();

            // Calculer les variations de manière sécurisée
            double totalIncidentsChange = calculatePercentageChange(previousPeriod.getTotalIncidents(), currentPeriod.getTotalIncidents());
            double openIncidentsChange = calculatePercentageChange(previousPeriod.getOpenIncidents(), currentPeriod.getOpenIncidents());
            double totalReparationsChange = calculatePercentageChange(previousPeriod.getTotalReparations(), currentPeriod.getTotalReparations());
            double inProgressReparationsChange = calculatePercentageChange(previousPeriod.getInProgressReparations(), currentPeriod.getInProgressReparations());
            double completedReparationsChange = calculatePercentageChange(previousPeriod.getCompletedReparations(), currentPeriod.getCompletedReparations());
            double totalCostChange = calculatePercentageChange(previousPeriod.getTotalCost().doubleValue(), currentPeriod.getTotalCost().doubleValue());

            // Mettre à jour les variations dans le DTO
            currentPeriod.setTotalIncidentsChange(totalIncidentsChange);
            currentPeriod.setOpenIncidentsChange(openIncidentsChange);
            currentPeriod.setTotalReparationsChange(totalReparationsChange);
            currentPeriod.setInProgressReparationsChange(inProgressReparationsChange);
            currentPeriod.setCompletedReparationsChange(completedReparationsChange);
            currentPeriod.setTotalCostChange(totalCostChange);
        } catch (Exception e) {
            log.error("Error calculating changes: {}", e.getMessage(), e);
            // En cas d'erreur, définir toutes les variations à 0
            setZeroChanges(currentPeriod);
        }
    }



    /**
     * Crée un objet DashboardSummaryDTO vide avec toutes les valeurs à 0
     */
    private DashboardSummaryDTO createEmptyDashboardSummary() {
        DashboardSummaryDTO dto = new DashboardSummaryDTO();
        dto.setTotalIncidents(0);
        dto.setOpenIncidents(0);
        dto.setInProgressIncidents(0);
        dto.setResolvedIncidents(0);
        dto.setHighPriorityIncidents(0);
        dto.setTotalReparations(0);
        dto.setInProgressReparations(0);
        dto.setCompletedReparations(0);
        dto.setTotalCost(BigDecimal.ZERO);
        dto.setAverageCost(BigDecimal.ZERO);
        setZeroChanges(dto);
        return dto;
    }

    /**
     * Définit toutes les variations à 0
     */
    private void setZeroChanges(DashboardSummaryDTO dto) {
        dto.setTotalIncidentsChange(0.0);
        dto.setOpenIncidentsChange(0.0);
        dto.setTotalReparationsChange(0.0);
        dto.setInProgressReparationsChange(0.0);
        dto.setCompletedReparationsChange(0.0);
        dto.setTotalCostChange(0.0);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IncidentResponseDTO> getRecentIncidents(LocalDate debut, LocalDate fin, Long vehiculeId) {
        log.info("Service request to get recent incidents for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        // Récupérer les incidents récents
        List<Incident> incidents = dashboardRepository.getRecentIncidents(debut, fin, vehiculeId, RECENT_ITEMS_LIMIT);
        List<IncidentResponseDTO> incidentDTOs = incidentMapper.toResponseDTOList(incidents);

        // Enrichir les incidents avec les immatriculations des véhicules
        enrichIncidentsWithVehicleInfo(incidentDTOs);

        return incidentDTOs;
    }

    @Override
    @Transactional(readOnly = true)
    public List<ReparationResponseDTO> getRecentRepairs(LocalDate debut, LocalDate fin, Long vehiculeId) {
        log.info("Service request to get recent repairs for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        // Récupérer les réparations dans la plage de dates
        List<Reparation> allReparations = reparationRepository.findByDateReparationBetween(debut, fin);

        // Filtrer par véhicule si un ID est fourni
        List<Reparation> filteredReparations = allReparations;
        if (vehiculeId != null) {
            filteredReparations = allReparations.stream()
                    .filter(r -> vehiculeId.equals(r.getIncident().getVehiculeId()))
                    .toList();
        }

        // Trier par date (les plus récents d'abord) et limiter à 5
        List<Reparation> recentReparations = filteredReparations.stream()
                .sorted(Comparator.comparing(Reparation::getDateReparation).reversed())
                .limit(RECENT_ITEMS_LIMIT)
                .toList();

        List<ReparationResponseDTO> reparationDTOs = reparationMapper.toDTOList(recentReparations);

        // Enrichir les réparations avec les immatriculations des véhicules
        enrichRepairsWithVehicleInfo(reparationDTOs);

        return reparationDTOs;
    }

    @Override
    @Transactional(readOnly = true)
    public IncidentStatusDistributionDTO getIncidentStatusDistribution(LocalDate debut, LocalDate fin, Long vehiculeId) {
        log.info("Service request to get incident status distribution for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        return dashboardRepository.getIncidentStatusDistribution(debut, fin, vehiculeId);
    }

    @Override
    @Transactional(readOnly = true)
    public IncidentTypeDistributionDTO getIncidentTypeDistribution(LocalDate debut, LocalDate fin, Long vehiculeId) {
        log.info("Service request to get incident type distribution for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        List<Object[]> results = dashboardRepository.getIncidentTypeDistribution(debut, fin, vehiculeId);
        Map<String, Long> countByType = new HashMap<>();

        for (Object[] result : results) {
            TypeIncident type = (TypeIncident) result[0];
            Long count = (Long) result[1];
            countByType.put(type.toString(), count);
        }

        return IncidentTypeDistributionDTO.builder()
                .countByType(countByType)
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public VehicleIncidentDistributionDTO getVehicleIncidentDistribution(LocalDate debut, LocalDate fin, Long vehiculeId) {
        log.info("Service request to get vehicle incident distribution for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        List<Object[]> results = dashboardRepository.getIncidentCountByVehicle(debut, fin, vehiculeId);
        Map<Long, Long> countByVehicle = new HashMap<>();

        for (Object[] result : results) {
            Long vehicleId = (Long) result[0];
            Long count = (Long) result[1];
            countByVehicle.put(vehicleId, count);
        }

        // Récupérer les immatriculations des véhicules
        Map<Long, String> vehicleImmatriculations = getVehicleImmatriculations(countByVehicle.keySet());

        return VehicleIncidentDistributionDTO.builder()
                .countByVehicle(countByVehicle)
                .vehicleImmatriculations(vehicleImmatriculations)
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public VehicleIncidentTypeDistributionDTO getVehicleIncidentTypeDistribution(LocalDate debut, LocalDate fin, Long vehiculeId) {
        log.info("Service request to get vehicle incident type distribution for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        List<Object[]> results = dashboardRepository.getIncidentCountByVehicleAndType(debut, fin, vehiculeId);
        Map<Long, Map<String, Long>> countByVehicleAndType = new HashMap<>();

        for (Object[] result : results) {
            Long vehicleId = (Long) result[0];
            TypeIncident type = (TypeIncident) result[1];
            Long count = (Long) result[2];

            countByVehicleAndType.computeIfAbsent(vehicleId, k -> new HashMap<>());
            countByVehicleAndType.get(vehicleId).put(type.toString(), count);
        }

        // Récupérer les immatriculations des véhicules
        Map<Long, String> vehicleImmatriculations = getVehicleImmatriculations(countByVehicleAndType.keySet());

        return VehicleIncidentTypeDistributionDTO.builder()
                .countByVehicleAndType(countByVehicleAndType)
                .vehicleImmatriculations(vehicleImmatriculations)
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public RepairCostByVehicleDTO getRepairCostByVehicle(LocalDate debut, LocalDate fin, Long vehiculeId) {
        log.info("Service request to get repair cost by vehicle for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        // Récupérer les réparations dans la plage de dates
        List<Reparation> reparations = reparationRepository.findByDateReparationBetween(debut, fin);

        // Filtrer par véhicule si un ID est fourni
        if (vehiculeId != null) {
            reparations = reparations.stream()
                    .filter(r -> vehiculeId.equals(r.getIncident().getVehiculeId()))
                    .toList();
        }

        Map<Long, Map<String, BigDecimal>> costByVehicleAndStatus = new HashMap<>();

        for (Reparation reparation : reparations) {
            Long reparationVehiculeId = reparation.getIncident().getVehiculeId();
            if (reparationVehiculeId == null) continue;

            String status = reparation.getStatus().toString();
            BigDecimal cout = reparation.getCout();

            costByVehicleAndStatus.computeIfAbsent(reparationVehiculeId, k -> new HashMap<>());
            costByVehicleAndStatus.get(reparationVehiculeId).merge(status, cout, BigDecimal::add);
        }

        // Récupérer les immatriculations des véhicules
        Map<Long, String> vehicleImmatriculations = getVehicleImmatriculations(costByVehicleAndStatus.keySet());

        return RepairCostByVehicleDTO.builder()
                .costByVehicleAndStatus(costByVehicleAndStatus)
                .vehicleImmatriculations(vehicleImmatriculations)
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public InsuranceCoverageDTO getInsuranceCoverage(LocalDate debut, LocalDate fin, Long vehiculeId) {
        log.info("Service request to get insurance coverage for date range: {} to {} and vehiculeId: {}", debut, fin, vehiculeId);

        // Récupérer les réparations liées aux accidents
        List<Reparation> accidentReparations = getAccidentReparations(debut, fin, vehiculeId);

        // Calculer la couverture d'assurance par véhicule
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = calculateInsuranceCoverage(accidentReparations);

        // Récupérer les immatriculations des véhicules
        Map<Long, String> vehicleImmatriculations = getVehicleImmatriculations(insuranceCoverageByVehicle.keySet());

        return InsuranceCoverageDTO.builder()
                .insuranceCoverageByVehicle(insuranceCoverageByVehicle)
                .vehicleImmatriculations(vehicleImmatriculations)
                .build();
    }

    /**
     * Récupère les réparations liées aux accidents
     */
    private List<Reparation> getAccidentReparations(LocalDate debut, LocalDate fin, Long vehiculeId) {
        // Récupérer tous les incidents de type ACCIDENT dans la plage de dates
        List<Incident> accidents = incidentRepository.findByDateBetween(debut, fin)
                .stream()
                .filter(i -> i.getType() == TypeIncident.ACCIDENT)
                .toList();

        // Récupérer les IDs des incidents de type ACCIDENT
        Set<Long> accidentIds = accidents.stream()
                .map(Incident::getId)
                .collect(Collectors.toSet());

        log.info("Nombre d'incidents de type ACCIDENT trouvés: {}", accidentIds.size());

        // Récupérer toutes les réparations dans la plage de dates
        List<Reparation> allReparations = reparationRepository.findByDateReparationBetween(debut, fin);

        // Filtrer les réparations pour ne garder que celles liées à des incidents de type ACCIDENT
        List<Reparation> accidentReparations = allReparations.stream()
                .filter(r -> r.getIncident() != null && accidentIds.contains(r.getIncident().getId()))
                .toList();

        log.info("Nombre total de réparations: {}, Nombre de réparations liées à des accidents: {}",
                 allReparations.size(), accidentReparations.size());

        // Filtrer par véhicule si un ID est fourni
        if (vehiculeId != null) {
            accidentReparations = accidentReparations.stream()
                    .filter(r -> vehiculeId.equals(r.getIncident().getVehiculeId()))
                    .toList();
        }

        return accidentReparations;
    }

    /**
     * Calcule la couverture d'assurance pour les réparations
     */
    private Map<Long, Map<String, BigDecimal>> calculateInsuranceCoverage(List<Reparation> reparations) {
        Map<Long, Map<String, BigDecimal>> insuranceCoverageByVehicle = new HashMap<>();

        for (Reparation reparation : reparations) {
            Long vehiculeId = reparation.getIncident().getVehiculeId();
            if (vehiculeId == null) continue;

            BigDecimal cout = reparation.getCout();
            BigDecimal montantCouverture = BigDecimal.ZERO;

            // Calculer le montant couvert par l'assurance
            if (reparation.getRembourse() != null && reparation.getRembourse() && reparation.getMontantCouverture() != null) {
                montantCouverture = reparation.getMontantCouverture();
                // S'assurer que le montant de couverture ne dépasse pas le coût total
                if (montantCouverture.compareTo(cout) > 0) {
                    montantCouverture = cout;
                }
            }

            BigDecimal uncoveredAmount = cout.subtract(montantCouverture);

            insuranceCoverageByVehicle.computeIfAbsent(vehiculeId, k -> new HashMap<>());
            insuranceCoverageByVehicle.get(vehiculeId).merge("covered", montantCouverture, BigDecimal::add);
            insuranceCoverageByVehicle.get(vehiculeId).merge("uncovered", uncoveredAmount, BigDecimal::add);
        }

        return insuranceCoverageByVehicle;
    }

    /**
     * Enrichit les incidents avec les informations des véhicules (immatriculation)
     */
    private void enrichIncidentsWithVehicleInfo(List<IncidentResponseDTO> incidents) {
        if (incidents.isEmpty()) return;

        // Récupérer les IDs des véhicules uniques
        Set<Long> vehicleIds = incidents.stream()
                .map(IncidentResponseDTO::getVehiculeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (vehicleIds.isEmpty()) return;

        // Récupérer les immatriculations des véhicules
        Map<Long, String> vehicleImmatriculations = getVehicleImmatriculations(vehicleIds);

        // Pour chaque incident, ajouter l'immatriculation du véhicule
        for (IncidentResponseDTO incident : incidents) {
            if (incident.getVehiculeId() != null) {
                String immatriculation = vehicleImmatriculations.getOrDefault(incident.getVehiculeId(), DEFAULT_IMMATRICULATION);
                incident.setImmatriculation(immatriculation);
            } else {
                incident.setImmatriculation(DEFAULT_IMMATRICULATION);
            }
        }
    }

    /**
     * Enrichit les réparations avec les informations des véhicules (immatriculation)
     */
    private void enrichRepairsWithVehicleInfo(List<ReparationResponseDTO> repairs) {
        if (repairs.isEmpty()) return;

        // Récupérer les IDs des véhicules uniques
        Set<Long> vehicleIds = repairs.stream()
                .map(ReparationResponseDTO::getVehiculeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (vehicleIds.isEmpty()) return;

        // Récupérer les immatriculations des véhicules
        Map<Long, String> vehicleImmatriculations = getVehicleImmatriculations(vehicleIds);

        // Pour chaque réparation, ajouter l'immatriculation du véhicule
        for (ReparationResponseDTO repair : repairs) {
            if (repair.getVehiculeId() != null) {
                String immatriculation = vehicleImmatriculations.getOrDefault(repair.getVehiculeId(), DEFAULT_IMMATRICULATION);
                repair.setImmatriculation(immatriculation);
            } else {
                repair.setImmatriculation(DEFAULT_IMMATRICULATION);
            }
        }
    }

    /**
     * Récupère les immatriculations des véhicules à partir de leurs IDs
     */
    private Map<Long, String> getVehicleImmatriculations(Set<Long> vehicleIds) {
        Map<Long, String> vehicleImmatriculations = new HashMap<>();

        if (vehicleIds.isEmpty()) {
            return vehicleImmatriculations;
        }

        try {
            // Récupérer tous les véhicules
            List<VehiculeDto> allVehicules = vehiculeClient.getAllVehicules();

            // Créer un Map des IDs de véhicule vers leurs immatriculations
            Map<Long, String> vehiculeIdToImmatriculation = allVehicules.stream()
                    .collect(Collectors.toMap(
                        VehiculeDto::getIdVehicule,
                        VehiculeDto::getImmatriculation,
                        (existing, replacement) -> existing // En cas de doublon, garder la première valeur
                    ));

            // Pour chaque véhicule dans vehicleIds, récupérer son immatriculation
            for (Long vehicleId : vehicleIds) {
                String immatriculation = vehiculeIdToImmatriculation.get(vehicleId);
                vehicleImmatriculations.put(vehicleId, immatriculation != null ? immatriculation : DEFAULT_IMMATRICULATION);
            }
        } catch (Exception e) {
            log.error(ERROR_VEHICLE_IMMATRICULATION, e);
            // Fallback en cas d'erreur
            for (Long vehicleId : vehicleIds) {
                vehicleImmatriculations.put(vehicleId, DEFAULT_FALLBACK_IMMATRICULATION);
            }
        }
        return vehicleImmatriculations;
    }

    /**
     * Calcule le pourcentage de changement entre deux valeurs de manière sécurisée
     */
    private double calculatePercentageChange(double oldValue, double newValue) {
        try {
            if (oldValue == 0) {
                return newValue > 0 ? 100.0 : 0.0;
            }

            double change = ((newValue - oldValue) / oldValue) * 100.0;

            // Limiter les valeurs extrêmes pour éviter les problèmes d'affichage
            if (Double.isInfinite(change) || Double.isNaN(change)) {
                return 0.0;
            }

            // Limiter à +/- 1000% pour éviter les valeurs extrêmes
            if (change > 1000.0) {
                return 1000.0;
            } else if (change < -1000.0) {
                return -1000.0;
            }

            return change;
        } catch (Exception e) {
            log.error("Error calculating percentage change: {}", e.getMessage());
            return 0.0;
        }
    }
}
