package otbs.reservation.client.astreinte;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import otbs.reservation.client.VehiculeClient;
import otbs.reservation.dto.VehiculeDto;
import otbs.reservation.model.Reservation;
import otbs.reservation.model.ConsommationCarburant;
import otbs.reservation.repository.ReservationRepo;
import otbs.reservation.service.TicketRestaurantService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AstreinteReservationService {

    private final ReservationRepo reservationRepo;
    private final AstreinteClient astreinteClient;
    private final VehiculeClient vehiculeClient;
    private final TicketRestaurantService ticketRestaurantService;

    @Transactional
    public AstreinteReservationDto addAstreinteReservation(AstreinteReservationDto reservationDto) {
        log.info("Création d'une réservation pour astreintes: {}", reservationDto.getAstreinteIds());
        
        // 1. Validation du véhicule
        validateVehicleExists(reservationDto.getIdVehicule());
        
        // 2. Validation des astreintes
        validateAstreinteExists(reservationDto.getAstreinteIds());
        
        // 3. Vérification de disponibilité du véhicule
        if (isVehicleAvailable(reservationDto.getIdVehicule(), reservationDto.getDateDebut(), reservationDto.getDateFin())) {
            
            // 4. Création de la réservation
            Reservation reservation = new Reservation();
            setAstreinteAttributes(reservationDto, reservation);
            
            // 5. Sauvegarde
            Reservation savedReservation = reservationRepo.save(reservation);
            
            // 6. Création automatique du ticket restaurant
            ticketRestaurantService.createTicketFromReservation(savedReservation.getReservationId());
            
            // 7. Conversion en DTO
            return entityToAstreinteDto(savedReservation, fetchVehicleCache(List.of(reservationDto.getIdVehicule())));
        }
        
        throw new IllegalArgumentException("Véhicule non disponible pour la période demandée");
    }

    @Transactional(readOnly = true)
    public List<FullAstreinteReservationDto> getAllFutureAstreinteReservations() {
        log.info("Récupération de toutes les réservations d'astreintes futures");
        
        List<AstreinteDto> futureAstreintes = astreinteClient.findByDateDebutAfter();
        return findFullReservationsByAstreintes(futureAstreintes);
    }

    @Transactional(readOnly = true)
    public List<FullAstreinteReservationDto> getAstreinteReservationsByMonthAndConsultant(String consultantId, int monthNumber, int year) {
        log.info("Récupération des réservations d'astreintes pour consultant {} - mois {}/{}", consultantId, monthNumber, year);
        
        List<LocalDateTime> period = extendMonth(monthNumber, year);
        List<AstreinteDto> astreintes = astreinteClient.findAstreintesByConsultantAndDateRange(consultantId, period.get(0), period.get(1));
        return findFullReservationsByAstreintes(astreintes);
    }

    @Transactional(readOnly = true)
    public List<FullAstreinteReservationDto> getAstreinteReservationsByDayByConsultant(LocalDateTime date, String consultantId) {
        log.info("Récupération des réservations d'astreintes pour consultant {} - jour {}", consultantId, date);
        
        LocalDateTime dateDebut = date.toLocalDate().atStartOfDay();
        LocalDateTime dateFin = date.toLocalDate().atTime(23, 59, 59, 999_999_999);
        
        List<AstreinteDto> astreintes = astreinteClient.findAstreintesByConsultantAndDateRange(consultantId, dateDebut, dateFin);
        return findFullReservationsByAstreintes(astreintes);
    }

    @Transactional(readOnly = true)
    public List<AstreinteReservationDto> getAllAstreinteReservationsByDay(LocalDateTime day) {
        log.info("Récupération de toutes les réservations d'astreintes pour le jour {}", day);
        
        LocalDateTime dateDebut = day.toLocalDate().atStartOfDay();
        LocalDateTime dateFin = day.toLocalDate().atTime(23, 58, 59, 999_999_999);

        List<Reservation> reservations = reservationRepo.findByPeriod(dateDebut, dateFin)
                .stream()
                .filter(r -> r.getAstreinteIds() != null && !r.getAstreinteIds().isEmpty())
                .collect(Collectors.toList());

        Map<Long, VehiculeDto> vehicleCache = fetchVehicleCache(reservations.stream()
                .map(Reservation::getIdVehicule)
                .distinct()
                .toList());

        return reservations.stream()
                .map(reservation -> entityToAstreinteDto(reservation, vehicleCache))
                .toList();
    }

    // Méthodes privées utilitaires
    private void setAstreinteAttributes(AstreinteReservationDto reservationDto, Reservation reservation) {
        if (reservationDto.getDateDebut() == null || reservationDto.getDateFin() == null) {
            throw new IllegalArgumentException("Les dates de début et fin sont obligatoires");
        }
        if (reservationDto.getDateDebut().isAfter(reservationDto.getDateFin())) {
            throw new IllegalArgumentException("La date de début doit être antérieure à la date de fin");
        }
        
        reservation.setDateDebut(reservationDto.getDateDebut());
        reservation.setDateFin(reservationDto.getDateFin());
        reservation.setIdVehicule(reservationDto.getIdVehicule());
        reservation.setAstreinteIds(reservationDto.getAstreinteIds());
        reservation.setDescription(reservationDto.getDescription());
        
        if (reservationDto.getCreatorId() != null) {
            reservation.setCreatorId(reservationDto.getCreatorId());
        }
    }

    private void validateVehicleExists(Long idVehicule) {
        try {
            VehiculeDto vehicle = vehiculeClient.findVehiculeById(idVehicule);
            if (vehicle == null) {
                throw new IllegalArgumentException("Véhicule non trouvé avec l'ID: " + idVehicule);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("Erreur lors de la validation du véhicule: " + e.getMessage());
        }
    }

    private void validateAstreinteExists(List<Long> astreinteIds) {
        if (astreinteIds == null || astreinteIds.isEmpty()) {
            throw new IllegalArgumentException("Au moins une astreinte doit être spécifiée");
        }
        
        for (Long astreinteId : astreinteIds) {
            try {
                AstreinteDto astreinte = astreinteClient.getAstreinteById(astreinteId);
                if (astreinte == null) {
                    throw new IllegalArgumentException("Astreinte non trouvée avec l'ID: " + astreinteId);
                }
            } catch (Exception e) {
                throw new IllegalArgumentException("Erreur lors de la validation de l'astreinte " + astreinteId + ": " + e.getMessage());
            }
        }
    }

    private boolean isVehicleAvailable(Long idVehicule, LocalDateTime dateDebut, LocalDateTime dateFin) {
        if (dateDebut == null || dateFin == null || dateDebut.isAfter(dateFin)) {
            throw new IllegalArgumentException("Période de dates invalide");
        }
        
        List<Reservation> reservations = reservationRepo.findByPeriodAndVehiculeId(dateDebut, dateFin, idVehicule);
        return reservations.isEmpty();
    }

    private Map<Long, VehiculeDto> fetchVehicleCache(List<Long> vehicleIds) {
        Map<Long, VehiculeDto> vehicleCache = new HashMap<>();
        for (Long id : vehicleIds) {
            try {
                VehiculeDto vehicle = vehiculeClient.findVehiculeById(id);
                if (vehicle != null) {
                    vehicleCache.put(id, vehicle);
                }
            } catch (Exception e) {
                log.warn("Impossible de récupérer le véhicule {}: {}", id, e.getMessage());
            }
        }
        return vehicleCache;
    }

    private AstreinteReservationDto entityToAstreinteDto(Reservation reservation, Map<Long, VehiculeDto> vehicleCache) {
        AstreinteReservationDto dto = new AstreinteReservationDto();
        dto.setReservationId(reservation.getReservationId());
        dto.setDateDebut(reservation.getDateDebut());
        dto.setDateFin(reservation.getDateFin());
        dto.setAstreinteIds(reservation.getAstreinteIds());
        dto.setIdVehicule(reservation.getIdVehicule());
        dto.setDescription(reservation.getDescription());
        dto.setCreatorId(reservation.getCreatorId());
        
        // Ajout de l'immatriculation depuis le cache
        VehiculeDto vehicle = vehicleCache.get(reservation.getIdVehicule());
        if (vehicle != null) {
            dto.setImmatriculation(vehicle.getImmatriculation());
        }

        // Ajout des consommations carburant
        if (reservation.getConsommationCarburants() != null && !reservation.getConsommationCarburants().isEmpty()) {
            List<AstreinteReservationDto.ConsommationCarburantDto> consommationDtos = reservation.getConsommationCarburants().stream()
                    .map(this::mapConsommationCarburantToDto)
                    .collect(Collectors.toList());
            dto.setConsommationCarburant(consommationDtos);
        }

        // Ajout de l'ID de consommation télépéage
        if (reservation.getConsommationTelepeage() != null) {
            dto.setConsommationTelepeageId(reservation.getConsommationTelepeage().getId());
        }

        return dto;
    }

    private AstreinteReservationDto.ConsommationCarburantDto mapConsommationCarburantToDto(ConsommationCarburant consommation) {
        AstreinteReservationDto.ConsommationCarburantDto dto = new AstreinteReservationDto.ConsommationCarburantDto();
        dto.setConsommationId(consommation.getId());
        dto.setMontantConsommer(consommation.getMontantConsommer());
        dto.setMontantSortie(consommation.getMontantSortie());
        dto.setFilePath(consommation.getFilePath());
        dto.setTypeConsommation(consommation.getTypeConsommation().name());

        if (consommation.getCarteCarburant() != null) {
            dto.setCarteCarburantId(consommation.getCarteCarburant().getIdCarte());
        }

        return dto;
    }

    private List<FullAstreinteReservationDto> findFullReservationsByAstreintes(List<AstreinteDto> astreinteDtoList) {
        List<FullAstreinteReservationDto> fullReservationDtoList = new ArrayList<>();
        
        for (AstreinteDto astreinte : astreinteDtoList) {
            List<Reservation> reservations = findReservationsByAstreinteId(astreinte.getId());
            
            if (!reservations.isEmpty()) {
                for (Reservation reservation : reservations) {
                    FullAstreinteReservationDto fullDto = createFullAstreinteReservationFromReservation(reservation, astreinte);
                    fullReservationDtoList.add(fullDto);
                }
            } else {
                // Créer une réservation "virtuelle" pour l'astreinte sans réservation
                FullAstreinteReservationDto fullDto = new FullAstreinteReservationDto();
                fullDto.setAstreinteIds(List.of(astreinte.getId()));
                fullDto.setDateDebut(astreinte.getDateDebut());
                fullDto.setDateFin(astreinte.getDateFin());
                fullDto.setDescription(astreinte.getDescription());
                
                // Ajouter les noms des consultants
                if (astreinte.getConsultants() != null) {
                    fullDto.setConsultantNames(astreinte.getConsultants().stream()
                            .map(ConsultantAstreinteDto::getConsultantName)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList()));
                }
                
                fullReservationDtoList.add(fullDto);
            }
        }
        
        return fullReservationDtoList;
    }

    private List<Reservation> findReservationsByAstreinteId(Long astreinteId) {
        return reservationRepo.findAll().stream()
                .filter(r -> r.getAstreinteIds() != null && r.getAstreinteIds().contains(astreinteId))
                .collect(Collectors.toList());
    }

    private FullAstreinteReservationDto createFullAstreinteReservationFromReservation(Reservation reservation, AstreinteDto astreinte) {
        FullAstreinteReservationDto fullDto = new FullAstreinteReservationDto();
        fullDto.setReservationId(reservation.getReservationId());
        fullDto.setAstreinteIds(reservation.getAstreinteIds());
        fullDto.setDateDebut(reservation.getDateDebut());
        fullDto.setDateFin(reservation.getDateFin());
        fullDto.setDescription(reservation.getDescription());
        fullDto.setCreatorId(reservation.getCreatorId());
        
        // Récupérer les informations du véhicule
        try {
            VehiculeDto vehicle = vehiculeClient.findVehiculeById(reservation.getIdVehicule());
            if (vehicle != null) {
                fullDto.setImmatriculation(vehicle.getImmatriculation());
            }
        } catch (Exception e) {
            log.warn("Impossible de récupérer le véhicule pour la réservation {}", reservation.getReservationId());
        }
        
        // Ajouter les informations des consultants depuis l'astreinte
        if (astreinte.getConsultants() != null) {
            fullDto.setConsultantNames(astreinte.getConsultants().stream()
                    .map(ConsultantAstreinteDto::getConsultantName)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }

        // Récupérer les informations de télépéage
        if (reservation.getConsommationTelepeage() != null &&
            reservation.getConsommationTelepeage().getTelepeage() != null) {
            fullDto.setNumeroBadge(reservation.getConsommationTelepeage().getTelepeage().getNumeroBadge());
        }

        // Récupérer les informations des cartes carburant
        if (reservation.getConsommationCarburants() != null && !reservation.getConsommationCarburants().isEmpty()) {
            List<String> numerosCartes = reservation.getConsommationCarburants().stream()
                    .filter(consommation -> consommation.getCarteCarburant() != null)
                    .map(consommation -> consommation.getCarteCarburant().getNumeroCarte())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            fullDto.setNumerosCartes(numerosCartes);
        }

        // Récupérer les informations des tickets restaurant
        if (reservation.getTicketRestos() != null) {
            fullDto.setNombreTicketResto(reservation.getTicketRestos().getNbTicket());
            // ❌ NE PAS mettre les tickets restaurant dans le champ frais
            // fullDto.setFrais(reservation.getTicketRestos().getMontant());
        }

        // Calculer les vrais frais de carburant (Cash/Frais) depuis les consommations
        double totalFraisCarburant = 0.0;
        if (reservation.getConsommationCarburants() != null) {
            totalFraisCarburant = reservation.getConsommationCarburants().stream()
                    .filter(consommation -> consommation.getTypeConsommation() != null)
                    .filter(consommation ->
                        consommation.getTypeConsommation().name().equals("Cash") ||
                        consommation.getTypeConsommation().name().equals("Frais"))
                    .mapToDouble(consommation -> consommation.getMontantSortie() != null ? consommation.getMontantSortie() : 0.0)
                    .sum();
        }
        fullDto.setFrais(totalFraisCarburant);

        return fullDto;
    }

    @Transactional(readOnly = true)
    public AstreinteReservationDto getAstreinteReservation(Long reservationId) {
        log.info("Récupération de la réservation d'astreinte avec ID: {}", reservationId);

        Reservation reservation = findReservation(reservationId);
        return entityToAstreinteDto(reservation, fetchVehicleCache(List.of(reservation.getIdVehicule())));
    }

    @Transactional
    public AstreinteReservationDto updateAstreinteReservation(Long reservationId, AstreinteReservationDto reservationDto) {
        log.info("Mise à jour de la réservation d'astreinte avec ID: {}", reservationId);

        Reservation reservation = findReservation(reservationId);

        // Validation des nouvelles données
        if (reservationDto.getIdVehicule() != null) {
            validateVehicleExists(reservationDto.getIdVehicule());
        }
        if (reservationDto.getAstreinteIds() != null && !reservationDto.getAstreinteIds().isEmpty()) {
            validateAstreinteExists(reservationDto.getAstreinteIds());
        }

        // Mise à jour des attributs
        updateAstreinteAttributes(reservationDto, reservation);

        Reservation updatedReservation = reservationRepo.save(reservation);
        return entityToAstreinteDto(updatedReservation, fetchVehicleCache(List.of(updatedReservation.getIdVehicule())));
    }

    @Transactional
    public AstreinteReservationDto updateReservedVehicle(Long reservationId, AstreinteReservationDto reservationDto) {
        log.info("Mise à jour du véhicule pour la réservation d'astreinte avec ID: {}", reservationId);

        Reservation reservation = findReservation(reservationId);

        if (reservationDto.getIdVehicule() != null) {
            validateVehicleExists(reservationDto.getIdVehicule());
            reservation.setIdVehicule(reservationDto.getIdVehicule());
            Reservation updatedReservation = reservationRepo.save(reservation);
            return entityToAstreinteDto(updatedReservation, fetchVehicleCache(List.of(reservation.getIdVehicule())));
        } else {
            reservationRepo.delete(reservation);
            return new AstreinteReservationDto();
        }
    }

    @Transactional
    public void deleteAstreinteReservation(Long reservationId) {
        log.info("Suppression de la réservation d'astreinte avec ID: {}", reservationId);

        Reservation reservation = findReservation(reservationId);
        reservationRepo.delete(reservation);
    }

    @Transactional(readOnly = true)
    public List<AstreinteReservationDto> getAstreinteReservationsByMonth(int monthNumber, int year) {
        log.info("Récupération des réservations d'astreintes pour le mois {}/{}", monthNumber, year);

        List<LocalDateTime> period = extendMonth(monthNumber, year);
        List<Reservation> reservations = reservationRepo.findByMonthAndYear(period.get(0), period.get(1))
                .stream()
                .filter(r -> r.getAstreinteIds() != null && !r.getAstreinteIds().isEmpty())
                .collect(Collectors.toList());

        Map<Long, VehiculeDto> vehicleCache = fetchVehicleCache(reservations.stream()
                .map(Reservation::getIdVehicule)
                .distinct()
                .toList());

        return reservations.stream()
                .map(reservation -> entityToAstreinteDto(reservation, vehicleCache))
                .toList();
    }

    @Transactional(readOnly = true)
    public List<AstreinteReservationDto> getAstreinteReservationsByMonthAndVehicule(int monthNumber, int year, Long idVehicule) {
        log.info("Récupération des réservations d'astreintes pour le mois {}/{} et véhicule {}", monthNumber, year, idVehicule);

        List<LocalDateTime> period = extendMonth(monthNumber, year);
        List<Reservation> reservations = reservationRepo.findByMonthAndYearAndVehiculeId(period.get(0), period.get(1), idVehicule)
                .stream()
                .filter(r -> r.getAstreinteIds() != null && !r.getAstreinteIds().isEmpty())
                .collect(Collectors.toList());

        Map<Long, VehiculeDto> vehicleCache = fetchVehicleCache(List.of(idVehicule));
        return reservations.stream()
                .map(reservation -> entityToAstreinteDto(reservation, vehicleCache))
                .toList();
    }

    @Transactional(readOnly = true)
    public List<VehiculeDto> getAllAvailableCarsByPeriod(LocalDateTime dateDebut, LocalDateTime dateFin) {
        log.info("Récupération des véhicules disponibles pour la période {} - {}", dateDebut, dateFin);

        try {
            List<VehiculeDto> allVehicles = vehiculeClient.findAllVehicules();
            List<Reservation> reservations = reservationRepo.findByPeriod(dateDebut, dateFin);

            Set<Long> reservedVehicleIds = reservations.stream()
                    .map(Reservation::getIdVehicule)
                    .collect(Collectors.toSet());

            return allVehicles.stream()
                    .filter(vehicle -> !reservedVehicleIds.contains(vehicle.getIdVehicule()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des véhicules disponibles", e);
            return new ArrayList<>();
        }
    }

    @Transactional(readOnly = true)
    public List<FullAstreinteReservationDto> getFutureAstreinteReservationsByConsultant(String consultantId) {
        log.info("Récupération des réservations d'astreintes futures pour le consultant {}", consultantId);

        List<AstreinteDto> futureAstreintes = astreinteClient.findByDateDebutAfterAndConsultantUserId(consultantId);
        return findFullReservationsByAstreintes(futureAstreintes);
    }

    @Transactional(readOnly = true)
    public List<FullAstreinteReservationDto> getAstreinteReservationsWithUnreturnedResources() {
        log.info("Récupération des réservations d'astreintes avec ressources non retournées");

        LocalDateTime currentDateTime = LocalDateTime.now();
        List<Reservation> reservationList = reservationRepo.findAll().stream()
                .filter(r -> r.getAstreinteIds() != null && !r.getAstreinteIds().isEmpty())
                .filter(r -> r.getDateFin().isBefore(currentDateTime))
                .filter(r -> r.getConsommationTelepeage() == null ||
                           (r.getConsommationTelepeage().getTelepeage() != null &&
                            !Boolean.TRUE.equals(r.getConsommationTelepeage().getTelepeage().getRendu())))
                .collect(Collectors.toList());

        return getFullAstreinteReservationDtos(reservationList);
    }

    @Transactional(readOnly = true)
    public Map<LocalDate, Integer> getNumberOfVehiclesAvailableByMonth(int monthNumber, int year) {
        log.info("Calcul du nombre de véhicules disponibles par jour pour le mois {}/{}", monthNumber, year);

        Map<LocalDate, Integer> availabilityMap = new HashMap<>();
        List<LocalDateTime> period = extendMonth(monthNumber, year);

        try {
            List<VehiculeDto> allVehicles = vehiculeClient.findAllVehicules();
            int totalVehicles = allVehicles.size();

            LocalDate startDate = period.get(0).toLocalDate();
            LocalDate endDate = period.get(1).toLocalDate();

            for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
                LocalDateTime dayStart = date.atStartOfDay();
                LocalDateTime dayEnd = date.atTime(23, 59, 59);

                List<Reservation> dayReservations = reservationRepo.findByPeriod(dayStart, dayEnd);
                Set<Long> reservedVehicleIds = dayReservations.stream()
                        .map(Reservation::getIdVehicule)
                        .collect(Collectors.toSet());

                int availableVehicles = totalVehicles - reservedVehicleIds.size();
                availabilityMap.put(date, availableVehicles);
            }
        } catch (Exception e) {
            log.error("Erreur lors du calcul de disponibilité des véhicules", e);
        }

        return availabilityMap;
    }

    @Transactional(readOnly = true)
    public List<AstreinteReservationDto> findAllAstreinteReservations() {
        log.info("Récupération de toutes les réservations d'astreintes");

        List<Reservation> reservations = reservationRepo.findAll().stream()
                .filter(r -> r.getAstreinteIds() != null && !r.getAstreinteIds().isEmpty())
                .collect(Collectors.toList());

        Map<Long, VehiculeDto> vehicleCache = fetchVehicleCache(reservations.stream()
                .map(Reservation::getIdVehicule)
                .distinct()
                .toList());

        return reservations.stream()
                .map(reservation -> entityToAstreinteDto(reservation, vehicleCache))
                .toList();
    }

    @Transactional(readOnly = true)
    public List<FullAstreinteReservationDto> getAllAstreinteReservationsHistoryPaginated(int page, int size) {
        log.info("Récupération de l'historique des réservations d'astreintes (page {}, taille {})", page, size);

        Pageable pageable = PageRequest.of(page, size, Sort.by("dateDebut").descending());
        List<Reservation> reservationList = reservationRepo.findAll(pageable).getContent().stream()
                .filter(r -> r.getAstreinteIds() != null && !r.getAstreinteIds().isEmpty())
                .collect(Collectors.toList());

        return getFullAstreinteReservationDtos(reservationList);
    }

    @Transactional(readOnly = true)
    public List<FullAstreinteReservationDto> getAstreinteReservationsHistoryByConsultant(int page, int size, String userId) {
        log.info("Récupération de l'historique des réservations d'astreintes pour le consultant {} (page {}, taille {})", userId, page, size);

        Pageable pageable = PageRequest.of(page, size, Sort.by("dateDebut").descending());
        List<Reservation> allReservations = reservationRepo.findAll(pageable).getContent();
        List<Reservation> consultantReservations = new ArrayList<>();

        for (Reservation reservation : allReservations) {
            if (reservation.getAstreinteIds() != null && !reservation.getAstreinteIds().isEmpty()) {
                try {
                    for (Long astreinteId : reservation.getAstreinteIds()) {
                        AstreinteDto astreinte = astreinteClient.getAstreinteById(astreinteId);
                        if (astreinte.getConsultants() != null) {
                            boolean isConsultantInAstreinte = astreinte.getConsultants().stream()
                                    .anyMatch(consultant -> userId.equals(consultant.getConsultant()));
                            if (isConsultantInAstreinte) {
                                consultantReservations.add(reservation);
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    log.warn("Erreur lors de la vérification du consultant pour l'astreinte", e);
                }
            }
        }

        return getFullAstreinteReservationDtos(consultantReservations);
    }

    // Méthodes utilitaires privées
    private Reservation findReservation(Long reservationId) {
        return reservationRepo.findById(reservationId)
                .orElseThrow(() -> new IllegalArgumentException("Réservation non trouvée avec l'ID: " + reservationId));
    }

    private void updateAstreinteAttributes(AstreinteReservationDto reservationDto, Reservation reservation) {
        if (reservationDto.getDateDebut() != null) {
            reservation.setDateDebut(reservationDto.getDateDebut());
        }
        if (reservationDto.getDateFin() != null) {
            reservation.setDateFin(reservationDto.getDateFin());
        }
        if (reservationDto.getIdVehicule() != null) {
            reservation.setIdVehicule(reservationDto.getIdVehicule());
        }
        if (reservationDto.getAstreinteIds() != null) {
            reservation.setAstreinteIds(reservationDto.getAstreinteIds());
        }
        if (reservationDto.getDescription() != null) {
            reservation.setDescription(reservationDto.getDescription());
        }
        if (reservationDto.getCreatorId() != null) {
            reservation.setCreatorId(reservationDto.getCreatorId());
        }
    }

    private List<FullAstreinteReservationDto> getFullAstreinteReservationDtos(List<Reservation> reservationList) {
        List<FullAstreinteReservationDto> fullReservationDtoList = new ArrayList<>();

        for (Reservation reservation : reservationList) {
            if (reservation.getAstreinteIds() != null && !reservation.getAstreinteIds().isEmpty()) {
                try {
                    AstreinteDto astreinte = astreinteClient.getAstreinteById(reservation.getAstreinteIds().get(0));
                    FullAstreinteReservationDto fullDto = createFullAstreinteReservationFromReservation(reservation, astreinte);
                    fullReservationDtoList.add(fullDto);
                } catch (Exception e) {
                    log.warn("Erreur lors de la création du FullAstreinteReservationDto pour la réservation {}", reservation.getReservationId());
                }
            }
        }
        return fullReservationDtoList;
    }

    private List<LocalDateTime> extendMonth(int month, int year) {
        LocalDateTime startOfMonth = LocalDateTime.of(year, month, 1, 0, 0);
        LocalDateTime endOfMonth = startOfMonth.plusMonths(1).minusSeconds(1);
        return List.of(startOfMonth, endOfMonth);
    }
}
