package otbs.ms_incident.service;

import otbs.ms_incident.dto.ReparationRequestDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;
import otbs.ms_incident.exception.BadRequestException;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.ReparationMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReparationServiceImplTest {

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationMapper reparationMapper;

    @InjectMocks
    private ReparationServiceImpl reparationService;

    private Reparation reparation;
    private ReparationRequestDTO requestDTO;
    private ReparationResponseDTO responseDTO;
    private Incident incident;

    @BeforeEach
    void setUp() {
        incident = new Incident();
        incident.setId(1L);

        reparation = new Reparation();
        reparation.setId(1L);
        reparation.setDateReparation(LocalDate.of(2023, 6, 15));
        reparation.setDescription("Remplacement pare-choc avant");
        reparation.setCout(new BigDecimal("1200.50"));
        reparation.setGarage("Garage Central");
        reparation.setIncident(incident);

        requestDTO = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.of(2023, 6, 15))
                .description("Remplacement pare-choc avant")
                .cout(new BigDecimal("1200.50"))
                .garage("Garage Central")
                .incidentId(1L)
                .build();

        responseDTO = ReparationResponseDTO.builder()
                .id(1L)
                .dateReparation(LocalDate.of(2023, 6, 15))
                .description("Remplacement pare-choc avant")
                .cout(new BigDecimal("1200.50"))
                .garage("Garage Central")
                .incidentId(1L)
                .build();
    }

    @Test
    void createReparation_shouldReturnSavedReparation() {
        // Given
        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident));
        when(reparationMapper.toEntity(requestDTO)).thenReturn(reparation);
        when(reparationRepository.save(reparation)).thenReturn(reparation);
        when(reparationMapper.toDTO(reparation)).thenReturn(responseDTO);

        // When
        ReparationResponseDTO result = reparationService.createReparation(requestDTO);

        // Then
        assertThat(result).isEqualTo(responseDTO);
        verify(incidentRepository).findById(1L);
        verify(reparationMapper).toEntity(requestDTO);
        verify(reparationRepository).save(reparation);
        verify(reparationMapper).toDTO(reparation);
    }

    @Test
    void createReparation_shouldThrowResourceNotFoundException_whenIncidentNotFound() {
        // Given
        when(incidentRepository.findById(99L)).thenReturn(Optional.empty());
        requestDTO.setIncidentId(99L);

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> reparationService.createReparation(requestDTO));
        verify(incidentRepository).findById(99L);
        verify(reparationRepository, never()).save(any());
    }

    @Test
    void getReparationById_shouldReturnReparation_whenReparationExists() {
        // Given
        when(reparationRepository.findById(1L)).thenReturn(Optional.of(reparation));
        when(reparationMapper.toDTO(reparation)).thenReturn(responseDTO);

        // When
        ReparationResponseDTO result = reparationService.getReparationById(1L);

        // Then
        assertThat(result).isEqualTo(responseDTO);
        verify(reparationRepository).findById(1L);
        verify(reparationMapper).toDTO(reparation);
    }

    @Test
    void getReparationById_shouldThrowResourceNotFoundException_whenReparationDoesNotExist() {
        // Given
        when(reparationRepository.findById(99L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> reparationService.getReparationById(99L));
        verify(reparationRepository).findById(99L);
        verify(reparationMapper, never()).toDTO(any());
    }

    @Test
    void getAllReparations_shouldReturnAllReparations() {
        // Given
        Reparation reparation2 = new Reparation();
        reparation2.setId(2L);
        List<Reparation> reparations = Arrays.asList(reparation, reparation2);
        
        ReparationResponseDTO responseDTO2 = ReparationResponseDTO.builder().id(2L).build();
        List<ReparationResponseDTO> responseDTOs = Arrays.asList(responseDTO, responseDTO2);
        
        when(reparationRepository.findAll()).thenReturn(reparations);
        when(reparationMapper.toDTOList(reparations)).thenReturn(responseDTOs);

        // When
        List<ReparationResponseDTO> results = reparationService.getAllReparations();

        // Then
        assertThat(results).containsExactlyElementsOf(responseDTOs);
        verify(reparationRepository).findAll();
        verify(reparationMapper).toDTOList(reparations);
    }

    @Test
    void updateReparation_shouldUpdateAndReturnReparation_whenReparationExists() {
        // Given
        when(reparationRepository.findById(1L)).thenReturn(Optional.of(reparation));
        when(reparationRepository.save(reparation)).thenReturn(reparation);
        when(reparationMapper.toDTO(reparation)).thenReturn(responseDTO);

        // When
        ReparationResponseDTO result = reparationService.updateReparation(1L, requestDTO);

        // Then
        assertThat(result).isEqualTo(responseDTO);
        verify(reparationRepository).findById(1L);
        verify(reparationMapper).updateReparationFromDTO(requestDTO, reparation);
        verify(reparationRepository).save(reparation);
        verify(reparationMapper).toDTO(reparation);
        verify(incidentRepository, never()).findById(anyLong());
    }

    @Test
    void updateReparation_shouldUpdateIncidentAndReturnReparation_whenIncidentIdChanges() {
        // Given
        Incident newIncident = new Incident();
        newIncident.setId(2L);
        
        // Create a request with a different incident ID
        ReparationRequestDTO requestWithNewIncident = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.of(2023, 6, 15))
                .description("Remplacement pare-choc avant")
                .cout(new BigDecimal("1200.50"))
                .garage("Garage Central")
                .incidentId(2L) // Different ID from the original (1L)
                .build();
                
        when(reparationRepository.findById(1L)).thenReturn(Optional.of(reparation));
        when(incidentRepository.findById(2L)).thenReturn(Optional.of(newIncident));
        when(reparationRepository.save(reparation)).thenReturn(reparation);
        when(reparationMapper.toDTO(reparation)).thenReturn(responseDTO);

        // When
        ReparationResponseDTO result = reparationService.updateReparation(1L, requestWithNewIncident);

        // Then
        assertThat(result).isEqualTo(responseDTO);
        verify(reparationRepository).findById(1L);
        verify(incidentRepository).findById(2L);
        verify(reparationMapper).updateReparationFromDTO(requestWithNewIncident, reparation);
        verify(reparationRepository).save(reparation);
        verify(reparationMapper).toDTO(reparation);
    }

    @Test
    void deleteReparation_shouldDeleteReparation_whenReparationExists() {
        // Given
        when(reparationRepository.existsById(1L)).thenReturn(true);

        // When
        reparationService.deleteReparation(1L);

        // Then
        verify(reparationRepository).existsById(1L);
        verify(reparationRepository).deleteById(1L);
    }

    @Test
    void deleteReparation_shouldThrowResourceNotFoundException_whenReparationDoesNotExist() {
        // Given
        when(reparationRepository.existsById(99L)).thenReturn(false);

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> reparationService.deleteReparation(99L));
        verify(reparationRepository).existsById(99L);
        verify(reparationRepository, never()).deleteById(any());
    }

    @Test
    void getReparationsByIncidentId_shouldReturnReparationsForIncident() {
        // Given
        when(incidentRepository.existsById(1L)).thenReturn(true);
        when(reparationRepository.findByIncidentId(1L)).thenReturn(Arrays.asList(reparation));
        when(reparationMapper.toDTOList(Arrays.asList(reparation))).thenReturn(Arrays.asList(responseDTO));

        // When
        List<ReparationResponseDTO> results = reparationService.getReparationsByIncidentId(1L);

        // Then
        assertThat(results).hasSize(1);
        assertThat(results.get(0)).isEqualTo(responseDTO);
        verify(incidentRepository).existsById(1L);
        verify(reparationRepository).findByIncidentId(1L);
        verify(reparationMapper).toDTOList(Arrays.asList(reparation));
    }

    @Test
    void getReparationsByDateRange_shouldThrowBadRequestException_whenDebutIsAfterFin() {
        // Given
        LocalDate debut = LocalDate.of(2023, 6, 1);
        LocalDate fin = LocalDate.of(2023, 5, 31);

        // When & Then
        assertThrows(BadRequestException.class, () -> reparationService.getReparationsByDateRange(debut, fin));
        verify(reparationRepository, never()).findByDateReparationBetween(any(), any());
    }
} 