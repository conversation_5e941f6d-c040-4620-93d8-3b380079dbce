package otbs.ms_incident.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import otbs.ms_incident.client.VehiculeClient;
import otbs.ms_incident.client.VehiculeDto;
import otbs.ms_incident.commun.PageResponse;
import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;
import otbs.ms_incident.service.IncidentService;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class IncidentControllerAdvancedTest {

    @Mock
    private IncidentService incidentService;

    @Mock
    private VehiculeClient vehiculeClient;

    @InjectMocks
    private IncidentController incidentController;

    private IncidentResponseDTO responseDTO1;
    private IncidentResponseDTO responseDTO2;
    private List<IncidentResponseDTO> incidents;
    private VehiculeDto vehiculeDto;
    private PageResponse<IncidentResponseDTO> pageResponse;

    @BeforeEach
    void setUp() {
        // Setup test data

        responseDTO1 = IncidentResponseDTO.builder()
                .id(1L)
                .date(LocalDate.of(2023, 5, 10))
                .type(TypeIncident.ACCIDENT)
                .status(StatusIncident.A_TRAITER)
                .priorite(NiveauPrioriteIncident.FAIBLE)
                .lieu("Avenue des Champs-Élysées")
                .description("Collision avec un autre véhicule")
                .vehiculeId(100L)
                .build();

        responseDTO2 = IncidentResponseDTO.builder()
                .id(2L)
                .date(LocalDate.of(2023, 6, 15))
                .type(TypeIncident.PANNE)
                .status(StatusIncident.EN_COURS_TRAITEMENT)
                .priorite(NiveauPrioriteIncident.MOYEN)
                .lieu("Boulevard Haussmann")
                .description("Panne moteur")
                .vehiculeId(200L)
                .build();

        incidents = Arrays.asList(responseDTO1, responseDTO2);

        vehiculeDto = new VehiculeDto();
        vehiculeDto.setIdVehicule(100L);
        vehiculeDto.setImmatriculation("AB-123-CD");
        vehiculeDto.setMarque("Renault");
        vehiculeDto.setModele("Clio");

        pageResponse = new PageResponse<>();
        pageResponse.setContent(incidents);
        pageResponse.setTotalElements(2L);
        pageResponse.setTotalPages(1);
        pageResponse.setSize(10);
        pageResponse.setNumber(0);
    }

    @Test
    void createIncident_shouldThrowExceptionWhenRequiredFieldsAreMissing() {
        // Given
        IncidentRequestDTO invalidDTO = new IncidentRequestDTO();

        // When & Then
        assertThrows(IllegalArgumentException.class, () -> incidentController.createIncident(invalidDTO));
        verify(incidentService, never()).createIncident(any(IncidentRequestDTO.class));
    }

    @Test
    void getIncidentsForToday_shouldReturnTodaysIncidents() {
        // Given
        when(incidentService.getIncidentsForToday()).thenReturn(incidents);

        // When
        ResponseEntity<List<IncidentResponseDTO>> response = incidentController.getIncidentsForToday();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(incidents, response.getBody());
        verify(incidentService).getIncidentsForToday();
    }

    @Test
    void getIncidentsForLastMonth_shouldReturnLastMonthIncidents() {
        // Given
        when(incidentService.getIncidentsForLastMonth()).thenReturn(incidents);

        // When
        ResponseEntity<List<IncidentResponseDTO>> response = incidentController.getIncidentsForLastMonth();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(incidents, response.getBody());
        verify(incidentService).getIncidentsForLastMonth();
    }

    @Test
    void getIncidentsForLastSemester_shouldReturnLastSemesterIncidents() {
        // Given
        when(incidentService.getIncidentsForLastSemester()).thenReturn(incidents);

        // When
        ResponseEntity<List<IncidentResponseDTO>> response = incidentController.getIncidentsForLastSemester();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(incidents, response.getBody());
        verify(incidentService).getIncidentsForLastSemester();
    }

    @Test
    void getIncidentsForLastYear_shouldReturnLastYearIncidents() {
        // Given
        when(incidentService.getIncidentsForLastYear()).thenReturn(incidents);

        // When
        ResponseEntity<List<IncidentResponseDTO>> response = incidentController.getIncidentsForLastYear();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(incidents, response.getBody());
        verify(incidentService).getIncidentsForLastYear();
    }

    @Test
    void getAllIncidentsPaginated_shouldReturnPagedIncidents() {
        // Given
        when(incidentService.getAllIncidents(any(Pageable.class))).thenReturn(pageResponse);

        // When
        ResponseEntity<PageResponse<IncidentResponseDTO>> response = incidentController.getAllIncidentsPaginated(0, 10, "id", "DESC");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(pageResponse, response.getBody());
        verify(incidentService).getAllIncidents(any(Pageable.class));
    }

    @Test
    void getIncidentsByDateRangePaginated_shouldReturnPagedIncidents() {
        // Given
        LocalDate debut = LocalDate.of(2023, 5, 1);
        LocalDate fin = LocalDate.of(2023, 5, 31);
        Pageable pageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id"));
        when(incidentService.getIncidentsByDateRange(any(LocalDate.class), any(LocalDate.class), any(Pageable.class)))
                .thenReturn(pageResponse);

        // When
        ResponseEntity<PageResponse<IncidentResponseDTO>> response = incidentController.getIncidentsByDateRangePaginated(
                debut, fin, 0, 10, "id", "DESC");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(pageResponse, response.getBody());
        verify(incidentService).getIncidentsByDateRange(debut, fin, pageable);
    }

    @Test
    void getIncidentsByLieuPaginated_shouldReturnPagedIncidents() {
        // Given
        String lieu = "Paris";
        Pageable pageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id"));
        when(incidentService.getIncidentsByLieu(anyString(), any(Pageable.class))).thenReturn(pageResponse);

        // When
        ResponseEntity<PageResponse<IncidentResponseDTO>> response = incidentController.getIncidentsByLieuPaginated(
                lieu, 0, 10, "id", "DESC");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(pageResponse, response.getBody());
        verify(incidentService).getIncidentsByLieu(lieu, pageable);
    }

    @Test
    void getVehiculeByImmatriculation_shouldReturnVehicule() {
        // Given
        String immatriculation = "AB-123-CD";
        when(vehiculeClient.getVehiculeByImmatriculation(immatriculation)).thenReturn(vehiculeDto);

        // When
        ResponseEntity<VehiculeDto> response = incidentController.getVehiculeByImmatriculation(immatriculation);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(vehiculeDto, response.getBody());
        verify(vehiculeClient).getVehiculeByImmatriculation(immatriculation);
    }

    @Test
    void getVehiculeByImmatriculation_shouldReturnNotFoundWhenVehiculeDoesNotExist() {
        // Given
        String immatriculation = "XX-999-XX";
        when(vehiculeClient.getVehiculeByImmatriculation(immatriculation)).thenReturn(null);

        // When
        ResponseEntity<VehiculeDto> response = incidentController.getVehiculeByImmatriculation(immatriculation);

        // Then
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        verify(vehiculeClient).getVehiculeByImmatriculation(immatriculation);
    }

    @Test
    void getVehiculeDetailsWithIncidents_shouldReturnVehiculeAndIncidents() {
        // Given
        Long vehiculeId = 100L;
        when(vehiculeClient.getVehiculeById(vehiculeId)).thenReturn(vehiculeDto);
        when(incidentService.getIncidentsByVehiculeId(vehiculeId)).thenReturn(incidents);
        when(incidentService.countIncidentsByVehiculeId(vehiculeId)).thenReturn(2L);

        // When
        ResponseEntity<Map<String, Object>> response = incidentController.getVehiculeDetailsWithIncidents(vehiculeId);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(vehiculeDto, response.getBody().get("vehicule"));
        assertEquals(incidents, response.getBody().get("incidents"));
        assertEquals(2L, response.getBody().get("incidentCount"));
        verify(vehiculeClient).getVehiculeById(vehiculeId);
        verify(incidentService).getIncidentsByVehiculeId(vehiculeId);
        verify(incidentService).countIncidentsByVehiculeId(vehiculeId);
    }

    @Test
    void getVehiculeDetailsWithIncidents_shouldReturnNotFoundWhenVehiculeDoesNotExist() {
        // Given
        Long vehiculeId = 999L;
        when(vehiculeClient.getVehiculeById(vehiculeId)).thenReturn(null);

        // When
        ResponseEntity<Map<String, Object>> response = incidentController.getVehiculeDetailsWithIncidents(vehiculeId);

        // Then
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        verify(vehiculeClient).getVehiculeById(vehiculeId);
        verify(incidentService, never()).getIncidentsByVehiculeId(anyLong());
        verify(incidentService, never()).countIncidentsByVehiculeId(anyLong());
    }

    @Test
    void getVehiculeCompleteDetails_shouldReturnVehiculeAndIncidentsWithAdditionalInfo() {
        // Given
        Long vehiculeId = 100L;
        when(vehiculeClient.getVehiculeById(vehiculeId)).thenReturn(vehiculeDto);
        when(incidentService.getIncidentsByVehiculeId(vehiculeId)).thenReturn(incidents);
        when(incidentService.countIncidentsByVehiculeId(vehiculeId)).thenReturn(2L);

        // When
        ResponseEntity<Map<String, Object>> response = incidentController.getVehiculeCompleteDetails(vehiculeId);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(vehiculeDto, response.getBody().get("vehicule"));
        assertEquals(incidents, response.getBody().get("incidents"));
        assertEquals(2L, response.getBody().get("incidentCount"));
        assertEquals(true, response.getBody().get("incidentsFromMsIncident"));
        verify(vehiculeClient).getVehiculeById(vehiculeId);
        verify(incidentService).getIncidentsByVehiculeId(vehiculeId);
        verify(incidentService).countIncidentsByVehiculeId(vehiculeId);
    }

    @Test
    void getAllVehicules_shouldReturnAllVehicules() {
        // Given
        List<VehiculeDto> vehicules = Arrays.asList(vehiculeDto);
        when(vehiculeClient.getAllVehicules()).thenReturn(vehicules);

        // When
        ResponseEntity<List<VehiculeDto>> response = incidentController.getAllVehicules();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(vehicules, response.getBody());
        verify(vehiculeClient).getAllVehicules();
    }

    @Test
    void getAllVehicules_shouldHandleException() {
        // Given
        when(vehiculeClient.getAllVehicules()).thenThrow(new RuntimeException("Service unavailable"));

        // When
        ResponseEntity<List<VehiculeDto>> response = incidentController.getAllVehicules();

        // Then
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());
        assertNull(response.getBody());
        verify(vehiculeClient).getAllVehicules();
    }

    @Test
    void getIncidentStatsByDateRange_shouldReturnStatistics() {
        // Given
        LocalDate debut = LocalDate.of(2023, 5, 1);
        LocalDate fin = LocalDate.of(2023, 5, 31);
        when(incidentService.getIncidentsByDateRange(debut, fin)).thenReturn(incidents);
        List<VehiculeDto> allVehicules = Arrays.asList(vehiculeDto);
        when(vehiculeClient.getAllVehicules()).thenReturn(allVehicules);

        // When
        ResponseEntity<Map<String, Object>> response = incidentController.getIncidentStatsByDateRange(debut, fin);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(2L, response.getBody().get("totalIncidents"));
        assertEquals(1L, response.getBody().get("openIncidents"));
        assertEquals(1L, response.getBody().get("inProgressIncidents"));
        assertEquals(0L, response.getBody().get("resolvedIncidents"));
        assertNotNull(response.getBody().get("countByType"));
        assertNotNull(response.getBody().get("countByVehicule"));
        assertNotNull(response.getBody().get("vehiculeImmatriculations"));
        verify(incidentService).getIncidentsByDateRange(debut, fin);
        verify(vehiculeClient).getAllVehicules();
    }

    @Test
    void searchIncidents_shouldFilterIncidentsByAllCriteria() {
        // Given
        when(incidentService.getAllIncidents()).thenReturn(incidents);

        // When
        ResponseEntity<List<IncidentResponseDTO>> response = incidentController.searchIncidents(
                StatusIncident.A_TRAITER,
                TypeIncident.ACCIDENT,
                NiveauPrioriteIncident.FAIBLE,
                LocalDate.of(2023, 5, 1),
                LocalDate.of(2023, 5, 31),
                "Champs",
                100L
        );

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());
        assertEquals(responseDTO1.getId(), response.getBody().get(0).getId());
        verify(incidentService).getAllIncidents();
    }

    @Test
    void searchIncidents_shouldReturnEmptyListWhenNoIncidentsMatchCriteria() {
        // Given
        when(incidentService.getAllIncidents()).thenReturn(incidents);

        // When
        ResponseEntity<List<IncidentResponseDTO>> response = incidentController.searchIncidents(
                StatusIncident.RESOLU,
                null,
                null,
                null,
                null,
                null,
                null
        );

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isEmpty());
        verify(incidentService).getAllIncidents();
    }

    @Test
    void searchIncidents_shouldReturnAllIncidentsWhenNoCriteriaSpecified() {
        // Given
        when(incidentService.getAllIncidents()).thenReturn(incidents);

        // When
        ResponseEntity<List<IncidentResponseDTO>> response = incidentController.searchIncidents(
                null,
                null,
                null,
                null,
                null,
                null,
                null
        );

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(incidents, response.getBody());
        verify(incidentService).getAllIncidents();
    }
}
