package otbs.ms_incident.dto.dashboard;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class VehicleIncidentDistributionDTOTest {

    @Test
    void testNoArgsConstructor() {
        // When
        VehicleIncidentDistributionDTO dto = new VehicleIncidentDistributionDTO();

        // Then
        assertNull(dto.getCountByVehicle());
        assertNull(dto.getVehicleImmatriculations());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);
        countByVehicle.put(2L, 3L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        // When
        VehicleIncidentDistributionDTO dto = new VehicleIncidentDistributionDTO(countByVehicle, vehicleImmatriculations);

        // Then
        assertEquals(countByVehicle, dto.getCountByVehicle());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
        assertEquals(5L, dto.getCountByVehicle().get(1L));
        assertEquals(3L, dto.getCountByVehicle().get(2L));
        assertEquals("ABC123", dto.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", dto.getVehicleImmatriculations().get(2L));
    }

    @Test
    void testBuilder() {
        // Given
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);
        countByVehicle.put(2L, 3L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        // When
        VehicleIncidentDistributionDTO dto = VehicleIncidentDistributionDTO.builder()
                .countByVehicle(countByVehicle)
                .vehicleImmatriculations(vehicleImmatriculations)
                .build();

        // Then
        assertEquals(countByVehicle, dto.getCountByVehicle());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
        assertEquals(5L, dto.getCountByVehicle().get(1L));
        assertEquals(3L, dto.getCountByVehicle().get(2L));
        assertEquals("ABC123", dto.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", dto.getVehicleImmatriculations().get(2L));
    }

    @Test
    void testSettersAndGetters() {
        // Given
        VehicleIncidentDistributionDTO dto = new VehicleIncidentDistributionDTO();
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);
        countByVehicle.put(2L, 3L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        // When
        dto.setCountByVehicle(countByVehicle);
        dto.setVehicleImmatriculations(vehicleImmatriculations);

        // Then
        assertEquals(countByVehicle, dto.getCountByVehicle());
        assertEquals(vehicleImmatriculations, dto.getVehicleImmatriculations());
        assertEquals(5L, dto.getCountByVehicle().get(1L));
        assertEquals(3L, dto.getCountByVehicle().get(2L));
        assertEquals("ABC123", dto.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", dto.getVehicleImmatriculations().get(2L));
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        Map<Long, Long> countByVehicle1 = new HashMap<>();
        countByVehicle1.put(1L, 5L);
        countByVehicle1.put(2L, 3L);

        Map<Long, String> vehicleImmatriculations1 = new HashMap<>();
        vehicleImmatriculations1.put(1L, "ABC123");
        vehicleImmatriculations1.put(2L, "DEF456");

        Map<Long, Long> countByVehicle2 = new HashMap<>();
        countByVehicle2.put(1L, 5L);
        countByVehicle2.put(2L, 3L);

        Map<Long, String> vehicleImmatriculations2 = new HashMap<>();
        vehicleImmatriculations2.put(1L, "ABC123");
        vehicleImmatriculations2.put(2L, "DEF456");

        Map<Long, Long> countByVehicle3 = new HashMap<>();
        countByVehicle3.put(1L, 10L);
        countByVehicle3.put(2L, 6L);

        Map<Long, String> vehicleImmatriculations3 = new HashMap<>();
        vehicleImmatriculations3.put(1L, "GHI789");
        vehicleImmatriculations3.put(2L, "JKL012");

        VehicleIncidentDistributionDTO dto1 = new VehicleIncidentDistributionDTO(countByVehicle1, vehicleImmatriculations1);
        VehicleIncidentDistributionDTO dto2 = new VehicleIncidentDistributionDTO(countByVehicle2, vehicleImmatriculations2);
        VehicleIncidentDistributionDTO dto3 = new VehicleIncidentDistributionDTO(countByVehicle3, vehicleImmatriculations3);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testToString() {
        // Given
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);
        countByVehicle.put(2L, 3L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");
        vehicleImmatriculations.put(2L, "DEF456");

        VehicleIncidentDistributionDTO dto = new VehicleIncidentDistributionDTO(countByVehicle, vehicleImmatriculations);

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("countByVehicle="));
        assertTrue(result.contains("vehicleImmatriculations="));
    }

    @Test
    void testEqualsWithSameObject() {
        // Given
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentDistributionDTO dto = new VehicleIncidentDistributionDTO(countByVehicle, vehicleImmatriculations);

        // Then
        assertEquals(dto, dto);
    }

    @Test
    void testEqualsWithNull() {
        // Given
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentDistributionDTO dto = new VehicleIncidentDistributionDTO(countByVehicle, vehicleImmatriculations);

        // Then
        assertNotEquals(null, dto);
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Given
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentDistributionDTO dto = new VehicleIncidentDistributionDTO(countByVehicle, vehicleImmatriculations);

        // Then
        assertNotEquals("Not a DTO", dto);
    }

    @Test
    void testBuilderWithNullMaps() {
        // When
        VehicleIncidentDistributionDTO dto = VehicleIncidentDistributionDTO.builder()
                .countByVehicle(null)
                .vehicleImmatriculations(null)
                .build();

        // Then
        assertNull(dto.getCountByVehicle());
        assertNull(dto.getVehicleImmatriculations());
    }

    @Test
    void testEqualsWithDifferentCountByVehicle() {
        // Given
        Map<Long, Long> countByVehicle1 = new HashMap<>();
        countByVehicle1.put(1L, 5L);

        Map<Long, Long> countByVehicle2 = new HashMap<>();
        countByVehicle2.put(1L, 10L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentDistributionDTO dto1 = new VehicleIncidentDistributionDTO(countByVehicle1, vehicleImmatriculations);
        VehicleIncidentDistributionDTO dto2 = new VehicleIncidentDistributionDTO(countByVehicle2, vehicleImmatriculations);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithDifferentVehicleImmatriculations() {
        // Given
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);

        Map<Long, String> vehicleImmatriculations1 = new HashMap<>();
        vehicleImmatriculations1.put(1L, "ABC123");

        Map<Long, String> vehicleImmatriculations2 = new HashMap<>();
        vehicleImmatriculations2.put(1L, "DEF456");

        VehicleIncidentDistributionDTO dto1 = new VehicleIncidentDistributionDTO(countByVehicle, vehicleImmatriculations1);
        VehicleIncidentDistributionDTO dto2 = new VehicleIncidentDistributionDTO(countByVehicle, vehicleImmatriculations2);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithOneNullCountByVehicle() {
        // Given
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentDistributionDTO dto1 = new VehicleIncidentDistributionDTO(countByVehicle, vehicleImmatriculations);
        VehicleIncidentDistributionDTO dto2 = new VehicleIncidentDistributionDTO(null, vehicleImmatriculations);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithOneNullVehicleImmatriculations() {
        // Given
        Map<Long, Long> countByVehicle = new HashMap<>();
        countByVehicle.put(1L, 5L);

        Map<Long, String> vehicleImmatriculations = new HashMap<>();
        vehicleImmatriculations.put(1L, "ABC123");

        VehicleIncidentDistributionDTO dto1 = new VehicleIncidentDistributionDTO(countByVehicle, vehicleImmatriculations);
        VehicleIncidentDistributionDTO dto2 = new VehicleIncidentDistributionDTO(countByVehicle, null);

        // Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEqualsWithBothNullMaps() {
        // Given
        VehicleIncidentDistributionDTO dto1 = new VehicleIncidentDistributionDTO(null, null);
        VehicleIncidentDistributionDTO dto2 = new VehicleIncidentDistributionDTO(null, null);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testHashCodeWithNullMaps() {
        // Given
        VehicleIncidentDistributionDTO dto = new VehicleIncidentDistributionDTO(null, null);

        // When & Then
        assertDoesNotThrow(dto::hashCode);
    }
}
