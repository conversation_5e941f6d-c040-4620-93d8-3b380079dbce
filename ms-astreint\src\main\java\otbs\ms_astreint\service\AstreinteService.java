package otbs.ms_astreint.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import otbs.ms_astreint.client.keycloak.KeycloakService;
import otbs.ms_astreint.dto.AstreinteDto;
import otbs.ms_astreint.mapper.AstreinteMapper;
import otbs.ms_astreint.model.Astreinte;
import otbs.ms_astreint.model.ConsultantAstreinte;
import otbs.ms_astreint.repository.AstreinteRepository;
import otbs.ms_astreint.repository.ConsultantAstreinteRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class AstreinteService {

    private final AstreinteRepository astreinteRepository;
    private final ConsultantAstreinteRepository consultantAstreinteRepository;
    private final AstreinteMapper astreinteMapper;
    private final KeycloakService keycloakService;
    private final NotificationEventPublisher notificationEventPublisher;

    @Transactional(readOnly = true)
    public List<AstreinteDto> getAllAstreintes() {
        return astreinteMapper.toDtoList(astreinteRepository.findAll());
    }

    @Transactional(readOnly = true)
    public Optional<AstreinteDto> getAstreinteById(Long id) {
        return astreinteRepository.findById(id)
                .map(astreinteMapper::toDto);
    }

    @Transactional
    public AstreinteDto createAstreinte(AstreinteDto astreinteDto) {
        if (astreinteDto.getDateDebut() != null && astreinteDto.getDateFin() != null 
            && astreinteDto.getDateDebut().isAfter(astreinteDto.getDateFin())) {
            throw new IllegalArgumentException("La date de début doit être antérieure à la date de fin");
        }
        
        if (astreinteDto.getConsultants() != null) {
            astreinteDto.getConsultants().forEach(consultantDto -> {
                if (!keycloakService.isValidConsultant(consultantDto.getConsultant())) {
                    throw new IllegalArgumentException("Consultant invalide: " + consultantDto.getConsultant());
                }
            });
        }
        
        Astreinte astreinte = astreinteMapper.toEntity(astreinteDto);
        Astreinte savedAstreinte = astreinteRepository.save(astreinte);
        
        try {
            notificationEventPublisher.publishAstreinteCreated(savedAstreinte);
            log.info("Notification de création d'astreinte publiée pour l'astreinte {}", savedAstreinte.getIdAstreinte());
        } catch (Exception e) {
            log.error("Erreur lors de la publication de la notification de création d'astreinte {}: {}", 
                     savedAstreinte.getIdAstreinte(), e.getMessage(), e);
        }
        
        return astreinteMapper.toDto(savedAstreinte);
    }

    @Transactional
    public Optional<AstreinteDto> updateAstreinte(Long id, AstreinteDto astreinteDto) {
        return astreinteRepository.findById(id)
                .map(existingAstreinte -> {
                    if (astreinteDto.getDateDebut() != null && astreinteDto.getDateFin() != null 
                        && astreinteDto.getDateDebut().isAfter(astreinteDto.getDateFin())) {
                        throw new IllegalArgumentException("La date de début doit être antérieure à la date de fin");
                    }
                    
                    if (astreinteDto.getConsultants() != null) {
                        astreinteDto.getConsultants().forEach(consultantDto -> {
                            if (!keycloakService.isValidConsultant(consultantDto.getConsultant())) {
                                throw new IllegalArgumentException("Consultant invalide: " + consultantDto.getConsultant());
                            }
                        });
                    }
                    
                    existingAstreinte.setDateDebut(astreinteDto.getDateDebut());
                    existingAstreinte.setDateFin(astreinteDto.getDateFin());
                    existingAstreinte.setDescription(astreinteDto.getDescription());
                    
                    existingAstreinte.getConsultants().clear();
                    
                    if (astreinteDto.getConsultants() != null) {
                        astreinteDto.getConsultants().forEach(consultantDto -> 
                            existingAstreinte.ajouterConsultant(consultantDto.getConsultant(), consultantDto.getNiveau())
                        );
                    }
                    
                    Astreinte updatedAstreinte = astreinteRepository.save(existingAstreinte);
                    
                    try {
                        notificationEventPublisher.publishAstreinteUpdated(updatedAstreinte);
                        log.info("Notification de mise à jour d'astreinte publiée pour l'astreinte {}", updatedAstreinte.getIdAstreinte());
                    } catch (Exception e) {
                        log.error("Erreur lors de la publication de la notification de mise à jour d'astreinte {}: {}", 
                                 updatedAstreinte.getIdAstreinte(), e.getMessage(), e);
                    }
                    
                    return astreinteMapper.toDto(updatedAstreinte);
                });
    }

    @Transactional
    public boolean deleteAstreinte(Long id) {
        return astreinteRepository.findById(id)
                .map(astreinte -> {
                    try {
                        notificationEventPublisher.publishAstreinteDeleted(astreinte);
                        log.info("Notification de suppression d'astreinte publiée pour l'astreinte {}", astreinte.getIdAstreinte());
                    } catch (Exception e) {
                        log.error("Erreur lors de la publication de la notification de suppression d'astreinte {}: {}", 
                                 astreinte.getIdAstreinte(), e.getMessage(), e);
                    }
                    
                    astreinteRepository.delete(astreinte);
                    return true;
                })
                .orElse(false);
    }

    @Transactional(readOnly = true)
    public List<AstreinteDto> getAstreintesByConsultant(String consultant) {
        List<Astreinte> astreintes = astreinteRepository.findByConsultantsConsultant(consultant);
        return astreinteMapper.toDtoList(astreintes);
    }

    @Transactional(readOnly = true)
    public Long countConsultantsByAstreinteId(Long astreinteId) {
        Astreinte astreinte = astreinteRepository.findById(astreinteId)
            .orElseThrow(() -> new RuntimeException("Astreinte not found with id: " + astreinteId));
        return (long) astreinte.getConsultants().size();
    }

    @Transactional(readOnly = true)
    public List<AstreinteDto> findByDateDebutAfter() {
        LocalDateTime now = LocalDateTime.now();
        List<Astreinte> astreintes = astreinteRepository.findByDateDebutAfter(now);
        return astreinteMapper.toDtoList(astreintes);
    }

    @Transactional(readOnly = true)
    public List<AstreinteDto> findByDateDebutAfterAndConsultantUserId(String consultantId) {
        LocalDateTime now = LocalDateTime.now();
        List<Astreinte> astreintes = astreinteRepository.findByUserIdAsConsultantAndDateDebutAfter(consultantId, now);
        return astreinteMapper.toDtoList(astreintes);
    }

    @Transactional(readOnly = true)
    public List<AstreinteDto> findAstreintesByConsultantAndDateRange(String consultantId, LocalDateTime dateDebut, LocalDateTime dateFin) {
        List<Astreinte> astreintes = astreinteRepository.findAstreintesByConsultantAndDateRange(consultantId, dateDebut, dateFin);
        return astreinteMapper.toDtoList(astreintes);
    }

    @Transactional(readOnly = true)
    public List<AstreinteDto> findAstreintesByDateRange(LocalDateTime dateDebut, LocalDateTime dateFin) {
        List<Astreinte> astreintes = astreinteRepository.findAstreintesByDateRange(dateDebut, dateFin);
        return astreinteMapper.toDtoList(astreintes);
    }
}