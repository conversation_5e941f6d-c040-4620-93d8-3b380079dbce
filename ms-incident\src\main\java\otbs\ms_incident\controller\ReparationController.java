package otbs.ms_incident.controller;

import otbs.ms_incident.dto.ReparationRequestDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.service.ReparationService;
import otbs.ms_incident.client.VehiculeClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import otbs.ms_incident.commun.PageResponse;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import otbs.ms_incident.enums.StatusReparation;

/**
 * Contrôleur REST pour la gestion des réparations.
 * Cette classe expose les endpoints de l'API permettant de créer, lire, mettre à jour et supprimer
 * des réparations dans le système ParcAuto. Elle offre également des fonctionnalités de recherche
 * avancées selon différents critères.
 *
 * @version 0.0.1-SNAPSHOT
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/api/reparations")
@Tag(name = "Réparation", description = "API pour la gestion des réparations")
@RequiredArgsConstructor
@Slf4j
public class ReparationController {

    /**
     * service métier pour la gestion des réparations.
     * Injecté automatiquement par Spring grâce à l'annotation @RequiredArgsConstructor.
     */
    private final ReparationService reparationService;
    private final VehiculeClient vehiculeClient;

    @Operation(summary = "Créer une réparation", description = "Crée une nouvelle réparation dans le système")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Réparation créée avec succès",
                    content = @Content(schema = @Schema(implementation = ReparationResponseDTO.class))),
            @ApiResponse(responseCode = "400", description = "Données invalides"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé"),
            @ApiResponse(responseCode = "404", description = "Incident non trouvé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX')")
    @PostMapping
    public ResponseEntity<ReparationResponseDTO> createReparation(@Valid @RequestBody ReparationRequestDTO requestDTO) {
        log.info("REST request to create reparation: {}", requestDTO);
        ReparationResponseDTO responseDTO = reparationService.createReparation(requestDTO);
        return new ResponseEntity<>(responseDTO, HttpStatus.CREATED);
    }

    @Operation(summary = "Récupérer une réparation par ID", description = "Retourne une réparation basée sur son ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Réparation trouvée",
                    content = @Content(schema = @Schema(implementation = ReparationResponseDTO.class))),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé"),
            @ApiResponse(responseCode = "404", description = "Réparation non trouvée")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/{id}")
    public ResponseEntity<ReparationResponseDTO> getReparationById(
            @Parameter(description = "ID de la réparation à récupérer") @PathVariable Long id) {
        log.info("REST request to get reparation with ID: {}", id);
        ReparationResponseDTO responseDTO = reparationService.getReparationById(id);
        return ResponseEntity.ok(responseDTO);
    }

    @Operation(summary = "Récupérer toutes les réparations", description = "Retourne la liste de toutes les réparations")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des réparations récupérée"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping
    public ResponseEntity<List<ReparationResponseDTO>> getAllReparations() {
        log.info("REST request to get all reparations");
        List<ReparationResponseDTO> reparations = reparationService.getAllReparations();
        return ResponseEntity.ok(reparations);
    }

    @Operation(summary = "Récupérer toutes les réparations paginées", description = "Retourne la liste paginée de toutes les réparations")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des réparations récupérée"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/paginated")
    public ResponseEntity<PageResponse<ReparationResponseDTO>> getAllReparationsPaginated(
            @Parameter(description = "Numéro de page (commence à 0)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Taille de la page") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Champ de tri") @RequestParam(defaultValue = "id") String sort,
            @Parameter(description = "Direction du tri (ASC ou DESC)") @RequestParam(defaultValue = "DESC") String direction) {
        log.info("REST request to get paginated reparations: page={}, size={}, sort={}, direction={}", page, size, sort, direction);

        try {
            Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
            log.debug("Creating pageable with page={}, size={}, sort={}, direction={}", page, size, sort, sortDirection);

            Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
            log.debug("Pageable created successfully: {}", pageable);

            log.debug("Calling reparationService.getAllReparationsPaginated");
            PageResponse<ReparationResponseDTO> reparations = reparationService.getAllReparationsPaginated(pageable);
            log.debug("Received response from service with items");

            return ResponseEntity.ok(reparations);
        } catch (Exception e) {
            log.error("Error while getting paginated reparations", e);
            throw e;
        }
    }

    @Operation(summary = "Récupérer les réparations par incident",
            description = "Retourne la liste des réparations associées à un incident")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des réparations récupérée"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé"),
            @ApiResponse(responseCode = "404", description = "Incident non trouvé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/incident/{incidentId}")
    public ResponseEntity<List<ReparationResponseDTO>> getReparationsByIncidentId(
            @Parameter(description = "ID de l'incident") @PathVariable Long incidentId) {
        log.info("REST request to get reparations for incident with ID: {}", incidentId);
        List<ReparationResponseDTO> reparations = reparationService.getReparationsByIncidentId(incidentId);
        return ResponseEntity.ok(reparations);
    }

    @Operation(summary = "Récupérer les réparations par incident avec pagination",
            description = "Retourne la liste paginée des réparations associées à un incident")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des réparations récupérée"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé"),
            @ApiResponse(responseCode = "404", description = "Incident non trouvé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/incident/{incidentId}/paginated")
    public ResponseEntity<PageResponse<ReparationResponseDTO>> getReparationsByIncidentIdPaginated(
            @Parameter(description = "ID de l'incident") @PathVariable Long incidentId,
            @Parameter(description = "Numéro de page (commence à 0)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Taille de la page") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Champ de tri") @RequestParam(defaultValue = "id") String sort,
            @Parameter(description = "Direction du tri (ASC ou DESC)") @RequestParam(defaultValue = "DESC") String direction) {
        log.info("REST request to get paginated reparations for incident with ID: {}, page={}, size={}, sort={}, direction={}",
                incidentId, page, size, sort, direction);

        try {
            Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
            log.debug("Creating pageable with page={}, size={}, sort={}, direction={}", page, size, sort, sortDirection);

            Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
            log.debug("Pageable created successfully: {}", pageable);

            log.debug("Calling reparationService.getReparationsByIncidentIdPaginated with incidentId={}", incidentId);
            PageResponse<ReparationResponseDTO> reparations = reparationService.getReparationsByIncidentIdPaginated(incidentId, pageable);
            log.debug("Received response from service with items");

            return ResponseEntity.ok(reparations);
        } catch (Exception e) {
            log.error("Error while getting paginated reparations for incident with ID: {}", incidentId, e);
            throw e;
        }
    }

    @Operation(summary = "Mettre à jour une réparation", description = "Met à jour une réparation existante")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Réparation mise à jour"),
            @ApiResponse(responseCode = "400", description = "Données invalides"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé"),
            @ApiResponse(responseCode = "404", description = "Réparation non trouvée")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX')")
    @PutMapping("/{id}")
    public ResponseEntity<ReparationResponseDTO> updateReparation(
            @Parameter(description = "ID de la réparation à mettre à jour") @PathVariable Long id,
            @Valid @RequestBody ReparationRequestDTO requestDTO) {
        log.info("REST request to update reparation with ID: {}", id);
        ReparationResponseDTO responseDTO = reparationService.updateReparation(id, requestDTO);
        return ResponseEntity.ok(responseDTO);
    }

    @Operation(summary = "Supprimer une réparation", description = "Supprime une réparation par son ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Réparation supprimée"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé"),
            @ApiResponse(responseCode = "404", description = "Réparation non trouvée")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX')")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteReparation(
            @Parameter(description = "ID de la réparation à supprimer") @PathVariable Long id) {
        log.info("REST request to delete reparation with ID: {}", id);
        reparationService.deleteReparation(id);
        return ResponseEntity.noContent().build();
    }





    @Operation(summary = "Récupérer les réparations par période",
            description = "Retourne la liste des réparations effectuées entre les dates spécifiées")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des réparations récupérée"),
            @ApiResponse(responseCode = "400", description = "Plage de dates invalide"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/date-range")
    public ResponseEntity<List<ReparationResponseDTO>> getReparationsByDateRange(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        log.info("REST request to get reparations between dates: {} and {}", startDate, endDate);
        List<ReparationResponseDTO> reparations = reparationService.getReparationsByDateRange(startDate, endDate);
        return ResponseEntity.ok(reparations);
    }



    @Operation(summary = "Récupérer les réparations par statut et incident",
            description = "Retourne la liste des réparations filtrées par statut et par incident")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des réparations récupérée"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé"),
            @ApiResponse(responseCode = "404", description = "Incident non trouvé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/status/{status}/incident/{incidentId}")
    public ResponseEntity<List<ReparationResponseDTO>> getReparationsByStatusAndIncidentId(
            @Parameter(description = "Statut des réparations") @PathVariable StatusReparation status,
            @Parameter(description = "ID de l'incident") @PathVariable Long incidentId) {
        log.info("REST request to get reparations by status: {} and incident ID: {}", status, incidentId);
        List<ReparationResponseDTO> reparations = reparationService.getReparationsByStatusAndIncidentId(status, incidentId);
        return ResponseEntity.ok(reparations);
    }

    @Operation(summary = "Mettre à jour le statut d'une réparation",
            description = "Met à jour uniquement le statut d'une réparation existante")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statut de la réparation mis à jour"),
            @ApiResponse(responseCode = "400", description = "Statut invalide"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé"),
            @ApiResponse(responseCode = "404", description = "Réparation non trouvée")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX')")
    @PatchMapping("/{id}/status/{status}")
    public ResponseEntity<ReparationResponseDTO> updateReparationStatus(
            @Parameter(description = "ID de la réparation à mettre à jour") @PathVariable Long id,
            @Parameter(description = "Nouveau statut de la réparation") @PathVariable StatusReparation status) {
        log.info("REST request to update status of reparation with ID: {} to {}", id, status);
        ReparationResponseDTO responseDTO = reparationService.updateReparationStatus(id, status);
        return ResponseEntity.ok(responseDTO);
    }

    @Operation(summary = "Rechercher des réparations avec filtres",
            description = "Retourne la liste des réparations correspondant aux critères de recherche")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des réparations récupérée"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/search")
    public ResponseEntity<List<ReparationResponseDTO>> searchReparations(
            @Parameter(description = "Statut des réparations") @RequestParam(required = false) StatusReparation status,
            @Parameter(description = "ID de l'incident") @RequestParam(required = false) Long incidentId,
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
                @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
                @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "Coût minimum") @RequestParam(required = false) BigDecimal minCost,
            @Parameter(description = "Coût maximum") @RequestParam(required = false) BigDecimal maxCost,
            @Parameter(description = "Nom du garage (recherche partielle)") @RequestParam(required = false) String garage) {
        log.info("REST request to search reparations with filters: status={}, incidentId={}, startDate={}, endDate={}, minCost={}, maxCost={}, garage={}",
                status, incidentId, startDate, endDate, minCost, maxCost, garage);

        List<ReparationResponseDTO> reparations = reparationService.searchReparations(status, incidentId, startDate, endDate, minCost, maxCost, garage);
        return ResponseEntity.ok(reparations);
    }

    @Operation(summary = "Obtenir le coût total des réparations",
            description = "Retourne la somme des coûts de toutes les réparations")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Coût total récupéré"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/stats/total-cost")
    public ResponseEntity<Map<String, BigDecimal>> getTotalCost() {
        log.info("REST request to get total cost of all reparations");
        BigDecimal totalCost = reparationService.getTotalCost();
        Map<String, BigDecimal> response = Map.of("value", totalCost);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Obtenir le coût moyen des réparations",
            description = "Retourne la moyenne des coûts de toutes les réparations")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Coût moyen récupéré"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/stats/average-cost")
    public ResponseEntity<Map<String, BigDecimal>> getAverageCost() {
        log.info("REST request to get average cost of all reparations");
        BigDecimal averageCost = reparationService.getAverageCost();
        Map<String, BigDecimal> response = Map.of("value", averageCost);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Obtenir le nombre de réparations par statut",
            description = "Retourne le nombre de réparations pour chaque statut")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistiques récupérées"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/stats/count-by-status")
    public ResponseEntity<Map<String, Long>> getRepairCountByStatus() {
        log.info("REST request to get repair count by status");
        Map<String, Long> countByStatus = reparationService.getRepairCountByStatus();
        return ResponseEntity.ok(countByStatus);
    }

    
}