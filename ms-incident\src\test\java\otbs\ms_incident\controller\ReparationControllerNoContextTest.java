package otbs.ms_incident.controller;

import otbs.ms_incident.dto.ReparationRequestDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.exception.BadRequestException;
import otbs.ms_incident.service.ReparationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;


import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReparationControllerNoContextTest {

    @Mock
    private ReparationService reparationService;

    @InjectMocks
    private ReparationController reparationController;

    private ReparationRequestDTO requestDTO;
    private ReparationResponseDTO responseDTO;
    private List<ReparationResponseDTO> allReparations;

    @BeforeEach
    void setUp() {
        // Setup test data
        requestDTO = ReparationRequestDTO.builder()
                .dateReparation(LocalDate.of(2023, 6, 1))
                .description("Remplacement pare-choc avant")
                .cout(new BigDecimal("1200.50"))
                .garage("Garage Central")
                .incidentId(1L)
                .build();

        responseDTO = ReparationResponseDTO.builder()
                .id(1L)
                .dateReparation(LocalDate.of(2023, 6, 1))
                .description("Remplacement pare-choc avant")
                .cout(new BigDecimal("1200.50"))
                .garage("Garage Central")
                .incidentId(1L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        ReparationResponseDTO reparation2 = ReparationResponseDTO.builder()
                .id(2L)
                .dateReparation(LocalDate.of(2023, 7, 15))
                .description("Remplacement moteur")
                .cout(new BigDecimal("5000.00"))
                .garage("Garage Expert")
                .incidentId(2L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        allReparations = Arrays.asList(responseDTO, reparation2);
    }

    @Test
    void createReparation_shouldReturnCreatedReparation() {
        // Given
        when(reparationService.createReparation(any(ReparationRequestDTO.class))).thenReturn(responseDTO);

        // When
        ResponseEntity<ReparationResponseDTO> response = reparationController.createReparation(requestDTO);

        // Then
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        assertEquals("Remplacement pare-choc avant", response.getBody().getDescription());
        assertEquals(0, new BigDecimal("1200.50").compareTo(response.getBody().getCout()));
        verify(reparationService, times(1)).createReparation(requestDTO);
    }

    @Test
    void createReparation_shouldHandleResourceNotFoundException() {
        // Given
        when(reparationService.createReparation(any(ReparationRequestDTO.class)))
            .thenThrow(new ResourceNotFoundException("Incident not found with id: '1'"));

        // When/Then
        assertThrows(ResourceNotFoundException.class, () -> reparationController.createReparation(requestDTO));
        verify(reparationService).createReparation(requestDTO);
    }

    @Test
    void createReparation_shouldHandleBadRequestException() {
        // Given
        when(reparationService.createReparation(any(ReparationRequestDTO.class)))
            .thenThrow(new BadRequestException("Invalid data provided for reparation"));

        // When/Then
        assertThrows(BadRequestException.class, () -> reparationController.createReparation(requestDTO));
        verify(reparationService).createReparation(requestDTO);
    }

    @Test
    void getReparationById_shouldReturnReparation() {
        // Given
        when(reparationService.getReparationById(1L)).thenReturn(responseDTO);

        // When
        ResponseEntity<ReparationResponseDTO> response = reparationController.getReparationById(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        assertEquals("Remplacement pare-choc avant", response.getBody().getDescription());
        verify(reparationService, times(1)).getReparationById(1L);
    }

    @Test
    void getReparationById_shouldHandleResourceNotFoundException() {
        // Given
        when(reparationService.getReparationById(999L))
            .thenThrow(new ResourceNotFoundException("Reparation not found with id: '999'"));

        // When/Then
        assertThrows(ResourceNotFoundException.class, () -> reparationController.getReparationById(999L));
        verify(reparationService).getReparationById(999L);
    }

    @Test
    void getAllReparations_shouldReturnAllReparations() {
        // Given
        when(reparationService.getAllReparations()).thenReturn(allReparations);

        // When
        ResponseEntity<List<ReparationResponseDTO>> response = reparationController.getAllReparations();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().size());
        assertEquals(1L, response.getBody().get(0).getId());
        assertEquals(2L, response.getBody().get(1).getId());
        verify(reparationService, times(1)).getAllReparations();
    }

    @Test
    void getAllReparations_shouldReturnEmptyList() {
        // Given
        when(reparationService.getAllReparations()).thenReturn(Collections.emptyList());

        // When
        ResponseEntity<List<ReparationResponseDTO>> response = reparationController.getAllReparations();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().isEmpty());
        verify(reparationService).getAllReparations();
    }

    @Test
    void updateReparation_shouldReturnUpdatedReparation() {
        // Given
        when(reparationService.updateReparation(eq(1L), any(ReparationRequestDTO.class))).thenReturn(responseDTO);

        // When
        ResponseEntity<ReparationResponseDTO> response = reparationController.updateReparation(1L, requestDTO);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        assertEquals("Remplacement pare-choc avant", response.getBody().getDescription());
        verify(reparationService, times(1)).updateReparation(1L, requestDTO);
    }

    @Test
    void updateReparation_shouldHandleResourceNotFoundException() {
        // Given
        when(reparationService.updateReparation(eq(999L), any(ReparationRequestDTO.class)))
            .thenThrow(new ResourceNotFoundException("Reparation not found with id: '999'"));

        // When/Then
        assertThrows(ResourceNotFoundException.class,
            () -> reparationController.updateReparation(999L, requestDTO));
        verify(reparationService).updateReparation(999L, requestDTO);
    }

    @Test
    void updateReparation_shouldHandleBadRequestException() {
        // Given
        when(reparationService.updateReparation(eq(1L), any(ReparationRequestDTO.class)))
            .thenThrow(new BadRequestException("Invalid data provided for update"));

        // When/Then
        assertThrows(BadRequestException.class,
            () -> reparationController.updateReparation(1L, requestDTO));
        verify(reparationService).updateReparation(1L, requestDTO);
    }

    @Test
    void deleteReparation_shouldReturnNoContent() {
        // Given
        doNothing().when(reparationService).deleteReparation(1L);

        // When
        ResponseEntity<Void> response = reparationController.deleteReparation(1L);

        // Then
        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        verify(reparationService, times(1)).deleteReparation(1L);
    }

    @Test
    void deleteReparation_shouldHandleResourceNotFoundException() {
        // Given
        doThrow(new ResourceNotFoundException("Reparation not found with id: '999'"))
            .when(reparationService).deleteReparation(999L);

        // When/Then
        assertThrows(ResourceNotFoundException.class,
            () -> reparationController.deleteReparation(999L));
        verify(reparationService).deleteReparation(999L);
    }

    // Tests supprimés car l'endpoint getReparationsByGarage a été supprimé

    @Test
    void getReparationsByIncidentId_shouldReturnReparationsForIncident() {
        // Given
        Long incidentId = 1L;
        when(reparationService.getReparationsByIncidentId(incidentId))
                .thenReturn(Collections.singletonList(responseDTO));

        // When
        ResponseEntity<List<ReparationResponseDTO>> response =
                reparationController.getReparationsByIncidentId(incidentId);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());
        assertEquals(1L, response.getBody().get(0).getIncidentId());
        verify(reparationService, times(1)).getReparationsByIncidentId(incidentId);
    }

    @Test
    void getReparationsByIncidentId_shouldHandleResourceNotFoundException() {
        // Given
        Long incidentId = 999L;
        when(reparationService.getReparationsByIncidentId(incidentId))
                .thenThrow(new ResourceNotFoundException("Incident not found with id: '999'"));

        // When/Then
        assertThrows(ResourceNotFoundException.class,
            () -> reparationController.getReparationsByIncidentId(incidentId));
        verify(reparationService).getReparationsByIncidentId(incidentId);
    }

    // Tests supprimés car l'endpoint getReparationsByCoutGreaterThanEqual a été supprimé

    @Test
    void getReparationsByDateRange_shouldReturnReparationsInRange() {
        // Given
        LocalDate startDate = LocalDate.of(2023, 6, 1);
        LocalDate endDate = LocalDate.of(2023, 7, 31);
        when(reparationService.getReparationsByDateRange(startDate, endDate))
                .thenReturn(allReparations);

        // When
        ResponseEntity<List<ReparationResponseDTO>> response =
                reparationController.getReparationsByDateRange(startDate, endDate);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().size());
        assertEquals(LocalDate.of(2023, 6, 1), response.getBody().get(0).getDateReparation());
        assertEquals(LocalDate.of(2023, 7, 15), response.getBody().get(1).getDateReparation());
        verify(reparationService, times(1)).getReparationsByDateRange(startDate, endDate);
    }

    @Test
    void getReparationsByDateRange_shouldHandleBadRequestException() {
        // Given
        LocalDate startDate = LocalDate.of(2023, 7, 31);
        LocalDate endDate = LocalDate.of(2023, 6, 1);
        when(reparationService.getReparationsByDateRange(startDate, endDate))
                .thenThrow(new BadRequestException("Start date must be before or equal to end date"));

        // When/Then
        assertThrows(BadRequestException.class,
            () -> reparationController.getReparationsByDateRange(startDate, endDate));
        verify(reparationService).getReparationsByDateRange(startDate, endDate);
    }
}