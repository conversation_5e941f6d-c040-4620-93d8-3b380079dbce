package otbs.ms_incident.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration RabbitMQ pour le publisher d'événements d'incidents.
 * Configure l'exchange, les queues et les routing keys pour les notifications.
 */
@Configuration
public class RabbitMQPublisherConfig {

    // Noms des exchanges et queues selon le README
    public static final String NOTIFICATIONS_EXCHANGE = "parcauto.notifications";
    public static final String INCIDENT_NOTIFICATIONS_QUEUE = "incident.notifications";
    public static final String INCIDENT_CREATED_ROUTING_KEY = "incident.created";
    public static final String INCIDENT_RESOLVED_ROUTING_KEY = "incident.resolved";

    /**
     * Exchange principal pour les notifications
     */
    @Bean
    public TopicExchange notificationsExchange() {
        return ExchangeBuilder
                .topicExchange(NOTIFICATIONS_EXCHANGE)
                .durable(true)
                .build();
    }

    /**
     * Queue pour les notifications d'incidents
     */
    @Bean
    public Queue incidentNotificationsQueue() {
        return QueueBuilder
                .durable(INCIDENT_NOTIFICATIONS_QUEUE)
                .build();
    }

    /**
     * Binding pour les événements de création d'incidents
     */
    @Bean
    public Binding incidentCreatedBinding() {
        return BindingBuilder
                .bind(incidentNotificationsQueue())
                .to(notificationsExchange())
                .with(INCIDENT_CREATED_ROUTING_KEY);
    }

    /**
     * Binding pour les événements de résolution d'incidents
     */
    @Bean
    public Binding incidentResolvedBinding() {
        return BindingBuilder
                .bind(incidentNotificationsQueue())
                .to(notificationsExchange())
                .with(INCIDENT_RESOLVED_ROUTING_KEY);
    }

    /**
     * Template RabbitMQ avec sérialisation JSON
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate template = new RabbitTemplate(connectionFactory);
        template.setMessageConverter(new Jackson2JsonMessageConverter());
        return template;
    }
}
