package otbs.ms_incident.client;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO représentant un véhicule provenant du microservice vehicule.
 * Cette classe contient tous les champs nécessaires pour représenter un véhicule
 * dans le contexte du microservice incident.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VehiculeDto {
    private Long idVehicule;
    private String immatriculation;
    private String marque;
    private String modele;
    private int kilometrage;
    private String categorie;
    private String etat;
    private Date datePrelevementKilometrage;
    private String typeCarburant;
    private int nombreDePlaces;
    private boolean remisCles;
    private boolean astreinte;
}
