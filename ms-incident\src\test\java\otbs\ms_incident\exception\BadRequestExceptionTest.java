package otbs.ms_incident.exception;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class BadRequestExceptionTest {

    @Test
    void testConstructorWithMessage() {
        // Arrange
        String message = "Bad request details";
        
        // Act
        BadRequestException exception = new BadRequestException(message);
        
        // Assert
        assertEquals(message, exception.getMessage());
    }
} 