package otbs.ms_incident.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * Contrôleur pour servir les vues liées au téléchargement de fichiers.
 * Fournit des endpoints pour accéder aux pages HTML sans authentification.
 */
@Controller
public class FileUploadViewController {

    /**
     * Affiche la page de test d'upload de fichiers.
     * 
     * @return Le nom de la vue à afficher
     */
    @GetMapping("/upload-test")
    public String uploadTestPage() {
        return "upload-test";
    }
} 