import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  matSelectAnimations
} from "./chunk-JPJFU6CD.js";
import "./chunk-R4Q27T4U.js";
import "./chunk-KG7ZW2JT.js";
import "./chunk-Z5DNSJNV.js";
import "./chunk-ANSRHQX3.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  MatLabel,
  MatPrefix,
  MatSuffix
} from "./chunk-6VMUFBXS.js";
import "./chunk-GJJMHVXD.js";
import "./chunk-3AFAZVIW.js";
import "./chunk-S7HXCIC7.js";
import {
  Mat<PERSON>ptgroup,
  MatOption
} from "./chunk-YQKFE6SP.js";
import "./chunk-U2YTM3FN.js";
import "./chunk-L57JFFAX.js";
import "./chunk-WPM5VTLQ.js";
import "./chunk-PEBH6BBU.js";
import "./chunk-4S3KYZTJ.js";
import "./chunk-TF6TRJEI.js";
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
