package otbs.ms_incident.repository;

import otbs.ms_incident.entity.Reparation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import otbs.ms_incident.enums.StatusReparation;

@Repository
public interface ReparationRepository extends JpaRepository<Reparation, Long> {
    List<Reparation> findByIncidentId(Long incidentId);
    Page<Reparation> findByIncidentId(Long incidentId, Pageable pageable);
    List<Reparation> findByRembourse(Boolean rembourse);

    // Query methods for filtering
    List<Reparation> findByDateReparationBetween(LocalDate debut, LocalDate fin);

    // Query methods for status
    List<Reparation> findByStatus(StatusReparation status);
    List<Reparation> findByStatusAndIncidentId(StatusReparation status, Long incidentId);

    // Query methods for cost statistics
    List<Reparation> findByCoutBetween(BigDecimal minCout, BigDecimal maxCout);
}