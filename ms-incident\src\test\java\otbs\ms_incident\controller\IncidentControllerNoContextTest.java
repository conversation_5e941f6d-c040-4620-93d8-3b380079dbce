package otbs.ms_incident.controller;

import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.enums.TypeIncident;
import otbs.ms_incident.service.IncidentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IncidentControllerNoContextTest {

    @Mock
    private IncidentService incidentService;

    @InjectMocks
    private IncidentController incidentController;

    private IncidentRequestDTO requestDTO;
    private IncidentResponseDTO responseDTO;
    private List<IncidentResponseDTO> allIncidents;
    private ReparationResponseDTO reparationDTO;

    @BeforeEach
    void setUp() {
        // Setup test data
        requestDTO = IncidentRequestDTO.builder()
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Rue de la Paix, Paris")
                .constat("CONST-2023-05-15-001")
                .photos(Arrays.asList("https://example.com/photo.jpg"))
                .description("Collision latérale")
                .build();

        responseDTO = IncidentResponseDTO.builder()
                .id(1L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Rue de la Paix, Paris")
                .constat("CONST-2023-05-15-001")
                .photos(Arrays.asList("https://example.com/photo.jpg"))
                .description("Collision latérale")
                .reparations(Collections.emptyList())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        IncidentResponseDTO incident2 = IncidentResponseDTO.builder()
                .id(2L)
                .date(LocalDate.of(2023, 6, 1))
                .type(TypeIncident.PANNE)
                .lieu("Avenue Victor Hugo, Paris")
                .constat("CONST-2023-06-01-002")
                .description("Panne moteur")
                .reparations(Collections.emptyList())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        allIncidents = Arrays.asList(responseDTO, incident2);

        reparationDTO = ReparationResponseDTO.builder()
                .id(3L)
                .dateReparation(LocalDate.of(2023, 6, 1))
                .description("Remplacement pare-choc")
                .cout(new BigDecimal("1200.50"))
                .garage("Garage Central")
                .build();
    }

    @Test
    void createIncident_shouldReturnCreatedIncident() {
        // Given
        when(incidentService.createIncident(any(IncidentRequestDTO.class))).thenReturn(responseDTO);

        // When
        ResponseEntity<IncidentResponseDTO> response = incidentController.createIncident(requestDTO);

        // Then
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        assertEquals(TypeIncident.ACCIDENT, response.getBody().getType());
        assertEquals("Rue de la Paix, Paris", response.getBody().getLieu());
        verify(incidentService, times(1)).createIncident(requestDTO);
    }

    @Test
    void getIncidentById_shouldReturnIncident() {
        // Given
        when(incidentService.getIncidentById(1L)).thenReturn(responseDTO);

        // When
        ResponseEntity<IncidentResponseDTO> response = incidentController.getIncidentById(1L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        assertEquals("Rue de la Paix, Paris", response.getBody().getLieu());
        verify(incidentService, times(1)).getIncidentById(1L);
    }

    @Test
    void getAllIncidents_shouldReturnAllIncidents() {
        // Given
        when(incidentService.getAllIncidents()).thenReturn(allIncidents);

        // When
        ResponseEntity<List<IncidentResponseDTO>> response = incidentController.getAllIncidents();

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(2, response.getBody().size());
        assertEquals(1L, response.getBody().get(0).getId());
        assertEquals(2L, response.getBody().get(1).getId());
        verify(incidentService, times(1)).getAllIncidents();
    }

    @Test
    void updateIncident_shouldReturnUpdatedIncident() {
        // Given
        when(incidentService.updateIncident(eq(1L), any(IncidentRequestDTO.class))).thenReturn(responseDTO);

        // When
        ResponseEntity<IncidentResponseDTO> response = incidentController.updateIncident(1L, requestDTO);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        assertEquals("Rue de la Paix, Paris", response.getBody().getLieu());
        verify(incidentService, times(1)).updateIncident(1L, requestDTO);
    }

    @Test
    void deleteIncident_shouldReturnNoContent() {
        // Given
        doNothing().when(incidentService).deleteIncident(1L);

        // When
        ResponseEntity<Void> response = incidentController.deleteIncident(1L);

        // Then
        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        verify(incidentService, times(1)).deleteIncident(1L);
    }

    // Test supprimé car l'endpoint getIncidentsByType a été supprimé

    @Test
    void getIncidentsByDateRange_shouldReturnIncidentsInDateRange() {
        // Given
        LocalDate startDate = LocalDate.of(2023, 5, 1);
        LocalDate endDate = LocalDate.of(2023, 5, 31);
        when(incidentService.getIncidentsByDateRange(startDate, endDate))
                .thenReturn(Collections.singletonList(responseDTO));

        // When
        ResponseEntity<List<IncidentResponseDTO>> response =
                incidentController.getIncidentsByDateRange(startDate, endDate);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());
        assertEquals(LocalDate.of(2023, 5, 15), response.getBody().get(0).getDate());
        verify(incidentService, times(1)).getIncidentsByDateRange(startDate, endDate);
    }

    @Test
    void getIncidentsByLieu_shouldReturnIncidentsWithMatchingLocation() {
        // Given
        String lieu = "Paris";
        when(incidentService.getIncidentsByLieu(lieu))
                .thenReturn(Collections.singletonList(responseDTO));

        // When
        ResponseEntity<List<IncidentResponseDTO>> response =
                incidentController.getIncidentsByLieu(lieu);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());
        assertEquals("Rue de la Paix, Paris", response.getBody().get(0).getLieu());
        verify(incidentService, times(1)).getIncidentsByLieu(lieu);
    }

    @Test
    void addReparationToIncident_shouldReturnUpdatedIncident() {
        // Given
        IncidentResponseDTO updatedResponse = IncidentResponseDTO.builder()
                .id(1L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Rue de la Paix, Paris")
                .description("Collision latérale")
                .reparations(Collections.singletonList(reparationDTO))
                .build();

        when(incidentService.addReparationToIncident(1L, 3L)).thenReturn(updatedResponse);

        // When
        ResponseEntity<IncidentResponseDTO> response =
                incidentController.addReparationToIncident(1L, 3L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        assertEquals(1, response.getBody().getReparations().size());
        assertEquals(3L, response.getBody().getReparations().get(0).getId());
        verify(incidentService, times(1)).addReparationToIncident(1L, 3L);
    }

    @Test
    void removeReparationFromIncident_shouldReturnUpdatedIncident() {
        // Given
        when(incidentService.removeReparationFromIncident(1L, 3L)).thenReturn(responseDTO);

        // When
        ResponseEntity<IncidentResponseDTO> response =
                incidentController.removeReparationFromIncident(1L, 3L);

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().getId());
        assertEquals(0, response.getBody().getReparations().size());
        verify(incidentService, times(1)).removeReparationFromIncident(1L, 3L);
    }
}