package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import otbs.ms_incident.commun.PageResponse;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.enums.StatusReparation;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.ReparationMapper;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReparationServiceImplAdvancedTest {

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationMapper reparationMapper;

    @InjectMocks
    private ReparationServiceImpl reparationService;

    private Incident incident;
    private Reparation reparation1, reparation2;
    private ReparationResponseDTO responseDTO1, responseDTO2;
    private List<Reparation> reparations;
    private List<ReparationResponseDTO> responseDTOs;

    @BeforeEach
    void setUp() {
        incident = new Incident();
        incident.setId(1L);

        reparation1 = new Reparation();
        reparation1.setId(1L);
        reparation1.setDateReparation(LocalDate.of(2023, 5, 15));
        reparation1.setStatus(StatusReparation.EN_COURS);
        reparation1.setDescription("Réparation 1");
        reparation1.setCout(new BigDecimal("1000.00"));
        reparation1.setGarage("Garage A");
        reparation1.setIncident(incident);

        reparation2 = new Reparation();
        reparation2.setId(2L);
        reparation2.setDateReparation(LocalDate.of(2023, 6, 20));
        reparation2.setStatus(StatusReparation.TERMINEE);
        reparation2.setDescription("Réparation 2");
        reparation2.setCout(new BigDecimal("2000.00"));
        reparation2.setGarage("Garage B");
        reparation2.setIncident(incident);

        reparations = Arrays.asList(reparation1, reparation2);

        responseDTO1 = new ReparationResponseDTO();
        responseDTO1.setId(1L);
        responseDTO1.setIncidentId(1L);

        responseDTO2 = new ReparationResponseDTO();
        responseDTO2.setId(2L);
        responseDTO2.setIncidentId(1L);

        responseDTOs = Arrays.asList(responseDTO1, responseDTO2);
    }

    @Test
    void getReparationsByIncidentIdPaginated_shouldReturnPagedReparations() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<Reparation> page = new PageImpl<>(reparations, pageable, 2);

        when(incidentRepository.existsById(1L)).thenReturn(true);
        when(reparationRepository.findByIncidentId(1L, pageable)).thenReturn(page);
        when(reparationMapper.toDTO(reparation1)).thenReturn(responseDTO1);
        when(reparationMapper.toDTO(reparation2)).thenReturn(responseDTO2);

        // When
        PageResponse<ReparationResponseDTO> result = reparationService.getReparationsByIncidentIdPaginated(1L, pageable);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        assertEquals(2, result.getTotalElements());
        assertEquals(1, result.getTotalPages());
    }

    @Test
    void getReparationsByIncidentIdPaginated_shouldThrowException_whenIncidentDoesNotExist() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        when(incidentRepository.existsById(1L)).thenReturn(false);

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.getReparationsByIncidentIdPaginated(1L, pageable)
        );
    }

    @Test
    void getReparationsByStatusAndIncidentId_shouldReturnFilteredReparations() {
        // Given
        when(incidentRepository.existsById(1L)).thenReturn(true);
        when(reparationRepository.findByStatusAndIncidentId(StatusReparation.EN_COURS, 1L)).thenReturn(List.of(reparation1));
        when(reparationMapper.toDTOList(List.of(reparation1))).thenReturn(List.of(responseDTO1));

        // When
        List<ReparationResponseDTO> result = reparationService.getReparationsByStatusAndIncidentId(StatusReparation.EN_COURS, 1L);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(responseDTO1, result.get(0));
    }

    @Test
    void getReparationsByStatusAndIncidentId_shouldThrowException_whenIncidentDoesNotExist() {
        // Given
        when(incidentRepository.existsById(1L)).thenReturn(false);

        // When/Then
        assertThrows(ResourceNotFoundException.class, () ->
            reparationService.getReparationsByStatusAndIncidentId(StatusReparation.EN_COURS, 1L)
        );
    }

    @Test
    void searchReparations_shouldReturnFilteredReparations() {
        // Given
        LocalDate startDate = LocalDate.of(2023, 5, 1);
        LocalDate endDate = LocalDate.of(2023, 7, 1);
        BigDecimal minCost = new BigDecimal("500.00");
        BigDecimal maxCost = new BigDecimal("2500.00");

        when(reparationRepository.findAll()).thenReturn(reparations);
        when(reparationMapper.toDTOList(anyList())).thenReturn(responseDTOs);

        // When
        List<ReparationResponseDTO> result = reparationService.searchReparations(
            StatusReparation.EN_COURS, 1L, startDate, endDate, minCost, maxCost, "Garage"
        );

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    @Test
    void getAverageCost_shouldCalculateAverage() {
        // Given
        when(reparationRepository.findAll()).thenReturn(reparations);

        // When
        BigDecimal result = reparationService.getAverageCost();

        // Then
        assertEquals(new BigDecimal("1500.00"), result);
    }

    @Test
    void getAverageCost_shouldReturnZero_whenNoReparations() {
        // Given
        when(reparationRepository.findAll()).thenReturn(List.of());

        // When
        BigDecimal result = reparationService.getAverageCost();

        // Then
        assertEquals(BigDecimal.ZERO, result);
    }

    @Test
    void getRepairCountByStatus_shouldReturnStatusCounts() {
        // Given
        when(reparationRepository.findAll()).thenReturn(reparations);

        // When
        Map<String, Long> result = reparationService.getRepairCountByStatus();

        // Then
        assertNotNull(result);
        assertEquals(1L, result.get("EN_COURS"));
        assertEquals(1L, result.get("TERMINEE"));
    }
}
