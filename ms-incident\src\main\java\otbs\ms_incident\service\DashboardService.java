package otbs.ms_incident.service;

import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.dto.dashboard.*;

import java.time.LocalDate;
import java.util.List;

/**
 * Interface définissant les services pour le dashboard
 */
public interface DashboardService {

    /**
     * Récupère les statistiques générales pour le dashboard
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return DTO avec les statistiques générales
     */
    DashboardSummaryDTO getDashboardSummary(LocalDate debut, LocalDate fin, Long vehiculeId);

    /**
     * Récupère les incidents récents
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return Liste des incidents récents
     */
    List<IncidentResponseDTO> getRecentIncidents(LocalDate debut, LocalDate fin, Long vehiculeId);

    /**
     * Récupère les réparations récentes
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return Liste des réparations récentes
     */
    List<ReparationResponseDTO> getRecentRepairs(LocalDate debut, LocalDate fin, Long vehiculeId);

    /**
     * Récupère la répartition des incidents par statut
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return DTO avec la répartition des incidents par statut
     */
    IncidentStatusDistributionDTO getIncidentStatusDistribution(LocalDate debut, LocalDate fin, Long vehiculeId);

    /**
     * Récupère la répartition des incidents par type
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return DTO avec la répartition des incidents par type
     */
    IncidentTypeDistributionDTO getIncidentTypeDistribution(LocalDate debut, LocalDate fin, Long vehiculeId);

    /**
     * Récupère la répartition des incidents par véhicule
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return DTO avec la répartition des incidents par véhicule
     */
    VehicleIncidentDistributionDTO getVehicleIncidentDistribution(LocalDate debut, LocalDate fin, Long vehiculeId);

    /**
     * Récupère la répartition des incidents par véhicule et par type
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return DTO avec la répartition des incidents par véhicule et par type
     */
    VehicleIncidentTypeDistributionDTO getVehicleIncidentTypeDistribution(LocalDate debut, LocalDate fin, Long vehiculeId);

    /**
     * Récupère les coûts de réparation par véhicule et par statut
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return DTO avec les coûts de réparation par véhicule et par statut
     */
    RepairCostByVehicleDTO getRepairCostByVehicle(LocalDate debut, LocalDate fin, Long vehiculeId);

    /**
     * Récupère l'analyse de la couverture d'assurance
     * @param debut Date de début
     * @param fin Date de fin
     * @param vehiculeId ID du véhicule (optionnel)
     * @return DTO avec l'analyse de la couverture d'assurance
     */
    InsuranceCoverageDTO getInsuranceCoverage(LocalDate debut, LocalDate fin, Long vehiculeId);
}
