package otbs.ms_incident.repository;

import otbs.ms_incident.entity.Incident;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface IncidentRepository extends JpaRepository<Incident, Long> {
    // Méthodes non paginées
    List<Incident> findByDateBetween(LocalDate debut, LocalDate fin);
    List<Incident> findByLieuContaining(String lieu);
    List<Incident> findByVehiculeId(Long vehiculeId);
    Long countByVehiculeId(Long vehiculeId);

    // Méthodes paginées
    Page<Incident> findByDateBetween(LocalDate debut, LocalDate fin, Pageable pageable);
    Page<Incident> findByLieuContaining(String lieu, Pageable pageable);
    Page<Incident> findByVehiculeId(Long vehiculeId, Pageable pageable);
}