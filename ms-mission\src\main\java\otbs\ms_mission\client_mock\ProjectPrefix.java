package otbs.ms_mission.client_mock;

/**
 * Énumération des préfixes de projets supportés.
 */
public enum ProjectPrefix {
    CT("CT", "Contrats"),
    AF("AF", "Affaires");

    private final String code;
    private final String description;

    ProjectPrefix(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * Trouve un préfixe par son code.
     *
     * @param code Le code du préfixe
     * @return Le préfixe correspondant ou null si non trouvé
     */
    public static ProjectPrefix fromCode(String code) {
        if (code == null) {
            return null;
        }
        for (ProjectPrefix prefix : values()) {
            if (prefix.code.equalsIgnoreCase(code)) {
                return prefix;
            }
        }
        return null;
    }
}
