package otbs.ms_incident.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * Classe utilitaire pour les contrôleurs
 * Contient des méthodes communes utilisées par plusieurs contrôleurs
 */
@Slf4j
public class ControllerUtils {

    private static final String STATUS_ERROR = "error";
    private static final String ERROR_KEY = "error";
    private static final String STATUS_KEY = "status";

    /**
     * Constructeur privé pour empêcher l'instanciation de cette classe utilitaire
     */
    private ControllerUtils() {
        // Classe utilitaire, ne doit pas être instanciée
    }

    /**
     * Crée une réponse d'erreur standardisée
     *
     * @param errorMessage Message d'erreur
     * @return Map contenant les détails de l'erreur
     */
    public static Map<String, Object> createErrorResponse(String errorMessage) {
        Map<String, Object> error = new HashMap<>();
        error.put(ERROR_KEY, errorMessage);
        error.put(STATUS_KEY, STATUS_ERROR);
        return error;
    }

    /**
     * Crée une réponse de fallback standardisée pour les services indisponibles
     *
     * @param e Exception qui a déclenché le fallback
     * @param serviceName Nom du service indisponible
     * @param resourceId Identifiant de la ressource (optionnel)
     * @return ResponseEntity avec statut SERVICE_UNAVAILABLE
     */
    public static <T> ResponseEntity<T> createServiceUnavailableResponse(Exception e, String serviceName, Object resourceId) {
        if (resourceId != null) {
            log.error("Fallback: Erreur lors de l'accès au service {} pour la ressource {}: {}",
                serviceName, resourceId, e.getMessage());
        } else {
            log.error("Fallback: Erreur lors de l'accès au service {}: {}",
                serviceName, e.getMessage());
        }
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).build();
    }
}
