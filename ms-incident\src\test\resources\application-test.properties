# Nom de l'application
spring.application.name=ms-incident
server.port=${SERVER_PORT:9002}

# Configuration Spring Cloud Config (seulement en non-test)
spring.config.import=${SPRING_CONFIG_IMPORT:configserver:}
spring.cloud.config.uri=${CONFIG_SERVER_URL:http://*************:9101}

# Configuration de stockage de fichiers
storage.path=${STORAGE_PATH:/storage/path}
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Configuration de la langue (Français uniquement)
spring.web.locale=fr_FR
spring.web.locale-resolver=fixed
spring.messages.fallback-to-system-locale=false

# Chemin des fichiers de messages
spring.messages.basename=messages


