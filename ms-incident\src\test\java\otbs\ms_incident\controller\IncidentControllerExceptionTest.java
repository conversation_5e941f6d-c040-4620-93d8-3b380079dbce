package otbs.ms_incident.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import otbs.ms_incident.client.VehiculeClient;
import otbs.ms_incident.client.VehiculeDto;
import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.enums.TypeIncident;
import otbs.ms_incident.exception.BadRequestException;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.service.IncidentService;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
 class IncidentControllerExceptionTest {

    @Mock
    private IncidentService incidentService;

    @Mock
    private VehiculeClient vehiculeClient;

    @InjectMocks
    private IncidentController controller;

    private IncidentRequestDTO requestDTO;
    private IncidentResponseDTO responseDTO;
    private VehiculeDto vehiculeDto;

    @BeforeEach
    void setUp() {
        requestDTO = new IncidentRequestDTO();
        requestDTO.setVehiculeId(1L);
        requestDTO.setDate(LocalDate.now());
        requestDTO.setType(TypeIncident.ACCIDENT);
        requestDTO.setLieu("Paris");
        requestDTO.setDescription("Test incident");

        responseDTO = new IncidentResponseDTO();
        responseDTO.setId(1L);
        responseDTO.setVehiculeId(1L);
        responseDTO.setDate(LocalDate.now());
        responseDTO.setType(TypeIncident.ACCIDENT);
        responseDTO.setLieu("Paris");
        responseDTO.setDescription("Test incident");

        vehiculeDto = new VehiculeDto();
        vehiculeDto.setIdVehicule(1L);
        vehiculeDto.setImmatriculation("AB-123-CD");
        vehiculeDto.setMarque("Toyota");
        vehiculeDto.setModele("Corolla");
    }

    @Test
    void createIncident_shouldHandleValidationErrors() {
        // Given
        when(incidentService.createIncident(any(IncidentRequestDTO.class)))
            .thenThrow(new BadRequestException("Validation error"));

        // When
        try {
            ResponseEntity<IncidentResponseDTO> response = controller.createIncident(requestDTO);

            // Then - if controller catches the exception
            assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
            assertNull(response.getBody());
        } catch (BadRequestException e) {
            // Then - if controller propagates the exception
            assertEquals("Validation error", e.getMessage());
        }
    }

    @Test
    void getIncidentById_shouldHandleNonExistentId() {
        // Given
        when(incidentService.getIncidentById(999L))
            .thenThrow(new ResourceNotFoundException("Incident not found"));

        // When
        try {
            ResponseEntity<IncidentResponseDTO> response = controller.getIncidentById(999L);

            // Then - if controller catches the exception
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
            assertNull(response.getBody());
        } catch (ResourceNotFoundException e) {
            // Then - if controller propagates the exception
            assertEquals("Incident not found", e.getMessage());
        }
    }

    @Test
    void updateIncident_shouldHandleNonExistentId() {
        // Given
        when(incidentService.updateIncident(eq(999L), any(IncidentRequestDTO.class)))
            .thenThrow(new ResourceNotFoundException("Incident not found"));

        // When
        try {
            ResponseEntity<IncidentResponseDTO> response = controller.updateIncident(999L, requestDTO);

            // Then - if controller catches the exception
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
            assertNull(response.getBody());
        } catch (ResourceNotFoundException e) {
            // Then - if controller propagates the exception
            assertEquals("Incident not found", e.getMessage());
        }
    }

    @Test
    void deleteIncident_shouldHandleNonExistentId() {
        // Given
        doThrow(new ResourceNotFoundException("Incident not found"))
            .when(incidentService).deleteIncident(999L);

        // When
        try {
            ResponseEntity<Void> response = controller.deleteIncident(999L);

            // Then - if controller catches the exception
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        } catch (ResourceNotFoundException e) {
            // Then - if controller propagates the exception
            assertEquals("Incident not found", e.getMessage());
        }
    }

    @Test
    void getIncidentsByVehiculeId_shouldHandleNonExistentVehicule() {
        // Given
        when(vehiculeClient.getVehiculeById(999L))
            .thenThrow(new ResourceNotFoundException("Vehicule not found"));

        // When
        try {
            ResponseEntity<List<IncidentResponseDTO>> response = controller.getIncidentsByVehiculeId(999L);

            // Then - if controller catches the exception
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
            assertNull(response.getBody());
        } catch (ResourceNotFoundException e) {
            // Then - if controller propagates the exception
            assertEquals("Vehicule not found", e.getMessage());
        }
    }

    @Test
    void getVehiculeDetailsWithIncidents_shouldHandleNonExistentVehicule() {
        // Given
        when(vehiculeClient.getVehiculeById(999L))
            .thenThrow(new ResourceNotFoundException("Vehicule not found"));

        // When
        try {
            ResponseEntity<Map<String, Object>> response = controller.getVehiculeDetailsWithIncidents(999L);

            // Then - if controller catches the exception
            assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
            assertNull(response.getBody());
        } catch (ResourceNotFoundException e) {
            // Then - if controller propagates the exception
            assertEquals("Vehicule not found", e.getMessage());
        }
    }

    @Test
    void getAllVehicules_shouldHandleFeignException() {
        // Given
        when(vehiculeClient.getAllVehicules())
            .thenThrow(new RuntimeException("Service unavailable"));

        // When
        ResponseEntity<List<VehiculeDto>> response = controller.getAllVehicules();

        // Then
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());
        assertNull(response.getBody());
    }
}
