package otbs.ms_incident;

import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;


/**
 * Classe principale du microservice de gestion des incidents.
 * Cette classe démarre l'application Spring Boot et active la découverte de service
 * pour permettre l'enregistrement auprès du serveur Eureka.
 *
 * @version 0.0.1-SNAPSHOT
 * <AUTHOR>
 *
 */

@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@EnableRabbit

public class MsIncidentApplication {

	/**
	 * Point d'entrée principal de l'application.
	 * Lance le microservice de gestion des incidents qui s'enregistre
	 * automatiquement auprès du serveur Eureka pour la découverte de service.
	 *
	 * @param args Arguments de ligne de commande passés à l'application
	 */
	public static void main(String[] args) {
		SpringApplication.run(MsIncidentApplication.class, args);
	}
 
}
