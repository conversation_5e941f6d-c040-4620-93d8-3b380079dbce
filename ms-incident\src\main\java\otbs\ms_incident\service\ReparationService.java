package otbs.ms_incident.service;

import otbs.ms_incident.dto.ReparationRequestDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;

import org.springframework.data.domain.Pageable;
import otbs.ms_incident.commun.PageResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import otbs.ms_incident.enums.StatusReparation;

/**
 * Interface définissant les services de gestion des réparations.
 * Cette interface spécifie toutes les opérations disponibles pour manipuler
 * les réparations dans le système ParcAuto, incluant la création, la lecture,
 * la mise à jour, la suppression et diverses méthodes de recherche.
 *
 * @version 0.0.1-SNAPSHOT
 */
public interface ReparationService {
    /**
     * Crée une nouvelle réparation à partir des données fournies.
     *
     * @param requestDTO Les données nécessaires pour créer la réparation
     * @return La réparation créée avec son ID généré
     */
    ReparationResponseDTO createReparation(ReparationRequestDTO requestDTO);

    /**
     * Récupère une réparation par son identifiant.
     *
     * @param id L'identifiant de la réparation à récupérer
     * @return La réparation correspondant à l'ID fourni
     * @throws OTBS.ms_incident.exception.ResourceNotFoundException si aucune réparation n'est trouvée avec cet ID
     */
    ReparationResponseDTO getReparationById(Long id);

    /**
     * Récupère toutes les réparations enregistrées.
     *
     * @return La liste de toutes les réparations
     */
    List<ReparationResponseDTO> getAllReparations();

    /**
     * Récupère toutes les réparations avec pagination.
     *
     * @param pageable Les paramètres de pagination
     * @return Une page de réparations
     */
    PageResponse<ReparationResponseDTO> getAllReparationsPaginated(Pageable pageable);

    /**
     * Met à jour une réparation existante.
     *
     * @param id L'identifiant de la réparation à mettre à jour
     * @param requestDTO Les nouvelles données de la réparation
     * @return La réparation mise à jour
     * @throws OTBS.ms_incident.exception.ResourceNotFoundException si aucune réparation n'est trouvée avec cet ID
     */
    ReparationResponseDTO updateReparation(Long id, ReparationRequestDTO requestDTO);

    /**
     * Supprime une réparation par son identifiant.
     *
     * @param id L'identifiant de la réparation à supprimer
     * @throws OTBS.ms_incident.exception.ResourceNotFoundException si aucune réparation n'est trouvée avec cet ID
     */
    void deleteReparation(Long id);

    /**
     * Récupère les réparations associées à un incident.
     *
     * @param incidentId L'identifiant de l'incident
     * @return La liste des réparations associées à l'incident
     */
    List<ReparationResponseDTO> getReparationsByIncidentId(Long incidentId);

    /**
     * Récupère les réparations associées à un incident avec pagination.
     *
     * @param incidentId L'identifiant de l'incident
     * @param pageable Les paramètres de pagination
     * @return Une page de réparations associées à l'incident
     */
    PageResponse<ReparationResponseDTO> getReparationsByIncidentIdPaginated(Long incidentId, Pageable pageable);



    /**
     * Récupère les réparations effectuées dans une plage de dates donnée.
     *
     * @param debut La date de début de la période
     * @param fin La date de fin de la période
     * @return La liste des réparations effectuées durant cette période
     */
    List<ReparationResponseDTO> getReparationsByDateRange(LocalDate debut, LocalDate fin);

    /**
     * Récupère les réparations par statut.
     *
     * @param status Le statut des réparations à récupérer
     * @return La liste des réparations ayant ce statut
     */
    List<ReparationResponseDTO> getReparationsByStatus(StatusReparation status);



    /**
     * Récupère les réparations par statut et par incident.
     *
     * @param status Le statut des réparations à récupérer
     * @param incidentId L'identifiant de l'incident
     * @return La liste des réparations correspondant aux critères
     */
    List<ReparationResponseDTO> getReparationsByStatusAndIncidentId(StatusReparation status, Long incidentId);

    /**
     * Met à jour uniquement le statut d'une réparation.
     *
     * @param id L'identifiant de la réparation à mettre à jour
     * @param status Le nouveau statut
     * @return La réparation mise à jour
     */
    ReparationResponseDTO updateReparationStatus(Long id, StatusReparation status);

    /**
     * Recherche des réparations selon plusieurs critères.
     *
     * @param status Le statut des réparations (optionnel)
     * @param incidentId L'identifiant de l'incident (optionnel)
     * @param startDate La date de début de la période (optionnel)
     * @param endDate La date de fin de la période (optionnel)
     * @param minCost Le coût minimal (optionnel)
     * @param maxCost Le coût maximal (optionnel)
     * @param garage Le nom du garage (optionnel)
     * @return La liste des réparations correspondant aux critères
     */
    List<ReparationResponseDTO> searchReparations(StatusReparation status, Long incidentId, LocalDate startDate,
                                                LocalDate endDate, BigDecimal minCost, BigDecimal maxCost, String garage);

    /**
     * Calcule le coût total de toutes les réparations.
     *
     * @return Le coût total
     */
    BigDecimal getTotalCost();

    /**
     * Calcule le coût moyen des réparations.
     *
     * @return Le coût moyen
     */
    BigDecimal getAverageCost();

    /**
     * Compte le nombre de réparations par statut.
     *
     * @return Une map avec le statut comme clé et le nombre comme valeur
     */
    Map<String, Long> getRepairCountByStatus();
}