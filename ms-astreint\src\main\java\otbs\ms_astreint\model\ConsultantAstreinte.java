package otbs.ms_astreint.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "consultants_astreinte")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ConsultantAstreinte extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id_consultant_astreinte")
    private Long idConsultantAstreinte;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "id_astreinte", nullable = false)
    private Astreinte astreinte;

    @NotNull(message = "Le consultant est obligatoire")
    @Column(name = "consultant", nullable = false, length = 100)
    private String consultant;

    @NotNull(message = "Le niveau d'astreinte est obligatoire")
    @Enumerated(EnumType.STRING)
    @Column(name = "niveau_astreinte", nullable = false)
    private NiveauAstreinte niveauAstreinte;

    public ConsultantAstreinte(Astreinte astreinte, String consultant, NiveauAstreinte niveauAstreinte) {
        this.astreinte = astreinte;
        this.consultant = consultant;
        this.niveauAstreinte = niveauAstreinte;
    }

    @Override
    public String toString() {
        return "ConsultantAstreinte{" +
                "idConsultantAstreinte=" + idConsultantAstreinte +
                ", consultant='" + consultant + '\'' +
                ", niveauAstreinte=" + niveauAstreinte +
                '}';
    }
} 