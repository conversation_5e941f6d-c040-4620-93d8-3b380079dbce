package otbs.ms_incident.config;


import org.junit.jupiter.api.Test;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.caffeine.CaffeineCacheManager;

import java.lang.reflect.Field;
import java.util.Collection;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class CacheConfigTest {

    @Test
    void cacheConstantsDefinitions_shouldBeCorrect() {
        // Verify the cache constant values
        assertThat(CacheConfig.INCIDENTS_CACHE).isEqualTo("incidents");
        assertThat(CacheConfig.REPARATIONS_CACHE).isEqualTo("reparations");
        assertThat(CacheConfig.INCIDENT_BY_ID_CACHE).isEqualTo("incidentById");
        assertThat(CacheConfig.REPARATION_BY_ID_CACHE).isEqualTo("reparationById");
    }

    @Test
    void cacheManagerShouldNotBeNull() {
        CacheConfig cacheConfig = new CacheConfig();
        CacheManager cacheManager = cacheConfig.cacheManager();
        assertThat(cacheManager).isNotNull();
    }

    @Test
    void cacheManagerShouldBeInstanceOfCaffeineCacheManager() {
        CacheConfig cacheConfig = new CacheConfig();
        CacheManager cacheManager = cacheConfig.cacheManager();
        assertThat(cacheManager).isInstanceOf(CaffeineCacheManager.class);
    }

    @Test
    void cacheCaffeineSpecShouldHaveExpectedProperties() throws Exception {
        CacheConfig cacheConfig = new CacheConfig();
        CaffeineCacheManager cacheManager = (CaffeineCacheManager) cacheConfig.cacheManager();
        
        // Get the Caffeine instance using reflection since it's not directly accessible
        Field caffeineField = CaffeineCacheManager.class.getDeclaredField("cacheBuilder");
        caffeineField.setAccessible(true);
        
        
        // Create a cache to inspect its properties
        CaffeineCache cache = (CaffeineCache) cacheManager.getCache(CacheConfig.INCIDENTS_CACHE);
        assertThat(cache).isNotNull();
        
        // Retrieve cache stats to verify recording stats is enabled
        assertThat(cache.getNativeCache().stats().hitCount()).isZero(); // Just verifying stats are accessible
    }
    
    @Test
    void cacheManagerShouldContainAllDefinedCaches() {
        CacheConfig cacheConfig = new CacheConfig();
        CaffeineCacheManager cacheManager = (CaffeineCacheManager) cacheConfig.cacheManager();
        
        // Get all cache names
        Collection<String> cacheNames = cacheManager.getCacheNames();
        
        // Verify all expected caches are defined
        assertThat(cacheNames).contains(
            CacheConfig.INCIDENTS_CACHE,
            CacheConfig.REPARATIONS_CACHE,
            CacheConfig.INCIDENT_BY_ID_CACHE,
            CacheConfig.REPARATION_BY_ID_CACHE
        );
    }
    
    @Test
    void cacheManagerShouldAllowDynamicCacheCreation() {
        CacheConfig cacheConfig = new CacheConfig();
        CaffeineCacheManager cacheManager = (CaffeineCacheManager) cacheConfig.cacheManager();
        
        // Set dynamic cache creation to true
        cacheManager.setAllowNullValues(true);
        
        // Create a new dynamic cache by adding it to the cache names
        String dynamicCacheName = "testDynamicCache";
        cacheManager.setCacheNames(java.util.Collections.singleton(dynamicCacheName));
        
        // Get the cache - it should be created
        CaffeineCache dynamicCache = (CaffeineCache) cacheManager.getCache(dynamicCacheName);
        
        // Verify the cache exists
        assertNotNull(dynamicCache);
        assertThat(dynamicCache.getName()).isEqualTo(dynamicCacheName);
    }
} 