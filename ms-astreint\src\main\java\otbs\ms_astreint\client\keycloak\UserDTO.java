package otbs.ms_astreint.client.keycloak;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDTO {

    private String id;
    private String username;
    private String email;
    private String firstName;
    private String lastName;
    private boolean enabled;
    private boolean emailVerified;
    private LocalDateTime createdAt;
    private Map<String, List<String>> attributes;

    public String getFullName() {
        if (firstName != null && lastName != null) {
            return firstName + " " + lastName;
        } else if (firstName != null) {
            return firstName;
        } else if (lastName != null) {
            return lastName;
        } else {
            return username != null ? username : id;
        }
    }

    public String getAttribute(String attributeName) {
        if (attributes != null && attributes.containsKey(attributeName)) {
            List<String> values = attributes.get(attributeName);
            return values != null && !values.isEmpty() ? values.get(0) : null;
        }
        return null;
    }

    public List<String> getAttributeValues(String attributeName) {
        if (attributes != null && attributes.containsKey(attributeName)) {
            return attributes.get(attributeName);
        }
        return List.of();
    }
} 