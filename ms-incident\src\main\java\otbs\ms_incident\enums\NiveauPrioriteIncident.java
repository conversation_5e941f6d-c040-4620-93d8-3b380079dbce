package otbs.ms_incident.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Niveaux de priorité possibles pour un incident")
public enum NiveauPrioriteIncident {
    @Schema(description = "Impact majeur, intervention immédiate")
    CRITIQUE,

    @Schema(description = "Impact modéré, planification normale")
    MOYEN,

    @Schema(description = "Impact mineur, peut attendre")
    FAIBLE
} 