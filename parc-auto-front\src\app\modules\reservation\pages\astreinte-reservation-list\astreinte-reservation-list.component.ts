import { CommonModule, CurrencyPipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { NavGuideComponent } from '../../components/nav-guide/nav-guide.component';
import { ImmatriculeComponent } from '../../../vehicules/components/immatricule/immatricule.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AffecterTelepeageAstreinteComponent } from '../../components/affecter-telepeage-astreinte/affecter-telepeage-astreinte.component';
import { AstreinteReservationService } from '../../services/astreinte-reservation.service';
import { FullAstreinteReservation } from '../../models/FullAstreinteReservation';
import { AstreinteReservation } from '../../models/AstreinteReservation';
import { AffecterCarteComponent } from '../../components/affecter-carte/affecter-carte.component';
import { AffecterVehiculeAstreinteComponent } from '../../components/affecter-vehicule-astreinte/affecter-vehicule-astreinte.component';
import { ConsommationCarburant, TypeConsommation } from '../../models/ConsommationCarburant';
import { Observable, of } from 'rxjs';
import { AuthService } from '../../../../shared/services/auth/auth.service';

@Component({
  selector: 'app-astreinte-reservation-list',
  standalone: true,
  imports: [
    CommonModule,
    NavGuideComponent,
    ImmatriculeComponent,
    MatDialogModule,
    RouterModule,
    CurrencyPipe,
    AffecterTelepeageAstreinteComponent,
    AffecterCarteComponent,
    AffecterVehiculeAstreinteComponent
  ],
  templateUrl: './astreinte-reservation-list.component.html',
  styleUrls: ['./astreinte-reservation-list.component.scss'],
  providers: [CurrencyPipe]
})
export class AstreinteReservationListComponent implements OnInit {
  dataSource: FullAstreinteReservation[] = [];
  typeConsommation = TypeConsommation;
  reservationDetails: Map<number, AstreinteReservation> = new Map();
  carteCarburantDetails: Map<number, string> = new Map();

  constructor(
    private dialog: MatDialog,
    private astreinteReservationService: AstreinteReservationService,
    private router: Router,
    private currencyPipe: CurrencyPipe,
    private authService: AuthService
  ) {}

  hasRole(role: string): boolean {
    return this.authService.hasRole(role);
  }

  ngOnInit(): void {
    this.loadFutureAstreinteReservations();
  }

  private loadFutureAstreinteReservations(): void {
    if(this.hasRole('CONSULTANT')){
      this.astreinteReservationService.getFutureAstreinteReservationsByConsultant(this.authService.getUserId()).subscribe({
        next: (reservations) => {
          this.dataSource = reservations;
          this.loadReservationDetails();
        },
        error: (error) => {
          console.error('Error fetching future astreinte reservations:', error);
        }
      });
    } else {
      this.astreinteReservationService.getAllFutureAstreinteReservations().subscribe({
        next: (reservations) => {
          this.dataSource = reservations;
          this.loadReservationDetails();
        },
        error: (error) => {
          console.error('Error fetching future astreinte reservations:', error);
        }
      });
    }
  }

  private loadReservationDetails(): void {
    this.dataSource.forEach(fullReservation => {
      if(fullReservation.reservationId){
        this.astreinteReservationService.getAstreinteReservation(fullReservation.reservationId).subscribe({
          next: (reservation) => {
            this.reservationDetails.set(fullReservation.reservationId, reservation);

            // If there are consommationCarburant records, load card details for Carburant type
            if (reservation.consommationCarburant && reservation.consommationCarburant.length > 0) {
              reservation.consommationCarburant.forEach(consommation => {
                if (consommation.typeConsommation === TypeConsommation.Carburant && consommation.carteCarburantId) {
                  this.loadCarteCarburantDetails(consommation.carteCarburantId);
                }
              });
            }
          },
          error: (error) => {
            console.error(`Error fetching astreinte reservation details for ID ${fullReservation.reservationId}:`, error);
          }
        });
      }
    });
  }

  private loadCarteCarburantDetails(carteId: number): void {
    // TODO: Implémenter quand CarteCarburantService sera disponible
    // Default fallback - just show the ID
    this.carteCarburantDetails.set(carteId, `${carteId}`);
  }

  // Fonction existante mais toujours utile
  getConsommationInfo(reservationId: number): string {
    const reservation = this.reservationDetails.get(reservationId);
    if (!reservation || !reservation.consommationCarburant || reservation.consommationCarburant.length === 0) {
      return 'Aucune consommation';
    }

    const consommationItems: string[] = [];

    // Process each consumption and format based on type
    reservation.consommationCarburant.forEach(consommation => {
      switch (consommation.typeConsommation) {
        case TypeConsommation.Cash:
          consommationItems.push(`Cash: #${consommation.id}`);
          break;
        case TypeConsommation.Frais:
          consommationItems.push(`Frais: #${consommation.id}`);
          break;
        case TypeConsommation.Carburant:
          if (consommation.carteCarburantId) {
            const cardNumber = this.carteCarburantDetails.get(consommation.carteCarburantId) ||
              `${consommation.carteCarburantId}`;
            consommationItems.push(`Carte: ${cardNumber}`);
          }
          break;
      }
    });

    return consommationItems.join(', ');
  }

  // Nouvelles méthodes pour l'affichage amélioré des consommations
  getConsommationItems(reservationId: number): Array<{ type: string; value: string; id: string | null }> {
    const reservation = this.reservationDetails.get(reservationId);
    if (!reservation || !reservation.consommationCarburant || reservation.consommationCarburant.length === 0) {
      return [];
    }

    const consommationItems: Array<{ type: string; value: string; id: string | null }> = [];

    reservation.consommationCarburant.forEach(consommation => {
      switch (consommation.typeConsommation) {
        case TypeConsommation.Cash:
          consommationItems.push({ type: 'Cash', value: '', id: `${consommation.id}` });
          break;
        case TypeConsommation.Frais:
          if (consommation.montantSortie) {
              const fraisValue =
                `${consommation.montantSortie}`;
            consommationItems.push({ type: 'Frais', value: fraisValue, id: `${consommation.id}` });
          }
          break;
        case TypeConsommation.Carburant:
          if (consommation.carteCarburantId) {
            const cardNumber = this.carteCarburantDetails.get(consommation.carteCarburantId) ||
              `${consommation.carteCarburantId}`;
            consommationItems.push({ type: 'Carte', value: cardNumber, id: null });
          }
          break;
      }
    });

    return consommationItems;
  }

  hasConsommations(reservationId: number): boolean {
    return this.getConsommationItems(reservationId).length > 0;
  }

  getTypeClass(type: string): string {
    switch (type) {
      case 'Carte': return 'badge-carte';
      case 'Frais': return 'badge-frais';
      case 'Cash': return 'badge-cash';
      default: return 'badge-default';
    }
  }

  formatCurrency(amount: number | undefined | null): string {
    if (amount === undefined || amount === null) {
      return '0,00 TND';
    }
    const formatted = (6 * amount).toFixed(2).replace('.', ',');
    return `${formatted} TND`;
  }

  getConsommationsByReservation(reservationId: number): Observable<ConsommationCarburant[]> {
    // TODO: Implémenter quand ConsommationCarburantService sera disponible
    return of([]);
  }

  addCarte(reservation: FullAstreinteReservation): void {
    if(reservation.immatriculation){
      const dialogRef = this.dialog.open(AffecterCarteComponent, {
        width: '500px',
        data: {
          reservationId: reservation.reservationId,
          dateDebut: new Date(reservation.dateDebut).toISOString(),
          dateFin: new Date(reservation.dateFin).toISOString()
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          console.log('Affectation de Carte ou frais:', result);
          this.loadFutureAstreinteReservations();
        }
      });
    }else{
      alert("Réservez un véhicule d'abord");
    }
  }

  attributeVehicule(reservation: FullAstreinteReservation): void {
    const dialogRef = this.dialog.open(AffecterVehiculeAstreinteComponent, {
      width: '500px',
      data: {
        reservationId: reservation.reservationId,
        dateDebut: new Date(reservation.dateDebut).toISOString(),
        dateFin: new Date(reservation.dateFin).toISOString(),
        reservationIds: reservation.astreinteIds,
        mode: "add"
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadFutureAstreinteReservations();
      }
    });
  }

  updateAttributedVehicule(reservation: FullAstreinteReservation): void {
    const dialogRef = this.dialog.open(AffecterVehiculeAstreinteComponent, {
      width: '500px',
      data: {
        reservationId: reservation.reservationId,
        dateDebut: new Date(reservation.dateDebut).toISOString(),
        dateFin: new Date(reservation.dateFin).toISOString(),
        reservationIds: reservation.astreinteIds,
        mode: "update"
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadFutureAstreinteReservations();
      }
    });
  }

  addTelepeage(reservation: FullAstreinteReservation): void {
    if(reservation.immatriculation){
      const dialogRef = this.dialog.open(AffecterTelepeageAstreinteComponent, {
        width: '500px',
        data: {
          reservationId: reservation.reservationId,
          dateDebut: new Date(reservation.dateDebut).toISOString(),
          dateFin: new Date(reservation.dateFin).toISOString(),
          mode: "add"
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.loadFutureAstreinteReservations();
        }
      });
    }else{
      alert("Réservez un véhicule d'abord");
    }
  }

  updateAttributedTelepeage(reservation: FullAstreinteReservation): void {
    if(reservation.immatriculation){
      const dialogRef = this.dialog.open(AffecterTelepeageAstreinteComponent, {
        width: '500px',
        data: {
          reservationId: reservation.reservationId,
          dateDebut: new Date(reservation.dateDebut).toISOString(),
          dateFin: new Date(reservation.dateFin).toISOString(),
          mode: "update"
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.loadFutureAstreinteReservations();
        }
      });
    }else{
      alert("Réservez un véhicule d'abord");
    }
  }

  goToConsommationCarburant(reservation: FullAstreinteReservation): void {
    if (reservation && reservation.reservationId) {
      const path = ['/reservations/ConsommationCarburant', reservation.reservationId];
      console.log('Navigating to:', path);

      this.router.navigate(path).then(
        success => {
          console.log('Navigation successful:', success);
        },
        error => {
          console.error('Navigation error:', error);
        }
      );
    } else {
      console.error('Invalid reservation or ID');
    }
  }

  // Méthode pour créer manuellement un ticket restaurant si nécessaire
  createTicketManually(reservation: FullAstreinteReservation): void {
    // TODO: Implémenter quand TicketRestaurantService sera disponible
    console.log('Création manuelle de ticket pour astreinte:', reservation.reservationId);
  }

  download(reservation: FullAstreinteReservation): void {
    if (reservation.reservationId) {
      // TODO: Implémenter téléchargement PDF pour astreintes
      console.log('Télécharger PDF pour astreinte:', reservation);
    } else {
      console.log(`L'ordre d'astreinte ${reservation.astreinteIds[0]} ne peut pas être téléchargé`);
    }
  }
}
