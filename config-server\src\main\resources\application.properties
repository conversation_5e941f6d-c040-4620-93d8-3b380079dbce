spring.application.name=config-server
server.port=${PORT_NB:9101}
spring.cloud.config.server.git.skipSslValidation=${SKIP_SSL:true}
spring.cloud.config.server.git.uri=${GIT_URL:https://gitlab-sys.onetech-group.corp/parc-auto/back-end/config-repo.git}
spring.cloud.config.server.git.default-label=${GIT_RIVESION:develop}
spring.cloud.config.server.git.username=${USERNAME:Rihem.Smiri}
spring.cloud.config.server.git.password=${PASSWORD:**************************}