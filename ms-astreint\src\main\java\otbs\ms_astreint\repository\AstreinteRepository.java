package otbs.ms_astreint.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import otbs.ms_astreint.model.Astreinte;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AstreinteRepository extends JpaRepository<Astreinte, Long> {
    @Query("SELECT DISTINCT a FROM Astreinte a JOIN a.consultants c WHERE c.consultant = :consultant")
    List<Astreinte> findByConsultantsConsultant(@Param("consultant") String consultant);

    List<Astreinte> findByDateDebutAfter(LocalDateTime date);

    @Query("SELECT DISTINCT a FROM Astreinte a LEFT JOIN a.consultants c " +
           "WHERE c.consultant = :userId AND a.dateDebut > :date")
    List<Astreinte> findByUserIdAsConsultantAndDateDebutAfter(@Param("userId") String userId, @Param("date") LocalDateTime date);

    @Query("SELECT DISTINCT a FROM Astreinte a LEFT JOIN a.consultants c " +
           "WHERE c.consultant = :userId AND (a.dateDebut <= :dateFin AND a.dateFin >= :dateDebut)")
    List<Astreinte> findAstreintesByConsultantAndDateRange(@Param("userId") String userId, @Param("dateDebut") LocalDateTime dateDebut, @Param("dateFin") LocalDateTime dateFin);

    @Query("SELECT DISTINCT a FROM Astreinte a WHERE a.dateDebut <= :dateFin AND a.dateFin >= :dateDebut")
    List<Astreinte> findAstreintesByDateRange(@Param("dateDebut") LocalDateTime dateDebut, @Param("dateFin") LocalDateTime dateFin);
}