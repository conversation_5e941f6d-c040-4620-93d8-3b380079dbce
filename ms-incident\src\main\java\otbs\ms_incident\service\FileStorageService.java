package otbs.ms_incident.service;

import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;

/**
 * Interface pour le service de stockage de fichiers
 */
public interface FileStorageService {
    
    /**
     * Saves a file to the specified path
     * 
     * @param filePath Path where the file should be saved
     * @param fileBytes Contents of the file as a byte array
     */
    void save(Path filePath, byte[] fileBytes);
    
    /**
     * Saves a file to the specified path with option to overwrite existing file
     * 
     * @param filePath Path where the file should be saved
     * @param fileBytes Contents of the file as a byte array
     * @param overwrite Whether to overwrite if file exists
     */
    void save(Path filePath, byte[] fileBytes, boolean overwrite);

    /**
     * Loads a file as a Resource
     * 
     * @param filePath Path to the file
     * @return Resource representing the file
     */
    Resource load(Path filePath);

    /**
     * Checks if a file exists
     * 
     * @param filePath Path to check
     * @return true if file exists, false otherwise
     */
    boolean exists(Path filePath);

    /**
     * Supprime un fichier
     * 
     * @param path Chemin du fichier à supprimer
     * @return true si la suppression est réussie, false en cas d'échec
     */
    boolean delete(Path path);
    
    /**
     * Validates file extension against allowed extensions
     * 
     * @param filename Name of the file to validate
     * @return true if file extension is allowed
     */
    boolean isValidFileExtension(String filename);

    /**
     * Stocke un fichier à l'emplacement spécifié
     * 
     * @param file Fichier à stocker
     * @param path Chemin où stocker le fichier
     * @return Chemin complet du fichier stocké
     */
    Path store(MultipartFile file, String path);
    
    /**
     * Crée un répertoire et ses parents si nécessaire
     * 
     * @param directory Chemin du répertoire à créer
     * @throws IOException Si la création échoue
     */
    void createDirectories(Path directory) throws IOException;
    
    /**
     * Charge un fichier en tant que ressource à partir d'un chemin sous forme de chaîne
     * 
     * @param path Chemin du fichier à charger sous forme de chaîne
     * @return Ressource représentant le fichier
     */
    Resource loadFileAsResource(String path);
    
    /**
     * Retourne le chemin de base du stockage de fichiers
     * 
     * @return Chemin de base du stockage sous forme de chaîne
     */
    String getStoragePath();
} 