package otbs.ms_incident.entity;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class BaseEntityTest {

    private static class TestEntity extends BaseEntity {
        // Concrete implementation for testing
    }

    private TestEntity entity;
    private LocalDateTime now;

    @BeforeEach
    void setUp() {
        entity = new TestEntity();
        now = LocalDateTime.now();
    }

    @Test
    void testCreatedAtGetterAndSetter() {
        entity.setCreatedAt(now);
        assertEquals(now, entity.getCreatedAt());
    }

    @Test
    void testUpdatedAtGetterAndSetter() {
        entity.setUpdatedAt(now);
        assertEquals(now, entity.getUpdatedAt());
    }

    @Test
    void testCreatedByGetterAndSetter() {
        String creator = "Test User";
        entity.setCreatedBy(creator);
        assertEquals(creator, entity.getCreatedBy());
    }

    @Test
    void testUpdatedByGetterAndSetter() {
        String updater = "Admin User";
        entity.setUpdatedBy(updater);
        assertEquals(updater, entity.getUpdatedBy());
    }

    @Test
    void testInitialValues() {
        assertNull(entity.getCreatedAt());
        assertNull(entity.getUpdatedAt());
        assertNull(entity.getCreatedBy());
        assertNull(entity.getUpdatedBy());
    }
} 