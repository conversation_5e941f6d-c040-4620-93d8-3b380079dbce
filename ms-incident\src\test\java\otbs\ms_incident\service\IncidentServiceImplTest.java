package otbs.ms_incident.service;

import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.enums.TypeIncident;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;
import otbs.ms_incident.exception.BadRequestException;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.IncidentMapper;
import otbs.ms_incident.service.NotificationEventPublisher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IncidentServiceImplTest {

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentMapper incidentMapper;

    @Mock
    private NotificationEventPublisher notificationEventPublisher;

    @InjectMocks
    private IncidentServiceImpl incidentService;

    private Incident incident;
    private Incident incident2;
    private IncidentRequestDTO requestDTO;
    private IncidentResponseDTO responseDTO;
    private IncidentResponseDTO responseDTO2;
    private Reparation reparation;

    @BeforeEach
    void setUp() {
        // Setup test data
        incident = new Incident();
        incident.setId(1L);
        incident.setDate(LocalDate.of(2023, 5, 10));
        incident.setType(TypeIncident.ACCIDENT);
        incident.setLieu("Avenue des Champs-Élysées");
        incident.setDescription("Collision avec un autre véhicule");
        incident.setReparations(new ArrayList<>());

        incident2 = new Incident();
        incident2.setId(2L);
        incident2.setDate(LocalDate.of(2023, 6, 15));
        incident2.setType(TypeIncident.PANNE);
        incident2.setLieu("Boulevard Haussmann");
        incident2.setDescription("Panne moteur");
        incident2.setReparations(new ArrayList<>());

        requestDTO = IncidentRequestDTO.builder()
                .date(LocalDate.of(2023, 5, 10))
                .type(TypeIncident.ACCIDENT)
                .lieu("Avenue des Champs-Élysées")
                .description("Collision avec un autre véhicule")
                .build();

        responseDTO = IncidentResponseDTO.builder()
                .id(1L)
                .date(LocalDate.of(2023, 5, 10))
                .type(TypeIncident.ACCIDENT)
                .lieu("Avenue des Champs-Élysées")
                .description("Collision avec un autre véhicule")
                .reparations(new ArrayList<>())
                .build();

        responseDTO2 = IncidentResponseDTO.builder()
                .id(2L)
                .date(LocalDate.of(2023, 6, 15))
                .type(TypeIncident.PANNE)
                .lieu("Boulevard Haussmann")
                .description("Panne moteur")
                .reparations(new ArrayList<>())
                .build();

        reparation = new Reparation();
        reparation.setId(1L);
        reparation.setDescription("Remplacement pare-choc avant");
    }

    @Test
    void createIncident_shouldReturnSavedIncident() {
        // Given
        when(incidentMapper.toEntity(requestDTO)).thenReturn(incident);
        when(incidentRepository.save(incident)).thenReturn(incident);
        when(incidentMapper.toResponseDTO(incident)).thenReturn(responseDTO);

        // When
        IncidentResponseDTO result = incidentService.createIncident(requestDTO);

        // Then
        assertThat(result).isEqualTo(responseDTO);
        verify(incidentMapper).toEntity(requestDTO);
        verify(incidentRepository).save(incident);
        verify(incidentMapper).toResponseDTO(incident);
    }

    @Test
    void getIncidentById_shouldReturnIncident_whenIncidentExists() {
        // Given
        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident));
        when(incidentMapper.toResponseDTO(incident)).thenReturn(responseDTO);

        // When
        IncidentResponseDTO result = incidentService.getIncidentById(1L);

        // Then
        assertThat(result).isEqualTo(responseDTO);
        verify(incidentRepository).findById(1L);
        verify(incidentMapper).toResponseDTO(incident);
    }

    @Test
    void getIncidentById_shouldThrowResourceNotFoundException_whenIncidentDoesNotExist() {
        // Given
        when(incidentRepository.findById(99L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> incidentService.getIncidentById(99L));
        verify(incidentRepository).findById(99L);
        verify(incidentMapper, never()).toResponseDTO(any());
    }

    @Test
    void getAllIncidents_shouldReturnAllIncidents() {
        // Given
        List<Incident> incidents = Arrays.asList(incident, incident2);
        List<IncidentResponseDTO> responseDTOs = Arrays.asList(responseDTO, responseDTO2);

        when(incidentRepository.findAll()).thenReturn(incidents);
        when(incidentMapper.toResponseDTOList(incidents)).thenReturn(responseDTOs);

        // When
        List<IncidentResponseDTO> results = incidentService.getAllIncidents();

        // Then
        assertThat(results).containsExactlyElementsOf(responseDTOs);
        verify(incidentRepository).findAll();
        verify(incidentMapper).toResponseDTOList(incidents);
    }

    @Test
    void updateIncident_shouldUpdateAndReturnIncident_whenIncidentExists() {
        // Given
        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident));
        when(incidentRepository.save(incident)).thenReturn(incident);
        when(incidentMapper.toResponseDTO(incident)).thenReturn(responseDTO);

        // When
        IncidentResponseDTO result = incidentService.updateIncident(1L, requestDTO);

        // Then
        assertThat(result).isEqualTo(responseDTO);
        verify(incidentRepository).findById(1L);
        verify(incidentMapper).updateIncidentFromDTO(requestDTO, incident);
        verify(incidentRepository).save(incident);
        verify(incidentMapper).toResponseDTO(incident);
    }

    @Test
    void updateIncident_shouldThrowResourceNotFoundException_whenIncidentDoesNotExist() {
        // Given
        when(incidentRepository.findById(99L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> incidentService.updateIncident(99L, requestDTO));
        verify(incidentRepository).findById(99L);
        verify(incidentMapper, never()).updateIncidentFromDTO(any(), any());
        verify(incidentRepository, never()).save(any());
    }

    @Test
    void deleteIncident_shouldDeleteIncident_whenIncidentExists() {
        // Given
        when(incidentRepository.existsById(1L)).thenReturn(true);

        // When
        incidentService.deleteIncident(1L);

        // Then
        verify(incidentRepository).existsById(1L);
        verify(incidentRepository).deleteById(1L);
    }

    @Test
    void deleteIncident_shouldThrowResourceNotFoundException_whenIncidentDoesNotExist() {
        // Given
        when(incidentRepository.existsById(99L)).thenReturn(false);

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> incidentService.deleteIncident(99L));
        verify(incidentRepository).existsById(99L);
        verify(incidentRepository, never()).deleteById(anyLong());
    }

    // Test supprimé car la méthode getIncidentsByType a été supprimée

    @Test
    void getIncidentsByDateRange_shouldReturnIncidentsInDateRange() {
        // Given
        LocalDate debut = LocalDate.of(2023, 5, 1);
        LocalDate fin = LocalDate.of(2023, 5, 31);
        List<Incident> incidents = Arrays.asList(incident);
        List<IncidentResponseDTO> responseDTOs = Arrays.asList(responseDTO);

        when(incidentRepository.findByDateBetween(debut, fin)).thenReturn(incidents);
        when(incidentMapper.toResponseDTOList(incidents)).thenReturn(responseDTOs);

        // When
        List<IncidentResponseDTO> results = incidentService.getIncidentsByDateRange(debut, fin);

        // Then
        assertThat(results).containsExactlyElementsOf(responseDTOs);
        verify(incidentRepository).findByDateBetween(debut, fin);
        verify(incidentMapper).toResponseDTOList(incidents);
    }

    @Test
    void getIncidentsByDateRange_shouldThrowBadRequestException_whenDebutIsAfterFin() {
        // Given
        LocalDate debut = LocalDate.of(2023, 6, 1);
        LocalDate fin = LocalDate.of(2023, 5, 31);

        // When & Then
        assertThrows(BadRequestException.class, () -> incidentService.getIncidentsByDateRange(debut, fin));
        verify(incidentRepository, never()).findByDateBetween(any(), any());
    }

    @Test
    void getIncidentsByLieu_shouldReturnIncidentsMatchingLieu() {
        // Given
        String lieu = "Champs";
        List<Incident> incidents = Arrays.asList(incident);
        List<IncidentResponseDTO> responseDTOs = Arrays.asList(responseDTO);

        when(incidentRepository.findByLieuContaining(lieu)).thenReturn(incidents);
        when(incidentMapper.toResponseDTOList(incidents)).thenReturn(responseDTOs);

        // When
        List<IncidentResponseDTO> results = incidentService.getIncidentsByLieu(lieu);

        // Then
        assertThat(results).containsExactlyElementsOf(responseDTOs);
        verify(incidentRepository).findByLieuContaining(lieu);
        verify(incidentMapper).toResponseDTOList(incidents);
    }

    @Test
    void addReparationToIncident_shouldAddReparationAndReturnUpdatedIncident() {
        // Given
        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident));
        when(reparationRepository.findById(1L)).thenReturn(Optional.of(reparation));
        when(incidentRepository.save(incident)).thenReturn(incident);
        when(incidentMapper.toResponseDTO(incident)).thenReturn(responseDTO);

        // When
        IncidentResponseDTO result = incidentService.addReparationToIncident(1L, 1L);

        // Then
        assertThat(result).isEqualTo(responseDTO);
        verify(incidentRepository).findById(1L);
        verify(reparationRepository).findById(1L);
        verify(incidentRepository).save(incident);
        verify(incidentMapper).toResponseDTO(incident);

        // Verify reparation was added to incident
        assertThat(incident.getReparations()).contains(reparation);
        assertThat(reparation.getIncident()).isEqualTo(incident);
    }

    @Test
    void addReparationToIncident_shouldThrowResourceNotFoundException_whenIncidentDoesNotExist() {
        // Given
        when(incidentRepository.findById(99L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> incidentService.addReparationToIncident(99L, 1L));
        verify(incidentRepository).findById(99L);
        verify(reparationRepository, never()).findById(anyLong());
    }

    @Test
    void addReparationToIncident_shouldThrowResourceNotFoundException_whenReparationDoesNotExist() {
        // Given
        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident));
        when(reparationRepository.findById(99L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> incidentService.addReparationToIncident(1L, 99L));
        verify(incidentRepository).findById(1L);
        verify(reparationRepository).findById(99L);
        verify(incidentRepository, never()).save(any());
    }

    @Test
    void addReparationToIncident_shouldThrowBadRequestException_whenReparationAlreadyAssociated() {
        // Given
        Incident anotherIncident = new Incident();
        anotherIncident.setId(2L);

        Reparation associatedReparation = new Reparation();
        associatedReparation.setId(1L);
        associatedReparation.setIncident(anotherIncident);

        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident));
        when(reparationRepository.findById(1L)).thenReturn(Optional.of(associatedReparation));

        // When & Then
        assertThrows(BadRequestException.class, () -> incidentService.addReparationToIncident(1L, 1L));
        verify(incidentRepository).findById(1L);
        verify(reparationRepository).findById(1L);
        verify(incidentRepository, never()).save(any());
    }

    @Test
    void removeReparationFromIncident_shouldRemoveReparationAndReturnUpdatedIncident() {
        // Given
        // Set up the relationship
        reparation.setIncident(incident);
        incident.getReparations().add(reparation);

        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident));
        when(reparationRepository.findById(1L)).thenReturn(Optional.of(reparation));
        when(incidentRepository.save(incident)).thenReturn(incident);
        when(incidentMapper.toResponseDTO(incident)).thenReturn(responseDTO);

        // When
        IncidentResponseDTO result = incidentService.removeReparationFromIncident(1L, 1L);

        // Then
        assertThat(result).isEqualTo(responseDTO);
        verify(incidentRepository).findById(1L);
        verify(reparationRepository).findById(1L);
        verify(incidentRepository).save(incident);
        verify(incidentMapper).toResponseDTO(incident);

        // Verify reparation was removed from incident
        assertThat(incident.getReparations()).doesNotContain(reparation);
        assertThat(reparation.getIncident()).isNull();
    }

    @Test
    void removeReparationFromIncident_shouldThrowResourceNotFoundException_whenIncidentDoesNotExist() {
        // Given
        when(incidentRepository.findById(99L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> incidentService.removeReparationFromIncident(99L, 1L));
        verify(incidentRepository).findById(99L);
        verify(reparationRepository, never()).findById(anyLong());
    }

    @Test
    void removeReparationFromIncident_shouldThrowResourceNotFoundException_whenReparationDoesNotExist() {
        // Given
        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident));
        when(reparationRepository.findById(99L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> incidentService.removeReparationFromIncident(1L, 99L));
        verify(incidentRepository).findById(1L);
        verify(reparationRepository).findById(99L);
        verify(incidentRepository, never()).save(any());
    }

    @Test
    void removeReparationFromIncident_shouldThrowBadRequestException_whenReparationNotAssociatedWithIncident() {
        // Given
        Incident anotherIncident = new Incident();
        anotherIncident.setId(2L);

        Reparation unassociatedReparation = new Reparation();
        unassociatedReparation.setId(1L);
        // Reparation is not associated with our test incident

        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident));
        when(reparationRepository.findById(1L)).thenReturn(Optional.of(unassociatedReparation));

        // When & Then
        assertThrows(BadRequestException.class, () -> incidentService.removeReparationFromIncident(1L, 1L));
        verify(incidentRepository).findById(1L);
        verify(reparationRepository).findById(1L);
        verify(incidentRepository, never()).save(any());
    }

    @Test
    void getIncidentEntityById_shouldReturnIncidentEntity_whenIncidentExists() {
        // Given
        when(incidentRepository.findById(1L)).thenReturn(Optional.of(incident));

        // When
        Incident result = incidentService.getIncidentEntityById(1L);

        // Then
        assertThat(result).isEqualTo(incident);
        verify(incidentRepository).findById(1L);
    }

    @Test
    void getIncidentEntityById_shouldThrowResourceNotFoundException_whenIncidentDoesNotExist() {
        // Given
        when(incidentRepository.findById(99L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> incidentService.getIncidentEntityById(99L));
        verify(incidentRepository).findById(99L);
    }
}