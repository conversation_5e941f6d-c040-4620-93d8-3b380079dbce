package otbs.ms_incident.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * DTO pour les coûts de réparation par véhicule et par statut
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepairCostByVehicleDTO {
    private Map<Long, Map<String, BigDecimal>> costByVehicleAndStatus;
    private Map<Long, String> vehicleImmatriculations;
}
