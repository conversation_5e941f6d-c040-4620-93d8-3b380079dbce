package otbs.ms_incident.exception;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.MessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Path;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class GlobalExceptionHandlerTest {

    @InjectMocks
    private GlobalExceptionHandler exceptionHandler;

    @Mock
    private MessageSource messageSource;

    @Mock
    private HttpServletRequest request;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(request.getRequestURI()).thenReturn("/api/test");
        when(request.getLocale()).thenReturn(Locale.FRENCH);
        when(messageSource.getMessage(anyString(), any(), any(Locale.class)))
            .thenReturn("Message localisé");
    }

    @Test
    void handleResourceNotFoundException() {
        // Arrange
        ResourceNotFoundException ex = new ResourceNotFoundException("Resource not found");
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleResourceNotFoundException(ex, request);
        
        // Assert
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.NOT_FOUND.value(), body.getStatus());
        assertEquals("Resource not found", body.getMessage());
        assertEquals("/api/test", body.getPath());
        assertNotNull(body.getTimestamp());
        assertNotNull(body.getTraceId());
    }

    @Test
    void handleBadRequestException() {
        // Arrange
        BadRequestException ex = new BadRequestException("Bad request");
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleBadRequestException(ex, request);
        
        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.BAD_REQUEST.value(), body.getStatus());
        assertEquals("Bad request", body.getMessage());
    }

    @Test
    void handleFileStorageException() {
        // Arrange
        FileStorageException ex = new FileStorageException("File error", HttpStatus.INTERNAL_SERVER_ERROR, "FILE_ERROR");
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleFileStorageException(ex, request);
        
        // Assert
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), body.getStatus());
        assertEquals("Message localisé", body.getMessage());
        assertEquals("FILE_ERROR", body.getCode());
    }

    @Test
    void handleValidationExceptions() {
        // Arrange
        MethodArgumentNotValidException ex = mock(MethodArgumentNotValidException.class);
        BindingResult bindingResult = mock(BindingResult.class);
        FieldError fieldError = new FieldError("testObject", "field", "Field has an error");
        
        when(ex.getBindingResult()).thenReturn(bindingResult);
        when(bindingResult.getFieldErrors()).thenReturn(Collections.singletonList(fieldError));
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleValidationExceptions(ex, request);
        
        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.BAD_REQUEST.value(), body.getStatus());
        List<ErrorResponse.ValidationError> fieldErrors = body.getFieldErrors();
        assertNotNull(fieldErrors);
        assertEquals(1, fieldErrors.size());
        assertEquals("field", fieldErrors.get(0).getField());
        assertEquals("Field has an error", fieldErrors.get(0).getMessage());
    }

    @Test
    void handleMethodArgumentTypeMismatch() {
        // Arrange
        MethodArgumentTypeMismatchException ex = mock(MethodArgumentTypeMismatchException.class);
        when(ex.getName()).thenReturn("paramName");
        when(ex.getValue()).thenReturn("invalidValue");
        when(ex.getRequiredType()).thenReturn((Class)Integer.class);
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleMethodArgumentTypeMismatch(ex, request);
        
        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.BAD_REQUEST.value(), body.getStatus());
        assertEquals("Message localisé", body.getMessage());
    }

    @Test
    void handleGlobalException() {
        // Arrange
        Exception ex = new RuntimeException("Unexpected error");
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleGlobalException(ex, request);
        
        // Assert
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR.value(), body.getStatus());
        assertEquals("Message localisé", body.getMessage());
    }
    
    @Test
    void handleFileNotFoundException() {
        // Arrange
        FileNotFoundStorageException ex = new FileNotFoundStorageException("File not found path: /some/secret/path");
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleFileNotFoundException(ex, request);
        
        // Assert
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.NOT_FOUND.value(), body.getStatus());
        assertEquals("Message localisé", body.getMessage());
        assertEquals("FILE_NOT_FOUND", body.getCode());
    }
    
    @Test
    void handleInvalidFileTypeException() {
        // Arrange
        InvalidFileTypeException ex = new InvalidFileTypeException("Invalid file type");
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleInvalidFileTypeException(ex, request);
        
        // Assert
        assertEquals(HttpStatus.FORBIDDEN, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.FORBIDDEN.value(), body.getStatus());
        assertEquals("Message localisé", body.getMessage());
        assertEquals("INVALID_FILE_TYPE", body.getCode());
    }
    
    @Test
    void handleMaxSizeException() {
        // Arrange
        MaxUploadSizeExceededException ex = new MaxUploadSizeExceededException(10485760);
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleMaxSizeException(ex, request);
        
        // Assert
        assertEquals(HttpStatus.PAYLOAD_TOO_LARGE, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.PAYLOAD_TOO_LARGE.value(), body.getStatus());
        assertEquals("Message localisé", body.getMessage());
        assertEquals("FILE_TOO_LARGE", body.getCode());
    }
    
    @Test
    void handleIllegalArgumentException() {
        // Arrange
        IllegalArgumentException ex = new IllegalArgumentException("Invalid argument");
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleIllegalArgumentException(ex, request);
        
        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.BAD_REQUEST.value(), body.getStatus());
        assertEquals("Invalid argument", body.getMessage());
    }
    
    @Test
    void handleAccessDeniedException() {
        // Arrange
        AccessDeniedException ex = new AccessDeniedException("Access denied");
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleAccessDeniedException(ex, request);
        
        // Assert
        assertEquals(HttpStatus.FORBIDDEN, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.FORBIDDEN.value(), body.getStatus());
        assertEquals("Message localisé", body.getMessage());
    }
    
    @Test
    void handleDataIntegrityViolation() {
        // Arrange
        DataIntegrityViolationException ex = new DataIntegrityViolationException("Data integrity violation");
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleDataIntegrityViolation(ex, request);
        
        // Assert
        assertEquals(HttpStatus.CONFLICT, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.CONFLICT.value(), body.getStatus());
        assertEquals("Message localisé", body.getMessage());
    }
    
    @Test
    void handleConstraintViolation() {
        // Arrange
        Set<ConstraintViolation<?>> violations = new HashSet<>();
        ConstraintViolation<?> violation = mock(ConstraintViolation.class);
        Path path = mock(Path.class);
        
        when(violation.getPropertyPath()).thenReturn(path);
        when(path.toString()).thenReturn("object.fieldName");
        when(violation.getMessage()).thenReturn("Field error message");
        
        violations.add(violation);
        ConstraintViolationException ex = new ConstraintViolationException("Validation failed", violations);
        
        // Act
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleConstraintViolation(ex, request);
        
        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(HttpStatus.BAD_REQUEST.value(), body.getStatus());
        List<ErrorResponse.ValidationError> fieldErrors = body.getFieldErrors();
        assertNotNull(fieldErrors);
        assertEquals(1, fieldErrors.size());
        assertEquals("fieldName", fieldErrors.get(0).getField());
        assertEquals("Field error message", fieldErrors.get(0).getMessage());
    }
} 