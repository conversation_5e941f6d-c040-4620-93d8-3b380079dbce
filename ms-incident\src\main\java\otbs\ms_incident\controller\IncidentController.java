package otbs.ms_incident.controller;

import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.enums.TypeIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.commun.PageResponse;

import otbs.ms_incident.service.IncidentService;
import otbs.ms_incident.client.VehiculeClient;
import otbs.ms_incident.client.VehiculeDto;
import otbs.ms_incident.util.ControllerUtils;

import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Contrôleur REST pour la gestion des incidents.
 * Cette classe expose les endpoints de l'API permettant de créer, lire, mettre à jour et supprimer
 * des incidents dans le système ParcAuto. Elle offre également des fonctionnalités de recherche
 * avancées par type, date et lieu.
 */
@RestController
@RequestMapping("/api/incidents")
@Tag(name = "Incident", description = "API pour la gestion des incidents")
@RequiredArgsConstructor
@Slf4j
public class IncidentController {

    private static final String VEHICULE_NOT_FOUND_MESSAGE = "Véhicule avec ID {} non trouvé";

    private final IncidentService incidentService;
    private final VehiculeClient vehiculeClient;


    @Operation(summary = "Créer un incident", description = "Crée un nouvel incident dans le système")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Incident créé avec succès",
                    content = @Content(schema = @Schema(implementation = IncidentResponseDTO.class))),
            @ApiResponse(responseCode = "400", description = "Données invalides"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_TECHNIQUE', 'CONSULTANT')")
    @PostMapping
    public ResponseEntity<IncidentResponseDTO> createIncident(@Valid @RequestBody IncidentRequestDTO requestDTO) {
        log.info("REST request to create incident: {}", requestDTO);

        if (requestDTO.getType() == null || requestDTO.getLieu() == null || requestDTO.getDate() == null) {
            throw new IllegalArgumentException("Le type, le lieu et la date sont obligatoires pour créer un incident");
        }

        IncidentResponseDTO responseDTO = incidentService.createIncident(requestDTO);
        return new ResponseEntity<>(responseDTO, HttpStatus.CREATED);
    }

    @Operation(summary = "Récupérer un incident par ID", description = "Retourne un incident basé sur son ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Incident trouvé",
                    content = @Content(schema = @Schema(implementation = IncidentResponseDTO.class))),
            @ApiResponse(responseCode = "404", description = "Incident non trouvé"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL','CONSULTANT')")
    @GetMapping("/{id}")
    public ResponseEntity<IncidentResponseDTO> getIncidentById(
            @Parameter(description = "ID de l'incident à récupérer") @PathVariable Long id) {
        log.info("REST request to get incident with ID: {}", id);
        IncidentResponseDTO responseDTO = incidentService.getIncidentById(id);
        return ResponseEntity.ok(responseDTO);
    }

    @Operation(summary = "Récupérer tous les incidents", description = "Retourne la liste de tous les incidents")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des incidents récupérée"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL','CONSULTANT')")
    @GetMapping
    public ResponseEntity<List<IncidentResponseDTO>> getAllIncidents() {
        log.info("REST request to get all incidents");
        List<IncidentResponseDTO> incidents = incidentService.getAllIncidents();
        return ResponseEntity.ok(incidents);
    }

    @Operation(summary = "Mettre à jour un incident", description = "Met à jour les données d'un incident existant")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Incident mis à jour"),
            @ApiResponse(responseCode = "400", description = "Données invalides"),
            @ApiResponse(responseCode = "404", description = "Incident non trouvé"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX')")
    @PutMapping("/{id}")
    public ResponseEntity<IncidentResponseDTO> updateIncident(
            @Parameter(description = "ID de l'incident à mettre à jour") @PathVariable Long id,
            @Valid @RequestBody IncidentRequestDTO requestDTO) {
        log.info("REST request to update incident with ID: {}", id);
        IncidentResponseDTO responseDTO = incidentService.updateIncident(id, requestDTO);
        return ResponseEntity.ok(responseDTO);
    }

    @Operation(summary = "Supprimer un incident", description = "Supprime un incident du système")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Incident supprimé"),
            @ApiResponse(responseCode = "404", description = "Incident non trouvé"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX')")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteIncident(
            @Parameter(description = "ID de l'incident à supprimer") @PathVariable Long id) {
        log.info("REST request to delete incident with ID: {}", id);
        incidentService.deleteIncident(id);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Mettre à jour le statut d'un incident",
            description = "Met à jour uniquement le statut d'un incident existant")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statut de l'incident mis à jour"),
            @ApiResponse(responseCode = "400", description = "Statut invalide"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé"),
            @ApiResponse(responseCode = "404", description = "Incident non trouvé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX')")
    @PatchMapping("/{id}/status/{status}")
    public ResponseEntity<IncidentResponseDTO> updateIncidentStatus(
            @Parameter(description = "ID de l'incident à mettre à jour") @PathVariable Long id,
            @Parameter(description = "Nouveau statut de l'incident") @PathVariable StatusIncident status) {
        log.info("REST request to update status of incident with ID: {} to {}", id, status);
        IncidentResponseDTO responseDTO = incidentService.updateIncidentStatus(id, status);
        return ResponseEntity.ok(responseDTO);
    }



    @Operation(summary = "Rechercher des incidents par période", description = "Retourne les incidents survenus entre les dates spécifiées")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des incidents récupérée"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/date")
    public ResponseEntity<List<IncidentResponseDTO>> getIncidentsByDateRange(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin) {
        log.info("REST request to get incidents between dates: {} and {}", debut, fin);
        List<IncidentResponseDTO> incidents = incidentService.getIncidentsByDateRange(debut, fin);
        return ResponseEntity.ok(incidents);
    }

    @Operation(summary = "Rechercher des incidents par lieu", description = "Retourne les incidents correspondant au lieu spécifié")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Liste des incidents récupérée"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/lieu")
    public ResponseEntity<List<IncidentResponseDTO>> getIncidentsByLieu(
            @Parameter(description = "Lieu des incidents") @RequestParam String lieu) {
        log.info("REST request to get incidents by location: {}", lieu);
        List<IncidentResponseDTO> incidents = incidentService.getIncidentsByLieu(lieu);
        return ResponseEntity.ok(incidents);
    }

    @Operation(summary = "Ajouter une réparation à un incident", description = "Associe une réparation existante à un incident")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Réparation ajoutée à l'incident"),
            @ApiResponse(responseCode = "404", description = "Incident ou réparation non trouvé"),
            @ApiResponse(responseCode = "400", description = "La réparation est déjà associée à un incident"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX')")
    @PostMapping("/{incidentId}/reparations/{reparationId}")
    public ResponseEntity<IncidentResponseDTO> addReparationToIncident(
            @Parameter(description = "ID de l'incident") @PathVariable Long incidentId,
            @Parameter(description = "ID de la réparation à associer") @PathVariable Long reparationId) {
        log.info("REST request to add reparation {} to incident {}", reparationId, incidentId);
        IncidentResponseDTO responseDTO = incidentService.addReparationToIncident(incidentId, reparationId);
        return ResponseEntity.ok(responseDTO);
    }

    @Operation(summary = "Retirer une réparation d'un incident", description = "Dissocie une réparation d'un incident")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Réparation retirée de l'incident"),
            @ApiResponse(responseCode = "404", description = "Incident ou réparation non trouvé"),
            @ApiResponse(responseCode = "400", description = "La réparation n'est pas associée à cet incident"),
            @ApiResponse(responseCode = "401", description = "Non authentifié"),
            @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX')")
    @DeleteMapping("/{incidentId}/reparations/{reparationId}")
    public ResponseEntity<IncidentResponseDTO> removeReparationFromIncident(
            @Parameter(description = "ID de l'incident") @PathVariable Long incidentId,
            @Parameter(description = "ID de la réparation à dissocier") @PathVariable Long reparationId) {
        log.info("REST request to remove reparation {} from incident {}", reparationId, incidentId);
        IncidentResponseDTO responseDTO = incidentService.removeReparationFromIncident(incidentId, reparationId);
        return ResponseEntity.ok(responseDTO);
    }

    @GetMapping("/vehicule/{vehiculeId}")
    @Operation(summary = "Récupérer les incidents par véhicule",
              description = "Retourne tous les incidents associés à un véhicule spécifique")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Incidents récupérés avec succès"),
        @ApiResponse(responseCode = "404", description = "Véhicule non trouvé", content = @Content)
    })
    @CircuitBreaker(name = "vehiculeService", fallbackMethod = "getIncidentsByVehiculeIdFallback")
    @Retry(name = "vehiculeService")
    public ResponseEntity<List<IncidentResponseDTO>> getIncidentsByVehiculeId(
            @Parameter(description = "ID du véhicule", required = true)
            @PathVariable Long vehiculeId) {
        log.info("REST request to get incidents for vehicule {}", vehiculeId);

        // Vérifier d'abord que le véhicule existe
        VehiculeDto vehicule = vehiculeClient.getVehiculeById(vehiculeId);
        if (vehicule == null) {
            return ResponseEntity.notFound().build();
        }

        List<IncidentResponseDTO> incidents = incidentService.getIncidentsByVehiculeId(vehiculeId);
        return ResponseEntity.ok(incidents);
    }

    @GetMapping("/count/vehicule/{vehiculeId}")
    @Operation(summary = "Compter les incidents par véhicule",
              description = "Retourne le nombre d'incidents associés à un véhicule spécifique")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Comptage effectué avec succès"),
        @ApiResponse(responseCode = "404", description = "Véhicule non trouvé", content = @Content)
    })
    @CircuitBreaker(name = "vehiculeService", fallbackMethod = "countIncidentsByVehiculeIdFallback")
    @Retry(name = "vehiculeService")
    public ResponseEntity<Long> countIncidentsByVehiculeId(
            @Parameter(description = "ID du véhicule", required = true)
            @PathVariable Long vehiculeId) {
        log.info("REST request to count incidents for vehicule {}", vehiculeId);

        // Vérifier d'abord que le véhicule existe
        VehiculeDto vehicule = vehiculeClient.getVehiculeById(vehiculeId);
        if (vehicule == null) {
            return ResponseEntity.notFound().build();
        }

        Long count = incidentService.countIncidentsByVehiculeId(vehiculeId);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/vehicule/{vehiculeId}/details")
    @Operation(summary = "Récupérer les détails d'un véhicule",
              description = "Retourne les caractéristiques d'un véhicule d'après son ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Détails du véhicule récupérés avec succès",
                    content = @Content(schema = @Schema(implementation = VehiculeDto.class))),
        @ApiResponse(responseCode = "404", description = "Véhicule non trouvé", content = @Content),
        @ApiResponse(responseCode = "500", description = "Erreur lors de la récupération des détails du véhicule", content = @Content)
    })
    @CircuitBreaker(name = "vehiculeService", fallbackMethod = "getVehiculeDetailsFallback")
    @Retry(name = "vehiculeService")
    public ResponseEntity<VehiculeDto> getVehiculeDetails(
            @Parameter(description = "ID du véhicule", required = true)
            @PathVariable Long vehiculeId) {
        log.info("REST request to get details for vehicule {}", vehiculeId);

        VehiculeDto vehicule = vehiculeClient.getVehiculeById(vehiculeId);
        if (vehicule == null) {
            log.warn(VEHICULE_NOT_FOUND_MESSAGE, vehiculeId);
            return ResponseEntity.notFound().build();
        }

        log.info("Détails du véhicule récupérés avec succès: {}", vehicule);
        return ResponseEntity.ok(vehicule);
    }



    @GetMapping("/vehicule/{vehiculeId}/details-with-incidents")
    @Operation(summary = "Récupérer les détails d'un véhicule avec ses incidents",
              description = "Retourne les caractéristiques d'un véhicule et la liste de ses incidents associés")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Détails du véhicule et incidents récupérés avec succès"),
        @ApiResponse(responseCode = "404", description = "Véhicule non trouvé", content = @Content),
        @ApiResponse(responseCode = "500", description = "Erreur lors de la récupération des détails", content = @Content)
    })
    @CircuitBreaker(name = "vehiculeService", fallbackMethod = "getVehiculeDetailsWithIncidentsFallback")
    @Retry(name = "vehiculeService")
    public ResponseEntity<Map<String, Object>> getVehiculeDetailsWithIncidents(
            @Parameter(description = "ID du véhicule", required = true)
            @PathVariable Long vehiculeId) {
        log.info("REST request to get details and incidents for vehicule {}", vehiculeId);
        return getVehiculeDetailsWithIncidentsResponse(vehiculeId, false);
    }



    @GetMapping("/vehicules")
    @Operation(summary = "Récupérer tous les véhicules",
              description = "Retourne la liste de tous les véhicules disponibles")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Liste des véhicules récupérée avec succès"),
        @ApiResponse(responseCode = "500", description = "Erreur lors de la récupération des véhicules", content = @Content)
    })
    @CircuitBreaker(name = "vehiculeService", fallbackMethod = "getAllVehiculesFallback")
    @Retry(name = "vehiculeService")
    public ResponseEntity<List<VehiculeDto>> getAllVehicules() {
        log.info("REST request to get all vehicules");

        try {
            List<VehiculeDto> vehicules = vehiculeClient.getAllVehicules();
            log.info("Liste des véhicules récupérée avec succès: {} véhicules trouvés", vehicules.size());
            return ResponseEntity.ok(vehicules);
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des véhicules: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).build();
        }
    }

    public ResponseEntity<List<VehiculeDto>> getAllVehiculesFallback(Exception e) {
        return ControllerUtils.createServiceUnavailableResponse(e, "vehiculeService", null);
    }

    @GetMapping("/vehicules/immatriculation/{immatriculation}")
    @CircuitBreaker(name = "vehiculeService", fallbackMethod = "getVehiculeByImmatriculationFallback")
    @Retry(name = "vehiculeService")
    public ResponseEntity<VehiculeDto> getVehiculeByImmatriculation(
            @Parameter(description = "Immatriculation du véhicule", required = true)
            @PathVariable String immatriculation) {
        log.info("REST request to get vehicule by immatriculation: {}", immatriculation);

        VehiculeDto vehicule = vehiculeClient.getVehiculeByImmatriculation(immatriculation);
        if (vehicule == null) {
            log.warn("Véhicule avec immatriculation {} non trouvé", immatriculation);
            return ResponseEntity.notFound().build();
        }

        log.info("Véhicule avec immatriculation {} trouvé: {}", immatriculation, vehicule);
        return ResponseEntity.ok(vehicule);
    }

    @GetMapping("/vehicule/{vehiculeId}/complete")
    @Operation(summary = "Récupérer tous les détails d'un véhicule",
              description = "Retourne toutes les informations disponibles sur un véhicule d'après son ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Détails complets du véhicule récupérés avec succès"),
        @ApiResponse(responseCode = "404", description = "Véhicule non trouvé", content = @Content),
        @ApiResponse(responseCode = "500", description = "Erreur lors de la récupération des détails du véhicule", content = @Content)
    })
    @CircuitBreaker(name = "vehiculeService", fallbackMethod = "getVehiculeCompleteDetailsFallback")
    @Retry(name = "vehiculeService")
    public ResponseEntity<Map<String, Object>> getVehiculeCompleteDetails(
            @Parameter(description = "ID du véhicule", required = true)
            @PathVariable Long vehiculeId) {
        log.info("REST request to get complete details for vehicule {}", vehiculeId);
        return getVehiculeDetailsWithIncidentsResponse(vehiculeId, true);
    }

    /**
     * Méthode commune pour récupérer les détails d'un véhicule avec ses incidents
     * Utilisée par getVehiculeDetailsWithIncidents et getVehiculeCompleteDetails
     *
     * @param vehiculeId ID du véhicule
     * @param includeAdditionalInfo Indique si des informations supplémentaires doivent être incluses
     * @return ResponseEntity contenant les détails du véhicule et ses incidents
     */
    private ResponseEntity<Map<String, Object>> getVehiculeDetailsWithIncidentsResponse(Long vehiculeId, boolean includeAdditionalInfo) {
        // Récupérer les détails du véhicule
        VehiculeDto vehicule = vehiculeClient.getVehiculeById(vehiculeId);
        if (vehicule == null) {
            log.warn(VEHICULE_NOT_FOUND_MESSAGE, vehiculeId);
            return ResponseEntity.notFound().build();
        }

        // Récupérer les incidents associés au véhicule
        List<IncidentResponseDTO> incidents = incidentService.getIncidentsByVehiculeId(vehiculeId);
        Long incidentCount = incidentService.countIncidentsByVehiculeId(vehiculeId);

        // Créer la réponse combinée
        Map<String, Object> response = new HashMap<>();
        response.put("vehicule", vehicule);
        response.put("incidents", incidents);
        response.put("incidentCount", incidentCount);

        // Ajouter des informations supplémentaires si nécessaire
        if (includeAdditionalInfo) {
            response.put("incidentsFromMsIncident", true);
        }

        log.info("Détails du véhicule et incidents récupérés avec succès pour le véhicule {}", vehiculeId);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/dashboard/today")
    @Operation(summary = "Récupérer les incidents d'aujourd'hui", description = "Retourne tous les incidents survenus aujourd'hui")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Incidents récupérés avec succès")
    })
    public ResponseEntity<List<IncidentResponseDTO>> getIncidentsForToday() {
        log.info("Récupération des incidents survenus aujourd'hui");
        List<IncidentResponseDTO> incidents = incidentService.getIncidentsForToday();
        return ResponseEntity.ok(incidents);
    }

    @GetMapping("/dashboard/last-month")
    @Operation(summary = "Récupérer les incidents du dernier mois", description = "Retourne tous les incidents survenus au cours du dernier mois")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Incidents récupérés avec succès")
    })
    public ResponseEntity<List<IncidentResponseDTO>> getIncidentsForLastMonth() {
        log.info("Récupération des incidents survenus au cours du dernier mois");
        List<IncidentResponseDTO> incidents = incidentService.getIncidentsForLastMonth();
        return ResponseEntity.ok(incidents);
    }

    @GetMapping("/dashboard/last-semester")
    @Operation(summary = "Récupérer les incidents du dernier semestre", description = "Retourne tous les incidents survenus au cours du dernier semestre")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Incidents récupérés avec succès")
    })
    public ResponseEntity<List<IncidentResponseDTO>> getIncidentsForLastSemester() {
        log.info("Récupération des incidents survenus au cours du dernier semestre");
        List<IncidentResponseDTO> incidents = incidentService.getIncidentsForLastSemester();
        return ResponseEntity.ok(incidents);
    }

    @GetMapping("/dashboard/last-year")
    @Operation(summary = "Récupérer les incidents de la dernière année", description = "Retourne tous les incidents survenus au cours de la dernière année")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Incidents récupérés avec succès")
    })
    public ResponseEntity<List<IncidentResponseDTO>> getIncidentsForLastYear() {
        log.info("Récupération des incidents survenus au cours de la dernière année");
        List<IncidentResponseDTO> incidents = incidentService.getIncidentsForLastYear();
        return ResponseEntity.ok(incidents);
    }

    // Endpoints paginés

    @Operation(summary = "Récupérer tous les incidents avec pagination", description = "Retourne une page d'incidents")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Page d'incidents récupérée"),
        @ApiResponse(responseCode = "401", description = "Non authentifié"),
        @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL', 'CONSULTANT')")
    @GetMapping("/paginated")
    public ResponseEntity<PageResponse<IncidentResponseDTO>> getAllIncidentsPaginated(
            @Parameter(description = "Numéro de page (commence à 0)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Taille de la page") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Champ de tri") @RequestParam(defaultValue = "id") String sort,
            @Parameter(description = "Direction du tri (ASC ou DESC)") @RequestParam(defaultValue = "DESC") String direction) {
        log.info("REST request to get paginated incidents: page={}, size={}, sort={}, direction={}", page, size, sort, direction);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        PageResponse<IncidentResponseDTO> incidents = incidentService.getAllIncidents(pageable);
        return ResponseEntity.ok(incidents);
    }



    @Operation(summary = "Récupérer les incidents par période avec pagination", description = "Retourne une page d'incidents survenus entre les dates spécifiées")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Page d'incidents récupérée"),
        @ApiResponse(responseCode = "400", description = "Format de date invalide"),
        @ApiResponse(responseCode = "401", description = "Non authentifié"),
        @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/date/paginated")
    public ResponseEntity<PageResponse<IncidentResponseDTO>> getIncidentsByDateRangePaginated(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin,
            @Parameter(description = "Numéro de page (commence à 0)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Taille de la page") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Champ de tri") @RequestParam(defaultValue = "id") String sort,
            @Parameter(description = "Direction du tri (ASC ou DESC)") @RequestParam(defaultValue = "DESC") String direction) {
        log.info("REST request to get paginated incidents between dates: {} and {}, page={}, size={}, sort={}, direction={}", debut, fin, page, size, sort, direction);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        PageResponse<IncidentResponseDTO> incidents = incidentService.getIncidentsByDateRange(debut, fin, pageable);
        return ResponseEntity.ok(incidents);
    }

    @Operation(summary = "Récupérer les incidents par lieu avec pagination", description = "Retourne une page d'incidents correspondant au lieu spécifié")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Page d'incidents récupérée"),
        @ApiResponse(responseCode = "401", description = "Non authentifié"),
        @ApiResponse(responseCode = "403", description = "Accès refusé")
    })
    @PreAuthorize("hasAnyRole('ADMINISTRATEUR', 'RESPONSABLE_SERVICE_GENERAUX', 'DIRECTEUR_GENERAL')")
    @GetMapping("/lieu/paginated")
    public ResponseEntity<PageResponse<IncidentResponseDTO>> getIncidentsByLieuPaginated(
            @Parameter(description = "Lieu des incidents") @RequestParam String lieu,
            @Parameter(description = "Numéro de page (commence à 0)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Taille de la page") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Champ de tri") @RequestParam(defaultValue = "id") String sort,
            @Parameter(description = "Direction du tri (ASC ou DESC)") @RequestParam(defaultValue = "DESC") String direction) {
        log.info("REST request to get paginated incidents by location: {}, page={}, size={}, sort={}, direction={}", lieu, page, size, sort, direction);

        Sort.Direction sortDirection = direction.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));

        PageResponse<IncidentResponseDTO> incidents = incidentService.getIncidentsByLieu(lieu, pageable);
        return ResponseEntity.ok(incidents);
    }











    // Méthodes de fallback pour les endpoints

    public ResponseEntity<VehiculeDto> getVehiculeByImmatriculationFallback(String immatriculation, Exception e) {
        return ControllerUtils.createServiceUnavailableResponse(e, "vehiculeService", immatriculation);
    }

    @GetMapping("/stats/by-date-range")
    @Operation(summary = "Obtenir les statistiques des incidents par plage de dates",
               description = "Retourne les statistiques des incidents pour une plage de dates donnée")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistiques récupérées avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    public ResponseEntity<Map<String, Object>> getIncidentStatsByDateRange(
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate debut,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fin) {

        log.info("REST request to get incident stats for date range: {} to {}", debut, fin);

        // Récupérer les incidents dans la plage de dates
        List<IncidentResponseDTO> incidents = incidentService.getIncidentsByDateRange(debut, fin);

        // Calculer les statistiques
        long totalIncidents = incidents.size();
        long openIncidents = incidents.stream()
                .filter(i -> i.getStatus() == StatusIncident.A_TRAITER)
                .count();
        long inProgressIncidents = incidents.stream()
                .filter(i -> i.getStatus() == StatusIncident.EN_COURS_TRAITEMENT)
                .count();
        long resolvedIncidents = incidents.stream()
                .filter(i -> i.getStatus() == StatusIncident.RESOLU)
                .count();

        // Compter par type
        Map<String, Long> countByType = incidents.stream()
                .collect(Collectors.groupingBy(
                        i -> i.getType().toString(),
                        Collectors.counting()
                ));

        // Compter par véhicule
        Map<Long, Long> countByVehicule = incidents.stream()
                .collect(Collectors.groupingBy(
                        IncidentResponseDTO::getVehiculeId,
                        Collectors.counting()
                ));

        // Récupérer les immatriculations des véhicules depuis le service véhicule
        Map<String, String> vehiculeImmatriculations = new HashMap<>();

        // Récupérer tous les véhicules une seule fois pour optimiser
        List<VehiculeDto> allVehicules = vehiculeClient.getAllVehicules();
        Map<Long, String> vehiculeIdToImmatriculation = allVehicules.stream()
                .collect(Collectors.toMap(
                        VehiculeDto::getIdVehicule,
                        VehiculeDto::getImmatriculation,
                        (existing, replacement) -> existing // En cas de doublon, garder la première valeur
                ));

        // Associer les IDs de véhicule aux immatriculations
        incidents.stream()
                .filter(i -> i.getVehiculeId() != null)
                .forEach(i -> {
                    String immatriculation = vehiculeIdToImmatriculation.get(i.getVehiculeId());
                    if (immatriculation != null) {
                        vehiculeImmatriculations.put(i.getVehiculeId().toString(), immatriculation);
                    } else if (i.getImmatriculation() != null) {
                        // Fallback sur l'immatriculation générée si le service véhicule ne renvoie pas d'immatriculation
                        vehiculeImmatriculations.put(i.getVehiculeId().toString(), i.getImmatriculation());
                    }
                });

        // Construire la réponse
        Map<String, Object> response = new HashMap<>();
        response.put("totalIncidents", totalIncidents);
        response.put("openIncidents", openIncidents);
        response.put("inProgressIncidents", inProgressIncidents);
        response.put("resolvedIncidents", resolvedIncidents);
        response.put("countByType", countByType);
        response.put("countByVehicule", countByVehicule);
        response.put("vehiculeImmatriculations", vehiculeImmatriculations);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/search")
    @Operation(summary = "Rechercher des incidents avec plusieurs critères",
               description = "Retourne les incidents correspondant aux critères de recherche spécifiés")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Incidents récupérés avec succès"),
            @ApiResponse(responseCode = "400", description = "Format de date invalide")
    })
    public ResponseEntity<List<IncidentResponseDTO>> searchIncidents(
            @Parameter(description = "Statut de l'incident")
            @RequestParam(required = false) StatusIncident status,
            @Parameter(description = "Type d'incident")
            @RequestParam(required = false) TypeIncident type,
            @Parameter(description = "Priorité de l'incident")
            @RequestParam(required = false) NiveauPrioriteIncident priorite,
            @Parameter(description = "Date de début (format YYYY-MM-DD)")
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "Date de fin (format YYYY-MM-DD)")
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @Parameter(description = "Lieu de l'incident (recherche partielle)")
            @RequestParam(required = false) String lieu,
            @Parameter(description = "ID du véhicule")
            @RequestParam(required = false) Long vehiculeId) {

        log.info("REST request to search incidents with criteria: status={}, type={}, priorite={}, startDate={}, endDate={}, lieu={}, vehiculeId={}",
                status, type, priorite, startDate, endDate, lieu, vehiculeId);

        // Récupérer tous les incidents
        List<IncidentResponseDTO> allIncidents = incidentService.getAllIncidents();

        // Filtrer les incidents selon les critères fournis
        List<IncidentResponseDTO> filteredIncidents = allIncidents.stream()
            .filter(i -> status == null || i.getStatus() == status)
            .filter(i -> type == null || i.getType() == type)
            .filter(i -> priorite == null || i.getPriorite() == priorite)
            .filter(i -> startDate == null || (i.getDate() != null && !i.getDate().isBefore(startDate)))
            .filter(i -> endDate == null || (i.getDate() != null && !i.getDate().isAfter(endDate)))
            .filter(i -> lieu == null || (i.getLieu() != null && i.getLieu().toLowerCase().contains(lieu.toLowerCase())))
            .filter(i -> vehiculeId == null || i.getVehiculeId().equals(vehiculeId))
            .toList();

        return ResponseEntity.ok(filteredIncidents);
    }
}