package otbs.ms_incident.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import otbs.ms_incident.dto.IncidentRequestDTO;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.enums.FileType;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.service.FileStorageService;
import otbs.ms_incident.service.IncidentFileManager;
import otbs.ms_incident.service.IncidentService;

import java.net.URI;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FileStorageControllerAdvancedTest {

    @Mock
    private FileStorageService fileStorageService;

    @Mock
    private IncidentFileManager incidentFileManager;

    @Mock
    private IncidentService incidentService;

    @Mock
    private Path mockPath;

    @Mock
    private URI mockUri;

    @Mock
    private UrlResource mockUrlResource;

    @InjectMocks
    private FileStorageController fileStorageController;

    private MultipartFile testFile;
    private Map<String, Object> uploadResponse;
    private Resource testResource;
    private IncidentResponseDTO incidentResponseDTO;

    @BeforeEach
    void setUp() {
        // Setup test file
        testFile = new MockMultipartFile(
                "test.jpg",
                "test.jpg",
                MediaType.IMAGE_JPEG_VALUE,
                "test content".getBytes()
        );

        // Setup response for upload
        uploadResponse = new HashMap<>();
        uploadResponse.put("filename", "test.jpg");
        uploadResponse.put("path", "incident_123/photos/test.jpg");
        uploadResponse.put("status", "success");

        // Setup mock resource
        testResource = mock(Resource.class);
        when(testResource.exists()).thenReturn(true);
        when(testResource.getFilename()).thenReturn("test.jpg");

        // Setup incident response
        incidentResponseDTO = new IncidentResponseDTO();
        incidentResponseDTO.setId(123L);

        // File storage path
        when(fileStorageService.getStoragePath()).thenReturn("test/storage");
    }

    @Test
    void incidentExists_shouldReturnTrueWhenIncidentExists() {
        // Given
        doNothing().when(incidentFileManager).validateIncidentExists(anyLong());

        // When
        boolean result = invokeIncidentExists(123L);

        // Then
        assertTrue(result);
        verify(incidentFileManager).validateIncidentExists(123L);
    }

    @Test
    void incidentExists_shouldReturnFalseWhenIncidentDoesNotExist() {
        // Given
        doThrow(new ResourceNotFoundException("Incident not found"))
                .when(incidentFileManager).validateIncidentExists(anyLong());

        // When
        boolean result = invokeIncidentExists(123L);

        // Then
        assertFalse(result);
        verify(incidentFileManager).validateIncidentExists(123L);
    }

    @Test
    void createIncidentAutomatically_shouldReturnTrueWhenIncidentIsCreated() {
        // Given
        when(incidentService.createIncident(any(IncidentRequestDTO.class)))
                .thenReturn(incidentResponseDTO);

        // When
        boolean result = invokeCreateIncidentAutomatically();

        // Then
        assertTrue(result);
        verify(incidentService).createIncident(any(IncidentRequestDTO.class));
    }

    @Test
    void createIncidentAutomatically_shouldReturnFalseWhenIncidentCreationFails() {
        // Given
        when(incidentService.createIncident(any(IncidentRequestDTO.class)))
                .thenThrow(new RuntimeException("Failed to create incident"));

        // When
        boolean result = invokeCreateIncidentAutomatically();

        // Then
        assertFalse(result);
        verify(incidentService).createIncident(any(IncidentRequestDTO.class));
    }

    @Test
    void createErrorResponse_shouldReturnMapWithErrorStatus() {
        // Given
        String errorMessage = "Test error message";

        // When
        Map<String, Object> result = invokeCreateErrorResponse(errorMessage);

        // Then
        assertNotNull(result);
        assertEquals("error", result.get("status"));
        assertEquals(errorMessage, result.get("error"));
    }

    @Test
    void listIncidentFiles_shouldHandleEmptyFileType() {
        // Given
        doNothing().when(incidentFileManager).validateIncidentExists(anyLong());
        List<Map<String, Object>> filesList = new ArrayList<>();
        Map<String, Object> fileInfo = new HashMap<>();
        fileInfo.put("name", "test.jpg");
        fileInfo.put("path", "incident_123/photos/test.jpg");
        fileInfo.put("size", 123456L);
        filesList.add(fileInfo);

        when(incidentFileManager.listFiles(anyLong(), isNull()))
                .thenReturn(filesList);

        // When
        ResponseEntity<List<Map<String, Object>>> response = fileStorageController.listIncidentFiles(
                123L, "");

        // Then
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(filesList, response.getBody());
        verify(incidentFileManager).validateIncidentExists(123L);
        verify(incidentFileManager).listFiles(eq(123L), isNull());
    }

    @Test
    void downloadIncidentFile_shouldDelegateToGetFileResponse() {
        // Given
        doNothing().when(incidentFileManager).validateIncidentExists(anyLong());
        when(incidentFileManager.determineContentType(anyString())).thenReturn("image/jpeg");

        // When
        ResponseEntity<Resource> response = fileStorageController.downloadIncidentFile(
                123L, "PHOTO", "test.jpg");

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        verify(incidentFileManager).validateIncidentExists(123L);
    }

    @Test
    void getIncidentFile_shouldHandleIOException() {
        // Given
        doNothing().when(incidentFileManager).validateIncidentExists(123L);
        when(fileStorageService.getStoragePath()).thenReturn("test/storage");

        // Simuler une exception lors de l'accès au fichier
        when(fileStorageService.loadFileAsResource(anyString())).thenThrow(new RuntimeException("Test exception"));

        // When
        ResponseEntity<Resource> response = fileStorageController.getIncidentFile(
                123L, "PHOTO", "test.jpg");

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        verify(incidentFileManager).validateIncidentExists(123L);
    }

    @Test
    void uploadFile_shouldHandleInvalidFileType() {
        // Given
        doNothing().when(incidentFileManager).validateIncidentExists(anyLong());
        when(incidentFileManager.uploadFile(any(MultipartFile.class), anyLong(), any(FileType.class), anyBoolean()))
                .thenThrow(new IllegalArgumentException("Invalid file type"));

        // When
        ResponseEntity<Map<String, Object>> response = fileStorageController.uploadFile(
                testFile, 123L, "INVALID_TYPE", false);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("error", response.getBody().get("status"));
        verify(incidentFileManager).validateIncidentExists(123L);
    }

    // Helper methods to invoke private methods using reflection
    private boolean invokeIncidentExists(Long incidentId) {
        try {
            java.lang.reflect.Method method = FileStorageController.class.getDeclaredMethod("incidentExists", Long.class);
            method.setAccessible(true);
            return (boolean) method.invoke(fileStorageController, incidentId);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke incidentExists method", e);
        }
    }

    private boolean invokeCreateIncidentAutomatically() {
        try {
            java.lang.reflect.Method method = FileStorageController.class.getDeclaredMethod("createIncidentAutomatically");
            method.setAccessible(true);
            return (boolean) method.invoke(fileStorageController);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke createIncidentAutomatically method", e);
        }
    }

    private Map<String, Object> invokeCreateErrorResponse(String errorMessage) {
        try {
            java.lang.reflect.Method method = FileStorageController.class.getDeclaredMethod("createErrorResponse", String.class);
            method.setAccessible(true);
            return (Map<String, Object>) method.invoke(fileStorageController, errorMessage);
        } catch (Exception e) {
            throw new RuntimeException("Failed to invoke createErrorResponse method", e);
        }
    }
}
