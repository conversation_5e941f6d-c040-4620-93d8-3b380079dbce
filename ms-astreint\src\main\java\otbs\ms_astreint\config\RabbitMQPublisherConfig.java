package otbs.ms_astreint.config;

import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration pour la publication d'événements RabbitMQ depuis le microservice astreinte.
 */
@Configuration
public class RabbitMQPublisherConfig {

    // Exchange pour les notifications (doit être le même que les autres microservices)
    public static final String NOTIFICATIONS_EXCHANGE = "notifications.exchange";

    // Routing keys pour les événements d'astreinte
    public static final String ASTREINTE_CREATED_ROUTING_KEY = "astreinte.created";
    public static final String ASTREINTE_UPDATED_ROUTING_KEY = "astreinte.updated";
    public static final String ASTREINTE_DELETED_ROUTING_KEY = "astreinte.deleted";
    
    // Nom de la queue pour les notifications d'astreinte
    public static final String ASTREINTE_NOTIFICATIONS_QUEUE = "astreinte.notifications";

    /**
     * Crée l'exchange pour les notifications
     */
    @Bean
    public TopicExchange notificationsExchange() {
        return new TopicExchange(NOTIFICATIONS_EXCHANGE, true, false);
    }

    /**
     * Crée la queue pour les notifications d'astreinte
     */
    @Bean
    public Queue astreinteNotificationsQueue() {
        return new Queue(ASTREINTE_NOTIFICATIONS_QUEUE, true);
    }

    /**
     * Binding pour la queue astreinte.notifications avec le routing key astreinte.created
     */
    @Bean
    public Binding astreinteCreatedBinding(Queue astreinteNotificationsQueue, TopicExchange notificationsExchange) {
        return BindingBuilder.bind(astreinteNotificationsQueue)
                .to(notificationsExchange)
                .with(ASTREINTE_CREATED_ROUTING_KEY);
    }

    /**
     * Binding pour la queue astreinte.notifications avec le routing key astreinte.updated
     */
    @Bean
    public Binding astreinteUpdatedBinding(Queue astreinteNotificationsQueue, TopicExchange notificationsExchange) {
        return BindingBuilder.bind(astreinteNotificationsQueue)
                .to(notificationsExchange)
                .with(ASTREINTE_UPDATED_ROUTING_KEY);
    }

    /**
     * Binding pour la queue astreinte.notifications avec le routing key astreinte.deleted
     */
    @Bean
    public Binding astreinteDeletedBinding(Queue astreinteNotificationsQueue, TopicExchange notificationsExchange) {
        return BindingBuilder.bind(astreinteNotificationsQueue)
                .to(notificationsExchange)
                .with(ASTREINTE_DELETED_ROUTING_KEY);
    }

    /**
     * Template RabbitMQ avec sérialisation JSON
     */
    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        return rabbitTemplate;
    }
} 