<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion de fichiers d'incident</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 1px solid #ccc;
            padding-bottom: 10px;
        }
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            border-radius: 5px 5px 0 0;
        }
        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
            font-size: 16px;
        }
        .tab button:hover {
            background-color: #ddd;
        }
        .tab button.active {
            background-color: #4CAF50;
            color: white;
        }
        .tabcontent {
            display: none;
            padding: 20px;
            border: 1px solid #ccc;
            border-top: none;
            border-radius: 0 0 5px 5px;
            animation: fadeEffect 1s;
        }
        @keyframes fadeEffect {
            from {opacity: 0;}
            to {opacity: 1;}
        }
        form {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            display: block;
            margin-bottom: 10px;
        }
        input[type="number"] {
            padding: 5px;
            width: 100px;
        }
        select {
            padding: 5px;
            width: 200px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button.danger {
            background-color: #f44336;
        }
        button.danger:hover {
            background-color: #d32f2f;
        }
        button.info {
            background-color: #2196F3;
        }
        button.info:hover {
            background-color: #0b7dda;
        }
        .success {
            color: green;
            background-color: #f0fff0;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #c0e0c0;
        }
        .error {
            color: red;
            background-color: #fff0f0;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #e0c0c0;
        }
        pre {
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            overflow: auto;
            margin-top: 20px;
            border: 1px solid #ddd;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .actions {
            display: flex;
            gap: 5px;
        }
        .file-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .file-item {
            border: 1px solid #ddd;
            padding: 8px;
            border-radius: 4px;
            background-color: #f9f9f9;
            display: flex;
            align-items: center;
        }
        .file-item span {
            margin-right: 10px;
        }
        .file-item button {
            padding: 3px 8px;
            font-size: 12px;
        }
        .image-preview {
            width: 50px;
            height: 50px;
            object-fit: cover;
            margin-right: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .photo-gallery {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }
        .photo-item {
            width: 150px;
            height: 150px;
            border: 1px solid #ddd;
            border-radius: 4px;
            overflow: hidden;
            position: relative;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .photo-item:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .photo-item .photo-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px;
            font-size: 12px;
            text-align: center;
            opacity: 0;
            transition: opacity 0.2s;
        }
        .photo-item:hover .photo-info {
            opacity: 1;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0,0,0,0.9);
        }
        .modal-content {
            margin: auto;
            display: block;
            max-width: 90%;
            max-height: 90%;
            margin-top: 50px;
        }
        .close {
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            transition: 0.3s;
            cursor: pointer;
        }
        .close:hover {
            color: #bbb;
        }
        .upload-progress {
            margin-top: 15px;
            display: none;
        }
        progress {
            width: 100%;
            height: 20px;
        }
    </style>
</head>
<body>
    <h1>Gestion de fichiers d'incident</h1>

    <div class="tab">
        <button class="tablinks active" onclick="openTab(event, 'Upload')">Téléverser</button>
        <button class="tablinks" onclick="openTab(event, 'List')">Lister</button>
    </div>

    <div id="Upload" class="tabcontent" style="display: block;">
        <h2>Téléversement de fichier</h2>
        <form id="uploadForm">
            <div class="form-group">
                <label for="file">Sélectionner un ou plusieurs fichiers:</label>
                <input type="file" id="file" name="file" multiple>
                <div id="filePreview" class="file-preview"></div>
            </div>

            <div class="form-group">
                <label for="incidentId">ID de l'incident:</label>
                <input type="number" id="incidentId" name="incidentId" min="1" placeholder="Obligatoire">
            </div>

            <div class="form-group">
                <label for="fileType">Type de fichier:</label>
                <select id="fileType" name="fileType">
                    <option value="GENERIC">Générique</option>
                    <option value="CONSTAT">Constat</option>
                    <option value="PHOTO">Photo</option>
                </select>
            </div>

            <button type="button" onclick="uploadFiles()">Téléverser</button>

            <div id="uploadProgress" class="upload-progress">
                <p>Progression du téléversement: <span id="progressPercent">0%</span></p>
                <progress id="progressBar" value="0" max="100"></progress>
            </div>
        </form>

        <div id="success" class="success" style="display: none;"></div>
        <div id="error" class="error" style="display: none;"></div>

        <h3>Réponse du serveur</h3>
        <pre id="response"></pre>

        <h3>Journaux réseau</h3>
        <button type="button" onclick="clearNetworkLogs()">Effacer les journaux</button>
        <pre id="networkLogs"></pre>
    </div>

    <div id="List" class="tabcontent">
        <h2>Liste des fichiers par incident</h2>

        <div class="form-group">
            <label for="listIncidentId">ID de l'incident:</label>
            <input type="number" id="listIncidentId" min="1" placeholder="Entrez l'ID de l'incident">

            <label for="listFileType">Type de fichier (optionnel):</label>
            <select id="listFileType">
                <option value="">Tous les types</option>
                <option value="CONSTAT">Constat</option>
                <option value="PHOTO">Photo</option>
                <option value="GENERIC">Générique</option>
            </select>

            <button type="button" onclick="listFiles()">Rechercher</button>
        </div>

        <div id="listSuccess" class="success" style="display: none;"></div>
        <div id="listError" class="error" style="display: none;"></div>

        <!-- Galerie de photos -->
        <div id="photoGalleryContainer" style="display: none;">
            <h3>Galerie de photos</h3>
            <div id="photoGallery" class="photo-gallery"></div>
        </div>

        <div id="fileList">
            <table id="filesTable" style="display: none;">
                <thead>
                    <tr>
                        <th>Aperçu</th>
                        <th>Nom</th>
                        <th>Type</th>
                        <th>Taille</th>
                        <th>Date de création</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="filesTableBody">
                </tbody>
            </table>
            <div id="noFiles" style="display: none;">Aucun fichier trouvé pour cet incident.</div>
        </div>
    </div>

    <!-- Modal pour afficher les photos en grand format -->
    <div id="photoModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>

    <script>
        // Variables globales pour le suivi des fichiers
        let selectedFiles = [];

        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('error').style.display = 'none';
        }

        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('success').style.display = 'none';
        }

        function showListSuccess(message) {
            const successDiv = document.getElementById('listSuccess');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('listError').style.display = 'none';
        }

        function showListError(message) {
            const errorDiv = document.getElementById('listError');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('listSuccess').style.display = 'none';
        }

        function logResponse(response, error) {
            const responseElement = document.getElementById('response');

            if (error) {
                responseElement.textContent = 'Erreur: ' + error.message + '\n';
                if (response) {
                    responseElement.textContent += JSON.stringify(response, null, 2);
                }
            } else {
                responseElement.textContent = JSON.stringify(response, null, 2);
            }
        }

        function logNetworkEvent(event) {
            const logsElement = document.getElementById('networkLogs');
            logsElement.textContent += event + '\n';
        }

        function clearNetworkLogs() {
            document.getElementById('networkLogs').textContent = '';
        }

        function addNetworkLog(message) {
            const logsElement = document.getElementById('networkLogs');
            const timestamp = new Date().toLocaleTimeString();
            logsElement.textContent += `[${timestamp}] ${message}\n`;
            logsElement.scrollTop = logsElement.scrollHeight;
        }

        function formatBytes(bytes, decimals = 2) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return new Intl.DateTimeFormat('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            }).format(date);
        }

        // Gestionnaire d'événements pour l'input file
        document.getElementById('file').addEventListener('change', function(e) {
            const filePreview = document.getElementById('filePreview');
            filePreview.innerHTML = '';
            selectedFiles = Array.from(this.files);

            if (selectedFiles.length > 0) {
                selectedFiles.forEach((file, index) => {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';

                    const fileInfo = document.createElement('span');
                    fileInfo.textContent = file.name + ' (' + formatBytes(file.size) + ')';
                    fileItem.appendChild(fileInfo);

                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'danger';
                    removeBtn.textContent = 'Retirer';
                    removeBtn.onclick = () => {
                        selectedFiles.splice(index, 1);
                        fileItem.remove();
                        if (selectedFiles.length === 0) {
                            document.getElementById('file').value = ''; // Réinitialiser l'input
                        }
                    };
                    fileItem.appendChild(removeBtn);

                    filePreview.appendChild(fileItem);
                });
            }
        });

        function uploadFiles() {
            clearNetworkLogs();
            const fileInput = document.getElementById('file');
            const incidentId = document.getElementById('incidentId').value;
            const fileType = document.getElementById('fileType').value;
            const successDiv = document.getElementById('success');
            const errorDiv = document.getElementById('error');
            const progressDiv = document.getElementById('uploadProgress');
            const progressBar = document.getElementById('progressBar');
            const progressPercent = document.getElementById('progressPercent');
            const responseOutput = document.getElementById('response');

            successDiv.style.display = 'none';
            errorDiv.style.display = 'none';
            responseOutput.textContent = '';

            if (!fileInput.files.length) {
                errorDiv.textContent = 'Veuillez sélectionner au moins un fichier.';
                errorDiv.style.display = 'block';
                return;
            }

            if (!incidentId) {
                errorDiv.textContent = 'L\'ID de l\'incident est obligatoire.';
                errorDiv.style.display = 'block';
                return;
            }

            addNetworkLog('ID Incident: ' + incidentId);
            addNetworkLog('Type de fichier: ' + fileType);

            // Si plusieurs fichiers sont sélectionnés
            if (fileInput.files.length > 1) {
                // Progressions individuelles
                let completedUploads = 0;
                const totalFiles = fileInput.files.length;
                progressDiv.style.display = 'block';

                // Log pour le débogage
                addNetworkLog(`Téléversement de ${totalFiles} fichiers...`);

                // Pour chaque fichier, créer une FormData et l'envoyer
                Array.from(fileInput.files).forEach((file, index) => {
                    const formData = new FormData();
                    formData.append('file', file);
                    formData.append('incidentId', incidentId);
                    formData.append('fileType', fileType);
                    formData.append('overwrite', 'true');

                    addNetworkLog(`[${index+1}/${totalFiles}] Téléversement de ${file.name}...`);
                    addNetworkLog(`  - Taille: ${formatBytes(file.size)}`);
                    addNetworkLog(`  - Type: ${file.type}`);

                    fetch('/api/files/upload', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        addNetworkLog(`[${index+1}/${totalFiles}] Statut de la réponse: ${response.status}`);
                        return response.json();
                    })
                    .then(data => {
                        completedUploads++;
                        logResponse(data);

                        // Mise à jour de la progression
                        const progress = Math.round((completedUploads / totalFiles) * 100);
                        progressBar.value = progress;
                        progressPercent.textContent = `${progress}%`;

                        addNetworkLog(`[${index+1}/${totalFiles}] Téléversement terminé: ${file.name}`);

                        // Si tous les fichiers sont téléversés
                        if (completedUploads === totalFiles) {
                            successDiv.textContent = `${completedUploads} fichier(s) ont été téléversés avec succès.`;
                            successDiv.style.display = 'block';

                            // Rafraîchir la liste des fichiers
                            document.getElementById('listIncidentId').value = incidentId;
                            listFiles();

                            // Réinitialiser le formulaire
                            document.getElementById('filePreview').innerHTML = '';
                            document.getElementById('file').value = '';
                        }
                    })
                    .catch(error => {
                        completedUploads++;
                        addNetworkLog(`[${index+1}/${totalFiles}] Erreur: ${error.message}`);

                        // Mise à jour de la progression
                        const progress = Math.round((completedUploads / totalFiles) * 100);
                        progressBar.value = progress;
                        progressPercent.textContent = `${progress}%`;

                        // Si tous les fichiers ont été traités (même avec erreur)
                        if (completedUploads === totalFiles) {
                            errorDiv.textContent = `Une ou plusieurs erreurs se sont produites pendant le téléversement.`;
                            errorDiv.style.display = 'block';
                        }
                    });
                });
            } else {
                // Téléversement d'un seul fichier
                const file = fileInput.files[0];
                const formData = new FormData();
                formData.append('file', file);
                formData.append('incidentId', incidentId);
                formData.append('fileType', fileType);
                formData.append('overwrite', 'true');

                progressDiv.style.display = 'block';
                progressBar.value = 0;
                progressPercent.textContent = '0%';

                addNetworkLog(`Téléversement du fichier: ${file.name}`);
                addNetworkLog(`  - Taille: ${formatBytes(file.size)}`);
                addNetworkLog(`  - Type: ${file.type}`);

                fetch('/api/files/upload', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    addNetworkLog(`Statut de la réponse: ${response.status}`);
                    if (!response.ok) {
                        throw new Error('Erreur de téléversement: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    progressBar.value = 100;
                    progressPercent.textContent = '100%';

                        logResponse(data);

                    successDiv.textContent = 'Le fichier a été téléversé avec succès.';
                    successDiv.style.display = 'block';

                    // Rafraîchir la liste des fichiers
                    document.getElementById('listIncidentId').value = incidentId;
                listFiles();

            // Réinitialiser le formulaire
            document.getElementById('filePreview').innerHTML = '';
            document.getElementById('file').value = '';
                })
                .catch(error => {
                    errorDiv.textContent = 'Erreur lors du téléversement: ' + error.message;
                    errorDiv.style.display = 'block';
                    addNetworkLog(`Erreur: ${error.message}`);
                });
            }
        }

        async function listFiles() {
            document.getElementById('listSuccess').style.display = 'none';
            document.getElementById('listError').style.display = 'none';

            const incidentId = document.getElementById('listIncidentId').value;
            const fileType = document.getElementById('listFileType').value;

            if (!incidentId) {
                showListError('Veuillez spécifier l\'ID de l\'incident');
                return;
            }

            try {
                let url = `/api/files/list/incident/${incidentId}`;
                if (fileType) {
                    url += `?fileType=${fileType}`;
                }

                const response = await fetch(url);

                // Authentification désactivée pour les tests
                // if (response.status === 401) {
                //     showListError('Erreur 401: Non autorisé. Vous devez être authentifié.');
                //     return;
                // }

                if (!response.ok) {
                    const text = await response.text();
                    showListError(`Erreur ${response.status}: ${text}`);
                    return;
                }

                const files = await response.json();
                displayFiles(files);

            } catch (error) {
                showListError('Erreur: ' + error.message);
            }
        }

        function displayFiles(files) {
            const tableBody = document.getElementById('filesTableBody');
            const table = document.getElementById('filesTable');
            const noFiles = document.getElementById('noFiles');
            const photoGallery = document.getElementById('photoGallery');
            const photoGalleryContainer = document.getElementById('photoGalleryContainer');
            const incidentId = document.getElementById('listIncidentId').value;

            // Vider le tableau et la galerie
            tableBody.innerHTML = '';
            photoGallery.innerHTML = '';
            photoGalleryContainer.style.display = 'none';

            if (files.length === 0) {
                table.style.display = 'none';
                noFiles.style.display = 'block';
                return;
            }

            // Afficher le tableau, masquer le message
            table.style.display = 'table';
            noFiles.style.display = 'none';

            // Séparer les photos des autres fichiers
            const photos = files.filter(file => file.fileType === 'PHOTO');

            // Afficher la galerie de photos si des photos sont présentes
            if (photos.length > 0) {
                photoGalleryContainer.style.display = 'block';
                photoGallery.style.display = 'flex';

                // Créer la galerie de photos
                photos.forEach(photo => {
                    const photoItem = document.createElement('div');
                    photoItem.className = 'photo-item';

                    const img = document.createElement('img');
                    img.src = `/api/files/incident/${incidentId}/PHOTO/${photo.filename}`;
                    img.alt = photo.filename;
                    img.onerror = function() {
                        // Essayer une URL alternative en cas d'erreur
                        this.src = `/api/files/download/incident/${incidentId}/PHOTO/${photo.filename}`;
                    };

                    const photoInfo = document.createElement('div');
                    photoInfo.className = 'photo-info';
                    photoInfo.textContent = photo.filename;

                    photoItem.appendChild(img);
                    photoItem.appendChild(photoInfo);

                    // Ouvrir la photo en grand format au clic
                    photoItem.onclick = () => openModal(img.src);

                    photoGallery.appendChild(photoItem);
                });
            }

            // Remplir le tableau
            files.forEach(file => {
                const row = document.createElement('tr');

                // Aperçu (pour les images)
                const previewCell = document.createElement('td');
                if (file.fileType === 'PHOTO') {
                    const img = document.createElement('img');
                    img.className = 'image-preview';
                    img.src = `/api/files/incident/${incidentId}/PHOTO/${file.filename}`;
                    img.alt = file.filename;
                    img.onerror = function() {
                        // Essayer une URL alternative en cas d'erreur
                        this.src = `/api/files/download/incident/${incidentId}/PHOTO/${file.filename}`;
                    };
                    img.onclick = () => openModal(img.src);
                    previewCell.appendChild(img);
                } else if (file.fileType === 'CONSTAT' && file.filename.toLowerCase().endsWith('.pdf')) {
                    const icon = document.createElement('i');
                    icon.className = 'fas fa-file-pdf';
                    icon.style.fontSize = '24px';
                    icon.style.color = '#f40f02';
                    previewCell.appendChild(icon);
                } else {
                    const icon = document.createElement('i');
                    icon.className = 'fas fa-file';
                    icon.style.fontSize = '24px';
                    icon.style.color = '#666';
                    previewCell.appendChild(icon);
                }
                row.appendChild(previewCell);

                // Nom
                const nameCell = document.createElement('td');
                nameCell.textContent = file.filename;
                row.appendChild(nameCell);

                // Type
                const typeCell = document.createElement('td');
                typeCell.textContent = file.fileType;
                row.appendChild(typeCell);

                // Taille
                const sizeCell = document.createElement('td');
                sizeCell.textContent = formatBytes(file.size);
                row.appendChild(sizeCell);

                // Date
                const dateCell = document.createElement('td');
                dateCell.textContent = formatDate(file.creationTime);
                row.appendChild(dateCell);

                // Actions
                const actionsCell = document.createElement('td');
                actionsCell.className = 'actions';

                // Bouton visualiser (pour les images)
                if (file.fileType === 'PHOTO') {
                    const viewBtn = document.createElement('button');
                    viewBtn.className = 'info';
                    viewBtn.textContent = 'Visualiser';
                    viewBtn.onclick = (e) => {
                        e.preventDefault();
                        openModal(`/api/files/incident/${incidentId}/PHOTO/${file.filename}`);
                    };
                    actionsCell.appendChild(viewBtn);
                }

                // Bouton télécharger
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'info';
                downloadBtn.textContent = 'Télécharger';
                downloadBtn.onclick = () => downloadFile(file.filename, file.fileType);
                actionsCell.appendChild(downloadBtn);

                // Bouton supprimer
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'danger';
                deleteBtn.textContent = 'Supprimer';
                deleteBtn.onclick = () => deleteFile(file.filename, file.fileType);
                actionsCell.appendChild(deleteBtn);

                row.appendChild(actionsCell);

                tableBody.appendChild(row);
            });

            showListSuccess(`${files.length} fichier(s) trouvé(s) pour l'incident`);
        }

        // Fonction pour ouvrir le modal avec l'image en grand format
        function openModal(imgSrc) {
            const modal = document.getElementById('photoModal');
            const modalImg = document.getElementById('modalImage');
            modal.style.display = 'block';
            modalImg.src = imgSrc;

            // Ajouter un gestionnaire d'erreur pour l'image
            modalImg.onerror = function() {
                // Essayer une URL alternative en cas d'erreur
                if (imgSrc.includes('/incident/')) {
                    this.src = imgSrc.replace('/incident/', '/download/incident/');
                }
            };
        }

        // Fonction pour fermer le modal
        function closeModal() {
            document.getElementById('photoModal').style.display = 'none';
        }

        // Fermer le modal en cliquant en dehors de l'image
        window.onclick = function(event) {
            const modal = document.getElementById('photoModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

        function downloadFile(filename, fileType) {
            const incidentId = document.getElementById('listIncidentId').value;

            // Créer un lien temporaire et le cliquer pour télécharger
            const link = document.createElement('a');
            // Avec la nouvelle structure, le chemin est simplifié
            link.href = `/api/files/download/incident/${incidentId}/${fileType}/${filename}`;
            link.target = '_blank';
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        async function deleteFile(filename, fileType) {
            if (!confirm(`Êtes-vous sûr de vouloir supprimer le fichier ${filename} ?`)) {
                return;
            }

            const incidentId = document.getElementById('listIncidentId').value;

            try {
                // Avec la nouvelle structure, le chemin est simplifié
                const response = await fetch(`/api/files/delete/incident/${incidentId}/${fileType}/${filename}`, {
                    method: 'DELETE'
                });

                // Authentification désactivée pour les tests
                // if (response.status === 401) {
                //     showListError('Erreur 401: Non autorisé. Vous devez être authentifié avec le rôle ADMINISTRATEUR.');
                //     return;
                // }

                if (!response.ok) {
                    const text = await response.text();
                    showListError(`Erreur ${response.status}: ${text}`);
                    return;
                }

                showListSuccess(`Fichier ${filename} supprimé avec succès`);
                // Rafraîchir la liste
                listFiles();

            } catch (error) {
                showListError('Erreur: ' + error.message);
            }
        }
    </script>
</body>
</html>