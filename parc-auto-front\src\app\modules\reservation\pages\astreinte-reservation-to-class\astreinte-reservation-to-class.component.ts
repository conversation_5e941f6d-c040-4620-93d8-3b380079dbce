import { CommonModule, CurrencyPipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { NavGuideComponent } from '../../components/nav-guide/nav-guide.component';
import { ImmatriculeComponent } from '../../../vehicules/components/immatricule/immatricule.component';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { AstreinteReservation } from '../../models/AstreinteReservation';
import { AstreinteReservationService } from '../../services/astreinte-reservation.service';
import { FullAstreinteReservation } from '../../models/FullAstreinteReservation';
import { ConsommationCarburant, TypeConsommation } from '../../models/ConsommationCarburant';
import { catchError, Observable, of, forkJoin } from 'rxjs';
import { AuthService } from '../../../../shared/services/auth/auth.service';
import { CarteCarburantService } from '../../services/carte-carburant.service';
import { AffecterTelepeageAstreinteComponent } from '../../components/affecter-telepeage-astreinte/affecter-telepeage-astreinte.component';
// import { AddConsommationTelepeageComponent } from '../../components/add-consommation-telepeage/add-consommation-telepeage.component';

@Component({
  selector: 'app-astreinte-reservation-to-class',
  standalone: true,
  imports: [
    CommonModule,
    NavGuideComponent,
    ImmatriculeComponent,
    MatDialogModule,
    RouterModule,
    CurrencyPipe
  ],
  templateUrl: './astreinte-reservation-to-class.component.html',
  styleUrl: './astreinte-reservation-to-class.component.scss',
  providers: [CurrencyPipe]
})
export class AstreinteReservationToClassComponent implements OnInit {
  dataSource: FullAstreinteReservation[] = [];
  typeConsommation = TypeConsommation;
  reservationDetails: Map<number, AstreinteReservation> = new Map();
  carteCarburantDetails: Map<number, string> = new Map();

  // Pour gérer l'affichage des détails de consommation
  expandedDetailsMap: Map<number, boolean> = new Map();

  constructor(
    private dialog: MatDialog,
    private astreinteReservationService: AstreinteReservationService,
    private router: Router,
    private authService: AuthService,
    private carteCarburantService: CarteCarburantService
  ) {}

  hasRole(role: string): boolean {
    return this.authService.hasRole(role);
  }

  ngOnInit(): void {
    this.loadFutureAstreinteReservations();
  }

  private loadFutureAstreinteReservations(): void {
    // Pour les astreintes à finaliser, on charge toujours les réservations avec ressources non retournées
    // peu importe le rôle, car c'est pour la finalisation
    this.astreinteReservationService.getAstreinteReservationsWithUnreturnedResources().subscribe({
      next: (reservations) => {
        this.dataSource = reservations;
        this.loadReservationDetails();
      },
      error: (error) => {
        console.error('Error fetching astreinte reservations with unreturned resources:', error);
      }
    });
  }

  private loadReservationDetails(): void {
    this.dataSource.forEach(fullReservation => {
      this.astreinteReservationService.getAstreinteReservation(fullReservation.reservationId).subscribe({
        next: (reservation) => {
          this.reservationDetails.set(fullReservation.reservationId, reservation);

          // If there are consommationCarburant records, load card details for Carburant type
          if (reservation.consommationCarburant && reservation.consommationCarburant.length > 0) {
            reservation.consommationCarburant.forEach(consommation => {
              if (consommation.typeConsommation === TypeConsommation.Carburant && consommation.carteCarburantId) {
                this.loadCarteCarburantDetails(consommation.carteCarburantId);
              }
            });
          }
        },
        error: (error) => {
          console.error(`Error fetching astreinte reservation details for ID ${fullReservation.reservationId}:`, error);
        }
      });
    });
  }

  private loadCarteCarburantDetails(carteId: number): void {
    this.carteCarburantService.getById(carteId).subscribe({
      next: (carte) => {
        // Store the card number
        this.carteCarburantDetails.set(carteId, carte.numeroCarte || `${carteId}`);
      },
      error: (error) => {
        console.error(`Error fetching carte details for ID ${carteId}:`, error);
        // Default fallback - just show the ID
        this.carteCarburantDetails.set(carteId, `${carteId}`);
      }
    });
  }

  // Nouvelles méthodes pour l'affichage amélioré des consommations
  getConsommationItems(reservationId: number): Array<{ type: string; value: string; id: string | null }> {
    const reservation = this.reservationDetails.get(reservationId);
    if (!reservation || !reservation.consommationCarburant || reservation.consommationCarburant.length === 0) {
      return [];
    }

    const consommationItems: Array<{ type: string; value: string; id: string | null }> = [];

    reservation.consommationCarburant.forEach(consommation => {
      switch (consommation.typeConsommation) {
        case TypeConsommation.Carburant:
          if (consommation.carteCarburantId) {
            const cardNumber = this.carteCarburantDetails.get(consommation.carteCarburantId) ||
              `${consommation.carteCarburantId}`;
            consommationItems.push({ type: 'Carte', value: cardNumber, id: null });
          }
          break;
        case TypeConsommation.Cash:
          consommationItems.push({
            type: 'Cash',
            value: `#${consommation.id}`,
            id: consommation.id?.toString() || null
          });
          break;
        case TypeConsommation.Frais:
          consommationItems.push({
            type: 'Frais',
            value: `#${consommation.id}`,
            id: consommation.id?.toString() || null
          });
          break;
      }
    });

    return consommationItems;
  }

  hasConsommations(reservationId: number): boolean {
    return this.getConsommationItems(reservationId).length > 0;
  }

  getTypeClass(type: string): string {
    switch (type) {
      case 'Carte': return 'badge-carte';
      case 'Frais': return 'badge-frais';
      case 'Cash': return 'badge-cash';
      default: return 'badge-default';
    }
  }

  formatCurrency(amount: number | undefined | null): string {
    if (amount === undefined || amount === null) {
      return '0,00 TND';
    }
    const formatted = (6 * amount).toFixed(2).replace('.', ',');
    return `${formatted} TND`;
  }

  addConsommationTelepeage(reservation: FullAstreinteReservation): void {
    const dialogRef = this.dialog.open(AffecterTelepeageAstreinteComponent, {
      width: '600px',
      data: {
        reservationId: reservation.reservationId,
        dateDebut: new Date(reservation.dateDebut).toISOString(),
        dateFin: new Date(reservation.dateFin).toISOString(),
        mode: "add"
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Badge télépéage affecté avec succès');
        this.loadFutureAstreinteReservations();
      }
    });
  }

  goToConsommationCarburant(reservation: FullAstreinteReservation): void {
    if (reservation && reservation.reservationId) {
      const path = ['/reservations/ConsommationCarburant', reservation.reservationId];
      console.log('Navigating to:', path);

      this.router.navigate(path).then(
        success => {
          console.log('Navigation successful:', success);
        },
        error => {
          console.error('Navigation error:', error);
        }
      );
    } else {
      console.error('Invalid reservation or ID');
    }
  }

}
