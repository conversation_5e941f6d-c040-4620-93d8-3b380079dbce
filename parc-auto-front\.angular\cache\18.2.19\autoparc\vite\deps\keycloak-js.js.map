{"version": 3, "sources": ["../../../../../../node_modules/keycloak-js/lib/keycloak.js"], "sourcesContent": ["/*\n * Copyright 2016 Red Hat, Inc. and/or its affiliates\n * and other contributors as indicated by the <AUTHOR>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nfunction Keycloak(config) {\n  if (!(this instanceof Keycloak)) {\n    throw new Error(\"The 'Keycloak' constructor must be invoked with 'new'.\");\n  }\n  if (typeof config !== 'string' && !isObject(config)) {\n    throw new Error(\"The 'Keycloak' constructor must be provided with a configuration object, or a URL to a JSON configuration file.\");\n  }\n  if (isObject(config)) {\n    const requiredProperties = 'oidcProvider' in config ? ['clientId'] : ['url', 'realm', 'clientId'];\n    for (const property of requiredProperties) {\n      if (!config[property]) {\n        throw new Error(`The configuration object is missing the required '${property}' property.`);\n      }\n    }\n  }\n  var kc = this;\n  var adapter;\n  var refreshQueue = [];\n  var callbackStorage;\n  var loginIframe = {\n    enable: true,\n    callbackList: [],\n    interval: 5\n  };\n  kc.didInitialize = false;\n  var useNonce = true;\n  var logInfo = createLogger(console.info);\n  var logWarn = createLogger(console.warn);\n  if (!globalThis.isSecureContext) {\n    logWarn(\"[KEYCLOAK] Keycloak JS must be used in a 'secure context' to function properly as it relies on browser APIs that are otherwise not available.\\n\" + \"Continuing to run your application insecurely will lead to unexpected behavior and breakage.\\n\\n\" + \"For more information see: https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts\");\n  }\n  kc.init = function (initOptions = {}) {\n    if (kc.didInitialize) {\n      throw new Error(\"A 'Keycloak' instance can only be initialized once.\");\n    }\n    kc.didInitialize = true;\n    kc.authenticated = false;\n    callbackStorage = createCallbackStorage();\n    var adapters = ['default', 'cordova', 'cordova-native'];\n    if (adapters.indexOf(initOptions.adapter) > -1) {\n      adapter = loadAdapter(initOptions.adapter);\n    } else if (typeof initOptions.adapter === \"object\") {\n      adapter = initOptions.adapter;\n    } else {\n      if (window.Cordova || window.cordova) {\n        adapter = loadAdapter('cordova');\n      } else {\n        adapter = loadAdapter();\n      }\n    }\n    if (typeof initOptions.useNonce !== 'undefined') {\n      useNonce = initOptions.useNonce;\n    }\n    if (typeof initOptions.checkLoginIframe !== 'undefined') {\n      loginIframe.enable = initOptions.checkLoginIframe;\n    }\n    if (initOptions.checkLoginIframeInterval) {\n      loginIframe.interval = initOptions.checkLoginIframeInterval;\n    }\n    if (initOptions.onLoad === 'login-required') {\n      kc.loginRequired = true;\n    }\n    if (initOptions.responseMode) {\n      if (initOptions.responseMode === 'query' || initOptions.responseMode === 'fragment') {\n        kc.responseMode = initOptions.responseMode;\n      } else {\n        throw 'Invalid value for responseMode';\n      }\n    }\n    if (initOptions.flow) {\n      switch (initOptions.flow) {\n        case 'standard':\n          kc.responseType = 'code';\n          break;\n        case 'implicit':\n          kc.responseType = 'id_token token';\n          break;\n        case 'hybrid':\n          kc.responseType = 'code id_token token';\n          break;\n        default:\n          throw 'Invalid value for flow';\n      }\n      kc.flow = initOptions.flow;\n    }\n    if (initOptions.timeSkew != null) {\n      kc.timeSkew = initOptions.timeSkew;\n    }\n    if (initOptions.redirectUri) {\n      kc.redirectUri = initOptions.redirectUri;\n    }\n    if (initOptions.silentCheckSsoRedirectUri) {\n      kc.silentCheckSsoRedirectUri = initOptions.silentCheckSsoRedirectUri;\n    }\n    if (typeof initOptions.silentCheckSsoFallback === 'boolean') {\n      kc.silentCheckSsoFallback = initOptions.silentCheckSsoFallback;\n    } else {\n      kc.silentCheckSsoFallback = true;\n    }\n    if (typeof initOptions.pkceMethod !== \"undefined\") {\n      if (initOptions.pkceMethod !== \"S256\" && initOptions.pkceMethod !== false) {\n        throw new TypeError(`Invalid value for pkceMethod', expected 'S256' or false but got ${initOptions.pkceMethod}.`);\n      }\n      kc.pkceMethod = initOptions.pkceMethod;\n    } else {\n      kc.pkceMethod = \"S256\";\n    }\n    if (typeof initOptions.enableLogging === 'boolean') {\n      kc.enableLogging = initOptions.enableLogging;\n    } else {\n      kc.enableLogging = false;\n    }\n    if (initOptions.logoutMethod === 'POST') {\n      kc.logoutMethod = 'POST';\n    } else {\n      kc.logoutMethod = 'GET';\n    }\n    if (typeof initOptions.scope === 'string') {\n      kc.scope = initOptions.scope;\n    }\n    if (typeof initOptions.acrValues === 'string') {\n      kc.acrValues = initOptions.acrValues;\n    }\n    if (typeof initOptions.messageReceiveTimeout === 'number' && initOptions.messageReceiveTimeout > 0) {\n      kc.messageReceiveTimeout = initOptions.messageReceiveTimeout;\n    } else {\n      kc.messageReceiveTimeout = 10000;\n    }\n    if (!kc.responseMode) {\n      kc.responseMode = 'fragment';\n    }\n    if (!kc.responseType) {\n      kc.responseType = 'code';\n      kc.flow = 'standard';\n    }\n    var promise = createPromise();\n    var initPromise = createPromise();\n    initPromise.promise.then(function () {\n      kc.onReady && kc.onReady(kc.authenticated);\n      promise.setSuccess(kc.authenticated);\n    }).catch(function (error) {\n      promise.setError(error);\n    });\n    var configPromise = loadConfig();\n    function onLoad() {\n      var doLogin = function (prompt) {\n        if (!prompt) {\n          options.prompt = 'none';\n        }\n        if (initOptions.locale) {\n          options.locale = initOptions.locale;\n        }\n        kc.login(options).then(function () {\n          initPromise.setSuccess();\n        }).catch(function (error) {\n          initPromise.setError(error);\n        });\n      };\n      var checkSsoSilently = async function () {\n        var ifrm = document.createElement(\"iframe\");\n        var src = await kc.createLoginUrl({\n          prompt: 'none',\n          redirectUri: kc.silentCheckSsoRedirectUri\n        });\n        ifrm.setAttribute(\"src\", src);\n        ifrm.setAttribute(\"sandbox\", \"allow-storage-access-by-user-activation allow-scripts allow-same-origin\");\n        ifrm.setAttribute(\"title\", \"keycloak-silent-check-sso\");\n        ifrm.style.display = \"none\";\n        document.body.appendChild(ifrm);\n        var messageCallback = function (event) {\n          if (event.origin !== window.location.origin || ifrm.contentWindow !== event.source) {\n            return;\n          }\n          var oauth = parseCallback(event.data);\n          processCallback(oauth, initPromise);\n          document.body.removeChild(ifrm);\n          window.removeEventListener(\"message\", messageCallback);\n        };\n        window.addEventListener(\"message\", messageCallback);\n      };\n      var options = {};\n      switch (initOptions.onLoad) {\n        case 'check-sso':\n          if (loginIframe.enable) {\n            setupCheckLoginIframe().then(function () {\n              checkLoginIframe().then(function (unchanged) {\n                if (!unchanged) {\n                  kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n                } else {\n                  initPromise.setSuccess();\n                }\n              }).catch(function (error) {\n                initPromise.setError(error);\n              });\n            });\n          } else {\n            kc.silentCheckSsoRedirectUri ? checkSsoSilently() : doLogin(false);\n          }\n          break;\n        case 'login-required':\n          doLogin(true);\n          break;\n        default:\n          throw 'Invalid value for onLoad';\n      }\n    }\n    function processInit() {\n      var callback = parseCallback(window.location.href);\n      if (callback) {\n        window.history.replaceState(window.history.state, null, callback.newUrl);\n      }\n      if (callback && callback.valid) {\n        return setupCheckLoginIframe().then(function () {\n          processCallback(callback, initPromise);\n        }).catch(function (error) {\n          initPromise.setError(error);\n        });\n      }\n      if (initOptions.token && initOptions.refreshToken) {\n        setToken(initOptions.token, initOptions.refreshToken, initOptions.idToken);\n        if (loginIframe.enable) {\n          setupCheckLoginIframe().then(function () {\n            checkLoginIframe().then(function (unchanged) {\n              if (unchanged) {\n                kc.onAuthSuccess && kc.onAuthSuccess();\n                initPromise.setSuccess();\n                scheduleCheckIframe();\n              } else {\n                initPromise.setSuccess();\n              }\n            }).catch(function (error) {\n              initPromise.setError(error);\n            });\n          });\n        } else {\n          kc.updateToken(-1).then(function () {\n            kc.onAuthSuccess && kc.onAuthSuccess();\n            initPromise.setSuccess();\n          }).catch(function (error) {\n            kc.onAuthError && kc.onAuthError();\n            if (initOptions.onLoad) {\n              onLoad();\n            } else {\n              initPromise.setError(error);\n            }\n          });\n        }\n      } else if (initOptions.onLoad) {\n        onLoad();\n      } else {\n        initPromise.setSuccess();\n      }\n    }\n    configPromise.then(function () {\n      check3pCookiesSupported().then(processInit).catch(function (error) {\n        promise.setError(error);\n      });\n    });\n    configPromise.catch(function (error) {\n      promise.setError(error);\n    });\n    return promise.promise;\n  };\n  kc.login = function (options) {\n    return adapter.login(options);\n  };\n  function generateRandomData(len) {\n    if (typeof crypto === \"undefined\" || typeof crypto.getRandomValues === \"undefined\") {\n      throw new Error(\"Web Crypto API is not available.\");\n    }\n    return crypto.getRandomValues(new Uint8Array(len));\n  }\n  function generateCodeVerifier(len) {\n    return generateRandomString(len, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');\n  }\n  function generateRandomString(len, alphabet) {\n    var randomData = generateRandomData(len);\n    var chars = new Array(len);\n    for (var i = 0; i < len; i++) {\n      chars[i] = alphabet.charCodeAt(randomData[i] % alphabet.length);\n    }\n    return String.fromCharCode.apply(null, chars);\n  }\n  async function generatePkceChallenge(pkceMethod, codeVerifier) {\n    if (pkceMethod !== \"S256\") {\n      throw new TypeError(`Invalid value for 'pkceMethod', expected 'S256' but got '${pkceMethod}'.`);\n    }\n\n    // hash codeVerifier, then encode as url-safe base64 without padding\n    const hashBytes = new Uint8Array(await sha256Digest(codeVerifier));\n    const encodedHash = bytesToBase64(hashBytes).replace(/\\+/g, '-').replace(/\\//g, '_').replace(/\\=/g, '');\n    return encodedHash;\n  }\n  function buildClaimsParameter(requestedAcr) {\n    var claims = {\n      id_token: {\n        acr: requestedAcr\n      }\n    };\n    return JSON.stringify(claims);\n  }\n  kc.createLoginUrl = async function (options) {\n    var state = createUUID();\n    var nonce = createUUID();\n    var redirectUri = adapter.redirectUri(options);\n    var callbackState = {\n      state: state,\n      nonce: nonce,\n      redirectUri: encodeURIComponent(redirectUri),\n      loginOptions: options\n    };\n    if (options && options.prompt) {\n      callbackState.prompt = options.prompt;\n    }\n    var baseUrl;\n    if (options && options.action == 'register') {\n      baseUrl = kc.endpoints.register();\n    } else {\n      baseUrl = kc.endpoints.authorize();\n    }\n    var scope = options && options.scope || kc.scope;\n    if (!scope) {\n      // if scope is not set, default to \"openid\"\n      scope = \"openid\";\n    } else if (scope.indexOf(\"openid\") === -1) {\n      // if openid scope is missing, prefix the given scopes with it\n      scope = \"openid \" + scope;\n    }\n    var url = baseUrl + '?client_id=' + encodeURIComponent(kc.clientId) + '&redirect_uri=' + encodeURIComponent(redirectUri) + '&state=' + encodeURIComponent(state) + '&response_mode=' + encodeURIComponent(kc.responseMode) + '&response_type=' + encodeURIComponent(kc.responseType) + '&scope=' + encodeURIComponent(scope);\n    if (useNonce) {\n      url = url + '&nonce=' + encodeURIComponent(nonce);\n    }\n    if (options && options.prompt) {\n      url += '&prompt=' + encodeURIComponent(options.prompt);\n    }\n    if (options && typeof options.maxAge === 'number') {\n      url += '&max_age=' + encodeURIComponent(options.maxAge);\n    }\n    if (options && options.loginHint) {\n      url += '&login_hint=' + encodeURIComponent(options.loginHint);\n    }\n    if (options && options.idpHint) {\n      url += '&kc_idp_hint=' + encodeURIComponent(options.idpHint);\n    }\n    if (options && options.action && options.action != 'register') {\n      url += '&kc_action=' + encodeURIComponent(options.action);\n    }\n    if (options && options.locale) {\n      url += '&ui_locales=' + encodeURIComponent(options.locale);\n    }\n    if (options && options.acr) {\n      var claimsParameter = buildClaimsParameter(options.acr);\n      url += '&claims=' + encodeURIComponent(claimsParameter);\n    }\n    if (options && options.acrValues || kc.acrValues) {\n      url += '&acr_values=' + encodeURIComponent(options.acrValues || kc.acrValues);\n    }\n    if (kc.pkceMethod) {\n      try {\n        const codeVerifier = generateCodeVerifier(96);\n        const pkceChallenge = await generatePkceChallenge(kc.pkceMethod, codeVerifier);\n        callbackState.pkceCodeVerifier = codeVerifier;\n        url += '&code_challenge=' + pkceChallenge;\n        url += '&code_challenge_method=' + kc.pkceMethod;\n      } catch (error) {\n        throw new Error(\"Failed to generate PKCE challenge.\", {\n          cause: error\n        });\n      }\n    }\n    callbackStorage.add(callbackState);\n    return url;\n  };\n  kc.logout = function (options) {\n    return adapter.logout(options);\n  };\n  kc.createLogoutUrl = function (options) {\n    const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n    if (logoutMethod === 'POST') {\n      return kc.endpoints.logout();\n    }\n    var url = kc.endpoints.logout() + '?client_id=' + encodeURIComponent(kc.clientId) + '&post_logout_redirect_uri=' + encodeURIComponent(adapter.redirectUri(options, false));\n    if (kc.idToken) {\n      url += '&id_token_hint=' + encodeURIComponent(kc.idToken);\n    }\n    return url;\n  };\n  kc.register = function (options) {\n    return adapter.register(options);\n  };\n  kc.createRegisterUrl = async function (options) {\n    if (!options) {\n      options = {};\n    }\n    options.action = 'register';\n    return await kc.createLoginUrl(options);\n  };\n  kc.createAccountUrl = function (options) {\n    var realm = getRealmUrl();\n    var url = undefined;\n    if (typeof realm !== 'undefined') {\n      url = realm + '/account' + '?referrer=' + encodeURIComponent(kc.clientId) + '&referrer_uri=' + encodeURIComponent(adapter.redirectUri(options));\n    }\n    return url;\n  };\n  kc.accountManagement = function () {\n    return adapter.accountManagement();\n  };\n  kc.hasRealmRole = function (role) {\n    var access = kc.realmAccess;\n    return !!access && access.roles.indexOf(role) >= 0;\n  };\n  kc.hasResourceRole = function (role, resource) {\n    if (!kc.resourceAccess) {\n      return false;\n    }\n    var access = kc.resourceAccess[resource || kc.clientId];\n    return !!access && access.roles.indexOf(role) >= 0;\n  };\n  kc.loadUserProfile = function () {\n    var url = getRealmUrl() + '/account';\n    var req = new XMLHttpRequest();\n    req.open('GET', url, true);\n    req.setRequestHeader('Accept', 'application/json');\n    req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n    var promise = createPromise();\n    req.onreadystatechange = function () {\n      if (req.readyState == 4) {\n        if (req.status == 200) {\n          kc.profile = JSON.parse(req.responseText);\n          promise.setSuccess(kc.profile);\n        } else {\n          promise.setError();\n        }\n      }\n    };\n    req.send();\n    return promise.promise;\n  };\n  kc.loadUserInfo = function () {\n    var url = kc.endpoints.userinfo();\n    var req = new XMLHttpRequest();\n    req.open('GET', url, true);\n    req.setRequestHeader('Accept', 'application/json');\n    req.setRequestHeader('Authorization', 'bearer ' + kc.token);\n    var promise = createPromise();\n    req.onreadystatechange = function () {\n      if (req.readyState == 4) {\n        if (req.status == 200) {\n          kc.userInfo = JSON.parse(req.responseText);\n          promise.setSuccess(kc.userInfo);\n        } else {\n          promise.setError();\n        }\n      }\n    };\n    req.send();\n    return promise.promise;\n  };\n  kc.isTokenExpired = function (minValidity) {\n    if (!kc.tokenParsed || !kc.refreshToken && kc.flow != 'implicit') {\n      throw 'Not authenticated';\n    }\n    if (kc.timeSkew == null) {\n      logInfo('[KEYCLOAK] Unable to determine if token is expired as timeskew is not set');\n      return true;\n    }\n    var expiresIn = kc.tokenParsed['exp'] - Math.ceil(new Date().getTime() / 1000) + kc.timeSkew;\n    if (minValidity) {\n      if (isNaN(minValidity)) {\n        throw 'Invalid minValidity';\n      }\n      expiresIn -= minValidity;\n    }\n    return expiresIn < 0;\n  };\n  kc.updateToken = function (minValidity) {\n    var promise = createPromise();\n    if (!kc.refreshToken) {\n      promise.setError();\n      return promise.promise;\n    }\n    minValidity = minValidity || 5;\n    var exec = function () {\n      var refreshToken = false;\n      if (minValidity == -1) {\n        refreshToken = true;\n        logInfo('[KEYCLOAK] Refreshing token: forced refresh');\n      } else if (!kc.tokenParsed || kc.isTokenExpired(minValidity)) {\n        refreshToken = true;\n        logInfo('[KEYCLOAK] Refreshing token: token expired');\n      }\n      if (!refreshToken) {\n        promise.setSuccess(false);\n      } else {\n        var params = 'grant_type=refresh_token&' + 'refresh_token=' + kc.refreshToken;\n        var url = kc.endpoints.token();\n        refreshQueue.push(promise);\n        if (refreshQueue.length == 1) {\n          var req = new XMLHttpRequest();\n          req.open('POST', url, true);\n          req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n          req.withCredentials = true;\n          params += '&client_id=' + encodeURIComponent(kc.clientId);\n          var timeLocal = new Date().getTime();\n          req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n              if (req.status == 200) {\n                logInfo('[KEYCLOAK] Token refreshed');\n                timeLocal = (timeLocal + new Date().getTime()) / 2;\n                var tokenResponse = JSON.parse(req.responseText);\n                setToken(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], timeLocal);\n                kc.onAuthRefreshSuccess && kc.onAuthRefreshSuccess();\n                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                  p.setSuccess(true);\n                }\n              } else {\n                logWarn('[KEYCLOAK] Failed to refresh token');\n                if (req.status == 400) {\n                  kc.clearToken();\n                }\n                kc.onAuthRefreshError && kc.onAuthRefreshError();\n                for (var p = refreshQueue.pop(); p != null; p = refreshQueue.pop()) {\n                  p.setError(\"Failed to refresh token: An unexpected HTTP error occurred while attempting to refresh the token.\");\n                }\n              }\n            }\n          };\n          req.send(params);\n        }\n      }\n    };\n    if (loginIframe.enable) {\n      var iframePromise = checkLoginIframe();\n      iframePromise.then(function () {\n        exec();\n      }).catch(function (error) {\n        promise.setError(error);\n      });\n    } else {\n      exec();\n    }\n    return promise.promise;\n  };\n  kc.clearToken = function () {\n    if (kc.token) {\n      setToken(null, null, null);\n      kc.onAuthLogout && kc.onAuthLogout();\n      if (kc.loginRequired) {\n        kc.login();\n      }\n    }\n  };\n  function getRealmUrl() {\n    if (typeof kc.authServerUrl !== 'undefined') {\n      if (kc.authServerUrl.charAt(kc.authServerUrl.length - 1) == '/') {\n        return kc.authServerUrl + 'realms/' + encodeURIComponent(kc.realm);\n      } else {\n        return kc.authServerUrl + '/realms/' + encodeURIComponent(kc.realm);\n      }\n    } else {\n      return undefined;\n    }\n  }\n  function getOrigin() {\n    if (!window.location.origin) {\n      return window.location.protocol + \"//\" + window.location.hostname + (window.location.port ? ':' + window.location.port : '');\n    } else {\n      return window.location.origin;\n    }\n  }\n  function processCallback(oauth, promise) {\n    var code = oauth.code;\n    var error = oauth.error;\n    var prompt = oauth.prompt;\n    var timeLocal = new Date().getTime();\n    if (oauth['kc_action_status']) {\n      kc.onActionUpdate && kc.onActionUpdate(oauth['kc_action_status'], oauth['kc_action']);\n    }\n    if (error) {\n      if (prompt != 'none') {\n        if (oauth.error_description && oauth.error_description === \"authentication_expired\") {\n          kc.login(oauth.loginOptions);\n        } else {\n          var errorData = {\n            error: error,\n            error_description: oauth.error_description\n          };\n          kc.onAuthError && kc.onAuthError(errorData);\n          promise && promise.setError(errorData);\n        }\n      } else {\n        promise && promise.setSuccess();\n      }\n      return;\n    } else if (kc.flow != 'standard' && (oauth.access_token || oauth.id_token)) {\n      authSuccess(oauth.access_token, null, oauth.id_token, true);\n    }\n    if (kc.flow != 'implicit' && code) {\n      var params = 'code=' + code + '&grant_type=authorization_code';\n      var url = kc.endpoints.token();\n      var req = new XMLHttpRequest();\n      req.open('POST', url, true);\n      req.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');\n      params += '&client_id=' + encodeURIComponent(kc.clientId);\n      params += '&redirect_uri=' + oauth.redirectUri;\n      if (oauth.pkceCodeVerifier) {\n        params += '&code_verifier=' + oauth.pkceCodeVerifier;\n      }\n      req.withCredentials = true;\n      req.onreadystatechange = function () {\n        if (req.readyState == 4) {\n          if (req.status == 200) {\n            var tokenResponse = JSON.parse(req.responseText);\n            authSuccess(tokenResponse['access_token'], tokenResponse['refresh_token'], tokenResponse['id_token'], kc.flow === 'standard');\n            scheduleCheckIframe();\n          } else {\n            kc.onAuthError && kc.onAuthError();\n            promise && promise.setError();\n          }\n        }\n      };\n      req.send(params);\n    }\n    function authSuccess(accessToken, refreshToken, idToken, fulfillPromise) {\n      timeLocal = (timeLocal + new Date().getTime()) / 2;\n      setToken(accessToken, refreshToken, idToken, timeLocal);\n      if (useNonce && kc.idTokenParsed && kc.idTokenParsed.nonce != oauth.storedNonce) {\n        logInfo('[KEYCLOAK] Invalid nonce, clearing token');\n        kc.clearToken();\n        promise && promise.setError();\n      } else {\n        if (fulfillPromise) {\n          kc.onAuthSuccess && kc.onAuthSuccess();\n          promise && promise.setSuccess();\n        }\n      }\n    }\n  }\n  function loadConfig() {\n    var promise = createPromise();\n    var configUrl;\n    if (typeof config === 'string') {\n      configUrl = config;\n    }\n    function setupOidcEndoints(oidcConfiguration) {\n      if (!oidcConfiguration) {\n        kc.endpoints = {\n          authorize: function () {\n            return getRealmUrl() + '/protocol/openid-connect/auth';\n          },\n          token: function () {\n            return getRealmUrl() + '/protocol/openid-connect/token';\n          },\n          logout: function () {\n            return getRealmUrl() + '/protocol/openid-connect/logout';\n          },\n          checkSessionIframe: function () {\n            return getRealmUrl() + '/protocol/openid-connect/login-status-iframe.html';\n          },\n          thirdPartyCookiesIframe: function () {\n            return getRealmUrl() + '/protocol/openid-connect/3p-cookies/step1.html';\n          },\n          register: function () {\n            return getRealmUrl() + '/protocol/openid-connect/registrations';\n          },\n          userinfo: function () {\n            return getRealmUrl() + '/protocol/openid-connect/userinfo';\n          }\n        };\n      } else {\n        kc.endpoints = {\n          authorize: function () {\n            return oidcConfiguration.authorization_endpoint;\n          },\n          token: function () {\n            return oidcConfiguration.token_endpoint;\n          },\n          logout: function () {\n            if (!oidcConfiguration.end_session_endpoint) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.end_session_endpoint;\n          },\n          checkSessionIframe: function () {\n            if (!oidcConfiguration.check_session_iframe) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.check_session_iframe;\n          },\n          register: function () {\n            throw 'Redirection to \"Register user\" page not supported in standard OIDC mode';\n          },\n          userinfo: function () {\n            if (!oidcConfiguration.userinfo_endpoint) {\n              throw \"Not supported by the OIDC server\";\n            }\n            return oidcConfiguration.userinfo_endpoint;\n          }\n        };\n      }\n    }\n    if (configUrl) {\n      var req = new XMLHttpRequest();\n      req.open('GET', configUrl, true);\n      req.setRequestHeader('Accept', 'application/json');\n      req.onreadystatechange = function () {\n        if (req.readyState == 4) {\n          if (req.status == 200 || fileLoaded(req)) {\n            var config = JSON.parse(req.responseText);\n            kc.authServerUrl = config['auth-server-url'];\n            kc.realm = config['realm'];\n            kc.clientId = config['resource'];\n            setupOidcEndoints(null);\n            promise.setSuccess();\n          } else {\n            promise.setError();\n          }\n        }\n      };\n      req.send();\n    } else {\n      kc.clientId = config.clientId;\n      var oidcProvider = config['oidcProvider'];\n      if (!oidcProvider) {\n        kc.authServerUrl = config.url;\n        kc.realm = config.realm;\n        setupOidcEndoints(null);\n        promise.setSuccess();\n      } else {\n        if (typeof oidcProvider === 'string') {\n          var oidcProviderConfigUrl;\n          if (oidcProvider.charAt(oidcProvider.length - 1) == '/') {\n            oidcProviderConfigUrl = oidcProvider + '.well-known/openid-configuration';\n          } else {\n            oidcProviderConfigUrl = oidcProvider + '/.well-known/openid-configuration';\n          }\n          var req = new XMLHttpRequest();\n          req.open('GET', oidcProviderConfigUrl, true);\n          req.setRequestHeader('Accept', 'application/json');\n          req.onreadystatechange = function () {\n            if (req.readyState == 4) {\n              if (req.status == 200 || fileLoaded(req)) {\n                var oidcProviderConfig = JSON.parse(req.responseText);\n                setupOidcEndoints(oidcProviderConfig);\n                promise.setSuccess();\n              } else {\n                promise.setError();\n              }\n            }\n          };\n          req.send();\n        } else {\n          setupOidcEndoints(oidcProvider);\n          promise.setSuccess();\n        }\n      }\n    }\n    return promise.promise;\n  }\n  function fileLoaded(xhr) {\n    return xhr.status == 0 && xhr.responseText && xhr.responseURL.startsWith('file:');\n  }\n  function setToken(token, refreshToken, idToken, timeLocal) {\n    if (kc.tokenTimeoutHandle) {\n      clearTimeout(kc.tokenTimeoutHandle);\n      kc.tokenTimeoutHandle = null;\n    }\n    if (refreshToken) {\n      kc.refreshToken = refreshToken;\n      kc.refreshTokenParsed = decodeToken(refreshToken);\n    } else {\n      delete kc.refreshToken;\n      delete kc.refreshTokenParsed;\n    }\n    if (idToken) {\n      kc.idToken = idToken;\n      kc.idTokenParsed = decodeToken(idToken);\n    } else {\n      delete kc.idToken;\n      delete kc.idTokenParsed;\n    }\n    if (token) {\n      kc.token = token;\n      kc.tokenParsed = decodeToken(token);\n      kc.sessionId = kc.tokenParsed.sid;\n      kc.authenticated = true;\n      kc.subject = kc.tokenParsed.sub;\n      kc.realmAccess = kc.tokenParsed.realm_access;\n      kc.resourceAccess = kc.tokenParsed.resource_access;\n      if (timeLocal) {\n        kc.timeSkew = Math.floor(timeLocal / 1000) - kc.tokenParsed.iat;\n      }\n      if (kc.timeSkew != null) {\n        logInfo('[KEYCLOAK] Estimated time difference between browser and server is ' + kc.timeSkew + ' seconds');\n        if (kc.onTokenExpired) {\n          var expiresIn = (kc.tokenParsed['exp'] - new Date().getTime() / 1000 + kc.timeSkew) * 1000;\n          logInfo('[KEYCLOAK] Token expires in ' + Math.round(expiresIn / 1000) + ' s');\n          if (expiresIn <= 0) {\n            kc.onTokenExpired();\n          } else {\n            kc.tokenTimeoutHandle = setTimeout(kc.onTokenExpired, expiresIn);\n          }\n        }\n      }\n    } else {\n      delete kc.token;\n      delete kc.tokenParsed;\n      delete kc.subject;\n      delete kc.realmAccess;\n      delete kc.resourceAccess;\n      kc.authenticated = false;\n    }\n  }\n  function createUUID() {\n    if (typeof crypto === \"undefined\" || typeof crypto.randomUUID === \"undefined\") {\n      throw new Error(\"Web Crypto API is not available.\");\n    }\n    return crypto.randomUUID();\n  }\n  function parseCallback(url) {\n    var oauth = parseCallbackUrl(url);\n    if (!oauth) {\n      return;\n    }\n    var oauthState = callbackStorage.get(oauth.state);\n    if (oauthState) {\n      oauth.valid = true;\n      oauth.redirectUri = oauthState.redirectUri;\n      oauth.storedNonce = oauthState.nonce;\n      oauth.prompt = oauthState.prompt;\n      oauth.pkceCodeVerifier = oauthState.pkceCodeVerifier;\n      oauth.loginOptions = oauthState.loginOptions;\n    }\n    return oauth;\n  }\n  function parseCallbackUrl(url) {\n    var supportedParams;\n    switch (kc.flow) {\n      case 'standard':\n        supportedParams = ['code', 'state', 'session_state', 'kc_action_status', 'kc_action', 'iss'];\n        break;\n      case 'implicit':\n        supportedParams = ['access_token', 'token_type', 'id_token', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n        break;\n      case 'hybrid':\n        supportedParams = ['access_token', 'token_type', 'id_token', 'code', 'state', 'session_state', 'expires_in', 'kc_action_status', 'kc_action', 'iss'];\n        break;\n    }\n    supportedParams.push('error');\n    supportedParams.push('error_description');\n    supportedParams.push('error_uri');\n    var queryIndex = url.indexOf('?');\n    var fragmentIndex = url.indexOf('#');\n    var newUrl;\n    var parsed;\n    if (kc.responseMode === 'query' && queryIndex !== -1) {\n      newUrl = url.substring(0, queryIndex);\n      parsed = parseCallbackParams(url.substring(queryIndex + 1, fragmentIndex !== -1 ? fragmentIndex : url.length), supportedParams);\n      if (parsed.paramsString !== '') {\n        newUrl += '?' + parsed.paramsString;\n      }\n      if (fragmentIndex !== -1) {\n        newUrl += url.substring(fragmentIndex);\n      }\n    } else if (kc.responseMode === 'fragment' && fragmentIndex !== -1) {\n      newUrl = url.substring(0, fragmentIndex);\n      parsed = parseCallbackParams(url.substring(fragmentIndex + 1), supportedParams);\n      if (parsed.paramsString !== '') {\n        newUrl += '#' + parsed.paramsString;\n      }\n    }\n    if (parsed && parsed.oauthParams) {\n      if (kc.flow === 'standard' || kc.flow === 'hybrid') {\n        if ((parsed.oauthParams.code || parsed.oauthParams.error) && parsed.oauthParams.state) {\n          parsed.oauthParams.newUrl = newUrl;\n          return parsed.oauthParams;\n        }\n      } else if (kc.flow === 'implicit') {\n        if ((parsed.oauthParams.access_token || parsed.oauthParams.error) && parsed.oauthParams.state) {\n          parsed.oauthParams.newUrl = newUrl;\n          return parsed.oauthParams;\n        }\n      }\n    }\n  }\n  function parseCallbackParams(paramsString, supportedParams) {\n    var p = paramsString.split('&');\n    var result = {\n      paramsString: '',\n      oauthParams: {}\n    };\n    for (var i = 0; i < p.length; i++) {\n      var split = p[i].indexOf(\"=\");\n      var key = p[i].slice(0, split);\n      if (supportedParams.indexOf(key) !== -1) {\n        result.oauthParams[key] = p[i].slice(split + 1);\n      } else {\n        if (result.paramsString !== '') {\n          result.paramsString += '&';\n        }\n        result.paramsString += p[i];\n      }\n    }\n    return result;\n  }\n  function createPromise() {\n    // Need to create a native Promise which also preserves the\n    // interface of the custom promise type previously used by the API\n    var p = {\n      setSuccess: function (result) {\n        p.resolve(result);\n      },\n      setError: function (result) {\n        p.reject(result);\n      }\n    };\n    p.promise = new Promise(function (resolve, reject) {\n      p.resolve = resolve;\n      p.reject = reject;\n    });\n    return p;\n  }\n\n  // Function to extend existing native Promise with timeout\n  function applyTimeoutToPromise(promise, timeout, errorMessage) {\n    var timeoutHandle = null;\n    var timeoutPromise = new Promise(function (resolve, reject) {\n      timeoutHandle = setTimeout(function () {\n        reject({\n          \"error\": errorMessage || \"Promise is not settled within timeout of \" + timeout + \"ms\"\n        });\n      }, timeout);\n    });\n    return Promise.race([promise, timeoutPromise]).finally(function () {\n      clearTimeout(timeoutHandle);\n    });\n  }\n  function setupCheckLoginIframe() {\n    var promise = createPromise();\n    if (!loginIframe.enable) {\n      promise.setSuccess();\n      return promise.promise;\n    }\n    if (loginIframe.iframe) {\n      promise.setSuccess();\n      return promise.promise;\n    }\n    var iframe = document.createElement('iframe');\n    loginIframe.iframe = iframe;\n    iframe.onload = function () {\n      var authUrl = kc.endpoints.authorize();\n      if (authUrl.charAt(0) === '/') {\n        loginIframe.iframeOrigin = getOrigin();\n      } else {\n        loginIframe.iframeOrigin = authUrl.substring(0, authUrl.indexOf('/', 8));\n      }\n      promise.setSuccess();\n    };\n    var src = kc.endpoints.checkSessionIframe();\n    iframe.setAttribute('src', src);\n    iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n    iframe.setAttribute('title', 'keycloak-session-iframe');\n    iframe.style.display = 'none';\n    document.body.appendChild(iframe);\n    var messageCallback = function (event) {\n      if (event.origin !== loginIframe.iframeOrigin || loginIframe.iframe.contentWindow !== event.source) {\n        return;\n      }\n      if (!(event.data == 'unchanged' || event.data == 'changed' || event.data == 'error')) {\n        return;\n      }\n      if (event.data != 'unchanged') {\n        kc.clearToken();\n      }\n      var callbacks = loginIframe.callbackList.splice(0, loginIframe.callbackList.length);\n      for (var i = callbacks.length - 1; i >= 0; --i) {\n        var promise = callbacks[i];\n        if (event.data == 'error') {\n          promise.setError();\n        } else {\n          promise.setSuccess(event.data == 'unchanged');\n        }\n      }\n    };\n    window.addEventListener('message', messageCallback, false);\n    return promise.promise;\n  }\n  function scheduleCheckIframe() {\n    if (loginIframe.enable) {\n      if (kc.token) {\n        setTimeout(function () {\n          checkLoginIframe().then(function (unchanged) {\n            if (unchanged) {\n              scheduleCheckIframe();\n            }\n          });\n        }, loginIframe.interval * 1000);\n      }\n    }\n  }\n  function checkLoginIframe() {\n    var promise = createPromise();\n    if (loginIframe.iframe && loginIframe.iframeOrigin) {\n      var msg = kc.clientId + ' ' + (kc.sessionId ? kc.sessionId : '');\n      loginIframe.callbackList.push(promise);\n      var origin = loginIframe.iframeOrigin;\n      if (loginIframe.callbackList.length == 1) {\n        loginIframe.iframe.contentWindow.postMessage(msg, origin);\n      }\n    } else {\n      promise.setSuccess();\n    }\n    return promise.promise;\n  }\n  function check3pCookiesSupported() {\n    var promise = createPromise();\n    if ((loginIframe.enable || kc.silentCheckSsoRedirectUri) && typeof kc.endpoints.thirdPartyCookiesIframe === 'function') {\n      var iframe = document.createElement('iframe');\n      iframe.setAttribute('src', kc.endpoints.thirdPartyCookiesIframe());\n      iframe.setAttribute('sandbox', 'allow-storage-access-by-user-activation allow-scripts allow-same-origin');\n      iframe.setAttribute('title', 'keycloak-3p-check-iframe');\n      iframe.style.display = 'none';\n      document.body.appendChild(iframe);\n      var messageCallback = function (event) {\n        if (iframe.contentWindow !== event.source) {\n          return;\n        }\n        if (event.data !== \"supported\" && event.data !== \"unsupported\") {\n          return;\n        } else if (event.data === \"unsupported\") {\n          logWarn(\"[KEYCLOAK] Your browser is blocking access to 3rd-party cookies, this means:\\n\\n\" + \" - It is not possible to retrieve tokens without redirecting to the Keycloak server (a.k.a. no support for silent authentication).\\n\" + \" - It is not possible to automatically detect changes to the session status (such as the user logging out in another tab).\\n\\n\" + \"For more information see: https://www.keycloak.org/securing-apps/javascript-adapter#_modern_browsers\");\n          loginIframe.enable = false;\n          if (kc.silentCheckSsoFallback) {\n            kc.silentCheckSsoRedirectUri = false;\n          }\n        }\n        document.body.removeChild(iframe);\n        window.removeEventListener(\"message\", messageCallback);\n        promise.setSuccess();\n      };\n      window.addEventListener('message', messageCallback, false);\n    } else {\n      promise.setSuccess();\n    }\n    return applyTimeoutToPromise(promise.promise, kc.messageReceiveTimeout, \"Timeout when waiting for 3rd party check iframe message.\");\n  }\n  function loadAdapter(type) {\n    if (!type || type == 'default') {\n      return {\n        login: async function (options) {\n          window.location.assign(await kc.createLoginUrl(options));\n          return createPromise().promise;\n        },\n        logout: async function (options) {\n          const logoutMethod = options?.logoutMethod ?? kc.logoutMethod;\n          if (logoutMethod === \"GET\") {\n            window.location.replace(kc.createLogoutUrl(options));\n            return;\n          }\n\n          // Create form to send POST request.\n          const form = document.createElement(\"form\");\n          form.setAttribute(\"method\", \"POST\");\n          form.setAttribute(\"action\", kc.createLogoutUrl(options));\n          form.style.display = \"none\";\n\n          // Add data to form as hidden input fields.\n          const data = {\n            id_token_hint: kc.idToken,\n            client_id: kc.clientId,\n            post_logout_redirect_uri: adapter.redirectUri(options, false)\n          };\n          for (const [name, value] of Object.entries(data)) {\n            const input = document.createElement(\"input\");\n            input.setAttribute(\"type\", \"hidden\");\n            input.setAttribute(\"name\", name);\n            input.setAttribute(\"value\", value);\n            form.appendChild(input);\n          }\n\n          // Append form to page and submit it to perform logout and redirect.\n          document.body.appendChild(form);\n          form.submit();\n        },\n        register: async function (options) {\n          window.location.assign(await kc.createRegisterUrl(options));\n          return createPromise().promise;\n        },\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            window.location.href = accountUrl;\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n          return createPromise().promise;\n        },\n        redirectUri: function (options, encodeHash) {\n          if (arguments.length == 1) {\n            encodeHash = true;\n          }\n          if (options && options.redirectUri) {\n            return options.redirectUri;\n          } else if (kc.redirectUri) {\n            return kc.redirectUri;\n          } else {\n            return location.href;\n          }\n        }\n      };\n    }\n    if (type == 'cordova') {\n      loginIframe.enable = false;\n      var cordovaOpenWindowWrapper = function (loginUrl, target, options) {\n        if (window.cordova && window.cordova.InAppBrowser) {\n          // Use inappbrowser for IOS and Android if available\n          return window.cordova.InAppBrowser.open(loginUrl, target, options);\n        } else {\n          return window.open(loginUrl, target, options);\n        }\n      };\n      var shallowCloneCordovaOptions = function (userOptions) {\n        if (userOptions && userOptions.cordovaOptions) {\n          return Object.keys(userOptions.cordovaOptions).reduce(function (options, optionName) {\n            options[optionName] = userOptions.cordovaOptions[optionName];\n            return options;\n          }, {});\n        } else {\n          return {};\n        }\n      };\n      var formatCordovaOptions = function (cordovaOptions) {\n        return Object.keys(cordovaOptions).reduce(function (options, optionName) {\n          options.push(optionName + \"=\" + cordovaOptions[optionName]);\n          return options;\n        }, []).join(\",\");\n      };\n      var createCordovaOptions = function (userOptions) {\n        var cordovaOptions = shallowCloneCordovaOptions(userOptions);\n        cordovaOptions.location = 'no';\n        if (userOptions && userOptions.prompt == 'none') {\n          cordovaOptions.hidden = 'yes';\n        }\n        return formatCordovaOptions(cordovaOptions);\n      };\n      var getCordovaRedirectUri = function () {\n        return kc.redirectUri || 'http://localhost';\n      };\n      return {\n        login: async function (options) {\n          var promise = createPromise();\n          var cordovaOptions = createCordovaOptions(options);\n          var loginUrl = await kc.createLoginUrl(options);\n          var ref = cordovaOpenWindowWrapper(loginUrl, '_blank', cordovaOptions);\n          var completed = false;\n          var closed = false;\n          var closeBrowser = function () {\n            closed = true;\n            ref.close();\n          };\n          ref.addEventListener('loadstart', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              var callback = parseCallback(event.url);\n              processCallback(callback, promise);\n              closeBrowser();\n              completed = true;\n            }\n          });\n          ref.addEventListener('loaderror', function (event) {\n            if (!completed) {\n              if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                var callback = parseCallback(event.url);\n                processCallback(callback, promise);\n                closeBrowser();\n                completed = true;\n              } else {\n                promise.setError();\n                closeBrowser();\n              }\n            }\n          });\n          ref.addEventListener('exit', function (event) {\n            if (!closed) {\n              promise.setError({\n                reason: \"closed_by_user\"\n              });\n            }\n          });\n          return promise.promise;\n        },\n        logout: function (options) {\n          var promise = createPromise();\n          var logoutUrl = kc.createLogoutUrl(options);\n          var ref = cordovaOpenWindowWrapper(logoutUrl, '_blank', 'location=no,hidden=yes,clearcache=yes');\n          var error;\n          ref.addEventListener('loadstart', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n            }\n          });\n          ref.addEventListener('loaderror', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n            } else {\n              error = true;\n              ref.close();\n            }\n          });\n          ref.addEventListener('exit', function (event) {\n            if (error) {\n              promise.setError();\n            } else {\n              kc.clearToken();\n              promise.setSuccess();\n            }\n          });\n          return promise.promise;\n        },\n        register: async function (options) {\n          var promise = createPromise();\n          var registerUrl = await kc.createRegisterUrl();\n          var cordovaOptions = createCordovaOptions(options);\n          var ref = cordovaOpenWindowWrapper(registerUrl, '_blank', cordovaOptions);\n          ref.addEventListener('loadstart', function (event) {\n            if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n              ref.close();\n              var oauth = parseCallback(event.url);\n              processCallback(oauth, promise);\n            }\n          });\n          return promise.promise;\n        },\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            var ref = cordovaOpenWindowWrapper(accountUrl, '_blank', 'location=no');\n            ref.addEventListener('loadstart', function (event) {\n              if (event.url.indexOf(getCordovaRedirectUri()) == 0) {\n                ref.close();\n              }\n            });\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n        },\n        redirectUri: function (options) {\n          return getCordovaRedirectUri();\n        }\n      };\n    }\n    if (type == 'cordova-native') {\n      loginIframe.enable = false;\n      return {\n        login: async function (options) {\n          var promise = createPromise();\n          var loginUrl = await kc.createLoginUrl(options);\n          universalLinks.subscribe('keycloak', function (event) {\n            universalLinks.unsubscribe('keycloak');\n            window.cordova.plugins.browsertab.close();\n            var oauth = parseCallback(event.url);\n            processCallback(oauth, promise);\n          });\n          window.cordova.plugins.browsertab.openUrl(loginUrl);\n          return promise.promise;\n        },\n        logout: function (options) {\n          var promise = createPromise();\n          var logoutUrl = kc.createLogoutUrl(options);\n          universalLinks.subscribe('keycloak', function (event) {\n            universalLinks.unsubscribe('keycloak');\n            window.cordova.plugins.browsertab.close();\n            kc.clearToken();\n            promise.setSuccess();\n          });\n          window.cordova.plugins.browsertab.openUrl(logoutUrl);\n          return promise.promise;\n        },\n        register: async function (options) {\n          var promise = createPromise();\n          var registerUrl = await kc.createRegisterUrl(options);\n          universalLinks.subscribe('keycloak', function (event) {\n            universalLinks.unsubscribe('keycloak');\n            window.cordova.plugins.browsertab.close();\n            var oauth = parseCallback(event.url);\n            processCallback(oauth, promise);\n          });\n          window.cordova.plugins.browsertab.openUrl(registerUrl);\n          return promise.promise;\n        },\n        accountManagement: function () {\n          var accountUrl = kc.createAccountUrl();\n          if (typeof accountUrl !== 'undefined') {\n            window.cordova.plugins.browsertab.openUrl(accountUrl);\n          } else {\n            throw \"Not supported by the OIDC server\";\n          }\n        },\n        redirectUri: function (options) {\n          if (options && options.redirectUri) {\n            return options.redirectUri;\n          } else if (kc.redirectUri) {\n            return kc.redirectUri;\n          } else {\n            return \"http://localhost\";\n          }\n        }\n      };\n    }\n    throw 'invalid adapter type: ' + type;\n  }\n  const STORAGE_KEY_PREFIX = 'kc-callback-';\n  var LocalStorage = function () {\n    if (!(this instanceof LocalStorage)) {\n      return new LocalStorage();\n    }\n    localStorage.setItem('kc-test', 'test');\n    localStorage.removeItem('kc-test');\n    var cs = this;\n\n    /**\n     * Clears all values from local storage that are no longer valid.\n     */\n    function clearInvalidValues() {\n      const currentTime = Date.now();\n      for (const [key, value] of getStoredEntries()) {\n        // Attempt to parse the expiry time from the value.\n        const expiry = parseExpiry(value);\n\n        // Discard the value if it is malformed or expired.\n        if (expiry === null || expiry < currentTime) {\n          localStorage.removeItem(key);\n        }\n      }\n    }\n\n    /**\n     * Clears all known values from local storage.\n     */\n    function clearAllValues() {\n      for (const [key] of getStoredEntries()) {\n        localStorage.removeItem(key);\n      }\n    }\n\n    /**\n     * Gets all entries stored in local storage that are known to be managed by this class.\n     * @returns {Array<[string, unknown]>} An array of key-value pairs.\n     */\n    function getStoredEntries() {\n      return Object.entries(localStorage).filter(([key]) => key.startsWith(STORAGE_KEY_PREFIX));\n    }\n\n    /**\n     * Parses the expiry time from a value stored in local storage.\n     * @param {unknown} value\n     * @returns {number | null} The expiry time in milliseconds, or `null` if the value is malformed.\n     */\n    function parseExpiry(value) {\n      let parsedValue;\n\n      // Attempt to parse the value as JSON.\n      try {\n        parsedValue = JSON.parse(value);\n      } catch (error) {\n        return null;\n      }\n\n      // Attempt to extract the 'expires' property.\n      if (isObject(parsedValue) && 'expires' in parsedValue && typeof parsedValue.expires === 'number') {\n        return parsedValue.expires;\n      }\n      return null;\n    }\n    cs.get = function (state) {\n      if (!state) {\n        return;\n      }\n      var key = STORAGE_KEY_PREFIX + state;\n      var value = localStorage.getItem(key);\n      if (value) {\n        localStorage.removeItem(key);\n        value = JSON.parse(value);\n      }\n      clearInvalidValues();\n      return value;\n    };\n    cs.add = function (state) {\n      clearInvalidValues();\n      const key = STORAGE_KEY_PREFIX + state.state;\n      const value = JSON.stringify({\n        ...state,\n        // Set the expiry time to 1 hour from now.\n        expires: Date.now() + 60 * 60 * 1000\n      });\n      try {\n        localStorage.setItem(key, value);\n      } catch (error) {\n        // If the storage is full, clear all known values and try again.\n        clearAllValues();\n        localStorage.setItem(key, value);\n      }\n    };\n  };\n  var CookieStorage = function () {\n    if (!(this instanceof CookieStorage)) {\n      return new CookieStorage();\n    }\n    var cs = this;\n    cs.get = function (state) {\n      if (!state) {\n        return;\n      }\n      var value = getCookie(STORAGE_KEY_PREFIX + state);\n      setCookie(STORAGE_KEY_PREFIX + state, '', cookieExpiration(-100));\n      if (value) {\n        return JSON.parse(value);\n      }\n    };\n    cs.add = function (state) {\n      setCookie(STORAGE_KEY_PREFIX + state.state, JSON.stringify(state), cookieExpiration(60));\n    };\n    cs.removeItem = function (key) {\n      setCookie(key, '', cookieExpiration(-100));\n    };\n    var cookieExpiration = function (minutes) {\n      var exp = new Date();\n      exp.setTime(exp.getTime() + minutes * 60 * 1000);\n      return exp;\n    };\n    var getCookie = function (key) {\n      var name = key + '=';\n      var ca = document.cookie.split(';');\n      for (var i = 0; i < ca.length; i++) {\n        var c = ca[i];\n        while (c.charAt(0) == ' ') {\n          c = c.substring(1);\n        }\n        if (c.indexOf(name) == 0) {\n          return c.substring(name.length, c.length);\n        }\n      }\n      return '';\n    };\n    var setCookie = function (key, value, expirationDate) {\n      var cookie = key + '=' + value + '; ' + 'expires=' + expirationDate.toUTCString() + '; ';\n      document.cookie = cookie;\n    };\n  };\n  function createCallbackStorage() {\n    try {\n      return new LocalStorage();\n    } catch (err) {}\n    return new CookieStorage();\n  }\n  function createLogger(fn) {\n    return function () {\n      if (kc.enableLogging) {\n        fn.apply(console, Array.prototype.slice.call(arguments));\n      }\n    };\n  }\n}\nexport default Keycloak;\n\n/**\n * @param {ArrayBuffer} bytes\n * @see https://developer.mozilla.org/en-US/docs/Glossary/Base64#the_unicode_problem\n */\nfunction bytesToBase64(bytes) {\n  const binString = String.fromCodePoint(...bytes);\n  return btoa(binString);\n}\n\n/**\n * @param {string} message\n * @see https://developer.mozilla.org/en-US/docs/Web/API/SubtleCrypto/digest#basic_example\n */\nasync function sha256Digest(message) {\n  const encoder = new TextEncoder();\n  const data = encoder.encode(message);\n  if (typeof crypto === \"undefined\" || typeof crypto.subtle === \"undefined\") {\n    throw new Error(\"Web Crypto API is not available.\");\n  }\n  return await crypto.subtle.digest(\"SHA-256\", data);\n}\n\n/**\n * @param {string} token\n */\nfunction decodeToken(token) {\n  const [header, payload] = token.split(\".\");\n  if (typeof payload !== \"string\") {\n    throw new Error(\"Unable to decode token, payload not found.\");\n  }\n  let decoded;\n  try {\n    decoded = base64UrlDecode(payload);\n  } catch (error) {\n    throw new Error(\"Unable to decode token, payload is not a valid Base64URL value.\", {\n      cause: error\n    });\n  }\n  try {\n    return JSON.parse(decoded);\n  } catch (error) {\n    throw new Error(\"Unable to decode token, payload is not a valid JSON value.\", {\n      cause: error\n    });\n  }\n}\n\n/**\n * @param {string} input\n */\nfunction base64UrlDecode(input) {\n  let output = input.replaceAll(\"-\", \"+\").replaceAll(\"_\", \"/\");\n  switch (output.length % 4) {\n    case 0:\n      break;\n    case 2:\n      output += \"==\";\n      break;\n    case 3:\n      output += \"=\";\n      break;\n    default:\n      throw new Error(\"Input is not of the correct length.\");\n  }\n  try {\n    return b64DecodeUnicode(output);\n  } catch (error) {\n    return atob(output);\n  }\n}\n\n/**\n * @param {string} input\n */\nfunction b64DecodeUnicode(input) {\n  return decodeURIComponent(atob(input).replace(/(.)/g, (m, p) => {\n    let code = p.charCodeAt(0).toString(16).toUpperCase();\n    if (code.length < 2) {\n      code = \"0\" + code;\n    }\n    return \"%\" + code;\n  }));\n}\n\n/**\n * Check if the input is an object that can be operated on.\n * @param {unknown} input\n */\nfunction isObject(input) {\n  return typeof input === 'object' && input !== null;\n}"], "mappings": ";;;;;;;AAgBA,SAAS,SAAS,QAAQ;AACxB,MAAI,EAAE,gBAAgB,WAAW;AAC/B,UAAM,IAAI,MAAM,wDAAwD;AAAA,EAC1E;AACA,MAAI,OAAO,WAAW,YAAY,CAAC,SAAS,MAAM,GAAG;AACnD,UAAM,IAAI,MAAM,iHAAiH;AAAA,EACnI;AACA,MAAI,SAAS,MAAM,GAAG;AACpB,UAAM,qBAAqB,kBAAkB,SAAS,CAAC,UAAU,IAAI,CAAC,OAAO,SAAS,UAAU;AAChG,eAAW,YAAY,oBAAoB;AACzC,UAAI,CAAC,OAAO,QAAQ,GAAG;AACrB,cAAM,IAAI,MAAM,qDAAqD,QAAQ,aAAa;AAAA,MAC5F;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK;AACT,MAAI;AACJ,MAAI,eAAe,CAAC;AACpB,MAAI;AACJ,MAAI,cAAc;AAAA,IAChB,QAAQ;AAAA,IACR,cAAc,CAAC;AAAA,IACf,UAAU;AAAA,EACZ;AACA,KAAG,gBAAgB;AACnB,MAAI,WAAW;AACf,MAAI,UAAU,aAAa,QAAQ,IAAI;AACvC,MAAI,UAAU,aAAa,QAAQ,IAAI;AACvC,MAAI,CAAC,WAAW,iBAAiB;AAC/B,YAAQ,gVAA0V;AAAA,EACpW;AACA,KAAG,OAAO,SAAU,cAAc,CAAC,GAAG;AACpC,QAAI,GAAG,eAAe;AACpB,YAAM,IAAI,MAAM,qDAAqD;AAAA,IACvE;AACA,OAAG,gBAAgB;AACnB,OAAG,gBAAgB;AACnB,sBAAkB,sBAAsB;AACxC,QAAI,WAAW,CAAC,WAAW,WAAW,gBAAgB;AACtD,QAAI,SAAS,QAAQ,YAAY,OAAO,IAAI,IAAI;AAC9C,gBAAU,YAAY,YAAY,OAAO;AAAA,IAC3C,WAAW,OAAO,YAAY,YAAY,UAAU;AAClD,gBAAU,YAAY;AAAA,IACxB,OAAO;AACL,UAAI,OAAO,WAAW,OAAO,SAAS;AACpC,kBAAU,YAAY,SAAS;AAAA,MACjC,OAAO;AACL,kBAAU,YAAY;AAAA,MACxB;AAAA,IACF;AACA,QAAI,OAAO,YAAY,aAAa,aAAa;AAC/C,iBAAW,YAAY;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,qBAAqB,aAAa;AACvD,kBAAY,SAAS,YAAY;AAAA,IACnC;AACA,QAAI,YAAY,0BAA0B;AACxC,kBAAY,WAAW,YAAY;AAAA,IACrC;AACA,QAAI,YAAY,WAAW,kBAAkB;AAC3C,SAAG,gBAAgB;AAAA,IACrB;AACA,QAAI,YAAY,cAAc;AAC5B,UAAI,YAAY,iBAAiB,WAAW,YAAY,iBAAiB,YAAY;AACnF,WAAG,eAAe,YAAY;AAAA,MAChC,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,YAAY,MAAM;AACpB,cAAQ,YAAY,MAAM;AAAA,QACxB,KAAK;AACH,aAAG,eAAe;AAClB;AAAA,QACF,KAAK;AACH,aAAG,eAAe;AAClB;AAAA,QACF,KAAK;AACH,aAAG,eAAe;AAClB;AAAA,QACF;AACE,gBAAM;AAAA,MACV;AACA,SAAG,OAAO,YAAY;AAAA,IACxB;AACA,QAAI,YAAY,YAAY,MAAM;AAChC,SAAG,WAAW,YAAY;AAAA,IAC5B;AACA,QAAI,YAAY,aAAa;AAC3B,SAAG,cAAc,YAAY;AAAA,IAC/B;AACA,QAAI,YAAY,2BAA2B;AACzC,SAAG,4BAA4B,YAAY;AAAA,IAC7C;AACA,QAAI,OAAO,YAAY,2BAA2B,WAAW;AAC3D,SAAG,yBAAyB,YAAY;AAAA,IAC1C,OAAO;AACL,SAAG,yBAAyB;AAAA,IAC9B;AACA,QAAI,OAAO,YAAY,eAAe,aAAa;AACjD,UAAI,YAAY,eAAe,UAAU,YAAY,eAAe,OAAO;AACzE,cAAM,IAAI,UAAU,mEAAmE,YAAY,UAAU,GAAG;AAAA,MAClH;AACA,SAAG,aAAa,YAAY;AAAA,IAC9B,OAAO;AACL,SAAG,aAAa;AAAA,IAClB;AACA,QAAI,OAAO,YAAY,kBAAkB,WAAW;AAClD,SAAG,gBAAgB,YAAY;AAAA,IACjC,OAAO;AACL,SAAG,gBAAgB;AAAA,IACrB;AACA,QAAI,YAAY,iBAAiB,QAAQ;AACvC,SAAG,eAAe;AAAA,IACpB,OAAO;AACL,SAAG,eAAe;AAAA,IACpB;AACA,QAAI,OAAO,YAAY,UAAU,UAAU;AACzC,SAAG,QAAQ,YAAY;AAAA,IACzB;AACA,QAAI,OAAO,YAAY,cAAc,UAAU;AAC7C,SAAG,YAAY,YAAY;AAAA,IAC7B;AACA,QAAI,OAAO,YAAY,0BAA0B,YAAY,YAAY,wBAAwB,GAAG;AAClG,SAAG,wBAAwB,YAAY;AAAA,IACzC,OAAO;AACL,SAAG,wBAAwB;AAAA,IAC7B;AACA,QAAI,CAAC,GAAG,cAAc;AACpB,SAAG,eAAe;AAAA,IACpB;AACA,QAAI,CAAC,GAAG,cAAc;AACpB,SAAG,eAAe;AAClB,SAAG,OAAO;AAAA,IACZ;AACA,QAAI,UAAU,cAAc;AAC5B,QAAI,cAAc,cAAc;AAChC,gBAAY,QAAQ,KAAK,WAAY;AACnC,SAAG,WAAW,GAAG,QAAQ,GAAG,aAAa;AACzC,cAAQ,WAAW,GAAG,aAAa;AAAA,IACrC,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,cAAQ,SAAS,KAAK;AAAA,IACxB,CAAC;AACD,QAAI,gBAAgB,WAAW;AAC/B,aAAS,SAAS;AAChB,UAAI,UAAU,SAAU,QAAQ;AAC9B,YAAI,CAAC,QAAQ;AACX,kBAAQ,SAAS;AAAA,QACnB;AACA,YAAI,YAAY,QAAQ;AACtB,kBAAQ,SAAS,YAAY;AAAA,QAC/B;AACA,WAAG,MAAM,OAAO,EAAE,KAAK,WAAY;AACjC,sBAAY,WAAW;AAAA,QACzB,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,sBAAY,SAAS,KAAK;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,UAAI,mBAAmB,WAAkB;AAAA;AACvC,cAAI,OAAO,SAAS,cAAc,QAAQ;AAC1C,cAAI,MAAM,MAAM,GAAG,eAAe;AAAA,YAChC,QAAQ;AAAA,YACR,aAAa,GAAG;AAAA,UAClB,CAAC;AACD,eAAK,aAAa,OAAO,GAAG;AAC5B,eAAK,aAAa,WAAW,yEAAyE;AACtG,eAAK,aAAa,SAAS,2BAA2B;AACtD,eAAK,MAAM,UAAU;AACrB,mBAAS,KAAK,YAAY,IAAI;AAC9B,cAAI,kBAAkB,SAAU,OAAO;AACrC,gBAAI,MAAM,WAAW,OAAO,SAAS,UAAU,KAAK,kBAAkB,MAAM,QAAQ;AAClF;AAAA,YACF;AACA,gBAAI,QAAQ,cAAc,MAAM,IAAI;AACpC,4BAAgB,OAAO,WAAW;AAClC,qBAAS,KAAK,YAAY,IAAI;AAC9B,mBAAO,oBAAoB,WAAW,eAAe;AAAA,UACvD;AACA,iBAAO,iBAAiB,WAAW,eAAe;AAAA,QACpD;AAAA;AACA,UAAI,UAAU,CAAC;AACf,cAAQ,YAAY,QAAQ;AAAA,QAC1B,KAAK;AACH,cAAI,YAAY,QAAQ;AACtB,kCAAsB,EAAE,KAAK,WAAY;AACvC,+BAAiB,EAAE,KAAK,SAAU,WAAW;AAC3C,oBAAI,CAAC,WAAW;AACd,qBAAG,4BAA4B,iBAAiB,IAAI,QAAQ,KAAK;AAAA,gBACnE,OAAO;AACL,8BAAY,WAAW;AAAA,gBACzB;AAAA,cACF,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,4BAAY,SAAS,KAAK;AAAA,cAC5B,CAAC;AAAA,YACH,CAAC;AAAA,UACH,OAAO;AACL,eAAG,4BAA4B,iBAAiB,IAAI,QAAQ,KAAK;AAAA,UACnE;AACA;AAAA,QACF,KAAK;AACH,kBAAQ,IAAI;AACZ;AAAA,QACF;AACE,gBAAM;AAAA,MACV;AAAA,IACF;AACA,aAAS,cAAc;AACrB,UAAI,WAAW,cAAc,OAAO,SAAS,IAAI;AACjD,UAAI,UAAU;AACZ,eAAO,QAAQ,aAAa,OAAO,QAAQ,OAAO,MAAM,SAAS,MAAM;AAAA,MACzE;AACA,UAAI,YAAY,SAAS,OAAO;AAC9B,eAAO,sBAAsB,EAAE,KAAK,WAAY;AAC9C,0BAAgB,UAAU,WAAW;AAAA,QACvC,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,sBAAY,SAAS,KAAK;AAAA,QAC5B,CAAC;AAAA,MACH;AACA,UAAI,YAAY,SAAS,YAAY,cAAc;AACjD,iBAAS,YAAY,OAAO,YAAY,cAAc,YAAY,OAAO;AACzE,YAAI,YAAY,QAAQ;AACtB,gCAAsB,EAAE,KAAK,WAAY;AACvC,6BAAiB,EAAE,KAAK,SAAU,WAAW;AAC3C,kBAAI,WAAW;AACb,mBAAG,iBAAiB,GAAG,cAAc;AACrC,4BAAY,WAAW;AACvB,oCAAoB;AAAA,cACtB,OAAO;AACL,4BAAY,WAAW;AAAA,cACzB;AAAA,YACF,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,0BAAY,SAAS,KAAK;AAAA,YAC5B,CAAC;AAAA,UACH,CAAC;AAAA,QACH,OAAO;AACL,aAAG,YAAY,EAAE,EAAE,KAAK,WAAY;AAClC,eAAG,iBAAiB,GAAG,cAAc;AACrC,wBAAY,WAAW;AAAA,UACzB,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,eAAG,eAAe,GAAG,YAAY;AACjC,gBAAI,YAAY,QAAQ;AACtB,qBAAO;AAAA,YACT,OAAO;AACL,0BAAY,SAAS,KAAK;AAAA,YAC5B;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,WAAW,YAAY,QAAQ;AAC7B,eAAO;AAAA,MACT,OAAO;AACL,oBAAY,WAAW;AAAA,MACzB;AAAA,IACF;AACA,kBAAc,KAAK,WAAY;AAC7B,8BAAwB,EAAE,KAAK,WAAW,EAAE,MAAM,SAAU,OAAO;AACjE,gBAAQ,SAAS,KAAK;AAAA,MACxB,CAAC;AAAA,IACH,CAAC;AACD,kBAAc,MAAM,SAAU,OAAO;AACnC,cAAQ,SAAS,KAAK;AAAA,IACxB,CAAC;AACD,WAAO,QAAQ;AAAA,EACjB;AACA,KAAG,QAAQ,SAAU,SAAS;AAC5B,WAAO,QAAQ,MAAM,OAAO;AAAA,EAC9B;AACA,WAAS,mBAAmB,KAAK;AAC/B,QAAI,OAAO,WAAW,eAAe,OAAO,OAAO,oBAAoB,aAAa;AAClF,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,WAAO,OAAO,gBAAgB,IAAI,WAAW,GAAG,CAAC;AAAA,EACnD;AACA,WAAS,qBAAqB,KAAK;AACjC,WAAO,qBAAqB,KAAK,gEAAgE;AAAA,EACnG;AACA,WAAS,qBAAqB,KAAK,UAAU;AAC3C,QAAI,aAAa,mBAAmB,GAAG;AACvC,QAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,YAAM,CAAC,IAAI,SAAS,WAAW,WAAW,CAAC,IAAI,SAAS,MAAM;AAAA,IAChE;AACA,WAAO,OAAO,aAAa,MAAM,MAAM,KAAK;AAAA,EAC9C;AACA,WAAe,sBAAsB,YAAY,cAAc;AAAA;AAC7D,UAAI,eAAe,QAAQ;AACzB,cAAM,IAAI,UAAU,4DAA4D,UAAU,IAAI;AAAA,MAChG;AAGA,YAAM,YAAY,IAAI,WAAW,MAAM,aAAa,YAAY,CAAC;AACjE,YAAM,cAAc,cAAc,SAAS,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,EAAE;AACtG,aAAO;AAAA,IACT;AAAA;AACA,WAAS,qBAAqB,cAAc;AAC1C,QAAI,SAAS;AAAA,MACX,UAAU;AAAA,QACR,KAAK;AAAA,MACP;AAAA,IACF;AACA,WAAO,KAAK,UAAU,MAAM;AAAA,EAC9B;AACA,KAAG,iBAAiB,SAAgB,SAAS;AAAA;AAC3C,UAAI,QAAQ,WAAW;AACvB,UAAI,QAAQ,WAAW;AACvB,UAAI,cAAc,QAAQ,YAAY,OAAO;AAC7C,UAAI,gBAAgB;AAAA,QAClB;AAAA,QACA;AAAA,QACA,aAAa,mBAAmB,WAAW;AAAA,QAC3C,cAAc;AAAA,MAChB;AACA,UAAI,WAAW,QAAQ,QAAQ;AAC7B,sBAAc,SAAS,QAAQ;AAAA,MACjC;AACA,UAAI;AACJ,UAAI,WAAW,QAAQ,UAAU,YAAY;AAC3C,kBAAU,GAAG,UAAU,SAAS;AAAA,MAClC,OAAO;AACL,kBAAU,GAAG,UAAU,UAAU;AAAA,MACnC;AACA,UAAI,QAAQ,WAAW,QAAQ,SAAS,GAAG;AAC3C,UAAI,CAAC,OAAO;AAEV,gBAAQ;AAAA,MACV,WAAW,MAAM,QAAQ,QAAQ,MAAM,IAAI;AAEzC,gBAAQ,YAAY;AAAA,MACtB;AACA,UAAI,MAAM,UAAU,gBAAgB,mBAAmB,GAAG,QAAQ,IAAI,mBAAmB,mBAAmB,WAAW,IAAI,YAAY,mBAAmB,KAAK,IAAI,oBAAoB,mBAAmB,GAAG,YAAY,IAAI,oBAAoB,mBAAmB,GAAG,YAAY,IAAI,YAAY,mBAAmB,KAAK;AAC3T,UAAI,UAAU;AACZ,cAAM,MAAM,YAAY,mBAAmB,KAAK;AAAA,MAClD;AACA,UAAI,WAAW,QAAQ,QAAQ;AAC7B,eAAO,aAAa,mBAAmB,QAAQ,MAAM;AAAA,MACvD;AACA,UAAI,WAAW,OAAO,QAAQ,WAAW,UAAU;AACjD,eAAO,cAAc,mBAAmB,QAAQ,MAAM;AAAA,MACxD;AACA,UAAI,WAAW,QAAQ,WAAW;AAChC,eAAO,iBAAiB,mBAAmB,QAAQ,SAAS;AAAA,MAC9D;AACA,UAAI,WAAW,QAAQ,SAAS;AAC9B,eAAO,kBAAkB,mBAAmB,QAAQ,OAAO;AAAA,MAC7D;AACA,UAAI,WAAW,QAAQ,UAAU,QAAQ,UAAU,YAAY;AAC7D,eAAO,gBAAgB,mBAAmB,QAAQ,MAAM;AAAA,MAC1D;AACA,UAAI,WAAW,QAAQ,QAAQ;AAC7B,eAAO,iBAAiB,mBAAmB,QAAQ,MAAM;AAAA,MAC3D;AACA,UAAI,WAAW,QAAQ,KAAK;AAC1B,YAAI,kBAAkB,qBAAqB,QAAQ,GAAG;AACtD,eAAO,aAAa,mBAAmB,eAAe;AAAA,MACxD;AACA,UAAI,WAAW,QAAQ,aAAa,GAAG,WAAW;AAChD,eAAO,iBAAiB,mBAAmB,QAAQ,aAAa,GAAG,SAAS;AAAA,MAC9E;AACA,UAAI,GAAG,YAAY;AACjB,YAAI;AACF,gBAAM,eAAe,qBAAqB,EAAE;AAC5C,gBAAM,gBAAgB,MAAM,sBAAsB,GAAG,YAAY,YAAY;AAC7E,wBAAc,mBAAmB;AACjC,iBAAO,qBAAqB;AAC5B,iBAAO,4BAA4B,GAAG;AAAA,QACxC,SAAS,OAAO;AACd,gBAAM,IAAI,MAAM,sCAAsC;AAAA,YACpD,OAAO;AAAA,UACT,CAAC;AAAA,QACH;AAAA,MACF;AACA,sBAAgB,IAAI,aAAa;AACjC,aAAO;AAAA,IACT;AAAA;AACA,KAAG,SAAS,SAAU,SAAS;AAC7B,WAAO,QAAQ,OAAO,OAAO;AAAA,EAC/B;AACA,KAAG,kBAAkB,SAAU,SAAS;AACtC,UAAM,eAAe,SAAS,gBAAgB,GAAG;AACjD,QAAI,iBAAiB,QAAQ;AAC3B,aAAO,GAAG,UAAU,OAAO;AAAA,IAC7B;AACA,QAAI,MAAM,GAAG,UAAU,OAAO,IAAI,gBAAgB,mBAAmB,GAAG,QAAQ,IAAI,+BAA+B,mBAAmB,QAAQ,YAAY,SAAS,KAAK,CAAC;AACzK,QAAI,GAAG,SAAS;AACd,aAAO,oBAAoB,mBAAmB,GAAG,OAAO;AAAA,IAC1D;AACA,WAAO;AAAA,EACT;AACA,KAAG,WAAW,SAAU,SAAS;AAC/B,WAAO,QAAQ,SAAS,OAAO;AAAA,EACjC;AACA,KAAG,oBAAoB,SAAgB,SAAS;AAAA;AAC9C,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC;AAAA,MACb;AACA,cAAQ,SAAS;AACjB,aAAO,MAAM,GAAG,eAAe,OAAO;AAAA,IACxC;AAAA;AACA,KAAG,mBAAmB,SAAU,SAAS;AACvC,QAAI,QAAQ,YAAY;AACxB,QAAI,MAAM;AACV,QAAI,OAAO,UAAU,aAAa;AAChC,YAAM,QAAQ,uBAA4B,mBAAmB,GAAG,QAAQ,IAAI,mBAAmB,mBAAmB,QAAQ,YAAY,OAAO,CAAC;AAAA,IAChJ;AACA,WAAO;AAAA,EACT;AACA,KAAG,oBAAoB,WAAY;AACjC,WAAO,QAAQ,kBAAkB;AAAA,EACnC;AACA,KAAG,eAAe,SAAU,MAAM;AAChC,QAAI,SAAS,GAAG;AAChB,WAAO,CAAC,CAAC,UAAU,OAAO,MAAM,QAAQ,IAAI,KAAK;AAAA,EACnD;AACA,KAAG,kBAAkB,SAAU,MAAM,UAAU;AAC7C,QAAI,CAAC,GAAG,gBAAgB;AACtB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,eAAe,YAAY,GAAG,QAAQ;AACtD,WAAO,CAAC,CAAC,UAAU,OAAO,MAAM,QAAQ,IAAI,KAAK;AAAA,EACnD;AACA,KAAG,kBAAkB,WAAY;AAC/B,QAAI,MAAM,YAAY,IAAI;AAC1B,QAAI,MAAM,IAAI,eAAe;AAC7B,QAAI,KAAK,OAAO,KAAK,IAAI;AACzB,QAAI,iBAAiB,UAAU,kBAAkB;AACjD,QAAI,iBAAiB,iBAAiB,YAAY,GAAG,KAAK;AAC1D,QAAI,UAAU,cAAc;AAC5B,QAAI,qBAAqB,WAAY;AACnC,UAAI,IAAI,cAAc,GAAG;AACvB,YAAI,IAAI,UAAU,KAAK;AACrB,aAAG,UAAU,KAAK,MAAM,IAAI,YAAY;AACxC,kBAAQ,WAAW,GAAG,OAAO;AAAA,QAC/B,OAAO;AACL,kBAAQ,SAAS;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK;AACT,WAAO,QAAQ;AAAA,EACjB;AACA,KAAG,eAAe,WAAY;AAC5B,QAAI,MAAM,GAAG,UAAU,SAAS;AAChC,QAAI,MAAM,IAAI,eAAe;AAC7B,QAAI,KAAK,OAAO,KAAK,IAAI;AACzB,QAAI,iBAAiB,UAAU,kBAAkB;AACjD,QAAI,iBAAiB,iBAAiB,YAAY,GAAG,KAAK;AAC1D,QAAI,UAAU,cAAc;AAC5B,QAAI,qBAAqB,WAAY;AACnC,UAAI,IAAI,cAAc,GAAG;AACvB,YAAI,IAAI,UAAU,KAAK;AACrB,aAAG,WAAW,KAAK,MAAM,IAAI,YAAY;AACzC,kBAAQ,WAAW,GAAG,QAAQ;AAAA,QAChC,OAAO;AACL,kBAAQ,SAAS;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK;AACT,WAAO,QAAQ;AAAA,EACjB;AACA,KAAG,iBAAiB,SAAU,aAAa;AACzC,QAAI,CAAC,GAAG,eAAe,CAAC,GAAG,gBAAgB,GAAG,QAAQ,YAAY;AAChE,YAAM;AAAA,IACR;AACA,QAAI,GAAG,YAAY,MAAM;AACvB,cAAQ,2EAA2E;AACnF,aAAO;AAAA,IACT;AACA,QAAI,YAAY,GAAG,YAAY,KAAK,IAAI,KAAK,MAAK,oBAAI,KAAK,GAAE,QAAQ,IAAI,GAAI,IAAI,GAAG;AACpF,QAAI,aAAa;AACf,UAAI,MAAM,WAAW,GAAG;AACtB,cAAM;AAAA,MACR;AACA,mBAAa;AAAA,IACf;AACA,WAAO,YAAY;AAAA,EACrB;AACA,KAAG,cAAc,SAAU,aAAa;AACtC,QAAI,UAAU,cAAc;AAC5B,QAAI,CAAC,GAAG,cAAc;AACpB,cAAQ,SAAS;AACjB,aAAO,QAAQ;AAAA,IACjB;AACA,kBAAc,eAAe;AAC7B,QAAI,OAAO,WAAY;AACrB,UAAI,eAAe;AACnB,UAAI,eAAe,IAAI;AACrB,uBAAe;AACf,gBAAQ,6CAA6C;AAAA,MACvD,WAAW,CAAC,GAAG,eAAe,GAAG,eAAe,WAAW,GAAG;AAC5D,uBAAe;AACf,gBAAQ,4CAA4C;AAAA,MACtD;AACA,UAAI,CAAC,cAAc;AACjB,gBAAQ,WAAW,KAAK;AAAA,MAC1B,OAAO;AACL,YAAI,SAAS,4CAAiD,GAAG;AACjE,YAAI,MAAM,GAAG,UAAU,MAAM;AAC7B,qBAAa,KAAK,OAAO;AACzB,YAAI,aAAa,UAAU,GAAG;AAC5B,cAAI,MAAM,IAAI,eAAe;AAC7B,cAAI,KAAK,QAAQ,KAAK,IAAI;AAC1B,cAAI,iBAAiB,gBAAgB,mCAAmC;AACxE,cAAI,kBAAkB;AACtB,oBAAU,gBAAgB,mBAAmB,GAAG,QAAQ;AACxD,cAAI,aAAY,oBAAI,KAAK,GAAE,QAAQ;AACnC,cAAI,qBAAqB,WAAY;AACnC,gBAAI,IAAI,cAAc,GAAG;AACvB,kBAAI,IAAI,UAAU,KAAK;AACrB,wBAAQ,4BAA4B;AACpC,6BAAa,aAAY,oBAAI,KAAK,GAAE,QAAQ,KAAK;AACjD,oBAAI,gBAAgB,KAAK,MAAM,IAAI,YAAY;AAC/C,yBAAS,cAAc,cAAc,GAAG,cAAc,eAAe,GAAG,cAAc,UAAU,GAAG,SAAS;AAC5G,mBAAG,wBAAwB,GAAG,qBAAqB;AACnD,yBAAS,IAAI,aAAa,IAAI,GAAG,KAAK,MAAM,IAAI,aAAa,IAAI,GAAG;AAClE,oBAAE,WAAW,IAAI;AAAA,gBACnB;AAAA,cACF,OAAO;AACL,wBAAQ,oCAAoC;AAC5C,oBAAI,IAAI,UAAU,KAAK;AACrB,qBAAG,WAAW;AAAA,gBAChB;AACA,mBAAG,sBAAsB,GAAG,mBAAmB;AAC/C,yBAAS,IAAI,aAAa,IAAI,GAAG,KAAK,MAAM,IAAI,aAAa,IAAI,GAAG;AAClE,oBAAE,SAAS,mGAAmG;AAAA,gBAChH;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,KAAK,MAAM;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AACA,QAAI,YAAY,QAAQ;AACtB,UAAI,gBAAgB,iBAAiB;AACrC,oBAAc,KAAK,WAAY;AAC7B,aAAK;AAAA,MACP,CAAC,EAAE,MAAM,SAAU,OAAO;AACxB,gBAAQ,SAAS,KAAK;AAAA,MACxB,CAAC;AAAA,IACH,OAAO;AACL,WAAK;AAAA,IACP;AACA,WAAO,QAAQ;AAAA,EACjB;AACA,KAAG,aAAa,WAAY;AAC1B,QAAI,GAAG,OAAO;AACZ,eAAS,MAAM,MAAM,IAAI;AACzB,SAAG,gBAAgB,GAAG,aAAa;AACnC,UAAI,GAAG,eAAe;AACpB,WAAG,MAAM;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACA,WAAS,cAAc;AACrB,QAAI,OAAO,GAAG,kBAAkB,aAAa;AAC3C,UAAI,GAAG,cAAc,OAAO,GAAG,cAAc,SAAS,CAAC,KAAK,KAAK;AAC/D,eAAO,GAAG,gBAAgB,YAAY,mBAAmB,GAAG,KAAK;AAAA,MACnE,OAAO;AACL,eAAO,GAAG,gBAAgB,aAAa,mBAAmB,GAAG,KAAK;AAAA,MACpE;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AACA,WAAS,YAAY;AACnB,QAAI,CAAC,OAAO,SAAS,QAAQ;AAC3B,aAAO,OAAO,SAAS,WAAW,OAAO,OAAO,SAAS,YAAY,OAAO,SAAS,OAAO,MAAM,OAAO,SAAS,OAAO;AAAA,IAC3H,OAAO;AACL,aAAO,OAAO,SAAS;AAAA,IACzB;AAAA,EACF;AACA,WAAS,gBAAgB,OAAO,SAAS;AACvC,QAAI,OAAO,MAAM;AACjB,QAAI,QAAQ,MAAM;AAClB,QAAI,SAAS,MAAM;AACnB,QAAI,aAAY,oBAAI,KAAK,GAAE,QAAQ;AACnC,QAAI,MAAM,kBAAkB,GAAG;AAC7B,SAAG,kBAAkB,GAAG,eAAe,MAAM,kBAAkB,GAAG,MAAM,WAAW,CAAC;AAAA,IACtF;AACA,QAAI,OAAO;AACT,UAAI,UAAU,QAAQ;AACpB,YAAI,MAAM,qBAAqB,MAAM,sBAAsB,0BAA0B;AACnF,aAAG,MAAM,MAAM,YAAY;AAAA,QAC7B,OAAO;AACL,cAAI,YAAY;AAAA,YACd;AAAA,YACA,mBAAmB,MAAM;AAAA,UAC3B;AACA,aAAG,eAAe,GAAG,YAAY,SAAS;AAC1C,qBAAW,QAAQ,SAAS,SAAS;AAAA,QACvC;AAAA,MACF,OAAO;AACL,mBAAW,QAAQ,WAAW;AAAA,MAChC;AACA;AAAA,IACF,WAAW,GAAG,QAAQ,eAAe,MAAM,gBAAgB,MAAM,WAAW;AAC1E,kBAAY,MAAM,cAAc,MAAM,MAAM,UAAU,IAAI;AAAA,IAC5D;AACA,QAAI,GAAG,QAAQ,cAAc,MAAM;AACjC,UAAI,SAAS,UAAU,OAAO;AAC9B,UAAI,MAAM,GAAG,UAAU,MAAM;AAC7B,UAAI,MAAM,IAAI,eAAe;AAC7B,UAAI,KAAK,QAAQ,KAAK,IAAI;AAC1B,UAAI,iBAAiB,gBAAgB,mCAAmC;AACxE,gBAAU,gBAAgB,mBAAmB,GAAG,QAAQ;AACxD,gBAAU,mBAAmB,MAAM;AACnC,UAAI,MAAM,kBAAkB;AAC1B,kBAAU,oBAAoB,MAAM;AAAA,MACtC;AACA,UAAI,kBAAkB;AACtB,UAAI,qBAAqB,WAAY;AACnC,YAAI,IAAI,cAAc,GAAG;AACvB,cAAI,IAAI,UAAU,KAAK;AACrB,gBAAI,gBAAgB,KAAK,MAAM,IAAI,YAAY;AAC/C,wBAAY,cAAc,cAAc,GAAG,cAAc,eAAe,GAAG,cAAc,UAAU,GAAG,GAAG,SAAS,UAAU;AAC5H,gCAAoB;AAAA,UACtB,OAAO;AACL,eAAG,eAAe,GAAG,YAAY;AACjC,uBAAW,QAAQ,SAAS;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK,MAAM;AAAA,IACjB;AACA,aAAS,YAAY,aAAa,cAAc,SAAS,gBAAgB;AACvE,mBAAa,aAAY,oBAAI,KAAK,GAAE,QAAQ,KAAK;AACjD,eAAS,aAAa,cAAc,SAAS,SAAS;AACtD,UAAI,YAAY,GAAG,iBAAiB,GAAG,cAAc,SAAS,MAAM,aAAa;AAC/E,gBAAQ,0CAA0C;AAClD,WAAG,WAAW;AACd,mBAAW,QAAQ,SAAS;AAAA,MAC9B,OAAO;AACL,YAAI,gBAAgB;AAClB,aAAG,iBAAiB,GAAG,cAAc;AACrC,qBAAW,QAAQ,WAAW;AAAA,QAChC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,WAAS,aAAa;AACpB,QAAI,UAAU,cAAc;AAC5B,QAAI;AACJ,QAAI,OAAO,WAAW,UAAU;AAC9B,kBAAY;AAAA,IACd;AACA,aAAS,kBAAkB,mBAAmB;AAC5C,UAAI,CAAC,mBAAmB;AACtB,WAAG,YAAY;AAAA,UACb,WAAW,WAAY;AACrB,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,OAAO,WAAY;AACjB,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,QAAQ,WAAY;AAClB,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,oBAAoB,WAAY;AAC9B,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,yBAAyB,WAAY;AACnC,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,UACA,UAAU,WAAY;AACpB,mBAAO,YAAY,IAAI;AAAA,UACzB;AAAA,QACF;AAAA,MACF,OAAO;AACL,WAAG,YAAY;AAAA,UACb,WAAW,WAAY;AACrB,mBAAO,kBAAkB;AAAA,UAC3B;AAAA,UACA,OAAO,WAAY;AACjB,mBAAO,kBAAkB;AAAA,UAC3B;AAAA,UACA,QAAQ,WAAY;AAClB,gBAAI,CAAC,kBAAkB,sBAAsB;AAC3C,oBAAM;AAAA,YACR;AACA,mBAAO,kBAAkB;AAAA,UAC3B;AAAA,UACA,oBAAoB,WAAY;AAC9B,gBAAI,CAAC,kBAAkB,sBAAsB;AAC3C,oBAAM;AAAA,YACR;AACA,mBAAO,kBAAkB;AAAA,UAC3B;AAAA,UACA,UAAU,WAAY;AACpB,kBAAM;AAAA,UACR;AAAA,UACA,UAAU,WAAY;AACpB,gBAAI,CAAC,kBAAkB,mBAAmB;AACxC,oBAAM;AAAA,YACR;AACA,mBAAO,kBAAkB;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW;AACb,UAAI,MAAM,IAAI,eAAe;AAC7B,UAAI,KAAK,OAAO,WAAW,IAAI;AAC/B,UAAI,iBAAiB,UAAU,kBAAkB;AACjD,UAAI,qBAAqB,WAAY;AACnC,YAAI,IAAI,cAAc,GAAG;AACvB,cAAI,IAAI,UAAU,OAAO,WAAW,GAAG,GAAG;AACxC,gBAAIA,UAAS,KAAK,MAAM,IAAI,YAAY;AACxC,eAAG,gBAAgBA,QAAO,iBAAiB;AAC3C,eAAG,QAAQA,QAAO,OAAO;AACzB,eAAG,WAAWA,QAAO,UAAU;AAC/B,8BAAkB,IAAI;AACtB,oBAAQ,WAAW;AAAA,UACrB,OAAO;AACL,oBAAQ,SAAS;AAAA,UACnB;AAAA,QACF;AAAA,MACF;AACA,UAAI,KAAK;AAAA,IACX,OAAO;AACL,SAAG,WAAW,OAAO;AACrB,UAAI,eAAe,OAAO,cAAc;AACxC,UAAI,CAAC,cAAc;AACjB,WAAG,gBAAgB,OAAO;AAC1B,WAAG,QAAQ,OAAO;AAClB,0BAAkB,IAAI;AACtB,gBAAQ,WAAW;AAAA,MACrB,OAAO;AACL,YAAI,OAAO,iBAAiB,UAAU;AACpC,cAAI;AACJ,cAAI,aAAa,OAAO,aAAa,SAAS,CAAC,KAAK,KAAK;AACvD,oCAAwB,eAAe;AAAA,UACzC,OAAO;AACL,oCAAwB,eAAe;AAAA,UACzC;AACA,cAAI,MAAM,IAAI,eAAe;AAC7B,cAAI,KAAK,OAAO,uBAAuB,IAAI;AAC3C,cAAI,iBAAiB,UAAU,kBAAkB;AACjD,cAAI,qBAAqB,WAAY;AACnC,gBAAI,IAAI,cAAc,GAAG;AACvB,kBAAI,IAAI,UAAU,OAAO,WAAW,GAAG,GAAG;AACxC,oBAAI,qBAAqB,KAAK,MAAM,IAAI,YAAY;AACpD,kCAAkB,kBAAkB;AACpC,wBAAQ,WAAW;AAAA,cACrB,OAAO;AACL,wBAAQ,SAAS;AAAA,cACnB;AAAA,YACF;AAAA,UACF;AACA,cAAI,KAAK;AAAA,QACX,OAAO;AACL,4BAAkB,YAAY;AAC9B,kBAAQ,WAAW;AAAA,QACrB;AAAA,MACF;AAAA,IACF;AACA,WAAO,QAAQ;AAAA,EACjB;AACA,WAAS,WAAW,KAAK;AACvB,WAAO,IAAI,UAAU,KAAK,IAAI,gBAAgB,IAAI,YAAY,WAAW,OAAO;AAAA,EAClF;AACA,WAAS,SAAS,OAAO,cAAc,SAAS,WAAW;AACzD,QAAI,GAAG,oBAAoB;AACzB,mBAAa,GAAG,kBAAkB;AAClC,SAAG,qBAAqB;AAAA,IAC1B;AACA,QAAI,cAAc;AAChB,SAAG,eAAe;AAClB,SAAG,qBAAqB,YAAY,YAAY;AAAA,IAClD,OAAO;AACL,aAAO,GAAG;AACV,aAAO,GAAG;AAAA,IACZ;AACA,QAAI,SAAS;AACX,SAAG,UAAU;AACb,SAAG,gBAAgB,YAAY,OAAO;AAAA,IACxC,OAAO;AACL,aAAO,GAAG;AACV,aAAO,GAAG;AAAA,IACZ;AACA,QAAI,OAAO;AACT,SAAG,QAAQ;AACX,SAAG,cAAc,YAAY,KAAK;AAClC,SAAG,YAAY,GAAG,YAAY;AAC9B,SAAG,gBAAgB;AACnB,SAAG,UAAU,GAAG,YAAY;AAC5B,SAAG,cAAc,GAAG,YAAY;AAChC,SAAG,iBAAiB,GAAG,YAAY;AACnC,UAAI,WAAW;AACb,WAAG,WAAW,KAAK,MAAM,YAAY,GAAI,IAAI,GAAG,YAAY;AAAA,MAC9D;AACA,UAAI,GAAG,YAAY,MAAM;AACvB,gBAAQ,wEAAwE,GAAG,WAAW,UAAU;AACxG,YAAI,GAAG,gBAAgB;AACrB,cAAI,aAAa,GAAG,YAAY,KAAK,KAAI,oBAAI,KAAK,GAAE,QAAQ,IAAI,MAAO,GAAG,YAAY;AACtF,kBAAQ,iCAAiC,KAAK,MAAM,YAAY,GAAI,IAAI,IAAI;AAC5E,cAAI,aAAa,GAAG;AAClB,eAAG,eAAe;AAAA,UACpB,OAAO;AACL,eAAG,qBAAqB,WAAW,GAAG,gBAAgB,SAAS;AAAA,UACjE;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,aAAO,GAAG;AACV,aAAO,GAAG;AACV,aAAO,GAAG;AACV,aAAO,GAAG;AACV,aAAO,GAAG;AACV,SAAG,gBAAgB;AAAA,IACrB;AAAA,EACF;AACA,WAAS,aAAa;AACpB,QAAI,OAAO,WAAW,eAAe,OAAO,OAAO,eAAe,aAAa;AAC7E,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,WAAO,OAAO,WAAW;AAAA,EAC3B;AACA,WAAS,cAAc,KAAK;AAC1B,QAAI,QAAQ,iBAAiB,GAAG;AAChC,QAAI,CAAC,OAAO;AACV;AAAA,IACF;AACA,QAAI,aAAa,gBAAgB,IAAI,MAAM,KAAK;AAChD,QAAI,YAAY;AACd,YAAM,QAAQ;AACd,YAAM,cAAc,WAAW;AAC/B,YAAM,cAAc,WAAW;AAC/B,YAAM,SAAS,WAAW;AAC1B,YAAM,mBAAmB,WAAW;AACpC,YAAM,eAAe,WAAW;AAAA,IAClC;AACA,WAAO;AAAA,EACT;AACA,WAAS,iBAAiB,KAAK;AAC7B,QAAI;AACJ,YAAQ,GAAG,MAAM;AAAA,MACf,KAAK;AACH,0BAAkB,CAAC,QAAQ,SAAS,iBAAiB,oBAAoB,aAAa,KAAK;AAC3F;AAAA,MACF,KAAK;AACH,0BAAkB,CAAC,gBAAgB,cAAc,YAAY,SAAS,iBAAiB,cAAc,oBAAoB,aAAa,KAAK;AAC3I;AAAA,MACF,KAAK;AACH,0BAAkB,CAAC,gBAAgB,cAAc,YAAY,QAAQ,SAAS,iBAAiB,cAAc,oBAAoB,aAAa,KAAK;AACnJ;AAAA,IACJ;AACA,oBAAgB,KAAK,OAAO;AAC5B,oBAAgB,KAAK,mBAAmB;AACxC,oBAAgB,KAAK,WAAW;AAChC,QAAI,aAAa,IAAI,QAAQ,GAAG;AAChC,QAAI,gBAAgB,IAAI,QAAQ,GAAG;AACnC,QAAI;AACJ,QAAI;AACJ,QAAI,GAAG,iBAAiB,WAAW,eAAe,IAAI;AACpD,eAAS,IAAI,UAAU,GAAG,UAAU;AACpC,eAAS,oBAAoB,IAAI,UAAU,aAAa,GAAG,kBAAkB,KAAK,gBAAgB,IAAI,MAAM,GAAG,eAAe;AAC9H,UAAI,OAAO,iBAAiB,IAAI;AAC9B,kBAAU,MAAM,OAAO;AAAA,MACzB;AACA,UAAI,kBAAkB,IAAI;AACxB,kBAAU,IAAI,UAAU,aAAa;AAAA,MACvC;AAAA,IACF,WAAW,GAAG,iBAAiB,cAAc,kBAAkB,IAAI;AACjE,eAAS,IAAI,UAAU,GAAG,aAAa;AACvC,eAAS,oBAAoB,IAAI,UAAU,gBAAgB,CAAC,GAAG,eAAe;AAC9E,UAAI,OAAO,iBAAiB,IAAI;AAC9B,kBAAU,MAAM,OAAO;AAAA,MACzB;AAAA,IACF;AACA,QAAI,UAAU,OAAO,aAAa;AAChC,UAAI,GAAG,SAAS,cAAc,GAAG,SAAS,UAAU;AAClD,aAAK,OAAO,YAAY,QAAQ,OAAO,YAAY,UAAU,OAAO,YAAY,OAAO;AACrF,iBAAO,YAAY,SAAS;AAC5B,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF,WAAW,GAAG,SAAS,YAAY;AACjC,aAAK,OAAO,YAAY,gBAAgB,OAAO,YAAY,UAAU,OAAO,YAAY,OAAO;AAC7F,iBAAO,YAAY,SAAS;AAC5B,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,WAAS,oBAAoB,cAAc,iBAAiB;AAC1D,QAAI,IAAI,aAAa,MAAM,GAAG;AAC9B,QAAI,SAAS;AAAA,MACX,cAAc;AAAA,MACd,aAAa,CAAC;AAAA,IAChB;AACA,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,UAAI,QAAQ,EAAE,CAAC,EAAE,QAAQ,GAAG;AAC5B,UAAI,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,KAAK;AAC7B,UAAI,gBAAgB,QAAQ,GAAG,MAAM,IAAI;AACvC,eAAO,YAAY,GAAG,IAAI,EAAE,CAAC,EAAE,MAAM,QAAQ,CAAC;AAAA,MAChD,OAAO;AACL,YAAI,OAAO,iBAAiB,IAAI;AAC9B,iBAAO,gBAAgB;AAAA,QACzB;AACA,eAAO,gBAAgB,EAAE,CAAC;AAAA,MAC5B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,WAAS,gBAAgB;AAGvB,QAAI,IAAI;AAAA,MACN,YAAY,SAAU,QAAQ;AAC5B,UAAE,QAAQ,MAAM;AAAA,MAClB;AAAA,MACA,UAAU,SAAU,QAAQ;AAC1B,UAAE,OAAO,MAAM;AAAA,MACjB;AAAA,IACF;AACA,MAAE,UAAU,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACjD,QAAE,UAAU;AACZ,QAAE,SAAS;AAAA,IACb,CAAC;AACD,WAAO;AAAA,EACT;AAGA,WAAS,sBAAsB,SAAS,SAAS,cAAc;AAC7D,QAAI,gBAAgB;AACpB,QAAI,iBAAiB,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC1D,sBAAgB,WAAW,WAAY;AACrC,eAAO;AAAA,UACL,SAAS,gBAAgB,8CAA8C,UAAU;AAAA,QACnF,CAAC;AAAA,MACH,GAAG,OAAO;AAAA,IACZ,CAAC;AACD,WAAO,QAAQ,KAAK,CAAC,SAAS,cAAc,CAAC,EAAE,QAAQ,WAAY;AACjE,mBAAa,aAAa;AAAA,IAC5B,CAAC;AAAA,EACH;AACA,WAAS,wBAAwB;AAC/B,QAAI,UAAU,cAAc;AAC5B,QAAI,CAAC,YAAY,QAAQ;AACvB,cAAQ,WAAW;AACnB,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,YAAY,QAAQ;AACtB,cAAQ,WAAW;AACnB,aAAO,QAAQ;AAAA,IACjB;AACA,QAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,gBAAY,SAAS;AACrB,WAAO,SAAS,WAAY;AAC1B,UAAI,UAAU,GAAG,UAAU,UAAU;AACrC,UAAI,QAAQ,OAAO,CAAC,MAAM,KAAK;AAC7B,oBAAY,eAAe,UAAU;AAAA,MACvC,OAAO;AACL,oBAAY,eAAe,QAAQ,UAAU,GAAG,QAAQ,QAAQ,KAAK,CAAC,CAAC;AAAA,MACzE;AACA,cAAQ,WAAW;AAAA,IACrB;AACA,QAAI,MAAM,GAAG,UAAU,mBAAmB;AAC1C,WAAO,aAAa,OAAO,GAAG;AAC9B,WAAO,aAAa,WAAW,yEAAyE;AACxG,WAAO,aAAa,SAAS,yBAAyB;AACtD,WAAO,MAAM,UAAU;AACvB,aAAS,KAAK,YAAY,MAAM;AAChC,QAAI,kBAAkB,SAAU,OAAO;AACrC,UAAI,MAAM,WAAW,YAAY,gBAAgB,YAAY,OAAO,kBAAkB,MAAM,QAAQ;AAClG;AAAA,MACF;AACA,UAAI,EAAE,MAAM,QAAQ,eAAe,MAAM,QAAQ,aAAa,MAAM,QAAQ,UAAU;AACpF;AAAA,MACF;AACA,UAAI,MAAM,QAAQ,aAAa;AAC7B,WAAG,WAAW;AAAA,MAChB;AACA,UAAI,YAAY,YAAY,aAAa,OAAO,GAAG,YAAY,aAAa,MAAM;AAClF,eAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG,EAAE,GAAG;AAC9C,YAAIC,WAAU,UAAU,CAAC;AACzB,YAAI,MAAM,QAAQ,SAAS;AACzB,UAAAA,SAAQ,SAAS;AAAA,QACnB,OAAO;AACL,UAAAA,SAAQ,WAAW,MAAM,QAAQ,WAAW;AAAA,QAC9C;AAAA,MACF;AAAA,IACF;AACA,WAAO,iBAAiB,WAAW,iBAAiB,KAAK;AACzD,WAAO,QAAQ;AAAA,EACjB;AACA,WAAS,sBAAsB;AAC7B,QAAI,YAAY,QAAQ;AACtB,UAAI,GAAG,OAAO;AACZ,mBAAW,WAAY;AACrB,2BAAiB,EAAE,KAAK,SAAU,WAAW;AAC3C,gBAAI,WAAW;AACb,kCAAoB;AAAA,YACtB;AAAA,UACF,CAAC;AAAA,QACH,GAAG,YAAY,WAAW,GAAI;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACA,WAAS,mBAAmB;AAC1B,QAAI,UAAU,cAAc;AAC5B,QAAI,YAAY,UAAU,YAAY,cAAc;AAClD,UAAI,MAAM,GAAG,WAAW,OAAO,GAAG,YAAY,GAAG,YAAY;AAC7D,kBAAY,aAAa,KAAK,OAAO;AACrC,UAAI,SAAS,YAAY;AACzB,UAAI,YAAY,aAAa,UAAU,GAAG;AACxC,oBAAY,OAAO,cAAc,YAAY,KAAK,MAAM;AAAA,MAC1D;AAAA,IACF,OAAO;AACL,cAAQ,WAAW;AAAA,IACrB;AACA,WAAO,QAAQ;AAAA,EACjB;AACA,WAAS,0BAA0B;AACjC,QAAI,UAAU,cAAc;AAC5B,SAAK,YAAY,UAAU,GAAG,8BAA8B,OAAO,GAAG,UAAU,4BAA4B,YAAY;AACtH,UAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,aAAO,aAAa,OAAO,GAAG,UAAU,wBAAwB,CAAC;AACjE,aAAO,aAAa,WAAW,yEAAyE;AACxG,aAAO,aAAa,SAAS,0BAA0B;AACvD,aAAO,MAAM,UAAU;AACvB,eAAS,KAAK,YAAY,MAAM;AAChC,UAAI,kBAAkB,SAAU,OAAO;AACrC,YAAI,OAAO,kBAAkB,MAAM,QAAQ;AACzC;AAAA,QACF;AACA,YAAI,MAAM,SAAS,eAAe,MAAM,SAAS,eAAe;AAC9D;AAAA,QACF,WAAW,MAAM,SAAS,eAAe;AACvC,kBAAQ,wbAAuc;AAC/c,sBAAY,SAAS;AACrB,cAAI,GAAG,wBAAwB;AAC7B,eAAG,4BAA4B;AAAA,UACjC;AAAA,QACF;AACA,iBAAS,KAAK,YAAY,MAAM;AAChC,eAAO,oBAAoB,WAAW,eAAe;AACrD,gBAAQ,WAAW;AAAA,MACrB;AACA,aAAO,iBAAiB,WAAW,iBAAiB,KAAK;AAAA,IAC3D,OAAO;AACL,cAAQ,WAAW;AAAA,IACrB;AACA,WAAO,sBAAsB,QAAQ,SAAS,GAAG,uBAAuB,0DAA0D;AAAA,EACpI;AACA,WAAS,YAAY,MAAM;AACzB,QAAI,CAAC,QAAQ,QAAQ,WAAW;AAC9B,aAAO;AAAA,QACL,OAAO,SAAgB,SAAS;AAAA;AAC9B,mBAAO,SAAS,OAAO,MAAM,GAAG,eAAe,OAAO,CAAC;AACvD,mBAAO,cAAc,EAAE;AAAA,UACzB;AAAA;AAAA,QACA,QAAQ,SAAgB,SAAS;AAAA;AAC/B,kBAAM,eAAe,SAAS,gBAAgB,GAAG;AACjD,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO,SAAS,QAAQ,GAAG,gBAAgB,OAAO,CAAC;AACnD;AAAA,YACF;AAGA,kBAAM,OAAO,SAAS,cAAc,MAAM;AAC1C,iBAAK,aAAa,UAAU,MAAM;AAClC,iBAAK,aAAa,UAAU,GAAG,gBAAgB,OAAO,CAAC;AACvD,iBAAK,MAAM,UAAU;AAGrB,kBAAM,OAAO;AAAA,cACX,eAAe,GAAG;AAAA,cAClB,WAAW,GAAG;AAAA,cACd,0BAA0B,QAAQ,YAAY,SAAS,KAAK;AAAA,YAC9D;AACA,uBAAW,CAAC,MAAM,KAAK,KAAK,OAAO,QAAQ,IAAI,GAAG;AAChD,oBAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,oBAAM,aAAa,QAAQ,QAAQ;AACnC,oBAAM,aAAa,QAAQ,IAAI;AAC/B,oBAAM,aAAa,SAAS,KAAK;AACjC,mBAAK,YAAY,KAAK;AAAA,YACxB;AAGA,qBAAS,KAAK,YAAY,IAAI;AAC9B,iBAAK,OAAO;AAAA,UACd;AAAA;AAAA,QACA,UAAU,SAAgB,SAAS;AAAA;AACjC,mBAAO,SAAS,OAAO,MAAM,GAAG,kBAAkB,OAAO,CAAC;AAC1D,mBAAO,cAAc,EAAE;AAAA,UACzB;AAAA;AAAA,QACA,mBAAmB,WAAY;AAC7B,cAAI,aAAa,GAAG,iBAAiB;AACrC,cAAI,OAAO,eAAe,aAAa;AACrC,mBAAO,SAAS,OAAO;AAAA,UACzB,OAAO;AACL,kBAAM;AAAA,UACR;AACA,iBAAO,cAAc,EAAE;AAAA,QACzB;AAAA,QACA,aAAa,SAAU,SAAS,YAAY;AAC1C,cAAI,UAAU,UAAU,GAAG;AACzB,yBAAa;AAAA,UACf;AACA,cAAI,WAAW,QAAQ,aAAa;AAClC,mBAAO,QAAQ;AAAA,UACjB,WAAW,GAAG,aAAa;AACzB,mBAAO,GAAG;AAAA,UACZ,OAAO;AACL,mBAAO,SAAS;AAAA,UAClB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,WAAW;AACrB,kBAAY,SAAS;AACrB,UAAI,2BAA2B,SAAU,UAAU,QAAQ,SAAS;AAClE,YAAI,OAAO,WAAW,OAAO,QAAQ,cAAc;AAEjD,iBAAO,OAAO,QAAQ,aAAa,KAAK,UAAU,QAAQ,OAAO;AAAA,QACnE,OAAO;AACL,iBAAO,OAAO,KAAK,UAAU,QAAQ,OAAO;AAAA,QAC9C;AAAA,MACF;AACA,UAAI,6BAA6B,SAAU,aAAa;AACtD,YAAI,eAAe,YAAY,gBAAgB;AAC7C,iBAAO,OAAO,KAAK,YAAY,cAAc,EAAE,OAAO,SAAU,SAAS,YAAY;AACnF,oBAAQ,UAAU,IAAI,YAAY,eAAe,UAAU;AAC3D,mBAAO;AAAA,UACT,GAAG,CAAC,CAAC;AAAA,QACP,OAAO;AACL,iBAAO,CAAC;AAAA,QACV;AAAA,MACF;AACA,UAAI,uBAAuB,SAAU,gBAAgB;AACnD,eAAO,OAAO,KAAK,cAAc,EAAE,OAAO,SAAU,SAAS,YAAY;AACvE,kBAAQ,KAAK,aAAa,MAAM,eAAe,UAAU,CAAC;AAC1D,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG;AAAA,MACjB;AACA,UAAI,uBAAuB,SAAU,aAAa;AAChD,YAAI,iBAAiB,2BAA2B,WAAW;AAC3D,uBAAe,WAAW;AAC1B,YAAI,eAAe,YAAY,UAAU,QAAQ;AAC/C,yBAAe,SAAS;AAAA,QAC1B;AACA,eAAO,qBAAqB,cAAc;AAAA,MAC5C;AACA,UAAI,wBAAwB,WAAY;AACtC,eAAO,GAAG,eAAe;AAAA,MAC3B;AACA,aAAO;AAAA,QACL,OAAO,SAAgB,SAAS;AAAA;AAC9B,gBAAI,UAAU,cAAc;AAC5B,gBAAI,iBAAiB,qBAAqB,OAAO;AACjD,gBAAI,WAAW,MAAM,GAAG,eAAe,OAAO;AAC9C,gBAAI,MAAM,yBAAyB,UAAU,UAAU,cAAc;AACrE,gBAAI,YAAY;AAChB,gBAAI,SAAS;AACb,gBAAI,eAAe,WAAY;AAC7B,uBAAS;AACT,kBAAI,MAAM;AAAA,YACZ;AACA,gBAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,kBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,oBAAI,WAAW,cAAc,MAAM,GAAG;AACtC,gCAAgB,UAAU,OAAO;AACjC,6BAAa;AACb,4BAAY;AAAA,cACd;AAAA,YACF,CAAC;AACD,gBAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,kBAAI,CAAC,WAAW;AACd,oBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,sBAAI,WAAW,cAAc,MAAM,GAAG;AACtC,kCAAgB,UAAU,OAAO;AACjC,+BAAa;AACb,8BAAY;AAAA,gBACd,OAAO;AACL,0BAAQ,SAAS;AACjB,+BAAa;AAAA,gBACf;AAAA,cACF;AAAA,YACF,CAAC;AACD,gBAAI,iBAAiB,QAAQ,SAAU,OAAO;AAC5C,kBAAI,CAAC,QAAQ;AACX,wBAAQ,SAAS;AAAA,kBACf,QAAQ;AAAA,gBACV,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AACD,mBAAO,QAAQ;AAAA,UACjB;AAAA;AAAA,QACA,QAAQ,SAAU,SAAS;AACzB,cAAI,UAAU,cAAc;AAC5B,cAAI,YAAY,GAAG,gBAAgB,OAAO;AAC1C,cAAI,MAAM,yBAAyB,WAAW,UAAU,uCAAuC;AAC/F,cAAI;AACJ,cAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,gBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,kBAAI,MAAM;AAAA,YACZ;AAAA,UACF,CAAC;AACD,cAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,gBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,kBAAI,MAAM;AAAA,YACZ,OAAO;AACL,sBAAQ;AACR,kBAAI,MAAM;AAAA,YACZ;AAAA,UACF,CAAC;AACD,cAAI,iBAAiB,QAAQ,SAAU,OAAO;AAC5C,gBAAI,OAAO;AACT,sBAAQ,SAAS;AAAA,YACnB,OAAO;AACL,iBAAG,WAAW;AACd,sBAAQ,WAAW;AAAA,YACrB;AAAA,UACF,CAAC;AACD,iBAAO,QAAQ;AAAA,QACjB;AAAA,QACA,UAAU,SAAgB,SAAS;AAAA;AACjC,gBAAI,UAAU,cAAc;AAC5B,gBAAI,cAAc,MAAM,GAAG,kBAAkB;AAC7C,gBAAI,iBAAiB,qBAAqB,OAAO;AACjD,gBAAI,MAAM,yBAAyB,aAAa,UAAU,cAAc;AACxE,gBAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,kBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,oBAAI,MAAM;AACV,oBAAI,QAAQ,cAAc,MAAM,GAAG;AACnC,gCAAgB,OAAO,OAAO;AAAA,cAChC;AAAA,YACF,CAAC;AACD,mBAAO,QAAQ;AAAA,UACjB;AAAA;AAAA,QACA,mBAAmB,WAAY;AAC7B,cAAI,aAAa,GAAG,iBAAiB;AACrC,cAAI,OAAO,eAAe,aAAa;AACrC,gBAAI,MAAM,yBAAyB,YAAY,UAAU,aAAa;AACtE,gBAAI,iBAAiB,aAAa,SAAU,OAAO;AACjD,kBAAI,MAAM,IAAI,QAAQ,sBAAsB,CAAC,KAAK,GAAG;AACnD,oBAAI,MAAM;AAAA,cACZ;AAAA,YACF,CAAC;AAAA,UACH,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,QACA,aAAa,SAAU,SAAS;AAC9B,iBAAO,sBAAsB;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AACA,QAAI,QAAQ,kBAAkB;AAC5B,kBAAY,SAAS;AACrB,aAAO;AAAA,QACL,OAAO,SAAgB,SAAS;AAAA;AAC9B,gBAAI,UAAU,cAAc;AAC5B,gBAAI,WAAW,MAAM,GAAG,eAAe,OAAO;AAC9C,2BAAe,UAAU,YAAY,SAAU,OAAO;AACpD,6BAAe,YAAY,UAAU;AACrC,qBAAO,QAAQ,QAAQ,WAAW,MAAM;AACxC,kBAAI,QAAQ,cAAc,MAAM,GAAG;AACnC,8BAAgB,OAAO,OAAO;AAAA,YAChC,CAAC;AACD,mBAAO,QAAQ,QAAQ,WAAW,QAAQ,QAAQ;AAClD,mBAAO,QAAQ;AAAA,UACjB;AAAA;AAAA,QACA,QAAQ,SAAU,SAAS;AACzB,cAAI,UAAU,cAAc;AAC5B,cAAI,YAAY,GAAG,gBAAgB,OAAO;AAC1C,yBAAe,UAAU,YAAY,SAAU,OAAO;AACpD,2BAAe,YAAY,UAAU;AACrC,mBAAO,QAAQ,QAAQ,WAAW,MAAM;AACxC,eAAG,WAAW;AACd,oBAAQ,WAAW;AAAA,UACrB,CAAC;AACD,iBAAO,QAAQ,QAAQ,WAAW,QAAQ,SAAS;AACnD,iBAAO,QAAQ;AAAA,QACjB;AAAA,QACA,UAAU,SAAgB,SAAS;AAAA;AACjC,gBAAI,UAAU,cAAc;AAC5B,gBAAI,cAAc,MAAM,GAAG,kBAAkB,OAAO;AACpD,2BAAe,UAAU,YAAY,SAAU,OAAO;AACpD,6BAAe,YAAY,UAAU;AACrC,qBAAO,QAAQ,QAAQ,WAAW,MAAM;AACxC,kBAAI,QAAQ,cAAc,MAAM,GAAG;AACnC,8BAAgB,OAAO,OAAO;AAAA,YAChC,CAAC;AACD,mBAAO,QAAQ,QAAQ,WAAW,QAAQ,WAAW;AACrD,mBAAO,QAAQ;AAAA,UACjB;AAAA;AAAA,QACA,mBAAmB,WAAY;AAC7B,cAAI,aAAa,GAAG,iBAAiB;AACrC,cAAI,OAAO,eAAe,aAAa;AACrC,mBAAO,QAAQ,QAAQ,WAAW,QAAQ,UAAU;AAAA,UACtD,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,QACA,aAAa,SAAU,SAAS;AAC9B,cAAI,WAAW,QAAQ,aAAa;AAClC,mBAAO,QAAQ;AAAA,UACjB,WAAW,GAAG,aAAa;AACzB,mBAAO,GAAG;AAAA,UACZ,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,2BAA2B;AAAA,EACnC;AACA,QAAM,qBAAqB;AAC3B,MAAI,eAAe,WAAY;AAC7B,QAAI,EAAE,gBAAgB,eAAe;AACnC,aAAO,IAAI,aAAa;AAAA,IAC1B;AACA,iBAAa,QAAQ,WAAW,MAAM;AACtC,iBAAa,WAAW,SAAS;AACjC,QAAI,KAAK;AAKT,aAAS,qBAAqB;AAC5B,YAAM,cAAc,KAAK,IAAI;AAC7B,iBAAW,CAAC,KAAK,KAAK,KAAK,iBAAiB,GAAG;AAE7C,cAAM,SAAS,YAAY,KAAK;AAGhC,YAAI,WAAW,QAAQ,SAAS,aAAa;AAC3C,uBAAa,WAAW,GAAG;AAAA,QAC7B;AAAA,MACF;AAAA,IACF;AAKA,aAAS,iBAAiB;AACxB,iBAAW,CAAC,GAAG,KAAK,iBAAiB,GAAG;AACtC,qBAAa,WAAW,GAAG;AAAA,MAC7B;AAAA,IACF;AAMA,aAAS,mBAAmB;AAC1B,aAAO,OAAO,QAAQ,YAAY,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,IAAI,WAAW,kBAAkB,CAAC;AAAA,IAC1F;AAOA,aAAS,YAAY,OAAO;AAC1B,UAAI;AAGJ,UAAI;AACF,sBAAc,KAAK,MAAM,KAAK;AAAA,MAChC,SAAS,OAAO;AACd,eAAO;AAAA,MACT;AAGA,UAAI,SAAS,WAAW,KAAK,aAAa,eAAe,OAAO,YAAY,YAAY,UAAU;AAChG,eAAO,YAAY;AAAA,MACrB;AACA,aAAO;AAAA,IACT;AACA,OAAG,MAAM,SAAU,OAAO;AACxB,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,MAAM,qBAAqB;AAC/B,UAAI,QAAQ,aAAa,QAAQ,GAAG;AACpC,UAAI,OAAO;AACT,qBAAa,WAAW,GAAG;AAC3B,gBAAQ,KAAK,MAAM,KAAK;AAAA,MAC1B;AACA,yBAAmB;AACnB,aAAO;AAAA,IACT;AACA,OAAG,MAAM,SAAU,OAAO;AACxB,yBAAmB;AACnB,YAAM,MAAM,qBAAqB,MAAM;AACvC,YAAM,QAAQ,KAAK,UAAU,iCACxB,QADwB;AAAA;AAAA,QAG3B,SAAS,KAAK,IAAI,IAAI,KAAK,KAAK;AAAA,MAClC,EAAC;AACD,UAAI;AACF,qBAAa,QAAQ,KAAK,KAAK;AAAA,MACjC,SAAS,OAAO;AAEd,uBAAe;AACf,qBAAa,QAAQ,KAAK,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,MAAI,gBAAgB,WAAY;AAC9B,QAAI,EAAE,gBAAgB,gBAAgB;AACpC,aAAO,IAAI,cAAc;AAAA,IAC3B;AACA,QAAI,KAAK;AACT,OAAG,MAAM,SAAU,OAAO;AACxB,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,QAAQ,UAAU,qBAAqB,KAAK;AAChD,gBAAU,qBAAqB,OAAO,IAAI,iBAAiB,IAAI,CAAC;AAChE,UAAI,OAAO;AACT,eAAO,KAAK,MAAM,KAAK;AAAA,MACzB;AAAA,IACF;AACA,OAAG,MAAM,SAAU,OAAO;AACxB,gBAAU,qBAAqB,MAAM,OAAO,KAAK,UAAU,KAAK,GAAG,iBAAiB,EAAE,CAAC;AAAA,IACzF;AACA,OAAG,aAAa,SAAU,KAAK;AAC7B,gBAAU,KAAK,IAAI,iBAAiB,IAAI,CAAC;AAAA,IAC3C;AACA,QAAI,mBAAmB,SAAU,SAAS;AACxC,UAAI,MAAM,oBAAI,KAAK;AACnB,UAAI,QAAQ,IAAI,QAAQ,IAAI,UAAU,KAAK,GAAI;AAC/C,aAAO;AAAA,IACT;AACA,QAAI,YAAY,SAAU,KAAK;AAC7B,UAAI,OAAO,MAAM;AACjB,UAAI,KAAK,SAAS,OAAO,MAAM,GAAG;AAClC,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,YAAI,IAAI,GAAG,CAAC;AACZ,eAAO,EAAE,OAAO,CAAC,KAAK,KAAK;AACzB,cAAI,EAAE,UAAU,CAAC;AAAA,QACnB;AACA,YAAI,EAAE,QAAQ,IAAI,KAAK,GAAG;AACxB,iBAAO,EAAE,UAAU,KAAK,QAAQ,EAAE,MAAM;AAAA,QAC1C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,QAAI,YAAY,SAAU,KAAK,OAAO,gBAAgB;AACpD,UAAI,SAAS,MAAM,MAAM,QAAQ,eAAoB,eAAe,YAAY,IAAI;AACpF,eAAS,SAAS;AAAA,IACpB;AAAA,EACF;AACA,WAAS,wBAAwB;AAC/B,QAAI;AACF,aAAO,IAAI,aAAa;AAAA,IAC1B,SAAS,KAAK;AAAA,IAAC;AACf,WAAO,IAAI,cAAc;AAAA,EAC3B;AACA,WAAS,aAAa,IAAI;AACxB,WAAO,WAAY;AACjB,UAAI,GAAG,eAAe;AACpB,WAAG,MAAM,SAAS,MAAM,UAAU,MAAM,KAAK,SAAS,CAAC;AAAA,MACzD;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAO,mBAAQ;AAMf,SAAS,cAAc,OAAO;AAC5B,QAAM,YAAY,OAAO,cAAc,GAAG,KAAK;AAC/C,SAAO,KAAK,SAAS;AACvB;AAMA,SAAe,aAAa,SAAS;AAAA;AACnC,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,OAAO,QAAQ,OAAO,OAAO;AACnC,QAAI,OAAO,WAAW,eAAe,OAAO,OAAO,WAAW,aAAa;AACzE,YAAM,IAAI,MAAM,kCAAkC;AAAA,IACpD;AACA,WAAO,MAAM,OAAO,OAAO,OAAO,WAAW,IAAI;AAAA,EACnD;AAAA;AAKA,SAAS,YAAY,OAAO;AAC1B,QAAM,CAAC,QAAQ,OAAO,IAAI,MAAM,MAAM,GAAG;AACzC,MAAI,OAAO,YAAY,UAAU;AAC/B,UAAM,IAAI,MAAM,4CAA4C;AAAA,EAC9D;AACA,MAAI;AACJ,MAAI;AACF,cAAU,gBAAgB,OAAO;AAAA,EACnC,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,mEAAmE;AAAA,MACjF,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI;AACF,WAAO,KAAK,MAAM,OAAO;AAAA,EAC3B,SAAS,OAAO;AACd,UAAM,IAAI,MAAM,8DAA8D;AAAA,MAC5E,OAAO;AAAA,IACT,CAAC;AAAA,EACH;AACF;AAKA,SAAS,gBAAgB,OAAO;AAC9B,MAAI,SAAS,MAAM,WAAW,KAAK,GAAG,EAAE,WAAW,KAAK,GAAG;AAC3D,UAAQ,OAAO,SAAS,GAAG;AAAA,IACzB,KAAK;AACH;AAAA,IACF,KAAK;AACH,gBAAU;AACV;AAAA,IACF,KAAK;AACH,gBAAU;AACV;AAAA,IACF;AACE,YAAM,IAAI,MAAM,qCAAqC;AAAA,EACzD;AACA,MAAI;AACF,WAAO,iBAAiB,MAAM;AAAA,EAChC,SAAS,OAAO;AACd,WAAO,KAAK,MAAM;AAAA,EACpB;AACF;AAKA,SAAS,iBAAiB,OAAO;AAC/B,SAAO,mBAAmB,KAAK,KAAK,EAAE,QAAQ,QAAQ,CAAC,GAAG,MAAM;AAC9D,QAAI,OAAO,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY;AACpD,QAAI,KAAK,SAAS,GAAG;AACnB,aAAO,MAAM;AAAA,IACf;AACA,WAAO,MAAM;AAAA,EACf,CAAC,CAAC;AACJ;AAMA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU,YAAY,UAAU;AAChD;", "names": ["config", "promise"]}