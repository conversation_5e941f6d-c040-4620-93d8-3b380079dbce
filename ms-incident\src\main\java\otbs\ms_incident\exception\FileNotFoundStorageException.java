package otbs.ms_incident.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception spécifique pour les fichiers non trouvés dans le service de stockage
 */
public class FileNotFoundStorageException extends FileStorageException {

    /**
     * Crée une exception avec un message
     * 
     * @param message Message d'erreur
     */
    public FileNotFoundStorageException(String message) {
        super(message, HttpStatus.NOT_FOUND, "FILE_NOT_FOUND");
    }

    /**
     * Crée une exception avec un message et une cause
     * 
     * @param message Message d'erreur
     * @param cause Exception cause
     */
    public FileNotFoundStorageException(String message, Throwable cause) {
        super(message, cause, HttpStatus.NOT_FOUND, "FILE_NOT_FOUND");
    }
} 