package otbs.ms_mission.client.keycloak;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO léger pour les options du sélecteur d'utilisateurs (uniquement ID, nom complet et email).
 * Utilisé pour les menus déroulants et les sélecteurs dans l'interface utilisateur.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserSelectorOptionDTO {
    private String id;
    private String fullName;
    private String email;
}
