package otbs.ms_incident.dto.dashboard;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class IncidentStatusDistributionDTOTest {

    @Test
    void testNoArgsConstructor() {
        // When
        IncidentStatusDistributionDTO dto = new IncidentStatusDistributionDTO();

        // Then
        assertEquals(0, dto.getOpenIncidents());
        assertEquals(0, dto.getInProgressIncidents());
        assertEquals(0, dto.getResolvedIncidents());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        long openIncidents = 5;
        long inProgressIncidents = 3;
        long resolvedIncidents = 2;

        // When
        IncidentStatusDistributionDTO dto = new IncidentStatusDistributionDTO(
                openIncidents, inProgressIncidents, resolvedIncidents
        );

        // Then
        assertEquals(openIncidents, dto.getOpenIncidents());
        assertEquals(inProgressIncidents, dto.getInProgressIncidents());
        assertEquals(resolvedIncidents, dto.getResolvedIncidents());
    }

    @Test
    void testBuilder() {
        // Given
        long openIncidents = 5;
        long inProgressIncidents = 3;
        long resolvedIncidents = 2;

        // When
        IncidentStatusDistributionDTO dto = IncidentStatusDistributionDTO.builder()
                .openIncidents(openIncidents)
                .inProgressIncidents(inProgressIncidents)
                .resolvedIncidents(resolvedIncidents)
                .build();

        // Then
        assertEquals(openIncidents, dto.getOpenIncidents());
        assertEquals(inProgressIncidents, dto.getInProgressIncidents());
        assertEquals(resolvedIncidents, dto.getResolvedIncidents());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        IncidentStatusDistributionDTO dto = new IncidentStatusDistributionDTO();

        // When
        dto.setOpenIncidents(5);
        dto.setInProgressIncidents(3);
        dto.setResolvedIncidents(2);

        // Then
        assertEquals(5, dto.getOpenIncidents());
        assertEquals(3, dto.getInProgressIncidents());
        assertEquals(2, dto.getResolvedIncidents());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        IncidentStatusDistributionDTO dto1 = new IncidentStatusDistributionDTO(5, 3, 2);
        IncidentStatusDistributionDTO dto2 = new IncidentStatusDistributionDTO(5, 3, 2);
        IncidentStatusDistributionDTO dto3 = new IncidentStatusDistributionDTO(10, 6, 4);

        // Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
        assertNotEquals(dto1, dto3);
        assertNotEquals(dto1.hashCode(), dto3.hashCode());
    }

    @Test
    void testToString() {
        // Given
        IncidentStatusDistributionDTO dto = new IncidentStatusDistributionDTO(5, 3, 2);

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("openIncidents=5"));
        assertTrue(result.contains("inProgressIncidents=3"));
        assertTrue(result.contains("resolvedIncidents=2"));
    }

    @Test
    void testEqualsWithSameObject() {
        // Given
        IncidentStatusDistributionDTO dto = new IncidentStatusDistributionDTO(5, 3, 2);

        // Then
        assertEquals(dto, dto);
    }

    @Test
    void testEqualsWithNull() {
        // Given
        IncidentStatusDistributionDTO dto = new IncidentStatusDistributionDTO(5, 3, 2);

        // Then
        assertNotEquals(null, dto);
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Given
        IncidentStatusDistributionDTO dto = new IncidentStatusDistributionDTO(5, 3, 2);

        // Then
        assertNotEquals("Not a DTO", dto);
    }
}
