package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;
import otbs.ms_incident.mapper.IncidentMapper;
import otbs.ms_incident.repository.IncidentRepository;

import java.time.LocalDate;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
 class IncidentServiceBasicTest {

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private IncidentMapper incidentMapper;

    @InjectMocks
    private IncidentServiceImpl incidentService;

    private Incident incident1, incident2;
    private IncidentResponseDTO responseDTO1, responseDTO2;

    @BeforeEach
    void setUp() {
        // Setup test incidents
        incident1 = new Incident();
        incident1.setId(1L);
        incident1.setType(TypeIncident.ACCIDENT);
        incident1.setStatus(StatusIncident.A_TRAITER);
        incident1.setPriorite(NiveauPrioriteIncident.CRITIQUE);
        incident1.setLieu("Paris");
        incident1.setDate(LocalDate.of(2023, 5, 15));

        incident2 = new Incident();
        incident2.setId(2L);
        incident2.setType(TypeIncident.PANNE);
        incident2.setStatus(StatusIncident.EN_COURS_TRAITEMENT);
        incident2.setPriorite(NiveauPrioriteIncident.MOYEN);
        incident2.setLieu("Lyon");
        incident2.setDate(LocalDate.of(2023, 6, 20));

        // Setup response DTOs
        responseDTO1 = new IncidentResponseDTO();
        responseDTO1.setId(1L);
        responseDTO1.setType(TypeIncident.ACCIDENT);
        responseDTO1.setStatus(StatusIncident.A_TRAITER);
        responseDTO1.setLieu("Paris");

        responseDTO2 = new IncidentResponseDTO();
        responseDTO2.setId(2L);
        responseDTO2.setType(TypeIncident.PANNE);
        responseDTO2.setStatus(StatusIncident.EN_COURS_TRAITEMENT);
        responseDTO2.setLieu("Lyon");
    }

    // Test supprimé car la méthode getIncidentsByType a été supprimée

    // Test supprimé car la méthode getIncidentsByStatus a été supprimée

    @Test
    void getIncidentsByLieu_shouldReturnFilteredIncidents() {
        // Given
        List<Incident> incidents = List.of(incident1);
        when(incidentRepository.findByLieuContaining("Paris")).thenReturn(incidents);
        when(incidentMapper.toResponseDTOList(incidents)).thenReturn(List.of(responseDTO1));

        // When
        List<IncidentResponseDTO> result = incidentService.getIncidentsByLieu("Paris");

        // Then
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
    }

    // Test supprimé car la méthode getIncidentsByPriority a été supprimée

    @Test
    void getIncidentsByDateRange_shouldReturnFilteredIncidents() {
        // Given
        LocalDate startDate = LocalDate.of(2023, 5, 1);
        LocalDate endDate = LocalDate.of(2023, 5, 31);
        List<Incident> incidents = List.of(incident1);

        when(incidentRepository.findByDateBetween(startDate, endDate)).thenReturn(incidents);
        when(incidentMapper.toResponseDTOList(incidents)).thenReturn(List.of(responseDTO1));

        // When
        List<IncidentResponseDTO> result = incidentService.getIncidentsByDateRange(startDate, endDate);

        // Then
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
    }
}
