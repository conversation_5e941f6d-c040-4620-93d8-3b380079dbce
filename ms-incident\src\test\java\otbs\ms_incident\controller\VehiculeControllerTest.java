package otbs.ms_incident.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import otbs.ms_incident.client.VehiculeClient;
import otbs.ms_incident.client.VehiculeDto;
import otbs.ms_incident.service.IncidentService;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class VehiculeControllerTest {

    @Mock
    private VehiculeClient vehiculeClient;

    @Mock
    private IncidentService incidentService;

    @InjectMocks
    private VehiculeController vehiculeController;

    private VehiculeDto vehiculeDto;
    private Long vehiculeId;
    private String immatriculation;
    private String etat;

    @BeforeEach
    void setUp() {
        vehiculeId = 1L;
        immatriculation = "ABC-123-XY";
        etat = "DISPONIBLE";

        // Créer un DTO de véhicule
        vehiculeDto = new VehiculeDto();
        vehiculeDto.setIdVehicule(vehiculeId);
        vehiculeDto.setImmatriculation(immatriculation);
        vehiculeDto.setMarque("Renault");
        vehiculeDto.setModele("Clio");
        vehiculeDto.setEtat(etat);
        vehiculeDto.setKilometrage(50000);
        vehiculeDto.setCategorie("Berline");
        vehiculeDto.setDatePrelevementKilometrage(new Date());
        vehiculeDto.setTypeCarburant("Essence");
        vehiculeDto.setNombreDePlaces(5);
        vehiculeDto.setRemisCles(true);
        vehiculeDto.setAstreinte(false);
    }

    @Test
    void getAllVehicules_shouldReturnAllVehicules() {
        // Given
        List<VehiculeDto> vehicules = Arrays.asList(vehiculeDto);
        when(vehiculeClient.getAllVehicules()).thenReturn(vehicules);

        // When
        ResponseEntity<List<VehiculeDto>> response = vehiculeController.getAllVehicules();

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(vehicules, response.getBody());
        assertEquals(1, response.getBody().size());
        verify(vehiculeClient).getAllVehicules();
    }

    @Test
    void getAllVehiculesFallback_shouldReturnServiceUnavailable() {
        // Given
        Exception exception = new RuntimeException("Service unavailable");

        // When
        ResponseEntity<List<VehiculeDto>> response = vehiculeController.getAllVehiculesFallback(exception);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    void getVehiculeById_shouldReturnVehicule_whenFound() {
        // Given
        when(vehiculeClient.getVehiculeById(vehiculeId)).thenReturn(vehiculeDto);

        // When
        ResponseEntity<VehiculeDto> response = vehiculeController.getVehiculeById(vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(vehiculeDto, response.getBody());
        verify(vehiculeClient).getVehiculeById(vehiculeId);
    }

    @Test
    void getVehiculeById_shouldReturnNotFound_whenNotFound() {
        // Given
        when(vehiculeClient.getVehiculeById(vehiculeId)).thenReturn(null);

        // When
        ResponseEntity<VehiculeDto> response = vehiculeController.getVehiculeById(vehiculeId);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        verify(vehiculeClient).getVehiculeById(vehiculeId);
    }

    @Test
    void getVehiculeByIdFallback_shouldReturnServiceUnavailable() {
        // Given
        Exception exception = new RuntimeException("Service unavailable");

        // When
        ResponseEntity<VehiculeDto> response = vehiculeController.getVehiculeByIdFallback(vehiculeId, exception);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    void getVehiculeByImmatriculation_shouldReturnVehicule_whenFound() {
        // Given
        when(vehiculeClient.getVehiculeByImmatriculation(immatriculation)).thenReturn(vehiculeDto);

        // When
        ResponseEntity<VehiculeDto> response = vehiculeController.getVehiculeByImmatriculation(immatriculation);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(vehiculeDto, response.getBody());
        verify(vehiculeClient).getVehiculeByImmatriculation(immatriculation);
    }

    @Test
    void getVehiculeByImmatriculation_shouldReturnNotFound_whenNotFound() {
        // Given
        when(vehiculeClient.getVehiculeByImmatriculation(immatriculation)).thenReturn(null);

        // When
        ResponseEntity<VehiculeDto> response = vehiculeController.getVehiculeByImmatriculation(immatriculation);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());
        verify(vehiculeClient).getVehiculeByImmatriculation(immatriculation);
    }

    @Test
    void getVehiculeByImmatriculationFallback_shouldReturnServiceUnavailable() {
        // Given
        Exception exception = new RuntimeException("Service unavailable");

        // When
        ResponseEntity<VehiculeDto> response = vehiculeController.getVehiculeByImmatriculationFallback(immatriculation, exception);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    void getVehiculesByEtat_shouldReturnFilteredVehicules() {
        // Given
        VehiculeDto vehiculeDto2 = new VehiculeDto();
        vehiculeDto2.setIdVehicule(2L);
        vehiculeDto2.setImmatriculation("DEF-456-ZZ");
        vehiculeDto2.setEtat("EN_MAINTENANCE");

        List<VehiculeDto> allVehicules = Arrays.asList(vehiculeDto, vehiculeDto2);
        when(vehiculeClient.getAllVehicules()).thenReturn(allVehicules);

        // When
        ResponseEntity<List<VehiculeDto>> response = vehiculeController.getVehiculesByEtat(etat);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().size());
        assertEquals(vehiculeDto, response.getBody().get(0));
        verify(vehiculeClient).getAllVehicules();
    }

    @Test
    void getVehiculesByEtatFallback_shouldReturnServiceUnavailable() {
        // Given
        Exception exception = new RuntimeException("Service unavailable");

        // When
        ResponseEntity<List<VehiculeDto>> response = vehiculeController.getVehiculesByEtatFallback(etat, exception);

        // Then
        assertNotNull(response);
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());
        assertNull(response.getBody());
    }
}
