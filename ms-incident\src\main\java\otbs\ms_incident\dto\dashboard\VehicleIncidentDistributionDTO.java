package otbs.ms_incident.dto.dashboard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * DTO pour la répartition des incidents par véhicule
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VehicleIncidentDistributionDTO {
    private Map<Long, Long> countByVehicle;
    private Map<Long, String> vehicleImmatriculations;
}
