package otbs.ms_incident.dto;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import otbs.ms_incident.enums.NiveauPrioriteIncident;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.TypeIncident;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test for IncidentRequestDTO builder
 */
class IncidentRequestDTOBuilderTest {

    @Test
    void testBuilder_withAllFields() {
        // Given
        Long vehiculeId = 1L;
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        StatusIncident status = StatusIncident.A_TRAITER;
        NiveauPrioriteIncident priorite = NiveauPrioriteIncident.MOYEN;
        String lieu = "Paris";
        String description = "Description de l'incident";
        String constat = "constat.pdf";
        List<String> photos = Arrays.asList("photo1.jpg", "photo2.jpg");
        List<Long> reparationIds = Arrays.asList(1L, 2L);

        // When
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .vehiculeId(vehiculeId)
                .date(date)
                .type(type)
                .status(status)
                .priorite(priorite)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparationIds(reparationIds)
                .build();

        // Then
        assertEquals(vehiculeId, dto.getVehiculeId());
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertEquals(status, dto.getStatus());
        assertEquals(priorite, dto.getPriorite());
        assertEquals(lieu, dto.getLieu());
        assertEquals(description, dto.getDescription());
        assertEquals(constat, dto.getConstat());
        assertEquals(photos, dto.getPhotos());
        assertEquals(reparationIds, dto.getReparationIds());
    }

    @Test
    void testBuilder_withMinimalRequiredFields() {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";

        // When
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .build();

        // Then
        assertNull(dto.getVehiculeId());
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertNull(dto.getStatus());
        assertEquals(NiveauPrioriteIncident.FAIBLE, dto.getPriorite()); // Default value
        assertEquals(lieu, dto.getLieu());
        assertNull(dto.getDescription());
        assertNull(dto.getConstat());
        assertNotNull(dto.getPhotos());
        assertTrue(dto.getPhotos().isEmpty());
        assertNotNull(dto.getReparationIds());
        assertTrue(dto.getReparationIds().isEmpty());
    }

    @ParameterizedTest
    @EnumSource(TypeIncident.class)
    void testBuilder_withDifferentTypeValues(TypeIncident type) {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        String lieu = "Paris";

        // When
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .build();

        // Then
        assertEquals(type, dto.getType());
    }

    @ParameterizedTest
    @EnumSource(StatusIncident.class)
    void testBuilder_withDifferentStatusValues(StatusIncident status) {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";

        // When
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .status(status)
                .build();

        // Then
        assertEquals(status, dto.getStatus());
    }

    @ParameterizedTest
    @EnumSource(NiveauPrioriteIncident.class)
    void testBuilder_withDifferentPrioriteValues(NiveauPrioriteIncident priorite) {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";

        // When
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .priorite(priorite)
                .build();

        // Then
        assertEquals(priorite, dto.getPriorite());
    }

    @Test
    void testBuilder_withEmptyPhotos() {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";
        List<String> photos = new ArrayList<>();

        // When
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .photos(photos)
                .build();

        // Then
        assertNotNull(dto.getPhotos());
        assertTrue(dto.getPhotos().isEmpty());
    }

    @Test
    void testBuilder_withEmptyReparationIds() {
        // Given
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";
        List<Long> reparationIds = new ArrayList<>();

        // When
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .date(date)
                .type(type)
                .lieu(lieu)
                .reparationIds(reparationIds)
                .build();

        // Then
        assertNotNull(dto.getReparationIds());
        assertTrue(dto.getReparationIds().isEmpty());
    }

    @Test
    void testToString_shouldNotThrowException() {
        // Given
        IncidentRequestDTO dto = IncidentRequestDTO.builder()
                .vehiculeId(1L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        // When & Then
        assertDoesNotThrow(() -> {
            String result = dto.toString();
            assertNotNull(result);
            assertFalse(result.isEmpty());
        });
    }
}
