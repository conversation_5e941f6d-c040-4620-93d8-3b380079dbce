image: maven:3-eclipse-temurin-17   # Spécifier l'image de Maven avec OpenJDK 17

variables:
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Définit l'emplacement du cache de la tâche d'analyse
  GIT_DEPTH: "0"  # Dit à git de récupérer toutes les branches du projet, nécessaire pour la tâche d'analyse

stages:
  - sonarqube-check
  - sonarqube-vulnerability-report
  - package
  - build_deploy

# Etape d'analyse SonarQube
sonarqube-check:
  stage: sonarqube-check
  tags:
    - sonarqube-runner
  script: 
    - mvn verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar   # Exécute l'analyse SonarQube
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == 'master'
    - if: $CI_COMMIT_BRANCH == 'main'
    - if: $CI_COMMIT_BRANCH == 'develop'

# Etape pour générer le rapport de vulnérabilité SonarQube
sonarqube-vulnerability-report:
  stage: sonarqube-vulnerability-report
  script:
    - 'curl -u "${SONAR_TOKEN}:" "${SONAR_HOST_URL}/api/issues/gitlab_sast_export?projectKey=config_server&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" -o gl-sast-sonar-report.json'
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
    - if: $CI_COMMIT_BRANCH == 'master'
    - if: $CI_COMMIT_BRANCH == 'main'
    - if: $CI_COMMIT_BRANCH == 'develop'
  artifacts:
    expire_in: 1 day
    reports:
      sast: gl-sast-sonar-report.json   # Génère un rapport SAST

# Etape de packaging (construction du JAR avec Maven)
package:
  stage: package
  tags:
    - sonarqube-runner
  script:
    - mvn package -DskipTests   # Construire le package en sautant les tests
  artifacts:
    paths:
      - target/*.jar   # Spécifie le chemin vers le fichier JAR généré
    expire_in: 1 week  # Durée de rétention de l'artefact

# Etape de construction et déploiement du conteneur
build_deploy:
  stage: build_deploy
  tags:
    - sonarqube-runner
  script:
    - sudo podman build -t config-server .    # Construire l'image du conteneur avec Podman
    - sudo podman run -d --replace --name config-server -p 9101:9101 localhost/config-server:latest
    - sudo podman generate systemd --name config-server --new --files   # Générer un fichier de service systemd pour le conteneur
    - sudo systemctl enable /home/<USER>/config-server/container-config-server.service  # Activer le service
    - sudo systemctl start container-config-server.service  # Démarrer le service
    - sudo systemctl status container-config-server.service  # Vérifier le statut du service

  

