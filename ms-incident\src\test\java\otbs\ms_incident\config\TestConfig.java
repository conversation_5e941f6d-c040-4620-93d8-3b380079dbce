package otbs.ms_incident.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.web.client.RestTemplate;
import org.mockito.Mockito;
import org.springframework.cloud.client.discovery.DiscoveryClient;

/**
 * Test configuration that provides necessary mocks for integration tests.
 * This configuration is only active when the "test" profile is active.
 */
@TestConfiguration
@Profile("test")
class TestConfig {

    /**
     * Mock DiscoveryClient to avoid the need for Eureka connection during tests
     */
    @Bean
    @Primary
    public DiscoveryClient discoveryClient() {
        return Mockito.mock(DiscoveryClient.class);
    }
    
    /**
     * Provide a RestTemplate bean for tests
     */
    @Bean
    @Primary
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
} 