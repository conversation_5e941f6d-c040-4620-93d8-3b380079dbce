package otbs.ms_astreint.client.keycloak;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/keycloak")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Keycloak", description = "API de gestion des utilisateurs via Keycloak")
public class KeycloakController {

    private final KeycloakService keycloakService;

    @GetMapping("/users/selector-options")
    @Operation(summary = "Récupérer les options pour un sélecteur d'utilisateurs")
    public ResponseEntity<List<UserSelectorOptionDTO>> getUsersForSelector() {
        log.info("Récupération des options de sélecteur d'utilisateurs");
        List<UserSelectorOptionDTO> options = keycloakService.getUsersForSelector();
        log.info("Nombre d'options disponibles: {}", options.size());
        return ResponseEntity.ok(options);
    }
} 