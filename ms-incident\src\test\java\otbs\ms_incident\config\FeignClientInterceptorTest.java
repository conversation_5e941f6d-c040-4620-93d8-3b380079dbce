package otbs.ms_incident.config;

import feign.RequestTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FeignClientInterceptorTest {

    @Mock
    private RequestTemplate requestTemplate;

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    @Mock
    private JwtAuthenticationToken jwtAuthenticationToken;

    @Mock
    private Jwt jwt;

    private FeignClientInterceptor interceptor;

    @BeforeEach
    void setUp() {
        interceptor = new FeignClientInterceptor();
        SecurityContextHolder.setContext(securityContext);
    }

    @Test
    void apply_shouldAddAuthorizationHeader_whenJwtAuthenticationTokenIsPresent() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(jwtAuthenticationToken);
        when(jwtAuthenticationToken.getToken()).thenReturn(jwt);
        when(jwt.getTokenValue()).thenReturn("test-token");

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate).header("Authorization", "Bearer test-token");
    }

    @Test
    void apply_shouldNotAddAuthorizationHeader_whenAuthenticationIsNotJwtAuthenticationToken() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(authentication);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq("Authorization"), anyString());
    }

    @Test
    void apply_shouldNotAddAuthorizationHeader_whenAuthenticationIsNull() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(null);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq("Authorization"), anyString());
    }
}
