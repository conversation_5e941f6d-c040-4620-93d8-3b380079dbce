package otbs.ms_astreint.client.keycloak;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.admin.client.resource.UsersResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
public class KeycloakService {

    private final Keycloak keycloak;

    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String issuerUri;

    private String getTargetRealm() {
        return issuerUri.substring(issuerUri.lastIndexOf("/") + 1);
    }

    @Cacheable(value = "userInfo", key = "#userId")
    public Optional<UserDTO> getUserInfo(String userId) {
        return getUserById(userId);
    }

    @Cacheable(value = "userNames", key = "#userId")
    public String getFullNameFromUserId(String userId) {
        return getUserById(userId)
                .map(UserDTO::getFullName)
                .orElse(userId);
    }

    public boolean isValidConsultant(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        
        return getUserInfo(userId)
                .map(UserDTO::isEnabled)
                .orElse(false);
    }
    
    @Cacheable("users")
    public List<UserDTO> getAllUsers() {
        try {
            String realm = getTargetRealm();
            UsersResource usersResource = keycloak.realm(realm).users();
            List<UserRepresentation> users = usersResource.list();

            return users.stream()
                    .map(this::mapToUserDTO)
                    .toList();
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des utilisateurs depuis Keycloak: {}", e.getMessage());
            return List.of();
        }
    }

    @Cacheable(value = "users", key = "#id")
    public Optional<UserDTO> getUserById(String id) {
        try {
            String realm = getTargetRealm();
            UserResource userResource = keycloak.realm(realm).users().get(id);
            UserRepresentation userRepresentation = userResource.toRepresentation();

            if (userRepresentation != null) {
                return Optional.of(mapToUserDTO(userRepresentation));
            }
            return Optional.empty();
        } catch (Exception e) {
            log.error("Erreur lors de la récupération de l'utilisateur {}: {}", id, e.getMessage());
            return Optional.empty();
        }
    }

    @Cacheable("userSelectors")
    public List<UserSelectorOptionDTO> getUsersForSelector() {
        return getAllUsers().stream()
                .filter(UserDTO::isEnabled)
                .map(user -> UserSelectorOptionDTO.builder()
                        .id(user.getId())
                        .fullName(user.getFullName())
                        .email(user.getEmail())
                        .build())
                .toList();
    }

    private UserDTO mapToUserDTO(UserRepresentation userRepresentation) {
        LocalDateTime createdTimestamp = null;
        if (userRepresentation.getCreatedTimestamp() != null) {
            createdTimestamp = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(userRepresentation.getCreatedTimestamp()),
                    ZoneId.systemDefault()
            );
        }

        return UserDTO.builder()
                .id(userRepresentation.getId())
                .username(userRepresentation.getUsername())
                .email(userRepresentation.getEmail())
                .firstName(userRepresentation.getFirstName())
                .lastName(userRepresentation.getLastName())
                .enabled(userRepresentation.isEnabled())
                .emailVerified(userRepresentation.isEmailVerified())
                .createdAt(createdTimestamp)
                .attributes(userRepresentation.getAttributes())
                .build();
    }
} 