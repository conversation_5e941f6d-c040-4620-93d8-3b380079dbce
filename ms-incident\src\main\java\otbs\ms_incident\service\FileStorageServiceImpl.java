package otbs.ms_incident.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import otbs.ms_incident.exception.FileNotFoundStorageException;
import otbs.ms_incident.exception.FileStorageException;
import otbs.ms_incident.exception.InvalidFileTypeException;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * Implémentation du service de stockage de fichiers
 */
@Service
public class FileStorageServiceImpl implements FileStorageService {
    private static final Logger logger = LoggerFactory.getLogger(FileStorageServiceImpl.class);
    
    // Constants for log messages and repeated values
    private static final String LOG_FILE_SAVED = "File saved successfully: {}";
    private static final String LOG_INVALID_FILE_TYPE = "Invalid file type: {}";
    
    @Value("${storage.path:./files}")
    private String storagePath;
    
    // Common file extensions that are considered safe
    private static final Set<String> ALLOWED_EXTENSIONS = new HashSet<>(Arrays.asList(
        "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "csv", 
        "jpg", "jpeg", "png", "gif", "zip", "rar", "7z"
    ));

    /**
     * Saves a file to the specified path
     * 
     * @param filePath Path where the file should be saved
     * @param fileBytes Contents of the file as a byte array
     * @throws FileStorageException If file cannot be written
     */
    @Override
    public void save(Path filePath, byte[] fileBytes) {
        logger.info("Saving file to: {}", filePath);
        try {
            Files.createDirectories(filePath.getParent());
            Files.write(filePath, fileBytes);
            logger.info(LOG_FILE_SAVED, filePath.getFileName());
        } catch (IOException e) {
            String errorMsg = "Failed to save file: " + filePath.getFileName();
            throw new FileStorageException(
                errorMsg, 
                e,
                HttpStatus.INTERNAL_SERVER_ERROR,
                "FILE_SAVE_ERROR"
            );
        }
    }
    
    /**
     * Saves a file to the specified path with option to overwrite existing file
     * 
     * @param filePath Path where the file should be saved
     * @param fileBytes Contents of the file as a byte array
     * @param overwrite Whether to overwrite if file exists
     * @throws FileStorageException If file cannot be written
     */
    @Override
    public void save(Path filePath, byte[] fileBytes, boolean overwrite) {
        logger.info("Saving file to: {} (overwrite: {})", filePath, overwrite);
        try {
            Files.createDirectories(filePath.getParent());
            writeFileWithOverwriteOption(filePath, fileBytes, overwrite);
            logger.info(LOG_FILE_SAVED, filePath.getFileName());
        } catch (IOException e) {
            String errorMsg = "Failed to save file: " + filePath.getFileName();
            throw new FileStorageException(
                errorMsg, 
                e,
                HttpStatus.INTERNAL_SERVER_ERROR,
                "FILE_SAVE_ERROR"
            );
        }
    }
    
    /**
     * Helper method to write file with overwrite option
     */
    private void writeFileWithOverwriteOption(Path filePath, byte[] fileBytes, boolean overwrite) throws IOException {
        if (overwrite && Files.exists(filePath)) {
            Files.delete(filePath);
            Files.write(filePath, fileBytes);
        } else if (!Files.exists(filePath)) {
            Files.write(filePath, fileBytes);
        } else {
            logger.warn("File already exists and overwrite is false: {}", filePath);
            throw new FileStorageException(
                "File already exists: " + filePath.getFileName(),
                HttpStatus.CONFLICT,
                "FILE_ALREADY_EXISTS"
            );
        }
    }

    /**
     * Loads a file as a Resource
     * 
     * @param filePath Path to the file
     * @return Resource representing the file
     * @throws FileNotFoundStorageException If file cannot be found
     * @throws FileStorageException If file cannot be loaded
     */
    @Override
    public Resource load(Path filePath) {
        try {
            logger.info("Loading file: {}", filePath);
            Resource resource = new UrlResource(filePath.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                logger.info("File loaded successfully: {}", filePath.getFileName());
                return resource;
            } else {
                String errorMsg = "File not found or not readable: " + filePath.getFileName();
                throw new FileNotFoundStorageException(errorMsg);
            }
        } catch (MalformedURLException e) {
            String errorMsg = "Error loading file: " + filePath.getFileName();
            throw new FileStorageException(
                errorMsg, 
                e,
                HttpStatus.INTERNAL_SERVER_ERROR,
                "FILE_URL_ERROR"
            );
        }
    }

    /**
     * Checks if a file exists
     * 
     * @param filePath Path to check
     * @return true if file exists, false otherwise
     */
    @Override
    public boolean exists(Path filePath) {
        boolean exists = Files.exists(filePath);
        logger.debug("Checking if file exists: {} - {}", filePath, exists);
        return exists;
    }

    /**
     * Supprime un fichier
     * 
     * @param path Chemin du fichier à supprimer
     * @return true si la suppression est réussie, false en cas d'échec
     */
    @Override
    public boolean delete(Path path) {
        logger.info("Deleting file: {}", path);
        try {
            if (Files.exists(path)) {
                Files.delete(path);
                logger.info("File deleted successfully: {}", path.getFileName());
                return true;
            } else {
                logger.warn("File not found for deletion: {}", path);
                return false;
            }
        } catch (IOException e) {
            // Log and handle the exception but don't rethrow
            logger.error("Failed to delete file: {} - {}", path, e.getMessage());
            return false;
        }
    }
    
    /**
     * Validates file extension against allowed extensions
     * 
     * @param filename Name of the file to validate
     * @return true if file extension is allowed
     * @throws InvalidFileTypeException if file extension is not allowed
     */
    @Override
    public boolean isValidFileExtension(String filename) {
        if (filename == null || filename.lastIndexOf(".") == -1) {
            logger.warn("Filename is null or has no extension: {}", filename);
            throw new InvalidFileTypeException("Filename is null or has no extension");
        }
        
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        boolean isValid = ALLOWED_EXTENSIONS.contains(extension);
        
        if (!isValid) {
            logger.warn(LOG_INVALID_FILE_TYPE, extension);
            throw new InvalidFileTypeException("File type not allowed for security reasons", extension);
        }
        
        return isValid;
    }
    
    /**
     * Stocke un fichier à l'emplacement spécifié
     * 
     * @param file Fichier à stocker
     * @param path Chemin où stocker le fichier
     * @return Chemin complet du fichier stocké
     */
    @Override
    public Path store(MultipartFile file, String path) {
        logger.info("Storing file to path: {}", path);
        try {
            validateFileForStorage(file, path);
            return storeFileInternal(file, path);
        } catch (IOException e) {
            String errorMsg = "Failed to store file: " + e.getMessage();
            throw new FileStorageException(errorMsg, e, HttpStatus.INTERNAL_SERVER_ERROR, "FILE_STORAGE_ERROR");
        }
    }
    
    /**
     * Validates a file before storage
     */
    private void validateFileForStorage(MultipartFile file, String path) {
        if (file.isEmpty()) {
            logger.warn("Attempted to store empty file at path: {}", path);
            throw new FileStorageException("Failed to store empty file", HttpStatus.BAD_REQUEST, "EMPTY_FILE");
        }
        
        Path destinationFile = Paths.get(path).normalize();
        
        // Vérifier que le chemin est sécurisé
        if (destinationFile.getFileName().toString().contains("..")) {
            logger.warn("Invalid file path detected: {}", destinationFile.getFileName());
            throw new FileStorageException(
                "Invalid file path: " + destinationFile.getFileName(), 
                HttpStatus.BAD_REQUEST,
                "INVALID_PATH"
            );
        }
    }
    
    /**
     * Handles the actual file storage operation
     */
    private Path storeFileInternal(MultipartFile file, String path) throws IOException {
        Path destinationFile = Paths.get(path).normalize();
        
        // Création des répertoires si nécessaire
        Files.createDirectories(destinationFile.getParent());
        
        // Copie du fichier
        Files.copy(file.getInputStream(), destinationFile);
        
        logger.info("Successfully stored file to {}", destinationFile);
        return destinationFile;
    }
    
    /**
     * Crée un répertoire et ses parents si nécessaire
     * 
     * @param directory Chemin du répertoire à créer
     * @throws IOException Si la création échoue
     */
    @Override
    public void createDirectories(Path directory) throws IOException {
        logger.info("Creating directories: {}", directory);
        try {
            Files.createDirectories(directory);
            logger.info("Directories created successfully: {}", directory);
        } catch (IOException e) {
            // Wrap the exception with more context and propagate
            throw new IOException("Failed to create directories: " + directory, e);
        }
    }
    
    /**
     * Charge un fichier en tant que ressource à partir d'un chemin sous forme de chaîne
     * 
     * @param path Chemin du fichier à charger sous forme de chaîne
     * @return Ressource représentant le fichier
     */
    @Override
    public Resource loadFileAsResource(String path) {
        try {
            Path filePath = Paths.get(path).normalize();
            logger.debug("Loading file as resource: {}", filePath);
            Resource resource = new UrlResource(filePath.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                logger.info("File loaded successfully as resource: {}", filePath);
                return resource;
            } else {
                String errorMsg = "File not found: " + filePath;
                throw new FileNotFoundStorageException(errorMsg);
            }
        } catch (MalformedURLException e) {
            String errorMsg = "Error loading file as resource: " + path;
            throw new FileNotFoundStorageException(errorMsg, e);
        }
    }
    
    /**
     * Retourne le chemin de base du stockage de fichiers
     * 
     * @return Chemin de base du stockage sous forme de chaîne
     */
    @Override
    public String getStoragePath() {
        return this.storagePath;
    }
} 