package otbs.ms_incident.dto;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import otbs.ms_incident.enums.StatusReparation;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test for ReparationResponseDTO builder
 */
class ReparationResponseDTOBuilderTest {

    @Test
    void testBuilder_withAllFields() {
        // Given
        Long id = 1L;
        Long incidentId = 100L;
        Long vehiculeId = 200L;
        String immatriculation = "AB-123-CD";
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        StatusReparation status = StatusReparation.EN_COURS;
        String description = "Réparation du pare-choc";
        BigDecimal cout = new BigDecimal("1500.00");
        String garage = "Garage Central";
        Boolean rembourse = true;
        BigDecimal montantCouverture = new BigDecimal("1000.00");
        LocalDateTime createdAt = LocalDateTime.of(2023, 6, 15, 10, 30);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 6, 16, 11, 45);


        // When
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(id)
                .incidentId(incidentId)
                .vehiculeId(vehiculeId)
                .immatriculation(immatriculation)
                .dateReparation(dateReparation)
                .status(status)
                .description(description)
                .cout(cout)
                .garage(garage)
                .rembourse(rembourse)
                .montantCouverture(montantCouverture)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        // Then
        assertEquals(id, dto.getId());
        assertEquals(incidentId, dto.getIncidentId());
        assertEquals(vehiculeId, dto.getVehiculeId());
        assertEquals(immatriculation, dto.getImmatriculation());
        assertEquals(dateReparation, dto.getDateReparation());
        assertEquals(status, dto.getStatus());
        assertEquals(description, dto.getDescription());
        assertEquals(cout, dto.getCout());
        assertEquals(garage, dto.getGarage());
        assertEquals(rembourse, dto.getRembourse());
        assertEquals(montantCouverture, dto.getMontantCouverture());
        assertEquals(createdAt, dto.getCreatedAt());
        assertEquals(updatedAt, dto.getUpdatedAt());
    }

    @Test
    void testBuilder_withMinimalFields() {
        // Given
        Long id = 1L;
        Long incidentId = 100L;
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = new BigDecimal("1500.00");

        // When
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(id)
                .incidentId(incidentId)
                .dateReparation(dateReparation)
                .cout(cout)
                .build();

        // Then
        assertEquals(id, dto.getId());
        assertEquals(incidentId, dto.getIncidentId());
        assertNull(dto.getVehiculeId());
        assertNull(dto.getImmatriculation());
        assertEquals(dateReparation, dto.getDateReparation());
        assertNull(dto.getStatus());
        assertNull(dto.getDescription());
        assertEquals(cout, dto.getCout());
        assertNull(dto.getGarage());
        assertNull(dto.getRembourse());
        assertNull(dto.getMontantCouverture());
        assertNull(dto.getCreatedAt());
        assertNull(dto.getUpdatedAt());
    }

    @ParameterizedTest
    @EnumSource(StatusReparation.class)
    void testBuilder_withDifferentStatusValues(StatusReparation status) {
        // Given
        Long id = 1L;
        Long incidentId = 100L;
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = new BigDecimal("1500.00");

        // When
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(id)
                .incidentId(incidentId)
                .dateReparation(dateReparation)
                .status(status)
                .cout(cout)
                .build();

        // Then
        assertEquals(status, dto.getStatus());
    }

    @Test
    void testBuilder_withNegativeCost() {
        // Given
        Long id = 1L;
        Long incidentId = 100L;
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = new BigDecimal("-1500.00");

        // When
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(id)
                .incidentId(incidentId)
                .dateReparation(dateReparation)
                .cout(cout)
                .build();

        // Then
        assertEquals(cout, dto.getCout());
    }

    @Test
    void testBuilder_withZeroCost() {
        // Given
        Long id = 1L;
        Long incidentId = 100L;
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = BigDecimal.ZERO;

        // When
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(id)
                .incidentId(incidentId)
                .dateReparation(dateReparation)
                .cout(cout)
                .build();

        // Then
        assertEquals(cout, dto.getCout());
    }

    @Test
    void testBuilder_withRembourseTrue() {
        // Given
        Long id = 1L;
        Long incidentId = 100L;
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = new BigDecimal("1500.00");
        Boolean rembourse = true;

        // When
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(id)
                .incidentId(incidentId)
                .dateReparation(dateReparation)
                .cout(cout)
                .rembourse(rembourse)
                .build();

        // Then
        assertTrue(dto.getRembourse());
    }

    @Test
    void testBuilder_withRembourseNull() {
        // Given
        Long id = 1L;
        Long incidentId = 100L;
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        BigDecimal cout = new BigDecimal("1500.00");
        Boolean rembourse = null;

        // When
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(id)
                .incidentId(incidentId)
                .dateReparation(dateReparation)
                .cout(cout)
                .rembourse(rembourse)
                .build();

        // Then
        assertNull(dto.getRembourse());
    }

    @Test
    void testToString_shouldNotThrowException() {
        // Given
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(1L)
                .incidentId(100L)
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .build();

        // When & Then
        assertDoesNotThrow(() -> {
            String result = dto.toString();
            assertNotNull(result);
            assertFalse(result.isEmpty());
        });
    }

    @Test
    void testEquals_withSameObject() {
        // Given
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(1L)
                .incidentId(100L)
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .build();

        // When & Then
        assertEquals(dto, dto);
    }

    @Test
    void testEquals_withEqualObject() {
        // Given
        ReparationResponseDTO dto1 = ReparationResponseDTO.builder()
                .id(1L)
                .incidentId(100L)
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .build();

        ReparationResponseDTO dto2 = ReparationResponseDTO.builder()
                .id(1L)
                .incidentId(100L)
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .build();

        // When & Then
        assertEquals(dto1, dto2);
        assertEquals(dto1.hashCode(), dto2.hashCode());
    }

    @Test
    void testEquals_withDifferentObject() {
        // Given
        ReparationResponseDTO dto1 = ReparationResponseDTO.builder()
                .id(1L)
                .incidentId(100L)
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .build();

        ReparationResponseDTO dto2 = ReparationResponseDTO.builder()
                .id(2L)
                .incidentId(100L)
                .dateReparation(LocalDate.of(2023, 6, 15))
                .cout(new BigDecimal("1500.00"))
                .build();

        // When & Then
        assertNotEquals(dto1, dto2);
        assertNotEquals(dto1.hashCode(), dto2.hashCode());
    }
}
