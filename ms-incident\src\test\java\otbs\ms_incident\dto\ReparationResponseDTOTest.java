package otbs.ms_incident.dto;

import org.junit.jupiter.api.Test;
import otbs.ms_incident.enums.StatusReparation;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class ReparationResponseDTOTest {

    @Test
    void testNoArgsConstructor() {
        // When
        ReparationResponseDTO dto = new ReparationResponseDTO();

        // Then
        assertNull(dto.getId());
        assertNull(dto.getDateReparation());
        assertNull(dto.getDescription());
        assertNull(dto.getCout());
        assertNull(dto.getGarage());
        assertNull(dto.getIncidentId());
        assertNull(dto.getCreatedAt());
        assertNull(dto.getUpdatedAt());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Long id = 1L;
        LocalDate dateReparation = LocalDate.of(2023, 5, 15);
        String description = "Remplacement pare-choc";
        BigDecimal cout = new BigDecimal("1500.50");
        String garage = "Garage Central";
        Long incidentId = 42L;
        LocalDateTime createdAt = LocalDateTime.of(2023, 5, 15, 10, 30);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 5, 16, 14, 45);

        // When
        ReparationResponseDTO dto = new ReparationResponseDTO(
            id, dateReparation, StatusReparation.EN_COURS, description, cout, garage, incidentId, 101L, "123-ABC", false, BigDecimal.ZERO, createdAt, updatedAt
        );

        // Then
        assertEquals(id, dto.getId());
        assertEquals(dateReparation, dto.getDateReparation());
        assertEquals(description, dto.getDescription());
        assertEquals(cout, dto.getCout());
        assertEquals(garage, dto.getGarage());
        assertEquals(incidentId, dto.getIncidentId());
        assertEquals(101L, dto.getVehiculeId());
        assertEquals("123-ABC", dto.getImmatriculation());
        assertEquals(createdAt, dto.getCreatedAt());
        assertEquals(updatedAt, dto.getUpdatedAt());
    }

    @Test
    void testBuilder() {
        // Given
        Long id = 2L;
        LocalDate dateReparation = LocalDate.of(2023, 6, 20);
        String description = "Changement de phare";
        BigDecimal cout = new BigDecimal("350.75");
        String garage = "Garage Express";
        Long incidentId = 43L;
        LocalDateTime createdAt = LocalDateTime.of(2023, 6, 20, 9, 15);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 6, 21, 11, 30);

        // When
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
            .id(id)
            .dateReparation(dateReparation)
            .description(description)
            .cout(cout)
            .garage(garage)
            .incidentId(incidentId)
            .vehiculeId(101L)
            .immatriculation("123-ABC")
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();

        // Then
        assertEquals(id, dto.getId());
        assertEquals(dateReparation, dto.getDateReparation());
        assertEquals(description, dto.getDescription());
        assertEquals(cout, dto.getCout());
        assertEquals(garage, dto.getGarage());
        assertEquals(incidentId, dto.getIncidentId());
        assertEquals(101L, dto.getVehiculeId());
        assertEquals("123-ABC", dto.getImmatriculation());
        assertEquals(createdAt, dto.getCreatedAt());
        assertEquals(updatedAt, dto.getUpdatedAt());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        ReparationResponseDTO dto = new ReparationResponseDTO();
        Long id = 3L;
        LocalDate dateReparation = LocalDate.of(2023, 7, 10);
        String description = "Remplacement de pneu";
        BigDecimal cout = new BigDecimal("250.00");
        String garage = "Garage Pneus Plus";
        Long incidentId = 44L;
        LocalDateTime createdAt = LocalDateTime.of(2023, 7, 10, 14, 0);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 7, 11, 9, 45);

        // When
        dto.setId(id);
        dto.setDateReparation(dateReparation);
        dto.setDescription(description);
        dto.setCout(cout);
        dto.setGarage(garage);
        dto.setIncidentId(incidentId);
        dto.setVehiculeId(101L);
        dto.setImmatriculation("123-ABC");
        dto.setCreatedAt(createdAt);
        dto.setUpdatedAt(updatedAt);

        // Then
        assertEquals(id, dto.getId());
        assertEquals(dateReparation, dto.getDateReparation());
        assertEquals(description, dto.getDescription());
        assertEquals(cout, dto.getCout());
        assertEquals(garage, dto.getGarage());
        assertEquals(incidentId, dto.getIncidentId());
        assertEquals(101L, dto.getVehiculeId());
        assertEquals("123-ABC", dto.getImmatriculation());
        assertEquals(createdAt, dto.getCreatedAt());
        assertEquals(updatedAt, dto.getUpdatedAt());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        ReparationResponseDTO dto1 = new ReparationResponseDTO();
        dto1.setId(1L);
        dto1.setDateReparation(LocalDate.of(2023, 8, 15));

        ReparationResponseDTO dto2 = new ReparationResponseDTO();
        dto2.setId(1L);
        dto2.setDateReparation(LocalDate.of(2023, 8, 15));

        ReparationResponseDTO dto3 = new ReparationResponseDTO();
        dto3.setId(2L);
        dto3.setDateReparation(LocalDate.of(2023, 8, 16));

        // Then
        // Test reflexivity
        assertEquals(dto1, dto1, "A DTO should equal itself");

        // Test symmetry
        assertEquals(dto1, dto2, "DTOs with equal properties should be equal");
        assertEquals(dto2, dto1, "Equals should be symmetric");

        // Test different objects
        assertNotEquals(dto1, dto3, "DTOs with different IDs should not be equal");

        // Test with null and different types
        assertNotEquals(null, dto1, "A DTO should not equal null");
        assertNotEquals( "Not a DTO",dto1 , "A DTO should not equal a different type");

        // Test hash codes
        assertEquals(dto1.hashCode(), dto2.hashCode(), "Equal DTOs should have the same hash code");
        assertNotEquals(dto1.hashCode(), dto3.hashCode(), "Different DTOs should have different hash codes");
    }

    @Test
    void testToString() {
        // Given
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
            .id(5L)
            .description("Test Description")
            .build();

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("id=5"), "toString should contain the id");
        assertTrue(result.contains("description=Test Description"), "toString should contain the description");
    }

    @Test
    void testBuilderWithNullValues() {
        // When
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
            .id(null)
            .dateReparation(null)
            .description(null)
            .cout(null)
            .garage(null)
            .incidentId(null)
            .vehiculeId(null)
            .immatriculation(null)
            .createdAt(null)
            .updatedAt(null)
            .build();

        // Then
        assertNull(dto.getId());
        assertNull(dto.getDateReparation());
        assertNull(dto.getDescription());
        assertNull(dto.getCout());
        assertNull(dto.getGarage());
        assertNull(dto.getIncidentId());
        assertNull(dto.getVehiculeId());
        assertNull(dto.getImmatriculation());
        assertNull(dto.getCreatedAt());
        assertNull(dto.getUpdatedAt());
    }

    @Test
    void testEqualsWithDifferentFields() {
        // Given - create DTOs with different fields
        LocalDateTime baseCreatedAt = LocalDateTime.of(2023, 10, 1, 10, 0);
        LocalDateTime baseUpdatedAt = LocalDateTime.of(2023, 10, 2, 10, 0);

        ReparationResponseDTO base = ReparationResponseDTO.builder()
            .id(10L)
            .dateReparation(LocalDate.of(2023, 10, 1))
            .description("Base description")
            .cout(new BigDecimal("500.00"))
            .garage("Base Garage")
            .incidentId(100L)
            .vehiculeId(101L)
            .immatriculation("123-ABC")
            .createdAt(baseCreatedAt)
            .updatedAt(baseUpdatedAt)
            .build();

        // Same ID but different dateReparation
        ReparationResponseDTO diffDate = ReparationResponseDTO.builder()
            .id(10L)
            .dateReparation(LocalDate.of(2023, 10, 2))
            .description("Base description")
            .cout(new BigDecimal("500.00"))
            .garage("Base Garage")
            .incidentId(100L)
            .vehiculeId(101L)
            .immatriculation("123-ABC")
            .createdAt(baseCreatedAt)
            .updatedAt(baseUpdatedAt)
            .build();

        // Same ID but different description
        ReparationResponseDTO diffDesc = ReparationResponseDTO.builder()
            .id(10L)
            .dateReparation(LocalDate.of(2023, 10, 1))
            .description("Different description")
            .cout(new BigDecimal("500.00"))
            .garage("Base Garage")
            .incidentId(100L)
            .vehiculeId(101L)
            .immatriculation("123-ABC")
            .createdAt(baseCreatedAt)
            .updatedAt(baseUpdatedAt)
            .build();

        // Same ID but different cout
        ReparationResponseDTO diffCout = ReparationResponseDTO.builder()
            .id(10L)
            .dateReparation(LocalDate.of(2023, 10, 1))
            .description("Base description")
            .cout(new BigDecimal("600.00"))
            .garage("Base Garage")
            .incidentId(100L)
            .vehiculeId(101L)
            .immatriculation("123-ABC")
            .createdAt(baseCreatedAt)
            .updatedAt(baseUpdatedAt)
            .build();

        // Same ID but different garage
        ReparationResponseDTO diffGarage = ReparationResponseDTO.builder()
            .id(10L)
            .dateReparation(LocalDate.of(2023, 10, 1))
            .description("Base description")
            .cout(new BigDecimal("500.00"))
            .garage("Different Garage")
            .incidentId(100L)
            .vehiculeId(101L)
            .immatriculation("123-ABC")
            .createdAt(baseCreatedAt)
            .updatedAt(baseUpdatedAt)
            .build();

        // Same ID but different incidentId
        ReparationResponseDTO diffIncidentId = ReparationResponseDTO.builder()
            .id(10L)
            .dateReparation(LocalDate.of(2023, 10, 1))
            .description("Base description")
            .cout(new BigDecimal("500.00"))
            .garage("Base Garage")
            .incidentId(101L)
            .vehiculeId(101L)
            .immatriculation("123-ABC")
            .createdAt(baseCreatedAt)
            .updatedAt(baseUpdatedAt)
            .build();

        // Different timestamps
        ReparationResponseDTO diffTimestamps = ReparationResponseDTO.builder()
            .id(10L)
            .dateReparation(LocalDate.of(2023, 10, 1))
            .description("Base description")
            .cout(new BigDecimal("500.00"))
            .garage("Base Garage")
            .incidentId(100L)
            .vehiculeId(101L)
            .immatriculation("123-ABC")
            .createdAt(LocalDateTime.of(2023, 11, 1, 10, 0))
            .updatedAt(LocalDateTime.of(2023, 11, 2, 10, 0))
            .build();

        // Then - verify equals behavior
        // Different date
        assertNotEquals(base, diffDate, "DTOs with different dates should not be equal");
        assertNotEquals(base.hashCode(), diffDate.hashCode(), "Hash codes should differ for DTOs with different dates");

        // Different description
        assertNotEquals(base, diffDesc, "DTOs with different descriptions should not be equal");
        assertNotEquals(base.hashCode(), diffDesc.hashCode(), "Hash codes should differ for DTOs with different descriptions");

        // Different cout
        assertNotEquals(base, diffCout, "DTOs with different costs should not be equal");
        assertNotEquals(base.hashCode(), diffCout.hashCode(), "Hash codes should differ for DTOs with different costs");

        // Different garage
        assertNotEquals(base, diffGarage, "DTOs with different garages should not be equal");
        assertNotEquals(base.hashCode(), diffGarage.hashCode(), "Hash codes should differ for DTOs with different garages");

        // Different incidentId
        assertNotEquals(base, diffIncidentId, "DTOs with different incident IDs should not be equal");
        assertNotEquals(base.hashCode(), diffIncidentId.hashCode(), "Hash codes should differ for DTOs with different incident IDs");

        // Different timestamps - Timestamps are included in equals/hashCode in this DTO
        assertNotEquals(base, diffTimestamps, "DTOs with different timestamps should not be equal");
        assertNotEquals(base.hashCode(), diffTimestamps.hashCode(), "Hash codes should differ for DTOs with different timestamps");
    }

    @Test
    void testEqualsWithNull() {
        // Given
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(1L)
                .dateReparation(LocalDate.now())
                .description("Réparation moteur")
                .cout(new BigDecimal("3000.00"))
                .garage("Garage Test")
                .incidentId(5L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Then
        assertNotEquals(null, dto);
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Given
        ReparationResponseDTO dto = ReparationResponseDTO.builder()
                .id(1L)
                .dateReparation(LocalDate.now())
                .description("Réparation moteur")
                .cout(new BigDecimal("3000.00"))
                .garage("Garage Test")
                .incidentId(5L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Then
        assertNotEquals("Not a DTO",dto );
    }

    @Test
    void testEqualsAndHashCodeWithExtensiveCoverage() {
        // Given
        Long id = 1L;
        LocalDate dateReparation = LocalDate.now();
        String description = "Réparation moteur";
        BigDecimal cout = new BigDecimal("3000.00");
        String garage = "Garage Test";
        Long incidentId = 5L;
        LocalDateTime createdAt = LocalDateTime.now();
        LocalDateTime updatedAt = LocalDateTime.now();

        // Create base DTO
        ReparationResponseDTO base = ReparationResponseDTO.builder()
                .id(id)
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        // Create identical DTO
        ReparationResponseDTO same = ReparationResponseDTO.builder()
                .id(id)
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        // Create DTOs with different fields
        ReparationResponseDTO differentId = ReparationResponseDTO.builder()
                .id(2L)
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        ReparationResponseDTO differentDate = ReparationResponseDTO.builder()
                .id(id)
                .dateReparation(dateReparation.minusDays(1))
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        ReparationResponseDTO differentDescription = ReparationResponseDTO.builder()
                .id(id)
                .dateReparation(dateReparation)
                .description("Different description")
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        ReparationResponseDTO differentCout = ReparationResponseDTO.builder()
                .id(id)
                .dateReparation(dateReparation)
                .description(description)
                .cout(new BigDecimal("4000.00"))
                .garage(garage)
                .incidentId(incidentId)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        ReparationResponseDTO differentGarage = ReparationResponseDTO.builder()
                .id(id)
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage("Different Garage")
                .incidentId(incidentId)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        ReparationResponseDTO differentIncidentId = ReparationResponseDTO.builder()
                .id(id)
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(6L)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        ReparationResponseDTO differentCreatedAt = ReparationResponseDTO.builder()
                .id(id)
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .createdAt(createdAt.minusDays(1))
                .updatedAt(updatedAt)
                .build();

        ReparationResponseDTO differentUpdatedAt = ReparationResponseDTO.builder()
                .id(id)
                .dateReparation(dateReparation)
                .description(description)
                .cout(cout)
                .garage(garage)
                .incidentId(incidentId)
                .createdAt(createdAt)
                .updatedAt(updatedAt.minusDays(1))
                .build();

        // Then - test reflexivity and symmetry
        assertEquals(base, base);
        assertEquals(base, same);
        assertEquals(same, base);

        // Test transitivity
        assertNotEquals(base, differentId);
        assertNotEquals(base, differentDate);
        assertNotEquals(base, differentDescription);
        assertNotEquals(base, differentCout);
        assertNotEquals(base, differentGarage);
        assertNotEquals(base, differentIncidentId);
        assertNotEquals(base, differentCreatedAt);
        assertNotEquals(base, differentUpdatedAt);

        // Test hashCode
        assertEquals(base.hashCode(), same.hashCode());
        assertNotEquals(base.hashCode(), differentId.hashCode());
        assertNotEquals(base.hashCode(), differentDate.hashCode());
        assertNotEquals(base.hashCode(), differentDescription.hashCode());
        assertNotEquals(base.hashCode(), differentCout.hashCode());
        assertNotEquals(base.hashCode(), differentGarage.hashCode());
        assertNotEquals(base.hashCode(), differentIncidentId.hashCode());
        assertNotEquals(base.hashCode(), differentCreatedAt.hashCode());
        assertNotEquals(base.hashCode(), differentUpdatedAt.hashCode());
    }

    @Test
    void testEqualsAndHashCodeWithNullFields() {
        // Given
        ReparationResponseDTO dtoWithNulls = ReparationResponseDTO.builder()
                .id(null)
                .dateReparation(null)
                .description(null)
                .cout(null)
                .garage(null)
                .incidentId(null)
                .createdAt(null)
                .updatedAt(null)
                .build();

        ReparationResponseDTO anotherDtoWithNulls = ReparationResponseDTO.builder()
                .id(null)
                .dateReparation(null)
                .description(null)
                .cout(null)
                .garage(null)
                .incidentId(null)
                .createdAt(null)
                .updatedAt(null)
                .build();

        ReparationResponseDTO dtoWithValues = ReparationResponseDTO.builder()
                .id(1L)
                .dateReparation(LocalDate.now())
                .description("Réparation")
                .cout(new BigDecimal("1000.00"))
                .garage("Garage")
                .incidentId(1L)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Then
        assertEquals(dtoWithNulls, anotherDtoWithNulls);
        assertEquals(dtoWithNulls.hashCode(), anotherDtoWithNulls.hashCode());
        assertNotEquals(dtoWithNulls, dtoWithValues);
        assertNotEquals(dtoWithNulls.hashCode(), dtoWithValues.hashCode());
    }
}