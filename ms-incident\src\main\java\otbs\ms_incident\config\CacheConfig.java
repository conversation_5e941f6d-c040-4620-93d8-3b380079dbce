package otbs.ms_incident.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * Configuration du cache pour l'application.
 * Cette classe configure Caffeine comme gestionnaire de cache
 * pour améliorer les performances des opérations fréquentes.
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * Définit les noms des caches utilisés dans l'application.
     */
    public static final String INCIDENTS_CACHE = "incidents";
    public static final String REPARATIONS_CACHE = "reparations";
    public static final String INCIDENT_BY_ID_CACHE = "incidentById";
    public static final String REPARATION_BY_ID_CACHE = "reparationById";

    /**
     * Configure le gestionnaire de cache Caffeine avec des paramètres optimisés.
     * 
     * @return Le gestionnaire de cache configuré
     */
    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCacheNames(Arrays.asList(
                INCIDENTS_CACHE, 
                REPARATIONS_CACHE,
                INCIDENT_BY_ID_CACHE,
                REPARATION_BY_ID_CACHE
        ));
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .initialCapacity(50)
                .maximumSize(500)
                .expireAfterAccess(10, TimeUnit.MINUTES)
                .recordStats());
        return cacheManager;
    }
} 