package otbs.ms_incident.service;

import otbs.ms_incident.commun.PageResponse;
import otbs.ms_incident.dto.ReparationRequestDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.enums.StatusReparation;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;
import otbs.ms_incident.config.CacheConfig;
import otbs.ms_incident.exception.BadRequestException;
import otbs.ms_incident.exception.ResourceNotFoundException;
import otbs.ms_incident.mapper.ReparationMapper;
import otbs.ms_incident.commun.PageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.math.RoundingMode;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReparationServiceImpl implements ReparationService {

    private static final String INCIDENT_NOT_FOUND_MESSAGE = "Incident n'a pas été trouvé avec id : '";

    private final ReparationRepository reparationRepository;
    private final IncidentRepository incidentRepository;
    private final ReparationMapper reparationMapper;

    @Override
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = CacheConfig.REPARATIONS_CACHE, allEntries = true)
    })
    public ReparationResponseDTO createReparation(ReparationRequestDTO requestDTO) {
        log.info("Creating reparation with data: {}", requestDTO);

        // Validation des données
        validateReparationData(requestDTO);

        // Vérifier que l'incident existe
        Incident incident = incidentRepository.findById(requestDTO.getIncidentId())
                .orElseThrow(() -> new ResourceNotFoundException("Incident not found with id : '" + requestDTO.getIncidentId() + "'"));

        Reparation reparation = reparationMapper.toEntity(requestDTO);
        reparation.setIncident(incident);

        Reparation savedReparation = reparationRepository.save(reparation);
        log.info("Reparation created with ID: {}", savedReparation.getId());

        return reparationMapper.toDTO(savedReparation);
    }

    private void validateReparationData(ReparationRequestDTO requestDTO) {
        // Vérifier que l'ID de l'incident n'est pas null
        if (requestDTO.getIncidentId() == null) {
            throw new BadRequestException("L'ID de l'incident ne peut pas être null");
        }

        // Vérifier que le coût n'est pas négatif
        if (requestDTO.getCout() != null && requestDTO.getCout().compareTo(BigDecimal.ZERO) < 0) {
            throw new BadRequestException("Le coût de la réparation ne peut pas être négatif");
        }

        // Vérifier que la date n'est pas null
        if (requestDTO.getDateReparation() == null) {
            throw new BadRequestException("La date de réparation ne peut pas être null");
        }

        // Vérifier que la date n'est pas dans le futur
        if (requestDTO.getDateReparation().isAfter(LocalDate.now())) {
            throw new BadRequestException("La date de réparation ne peut pas être dans le futur");
        }
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = CacheConfig.REPARATION_BY_ID_CACHE, key = "#id")
    public ReparationResponseDTO getReparationById(Long id) {
        log.info("Fetching reparation with ID: {}", id);
        Reparation reparation = findReparationById(id);
        return reparationMapper.toDTO(reparation);
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = CacheConfig.REPARATIONS_CACHE)
    public List<ReparationResponseDTO> getAllReparations() {
        log.info("Fetching all reparations");
        List<Reparation> reparations = reparationRepository.findAll();
        return reparationMapper.toDTOList(reparations);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<ReparationResponseDTO> getAllReparationsPaginated(Pageable pageable) {
        log.info("Fetching all reparations with pagination: {}", pageable);
        Page<Reparation> reparationPage = reparationRepository.findAll(pageable);
        return PageUtil.toPageResponse(reparationPage, reparationMapper::toDTO);
    }

    @Override
    @Transactional
    @Caching(
        put = { @CachePut(value = CacheConfig.REPARATION_BY_ID_CACHE, key = "#id") },
        evict = { @CacheEvict(value = CacheConfig.REPARATIONS_CACHE, allEntries = true) }
    )
    public ReparationResponseDTO updateReparation(Long id, ReparationRequestDTO requestDTO) {
        log.info("Updating reparation with ID: {} with data: {}", id, requestDTO);

        // Validation des données
        if (requestDTO.getCout() != null && requestDTO.getCout().compareTo(BigDecimal.ZERO) < 0) {
            throw new BadRequestException("Le coût de la réparation ne peut pas être négatif");
        }

        if (requestDTO.getDateReparation() != null && requestDTO.getDateReparation().isAfter(LocalDate.now())) {
            throw new BadRequestException("La date de réparation ne peut pas être dans le futur");
        }

        Reparation reparation = findReparationById(id);

        // Si l'ID d'incident a changé, vérifier que le nouvel incident existe
        if (requestDTO.getIncidentId() != null && !requestDTO.getIncidentId().equals(reparation.getIncident().getId())) {
            Incident newIncident = incidentRepository.findById(requestDTO.getIncidentId())
                    .orElseThrow(() -> new ResourceNotFoundException("Incident not found with id : '" + requestDTO.getIncidentId() + "'"));
            reparation.setIncident(newIncident);
        }

        reparationMapper.updateReparationFromDTO(requestDTO, reparation);
        Reparation updatedReparation = reparationRepository.save(reparation);
        log.info("Reparation with ID: {} updated successfully", id);

        return reparationMapper.toDTO(updatedReparation);
    }

    @Override
    @Transactional
    public void deleteReparation(Long id) {
        log.info("Deleting reparation with ID: {}", id);
        if (!reparationRepository.existsById(id)) {
            throw new ResourceNotFoundException("Reparation n'a pas été trouvée avec id : '" + id + "'");
        }
        reparationRepository.deleteById(id);
        log.info("Reparation with ID: {} deleted successfully", id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ReparationResponseDTO> getReparationsByIncidentId(Long incidentId) {
        log.info("Fetching reparations for incident with ID: {}", incidentId);
        // Vérifier que l'incident existe
        if (!incidentRepository.existsById(incidentId)) {
            throw new ResourceNotFoundException(INCIDENT_NOT_FOUND_MESSAGE + incidentId + "'");
        }

        List<Reparation> reparations = reparationRepository.findByIncidentId(incidentId);
        return reparationMapper.toDTOList(reparations);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<ReparationResponseDTO> getReparationsByIncidentIdPaginated(Long incidentId, Pageable pageable) {
        log.info("Fetching reparations for incident with ID: {} with pagination: {}", incidentId, pageable);
        // Vérifier que l'incident existe
        if (!incidentRepository.existsById(incidentId)) {
            throw new ResourceNotFoundException(INCIDENT_NOT_FOUND_MESSAGE + incidentId + "'");
        }

        Page<Reparation> reparationPage = reparationRepository.findByIncidentId(incidentId, pageable);
        return PageUtil.toPageResponse(reparationPage, reparationMapper::toDTO);
    }



    @Override
    @Transactional(readOnly = true)
    public List<ReparationResponseDTO> getReparationsByDateRange(LocalDate debut, LocalDate fin) {
        log.info("Fetching reparations between dates: {} and {}", debut, fin);
        if (debut.isAfter(fin)) {
            throw new BadRequestException("La date de début doit être antérieure ou égale à la date de fin");
        }

        List<Reparation> reparations = reparationRepository.findByDateReparationBetween(debut, fin);
        return reparationMapper.toDTOList(reparations);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ReparationResponseDTO> getReparationsByStatus(StatusReparation status) {
        log.info("Fetching reparations by status: {}", status);
        List<Reparation> reparations = reparationRepository.findByStatus(status);
        return reparationMapper.toDTOList(reparations);
    }



    @Override
    @Transactional(readOnly = true)
    public List<ReparationResponseDTO> getReparationsByStatusAndIncidentId(StatusReparation status, Long incidentId) {
        log.info("Fetching reparations by status: {} and incident ID: {}", status, incidentId);
        // Vérifier que l'incident existe
        if (!incidentRepository.existsById(incidentId)) {
            throw new ResourceNotFoundException(INCIDENT_NOT_FOUND_MESSAGE + incidentId + "'");
        }

        List<Reparation> reparations = reparationRepository.findByStatusAndIncidentId(status, incidentId);
        return reparationMapper.toDTOList(reparations);
    }

    @Override
    @Transactional
    @Caching(
        put = { @CachePut(value = CacheConfig.REPARATION_BY_ID_CACHE, key = "#id") },
        evict = { @CacheEvict(value = CacheConfig.REPARATIONS_CACHE, allEntries = true) }
    )
    public ReparationResponseDTO updateReparationStatus(Long id, StatusReparation status) {
        log.info("Updating status of reparation with ID: {} to {}", id, status);

        Reparation reparation = findReparationById(id);
        reparation.setStatus(status);

        Reparation updatedReparation = reparationRepository.save(reparation);
        log.info("Status of reparation with ID: {} updated successfully to {}", id, status);

        return reparationMapper.toDTO(updatedReparation);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ReparationResponseDTO> searchReparations(StatusReparation status, Long incidentId, LocalDate startDate,
                                                      LocalDate endDate, BigDecimal minCost, BigDecimal maxCost, String garage) {
        log.info("Searching reparations with filters: status={}, incidentId={}, startDate={}, endDate={}, minCost={}, maxCost={}, garage={}",
                status, incidentId, startDate, endDate, minCost, maxCost, garage);

        // Implémentation simple pour démonstration - dans un environnement de production,
        // il serait préférable d'utiliser une requête dynamique avec Criteria API ou QueryDSL
        List<Reparation> reparations = reparationRepository.findAll();

        // Filtrer les résultats selon les critères fournis
        List<Reparation> filteredReparations = reparations.stream()
            .filter(r -> status == null || r.getStatus().equals(status))
            .filter(r -> incidentId == null || r.getIncident().getId().equals(incidentId))
            .filter(r -> startDate == null || !r.getDateReparation().isBefore(startDate))
            .filter(r -> endDate == null || !r.getDateReparation().isAfter(endDate))
            .filter(r -> minCost == null || r.getCout().compareTo(minCost) >= 0)
            .filter(r -> maxCost == null || r.getCout().compareTo(maxCost) <= 0)
            .filter(r -> garage == null || r.getGarage().toLowerCase().contains(garage.toLowerCase()))
            .toList();

        return reparationMapper.toDTOList(filteredReparations);
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getTotalCost() {
        log.info("Calculating total cost of all reparations");

        List<Reparation> reparations = reparationRepository.findAll();
        return reparations.stream()
            .map(Reparation::getCout)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getAverageCost() {
        log.info("Calculating average cost of all reparations");

        List<Reparation> reparations = reparationRepository.findAll();
        if (reparations.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalCost = reparations.stream()
            .map(Reparation::getCout)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        return totalCost.divide(BigDecimal.valueOf(reparations.size()), 2, RoundingMode.HALF_UP);
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Long> getRepairCountByStatus() {
        log.info("Counting reparations by status");

        List<Reparation> reparations = reparationRepository.findAll();
        Map<String, Long> countByStatus = new HashMap<>();

        // Compter les réparations par statut
        for (Reparation reparation : reparations) {
            String status = reparation.getStatus().toString();
            countByStatus.put(status, countByStatus.getOrDefault(status, 0L) + 1);
        }

        return countByStatus;
    }

    // Helper methods
    private Reparation findReparationById(Long id) {
        return reparationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Reparation n'a pas été trouvée avec id : '" + id + "'"));
    }
}