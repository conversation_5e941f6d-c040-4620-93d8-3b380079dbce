package otbs.ms_incident.dto;

import otbs.ms_incident.enums.TypeIncident;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class IncidentResponseDTOTest {

    @Test
    void testNoArgsConstructor() {
        // When
        IncidentResponseDTO dto = new IncidentResponseDTO();

        // Then
        assertNull(dto.getId());
        assertNull(dto.getDate());
        assertNull(dto.getType());
        assertNull(dto.getLieu());
        assertNull(dto.getConstat());
        assertNotNull(dto.getPhotos());
        assertTrue(dto.getPhotos().isEmpty());
        assertNull(dto.getDescription());
        assertNotNull(dto.getReparations());
        assertTrue(dto.getReparations().isEmpty());
        assertNull(dto.getCreatedAt());
        assertNull(dto.getUpdatedAt());
    }

    @Test
    void testAllArgsConstructor() {
        // Given
        Long id = 1L;
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Rue de la Paix, Paris";
        String constat = "incidents/123/constats/constat_abc123.pdf";
        List<String> photos = Arrays.asList("photo1.jpg", "photo2.jpg");
        String description = "Collision latérale";
        List<ReparationResponseDTO> reparations = new ArrayList<>();
        LocalDateTime createdAt = LocalDateTime.of(2023, 5, 15, 10, 30);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 5, 16, 14, 45);

        ReparationResponseDTO reparation = new ReparationResponseDTO();
        reparation.setId(42L);
        reparations.add(reparation);

        // When
        IncidentResponseDTO dto = new IncidentResponseDTO();
        dto.setId(id);
        dto.setDate(date);
        dto.setType(type);
        dto.setLieu(lieu);
        dto.setConstat(constat);
        dto.setPhotos(photos);
        dto.setDescription(description);
        dto.setReparations(reparations);
        dto.setCreatedAt(createdAt);
        dto.setUpdatedAt(updatedAt);

        // Then
        assertEquals(id, dto.getId());
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertEquals(lieu, dto.getLieu());
        assertEquals(constat, dto.getConstat());
        assertEquals(photos, dto.getPhotos());
        assertEquals(description, dto.getDescription());
        assertEquals(reparations, dto.getReparations());
        assertEquals(createdAt, dto.getCreatedAt());
        assertEquals(updatedAt, dto.getUpdatedAt());
    }

    @Test
    void testBuilder() {
        // Given
        Long id = 2L;
        LocalDate date = LocalDate.of(2023, 6, 20);
        TypeIncident type = TypeIncident.PANNE;
        String lieu = "Avenue des Champs-Élysées, Paris";
        String constat = "incidents/456/constats/constat_def456.pdf";
        List<String> photos = Arrays.asList("photo3.jpg", "photo4.jpg");
        String description = "Panne moteur";
        List<ReparationResponseDTO> reparations = new ArrayList<>();
        LocalDateTime createdAt = LocalDateTime.of(2023, 6, 20, 9, 15);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 6, 21, 11, 30);

        ReparationResponseDTO reparation = new ReparationResponseDTO();
        reparation.setId(43L);
        reparations.add(reparation);

        // When
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
            .id(id)
            .date(date)
            .type(type)
            .lieu(lieu)
            .constat(constat)
            .photos(photos)
            .description(description)
            .reparations(reparations)
            .vehiculeId(1L)
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();

        // Then
        assertEquals(id, dto.getId());
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertEquals(lieu, dto.getLieu());
        assertEquals(constat, dto.getConstat());
        assertEquals(photos, dto.getPhotos());
        assertEquals(description, dto.getDescription());
        assertEquals(reparations, dto.getReparations());
        assertEquals(createdAt, dto.getCreatedAt());
        assertEquals(updatedAt, dto.getUpdatedAt());
    }

    @Test
    void testSettersAndGetters() {
        // Given
        IncidentResponseDTO dto = new IncidentResponseDTO();
        Long id = 3L;
        LocalDate date = LocalDate.of(2023, 7, 10);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Boulevard Haussmann, Paris";
        String constat = "incidents/789/constats/constat_ghi789.pdf";
        List<String> photos = Arrays.asList("photo5.jpg", "photo6.jpg");
        String description = "Collision frontale";
        List<ReparationResponseDTO> reparations = new ArrayList<>();
        LocalDateTime createdAt = LocalDateTime.of(2023, 7, 10, 14, 0);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 7, 11, 9, 45);

        ReparationResponseDTO reparation = new ReparationResponseDTO();
        reparation.setId(44L);
        reparations.add(reparation);

        // When
        dto.setId(id);
        dto.setDate(date);
        dto.setType(type);
        dto.setLieu(lieu);
        dto.setConstat(constat);
        dto.setPhotos(photos);
        dto.setDescription(description);
        dto.setReparations(reparations);
        dto.setCreatedAt(createdAt);
        dto.setUpdatedAt(updatedAt);

        // Then
        assertEquals(id, dto.getId());
        assertEquals(date, dto.getDate());
        assertEquals(type, dto.getType());
        assertEquals(lieu, dto.getLieu());
        assertEquals(constat, dto.getConstat());
        assertEquals(photos, dto.getPhotos());
        assertEquals(description, dto.getDescription());
        assertEquals(reparations, dto.getReparations());
        assertEquals(createdAt, dto.getCreatedAt());
        assertEquals(updatedAt, dto.getUpdatedAt());
    }

    @Test
    void testDefaultLists() {
        // When
        IncidentResponseDTO dto = IncidentResponseDTO.builder().build();

        // Then
        assertNotNull(dto.getPhotos(), "Photos list should be initialized by default");
        assertTrue(dto.getPhotos().isEmpty(), "Default photos list should be empty");

        assertNotNull(dto.getReparations(), "Reparations list should be initialized by default");
        assertTrue(dto.getReparations().isEmpty(), "Default reparations list should be empty");
    }

    @Test
    void testModifyingPhotos() {
        // Given
        IncidentResponseDTO dto = new IncidentResponseDTO();

        // When
        dto.getPhotos().add("new_photo.jpg");

        // Then
        assertEquals(1, dto.getPhotos().size());
        assertEquals("new_photo.jpg", dto.getPhotos().get(0));
    }

    @Test
    void testModifyingReparations() {
        // Given
        IncidentResponseDTO dto = new IncidentResponseDTO();
        ReparationResponseDTO reparation = new ReparationResponseDTO();
        reparation.setId(45L);

        // When
        dto.getReparations().add(reparation);

        // Then
        assertEquals(1, dto.getReparations().size());
        assertEquals(45L, dto.getReparations().get(0).getId());
    }

    @Test
    void testEqualsAndHashCode() {
        // Given
        IncidentResponseDTO dto1 = new IncidentResponseDTO();
        dto1.setId(1L);
        dto1.setDate(LocalDate.of(2023, 8, 15));

        IncidentResponseDTO dto2 = new IncidentResponseDTO();
        dto2.setId(1L);
        dto2.setDate(LocalDate.of(2023, 8, 15));

        IncidentResponseDTO dto3 = new IncidentResponseDTO();
        dto3.setId(2L);
        dto3.setDate(LocalDate.of(2023, 8, 16));

        // Then
        assertEquals(dto1, dto1, "A DTO should equal itself");
        assertEquals(dto1, dto2, "DTOs with equal properties should be equal");
        assertEquals(dto2, dto1, "Equals should be symmetric");
        assertNotEquals(dto1, dto3, "DTOs with different IDs should not be equal");
        assertNotEquals(null, dto1, "A DTO should not equal null");
        assertNotEquals("Not a DTO", dto1, "A DTO should not equal a different type");

        assertEquals(dto1.hashCode(), dto2.hashCode(), "Equal DTOs should have the same hash code");
        assertNotEquals(dto1.hashCode(), dto3.hashCode(), "Different DTOs should have different hash codes");
    }

    @Test
    void testToString() {
        // Given
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
            .id(5L)
            .type(TypeIncident.ACCIDENT)
            .description("Test Description")
            .build();

        // When
        String result = dto.toString();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("id=5"), "toString should contain the id");
        assertTrue(result.contains("type=ACCIDENT"), "toString should contain the type");
        assertTrue(result.contains("description=Test Description"), "toString should contain the description");
    }

    @Test
    void testNullPhotosInitializedToEmptyList() {
        // Given - Create a new dto with builder default lists
        IncidentResponseDTO dto = IncidentResponseDTO.builder().build();

        // Then - should have empty lists
        assertNotNull(dto.getPhotos(), "Photos should be initialized to empty list");
        assertTrue(dto.getPhotos().isEmpty(), "Photos should be empty");

        // When - add item to the list
        dto.getPhotos().add("test.jpg");

        // Then - should be able to add successfully
        assertEquals(1, dto.getPhotos().size(), "Should be able to add to the photos list");
    }

    @Test
    void testNullReparationsInitializedToEmptyList() {
        // Given - Create a new dto with builder default lists
        IncidentResponseDTO dto = IncidentResponseDTO.builder().build();

        // Then - should have empty lists
        assertNotNull(dto.getReparations(), "Reparations should be initialized to empty list");
        assertTrue(dto.getReparations().isEmpty(), "Reparations should be empty");

        // When - add item to the list
        ReparationResponseDTO reparation = new ReparationResponseDTO();
        reparation.setId(123L);
        dto.getReparations().add(reparation);

        // Then - should be able to add successfully
        assertEquals(1, dto.getReparations().size(), "Should be able to add to the reparations list");
    }

    @Test
    void testBuilderWithoutSettingLists() {
        // When
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
            .id(10L)
            .type(TypeIncident.PANNE)
            .build();

        // Then
        assertNotNull(dto.getPhotos(), "Photos should be initialized even when not set in builder");
        assertTrue(dto.getPhotos().isEmpty(), "Photos should be empty when not set in builder");

        assertNotNull(dto.getReparations(), "Reparations should be initialized even when not set in builder");
        assertTrue(dto.getReparations().isEmpty(), "Reparations should be empty when not set in builder");
    }

    @Test
    void testBuilderWithNullLists() {
        // Given
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
            .id(11L)
            .build();

        // Then - default annotation should create empty lists even if null isn't explicitly set
        assertNotNull(dto.getPhotos(), "Photos should be initialized with empty list by default");
        assertTrue(dto.getPhotos().isEmpty(), "Photos should be empty by default");

        assertNotNull(dto.getReparations(), "Reparations should be initialized with empty list by default");
        assertTrue(dto.getReparations().isEmpty(), "Reparations should be empty by default");
    }

    @Test
    void testEqualsWithExtensiveCoverage() {
        // Given
        Long id = 1L;
        LocalDate date = LocalDate.of(2023, 5, 15);
        TypeIncident type = TypeIncident.ACCIDENT;
        String lieu = "Paris";
        String description = "Accident description";
        String constat = "constat.pdf";
        List<String> photos = Arrays.asList("photo1.jpg", "photo2.jpg");
        List<ReparationResponseDTO> reparations = new ArrayList<>();
        LocalDateTime createdAt = LocalDateTime.of(2023, 5, 15, 10, 0);
        LocalDateTime updatedAt = LocalDateTime.of(2023, 5, 15, 11, 0);

        ReparationResponseDTO reparation = new ReparationResponseDTO();
        reparation.setId(1L);
        reparations.add(reparation);

        // Create base DTO
        IncidentResponseDTO base = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        // Create identical DTO
        IncidentResponseDTO same = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        // Create DTOs with different fields
        IncidentResponseDTO differentId = IncidentResponseDTO.builder()
                .id(2L)
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        IncidentResponseDTO differentDate = IncidentResponseDTO.builder()
                .id(id)
                .date(LocalDate.of(2023, 5, 16))
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        IncidentResponseDTO differentType = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(TypeIncident.PANNE)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        IncidentResponseDTO differentLieu = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu("Lyon")
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        IncidentResponseDTO differentDescription = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .description("Different description")
                .constat(constat)
                .photos(photos)
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        IncidentResponseDTO differentConstat = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat("different.pdf")
                .photos(photos)
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        IncidentResponseDTO differentPhotos = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(Arrays.asList("different.jpg"))
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        List<ReparationResponseDTO> differentReparationsList = new ArrayList<>();
        ReparationResponseDTO differentReparation = new ReparationResponseDTO();
        differentReparation.setId(2L);
        differentReparationsList.add(differentReparation);

        IncidentResponseDTO differentReparations = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparations(differentReparationsList)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();

        IncidentResponseDTO differentCreatedAt = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparations(reparations)
                .createdAt(LocalDateTime.of(2023, 5, 16, 10, 0))
                .updatedAt(updatedAt)
                .build();

        IncidentResponseDTO differentUpdatedAt = IncidentResponseDTO.builder()
                .id(id)
                .date(date)
                .type(type)
                .lieu(lieu)
                .description(description)
                .constat(constat)
                .photos(photos)
                .reparations(reparations)
                .createdAt(createdAt)
                .updatedAt(LocalDateTime.of(2023, 5, 16, 11, 0))
                .build();

        // Then - test reflexivity and symmetry
        assertEquals(base, base);
        assertEquals(base, same);
        assertEquals(same, base);

        // Test transitivity
        assertNotEquals(base, differentId);
        assertNotEquals(base, differentDate);
        assertNotEquals(base, differentType);
        assertNotEquals(base, differentLieu);
        assertNotEquals(base, differentDescription);
        assertNotEquals(base, differentConstat);
        assertNotEquals(base, differentPhotos);
        assertNotEquals(base, differentReparations);
        assertNotEquals(base, differentCreatedAt);
        assertNotEquals(base, differentUpdatedAt);

        // Test hashCode
        assertEquals(base.hashCode(), same.hashCode());
        assertNotEquals(base.hashCode(), differentId.hashCode());
        assertNotEquals(base.hashCode(), differentDate.hashCode());
        assertNotEquals(base.hashCode(), differentType.hashCode());
        assertNotEquals(base.hashCode(), differentLieu.hashCode());
        assertNotEquals(base.hashCode(), differentDescription.hashCode());
        assertNotEquals(base.hashCode(), differentConstat.hashCode());
        assertNotEquals(base.hashCode(), differentPhotos.hashCode());
        assertNotEquals(base.hashCode(), differentReparations.hashCode());
        assertNotEquals(base.hashCode(), differentCreatedAt.hashCode());
        assertNotEquals(base.hashCode(), differentUpdatedAt.hashCode());
    }

    @Test
    void testEqualsWithNull() {
        // Given
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
                .id(1L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        // Then
        assertNotEquals(null,dto );
    }

    @Test
    void testEqualsWithDifferentClass() {
        // Given
        IncidentResponseDTO dto = IncidentResponseDTO.builder()
                .id(1L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .build();

        // Then
        assertNotEquals("Not a DTO",dto );
    }

    @Test
    void testEqualsAndHashCodeWithNullFields() {
        // Given
        IncidentResponseDTO dtoWithNulls = IncidentResponseDTO.builder()
                .id(null)
                .date(null)
                .type(null)
                .lieu(null)
                .description(null)
                .constat(null)
                .createdAt(null)
                .updatedAt(null)
                .build();

        IncidentResponseDTO anotherDtoWithNulls = IncidentResponseDTO.builder()
                .id(null)
                .date(null)
                .type(null)
                .lieu(null)
                .description(null)
                .constat(null)
                .createdAt(null)
                .updatedAt(null)
                .build();

        IncidentResponseDTO dtoWithValues = IncidentResponseDTO.builder()
                .id(1L)
                .date(LocalDate.of(2023, 5, 15))
                .type(TypeIncident.ACCIDENT)
                .lieu("Paris")
                .description("Description")
                .constat("constat.pdf")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Then
        assertEquals(dtoWithNulls, anotherDtoWithNulls);
        assertEquals(dtoWithNulls.hashCode(), anotherDtoWithNulls.hashCode());
        assertNotEquals(dtoWithNulls, dtoWithValues);
        assertNotEquals(dtoWithNulls.hashCode(), dtoWithValues.hashCode());
    }
}