package otbs.ms_mission.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import org.springframework.web.bind.annotation.*;

import otbs.ms_mission.dto.UnifiedMissionCreateDTO;
import otbs.ms_mission.dto.UnifiedMissionUpdateDTO;
import otbs.ms_mission.dto.UnifiedMissionResponseDTO;
import otbs.ms_mission.dto.AccompagnantDTO;
import otbs.ms_mission.dto.AddAccompagnantsRequestDTO;
import otbs.ms_mission.dto.AddAccompagnantsResponseDTO;
import otbs.ms_mission.dto.MissionTypesResponseDTO;
import otbs.ms_mission.service.UnifiedMissionService;
import otbs.ms_mission.enums.TypeMission;


import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import jakarta.validation.Valid;
import org.springframework.format.annotation.DateTimeFormat;

@RestController
@RequestMapping("/api/missions")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Missions Unifiées", description = "API unifiée pour la création automatique de missions")
public class UnifiedMissionController {

    private final UnifiedMissionService unifiedMissionService;

    @PostMapping("/create")
    @Operation(
        summary = "Crée une mission unifiée",
        description = "Crée automatiquement une mission client ou projet selon le type spécifié. " +
                     "Types client: SITESURVEY, VISITEAVANTVENTE. " +
                     "Types projet: VISITEPREVENTIVE, VISITECURATIVE, PROJET. " +
                     "Gère automatiquement l'association projet-client pour les missions projet."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Mission créée avec succès"),
        @ApiResponse(responseCode = "400", description = "Données invalides"),
        @ApiResponse(responseCode = "404", description = "Client ou projet non trouvé"),
        @ApiResponse(responseCode = "401", description = "Non authentifié"),
        @ApiResponse(responseCode = "403", description = "Non autorisé"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<UnifiedMissionResponseDTO> createMission(
            @Parameter(description = "Données de création de la mission", required = true)
            @Valid @RequestBody UnifiedMissionCreateDTO createDTO) {

        log.info("Demande de création de mission unifiée de type: {}", createDTO.getType());

        try {
            UnifiedMissionResponseDTO response = unifiedMissionService.createMission(createDTO);

            log.info("Mission unifiée créée avec succès. ID: {}, Référence: {}",
                    response.getIdMission(), response.getReference());

            return ResponseEntity.status(HttpStatus.CREATED).body(response);

        } catch (IllegalArgumentException e) {
            log.warn("Données invalides pour la création de mission: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Erreur lors de la création de mission unifiée", e);
            throw e;
        }
    }

    /**
     * Endpoint de test pour vérifier les types de missions supportés.
     *
     * @return Liste des types de missions supportés
     */
    @GetMapping("/types")
    @Operation(
        summary = "Récupère les types de missions supportés",
        description = "Retourne la liste des types de missions supportés par l'API unifiée"
    )
    @ApiResponse(responseCode = "200", description = "Types récupérés avec succès")
    public ResponseEntity<MissionTypesResponseDTO> getSupportedTypes() {

        // Récupération dynamique des types depuis l'enum
        List<String> clientTypes = java.util.Arrays.stream(TypeMission.values())
                .filter(TypeMission::isClientMission)
                .map(Enum::name)
                .toList();

        List<String> projectTypes = java.util.Arrays.stream(TypeMission.values())
                .filter(TypeMission::isProjectMission)
                .map(Enum::name)
                .toList();

        MissionTypesResponseDTO response = MissionTypesResponseDTO.builder()
                .clientTypes(clientTypes)
                .projectTypes(projectTypes)
                .allTypes(java.util.Arrays.stream(TypeMission.values()).map(Enum::name).toList())
                .description("Types de missions supportés par l'API unifiée")
                .build();

        return ResponseEntity.ok(response);
    }

    /**
     * Récupère toutes les missions avec filtre optionnel par type.
     *
     * @param type Type de mission (optionnel)
     * @return Liste des missions
     */
    @GetMapping
    @Operation(
        summary = "Récupère toutes les missions",
        description = "Récupère toutes les missions avec possibilité de filtrer par type. " +
                     "Si aucun type n'est spécifié, retourne toutes les missions. " +
                     "Types disponibles: SITESURVEY, VISITEAVANTVENTE, VISITEPREVENTIVE, VISITECURATIVE, PROJET"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Missions récupérées avec succès"),
        @ApiResponse(responseCode = "400", description = "Type de mission invalide"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<List<UnifiedMissionResponseDTO>> getAllMissions(
            @Parameter(description = "Type de mission (optionnel)", required = false)
            @RequestParam(value = "type", required = false) String type) {

        log.debug("Demande de récupération des missions avec filtre type: {}", type);

        try {
            List<UnifiedMissionResponseDTO> missions = unifiedMissionService.getAllMissions(type);

            log.info("Récupération réussie de {} missions", missions.size());

            return ResponseEntity.ok(missions);

        } catch (IllegalArgumentException e) {
            log.warn("Type de mission invalide: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Erreur lors de la récupération des missions", e);
            throw e;
        }
    }

    /**
     * Récupère une mission par son ID.
     *
     * @param id ID de la mission
     * @return Mission avec tous ses détails enrichis
     */
    @GetMapping("/{id}")
    @Operation(
        summary = "Récupère une mission par son ID",
        description = "Récupère les détails complets d'une mission spécifique avec toutes les données enrichies (client, projet, accompagnants)"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Mission récupérée avec succès"),
        @ApiResponse(responseCode = "404", description = "Mission non trouvée"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<UnifiedMissionResponseDTO> getMissionById(
            @Parameter(description = "ID de la mission", required = true) @PathVariable Long id) {

        log.debug("Demande de récupération de la mission ID: {}", id);

        try {
            UnifiedMissionResponseDTO mission = unifiedMissionService.getMissionById(id);

            log.info("Mission {} récupérée avec succès", id);

            return ResponseEntity.ok(mission);

        } catch (Exception e) {
            log.error("Erreur lors de la récupération de la mission {}", id, e);
            throw e;
        }
    }

    /**
     * Met à jour une mission existante de manière unifiée.
     *
     * @param id ID de la mission à modifier
     * @param updateDTO DTO contenant les nouvelles données
     * @return Mission mise à jour
     */
    @PutMapping("/{id}")
    @Operation(summary = "Met à jour une mission existante",
               description = "Met à jour une mission existante avec gestion automatique des changements de type et des associations")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Mission mise à jour avec succès"),
        @ApiResponse(responseCode = "404", description = "Mission non trouvée"),
        @ApiResponse(responseCode = "400", description = "Données de modification invalides")
    })
    public ResponseEntity<UnifiedMissionResponseDTO> updateMission(
            @Parameter(description = "ID de la mission à modifier", required = true)
            @PathVariable Long id,
            @Parameter(description = "Nouvelles données de la mission", required = true)
            @Valid @RequestBody UnifiedMissionUpdateDTO updateDTO) {

        log.info("Demande de modification de la mission ID: {} vers le type: {}", id, updateDTO.getType());

        try {
            UnifiedMissionResponseDTO response = unifiedMissionService.updateMission(id, updateDTO);

            log.info("Mission {} mise à jour avec succès. Nouveau type: {}, Référence: {}",
                    id, response.getType(), response.getReference());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Erreur lors de la modification de la mission {}", id, e);
            throw e;
        }
    }

    /**
     * Supprime une mission existante de manière unifiée.
     *
     * @param id ID de la mission à supprimer
     * @return Confirmation de suppression
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Supprime une mission existante",
               description = "Supprime une mission existante avec nettoyage automatique de toutes les associations (accompagnants, client/projet)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Mission supprimée avec succès"),
        @ApiResponse(responseCode = "404", description = "Mission non trouvée"),
        @ApiResponse(responseCode = "409", description = "Impossible de supprimer la mission (contraintes métier)")
    })
    public ResponseEntity<Map<String, Object>> deleteMission(
            @Parameter(description = "ID de la mission à supprimer", required = true)
            @PathVariable Long id) {

        log.info("Demande de suppression de la mission ID: {}", id);

        try {
            unifiedMissionService.deleteMission(id);

            Map<String, Object> response = Map.of(
                "success", true,
                "message", "Mission supprimée avec succès",
                "missionId", id,
                "timestamp", java.time.LocalDateTime.now()
            );

            log.info("Mission {} supprimée avec succès", id);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Erreur lors de la suppression de la mission {}", id, e);
            throw e;
        }
    }

    /**
     * Récupère les missions dont la date de début est après la date actuelle.
     *
     * @return Liste des missions
     */
    @GetMapping("/date-debut-after")
    @Operation(summary = "Récupère les missions après la date actuelle",
               description = "Récupère toutes les missions dont la date de début est après la date actuelle")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Missions récupérées avec succès")
    })
    public ResponseEntity<List<UnifiedMissionResponseDTO>> findByDateDebutAfter() {
        log.debug("Demande de récupération des missions avec date de début après maintenant");

        try {
            List<UnifiedMissionResponseDTO> missions = unifiedMissionService.findByDateDebutAfter();

            log.info("{} missions récupérées avec date de début après maintenant", missions.size());

            return ResponseEntity.ok(missions);

        } catch (Exception e) {
            log.error("Erreur lors de la récupération des missions avec date de début après maintenant", e);
            throw e;
        }
    }

    /**
     * Récupère les missions d'un consultant après la date actuelle.
     * L'utilisateur peut être soit accompagnant, soit créateur de la mission.
     *
     * @param idConsultant L'ID du consultant
     * @return Liste des missions
     */
    @GetMapping("/consultant/{idConsultant}")
    @Operation(summary = "Récupère les missions d'un consultant après la date actuelle",
               description = "Récupère les missions où l'utilisateur est soit accompagnant, soit créateur, et dont la date de début est après la date actuelle")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Missions récupérées avec succès"),
        @ApiResponse(responseCode = "404", description = "Consultant non trouvé")
    })
    public ResponseEntity<List<UnifiedMissionResponseDTO>> findByDateDebutAfterAndAccompagnantUserId(
            @Parameter(description = "ID du consultant", required = true)
            @PathVariable String idConsultant) {
        log.debug("Demande de récupération des missions pour le consultant: {}", idConsultant);

        try {
            List<UnifiedMissionResponseDTO> missions = unifiedMissionService.findByDateDebutAfterAndAccompagnantUserId(idConsultant);

            log.info("{} missions récupérées pour le consultant {}", missions.size(), idConsultant);

            return ResponseEntity.ok(missions);

        } catch (Exception e) {
            log.error("Erreur lors de la récupération des missions pour le consultant {}", idConsultant, e);
            throw e;
        }
    }

    /**
     * Récupère les missions d'un consultant entre deux dates.
     * Le consultant peut être soit l'accompagnant, soit le créateur de la mission.
     *
     * @param consultantId L'ID du consultant
     * @param dateDebut La date de début de la période
     * @param dateFin La date de fin de la période
     * @return Liste des missions
     */
    @GetMapping("/consultant/{consultantId}/periode")
    @Operation(summary = "Récupère les missions d'un consultant entre deux dates",
               description = "Récupère les missions où l'utilisateur est soit accompagnant, soit créateur, et dont la date de début est entre les dates spécifiées")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Missions récupérées avec succès"),
        @ApiResponse(responseCode = "404", description = "Consultant non trouvé")
    })
    public ResponseEntity<List<UnifiedMissionResponseDTO>> findMissionsByConsultantAndDateRange(
            @Parameter(description = "ID du consultant", required = true)
            @PathVariable String consultantId,
            @Parameter(description = "Date de début de la période", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateDebut,
            @Parameter(description = "Date de fin de la période", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateFin) {
        log.debug("Demande de récupération des missions pour le consultant: {} entre {} et {}", consultantId, dateDebut, dateFin);

        try {
            List<UnifiedMissionResponseDTO> missions = unifiedMissionService.findMissionsByConsultantAndDateRange(consultantId, dateDebut, dateFin);

            log.info("{} missions récupérées pour le consultant {} entre {} et {}", missions.size(), consultantId, dateDebut, dateFin);

            return ResponseEntity.ok(missions);

        } catch (Exception e) {
            log.error("Erreur lors de la récupération des missions pour le consultant {} entre {} et {}", consultantId, dateDebut, dateFin, e);
            throw e;
        }
    }

    @GetMapping("/periode")
    @Operation(summary = "Récupère les missions entre deux dates",
               description = "Récupère les missions dont la date de début est entre les dates spécifiées")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Missions récupérées avec succès"),
        @ApiResponse(responseCode = "404", description = "Date invalide")
    })
    public ResponseEntity<List<UnifiedMissionResponseDTO>> findMissionsByDateRange(
            @Parameter(description = "Date de début de la période", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateDebut,
            @Parameter(description = "Date de fin de la période", required = true)
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime dateFin) {
        log.debug("Demande de récupération des missions entre {} et {}", dateDebut, dateFin);

        try {
            List<UnifiedMissionResponseDTO> missions = unifiedMissionService.findMissionsByDateRange( dateDebut, dateFin);


            return ResponseEntity.ok(missions);

        } catch (Exception e) {
            log.error("Erreur lors de la récupération des missions entre {} et {}", dateDebut, dateFin, e);
            throw e;
        }
    }

    /**
     * Compte le nombre d'accompagnants pour une mission.
     *
     * @param missionId L'ID de la mission
     * @return Le nombre d'accompagnants
     */
    @GetMapping("/nombre-accompagnants")
    @Operation(summary = "Compte le nombre d'accompagnants pour une mission",
               description = "Retourne le nombre d'accompagnants associés à une mission spécifique")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Nombre d'accompagnants récupéré avec succès"),
        @ApiResponse(responseCode = "404", description = "Mission non trouvée")
    })
    public ResponseEntity<Long> countAccompagnantsByMissionId(
            @Parameter(description = "ID de la mission", required = true)
            @RequestParam Long missionId) {
        log.debug("Demande de comptage des accompagnants pour la mission ID: {}", missionId);

        try {
            Long count = unifiedMissionService.countAccompagnantsByMissionId(missionId);

            log.info("{} accompagnants trouvés pour la mission {}", count, missionId);

            return ResponseEntity.ok(count);

        } catch (Exception e) {
            log.error("Erreur lors du comptage des accompagnants pour la mission {}", missionId, e);
            throw e;
        }
    }

    /**
     * Ajoute des accompagnants à une mission existante.
     *
     * @param missionId ID de la mission
     * @param request Requête contenant les IDs des accompagnants à ajouter
     * @return Réponse avec la liste des accompagnants ajoutés
     */
    @PostMapping("/{missionId}/accompagnants")
    @Operation(
        summary = "Ajoute des accompagnants à une mission",
        description = "Ajoute une liste d'accompagnants à une mission existante avec enrichissement des données utilisateur depuis Keycloak"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Accompagnants ajoutés avec succès"),
        @ApiResponse(responseCode = "404", description = "Mission non trouvée"),
        @ApiResponse(responseCode = "400", description = "Données invalides"),
        @ApiResponse(responseCode = "500", description = "Erreur interne du serveur")
    })
    public ResponseEntity<AddAccompagnantsResponseDTO> addAccompagnants(
            @Parameter(description = "ID de la mission", required = true) @PathVariable Long missionId,
            @Parameter(description = "Liste des IDs utilisateurs à ajouter comme accompagnants", required = true)
            @RequestBody @Valid AddAccompagnantsRequestDTO request) {

        log.debug("Demande d'ajout de {} accompagnants à la mission {}",
                 request.getUserIds().size(), missionId);

        try {
            List<AccompagnantDTO> accompagnants = unifiedMissionService.addAccompagnantsToMission(missionId, request.getUserIds());

            AddAccompagnantsResponseDTO response = AddAccompagnantsResponseDTO.builder()
                    .missionId(missionId)
                    .accompagnants(accompagnants)
                    .totalAdded(accompagnants.size())
                    .message("Accompagnants ajoutés avec succès")
                    .build();

            log.info("Ajout réussi de {} accompagnants à la mission {}", accompagnants.size(), missionId);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Erreur lors de l'ajout d'accompagnants à la mission {}", missionId, e);
            throw e;
        }
    }

}
