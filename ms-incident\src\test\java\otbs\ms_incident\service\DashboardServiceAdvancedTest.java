package otbs.ms_incident.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import otbs.ms_incident.client.VehiculeClient;
import otbs.ms_incident.dto.IncidentResponseDTO;
import otbs.ms_incident.dto.ReparationResponseDTO;
import otbs.ms_incident.client.VehiculeDto;
import otbs.ms_incident.dto.dashboard.InsuranceCoverageDTO;
import otbs.ms_incident.dto.dashboard.RepairCostByVehicleDTO;
import otbs.ms_incident.dto.dashboard.VehicleIncidentTypeDistributionDTO;
import otbs.ms_incident.entity.Incident;
import otbs.ms_incident.entity.Reparation;
import otbs.ms_incident.enums.StatusIncident;
import otbs.ms_incident.enums.StatusReparation;
import otbs.ms_incident.enums.TypeIncident;
import otbs.ms_incident.mapper.IncidentMapper;
import otbs.ms_incident.mapper.ReparationMapper;
import otbs.ms_incident.repository.DashboardRepository;
import otbs.ms_incident.repository.IncidentRepository;
import otbs.ms_incident.repository.ReparationRepository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DashboardServiceAdvancedTest {

    @Mock
    private DashboardRepository dashboardRepository;

    @Mock
    private IncidentRepository incidentRepository;

    @Mock
    private ReparationRepository reparationRepository;

    @Mock
    private IncidentMapper incidentMapper;

    @Mock
    private ReparationMapper reparationMapper;

    @Mock
    private VehiculeClient vehiculeClient;

    @InjectMocks
    private DashboardServiceImpl dashboardService;

    private LocalDate debut;
    private LocalDate fin;
    private Long vehiculeId;
    private List<Incident> mockIncidents;
    private List<Reparation> mockReparations;
    private List<IncidentResponseDTO> mockIncidentDTOs;
    private List<ReparationResponseDTO> mockReparationDTOs;

    @BeforeEach
    void setUp() {
        debut = LocalDate.of(2025, 5, 1);
        fin = LocalDate.of(2025, 5, 31);
        vehiculeId = 1L;

        // Créer des incidents pour les tests
        Incident incident1 = new Incident();
        incident1.setId(1L);
        incident1.setVehiculeId(1L);
        incident1.setType(TypeIncident.ACCIDENT);
        incident1.setStatus(StatusIncident.A_TRAITER);
        incident1.setDate(LocalDate.of(2025, 5, 15));

        Incident incident2 = new Incident();
        incident2.setId(2L);
        incident2.setVehiculeId(2L);
        incident2.setType(TypeIncident.PANNE);
        incident2.setStatus(StatusIncident.EN_COURS_TRAITEMENT);
        incident2.setDate(LocalDate.of(2025, 5, 20));

        mockIncidents = Arrays.asList(incident1, incident2);

        // Créer des réparations pour les tests
        Reparation reparation1 = new Reparation();
        reparation1.setId(1L);
        reparation1.setIncident(incident1);
        reparation1.setStatus(StatusReparation.EN_COURS);
        reparation1.setDateReparation(LocalDate.of(2025, 5, 16));
        reparation1.setCout(BigDecimal.valueOf(500));
        reparation1.setRembourse(true);
        reparation1.setMontantCouverture(BigDecimal.valueOf(300));

        Reparation reparation2 = new Reparation();
        reparation2.setId(2L);
        reparation2.setIncident(incident2);
        reparation2.setStatus(StatusReparation.TERMINEE);
        reparation2.setDateReparation(LocalDate.of(2025, 5, 21));
        reparation2.setCout(BigDecimal.valueOf(300));
        reparation2.setRembourse(false);
        reparation2.setMontantCouverture(BigDecimal.ZERO);

        mockReparations = Arrays.asList(reparation1, reparation2);

        // Créer des DTOs pour les tests
        IncidentResponseDTO incidentDTO1 = new IncidentResponseDTO();
        incidentDTO1.setId(1L);
        incidentDTO1.setVehiculeId(1L);
        incidentDTO1.setType(TypeIncident.ACCIDENT);
        incidentDTO1.setStatus(StatusIncident.A_TRAITER);

        IncidentResponseDTO incidentDTO2 = new IncidentResponseDTO();
        incidentDTO2.setId(2L);
        incidentDTO2.setVehiculeId(2L);
        incidentDTO2.setType(TypeIncident.PANNE);
        incidentDTO2.setStatus(StatusIncident.EN_COURS_TRAITEMENT);

        mockIncidentDTOs = Arrays.asList(incidentDTO1, incidentDTO2);

        ReparationResponseDTO reparationDTO1 = new ReparationResponseDTO();
        reparationDTO1.setId(1L);
        reparationDTO1.setIncidentId(1L);
        reparationDTO1.setVehiculeId(1L);
        reparationDTO1.setStatus(StatusReparation.EN_COURS);
        reparationDTO1.setCout(BigDecimal.valueOf(500));

        ReparationResponseDTO reparationDTO2 = new ReparationResponseDTO();
        reparationDTO2.setId(2L);
        reparationDTO2.setIncidentId(2L);
        reparationDTO2.setVehiculeId(2L);
        reparationDTO2.setStatus(StatusReparation.TERMINEE);
        reparationDTO2.setCout(BigDecimal.valueOf(300));

        mockReparationDTOs = Arrays.asList(reparationDTO1, reparationDTO2);
    }

    @Test
    void getRecentIncidents_Success() {
        // Arrange
        when(dashboardRepository.getRecentIncidents(eq(debut), eq(fin), eq(vehiculeId), anyInt())).thenReturn(mockIncidents);
        when(incidentMapper.toResponseDTOList(mockIncidents)).thenReturn(mockIncidentDTOs);

        List<VehiculeDto> mockVehicules = new ArrayList<>();
        VehiculeDto vehicule1 = new VehiculeDto();
        vehicule1.setIdVehicule(1L);
        vehicule1.setImmatriculation("ABC123");

        VehiculeDto vehicule2 = new VehiculeDto();
        vehicule2.setIdVehicule(2L);
        vehicule2.setImmatriculation("DEF456");

        mockVehicules.add(vehicule1);
        mockVehicules.add(vehicule2);
        when(vehiculeClient.getAllVehicules()).thenReturn(mockVehicules);

        // Act
        List<IncidentResponseDTO> result = dashboardService.getRecentIncidents(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0).getId());
        assertEquals(2L, result.get(1).getId());
        assertEquals("ABC123", result.get(0).getImmatriculation());
        assertEquals("DEF456", result.get(1).getImmatriculation());

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getRecentIncidents(eq(debut), eq(fin), eq(vehiculeId), anyInt());
        verify(incidentMapper).toResponseDTOList(mockIncidents);
        verify(vehiculeClient).getAllVehicules();
    }

    @Test
    void getRecentIncidents_EmptyList() {
        // Arrange
        when(dashboardRepository.getRecentIncidents(eq(debut), eq(fin), eq(vehiculeId), anyInt())).thenReturn(Collections.emptyList());
        when(incidentMapper.toResponseDTOList(Collections.emptyList())).thenReturn(Collections.emptyList());

        // Act
        List<IncidentResponseDTO> result = dashboardService.getRecentIncidents(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getRecentIncidents(eq(debut), eq(fin), eq(vehiculeId), anyInt());
        verify(incidentMapper).toResponseDTOList(Collections.emptyList());
        verify(vehiculeClient, never()).getAllVehicules();
    }

    @Test
    void getRecentRepairs_Success() {
        // Arrange
        when(reparationRepository.findByDateReparationBetween(debut, fin)).thenReturn(mockReparations);
        when(reparationMapper.toDTOList(anyList())).thenReturn(mockReparationDTOs);

        List<VehiculeDto> mockVehicules = new ArrayList<>();
        VehiculeDto vehicule1 = new VehiculeDto();
        vehicule1.setIdVehicule(1L);
        vehicule1.setImmatriculation("ABC123");

        VehiculeDto vehicule2 = new VehiculeDto();
        vehicule2.setIdVehicule(2L);
        vehicule2.setImmatriculation("DEF456");

        mockVehicules.add(vehicule1);
        mockVehicules.add(vehicule2);
        when(vehiculeClient.getAllVehicules()).thenReturn(mockVehicules);

        // Act
        List<ReparationResponseDTO> result = dashboardService.getRecentRepairs(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0).getId());
        assertEquals(2L, result.get(1).getId());
        assertEquals("ABC123", result.get(0).getImmatriculation());
        assertEquals("DEF456", result.get(1).getImmatriculation());

        // Vérifier les appels aux méthodes
        verify(reparationRepository).findByDateReparationBetween(debut, fin);
        verify(reparationMapper).toDTOList(anyList());
        verify(vehiculeClient).getAllVehicules();
    }

    @Test
    void getRecentRepairs_EmptyList() {
        // Arrange
        when(reparationRepository.findByDateReparationBetween(debut, fin)).thenReturn(Collections.emptyList());
        when(reparationMapper.toDTOList(Collections.emptyList())).thenReturn(Collections.emptyList());

        // Act
        List<ReparationResponseDTO> result = dashboardService.getRecentRepairs(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // Vérifier les appels aux méthodes
        verify(reparationRepository).findByDateReparationBetween(debut, fin);
        verify(reparationMapper).toDTOList(Collections.emptyList());
        verify(vehiculeClient, never()).getAllVehicules();
    }

    @Test
    void getVehicleIncidentTypeDistribution_Success() {
        // Arrange
        List<Object[]> mockResults = Arrays.asList(
                new Object[]{1L, TypeIncident.ACCIDENT, 3L},
                new Object[]{1L, TypeIncident.PANNE, 2L},
                new Object[]{2L, TypeIncident.ACCIDENT, 1L}
        );
        when(dashboardRepository.getIncidentCountByVehicleAndType(debut, fin, vehiculeId)).thenReturn(mockResults);

        List<VehiculeDto> mockVehicules = new ArrayList<>();
        VehiculeDto vehicule1 = new VehiculeDto();
        vehicule1.setIdVehicule(1L);
        vehicule1.setImmatriculation("ABC123");

        VehiculeDto vehicule2 = new VehiculeDto();
        vehicule2.setIdVehicule(2L);
        vehicule2.setImmatriculation("DEF456");

        mockVehicules.add(vehicule1);
        mockVehicules.add(vehicule2);
        when(vehiculeClient.getAllVehicules()).thenReturn(mockVehicules);

        // Act
        VehicleIncidentTypeDistributionDTO result = dashboardService.getVehicleIncidentTypeDistribution(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getCountByVehicleAndType().size());
        assertEquals(2, result.getCountByVehicleAndType().get(1L).size());
        assertEquals(1, result.getCountByVehicleAndType().get(2L).size());
        assertEquals(3L, result.getCountByVehicleAndType().get(1L).get("ACCIDENT"));
        assertEquals(2L, result.getCountByVehicleAndType().get(1L).get("PANNE"));
        assertEquals(1L, result.getCountByVehicleAndType().get(2L).get("ACCIDENT"));
        assertEquals("ABC123", result.getVehicleImmatriculations().get(1L));
        assertEquals("DEF456", result.getVehicleImmatriculations().get(2L));

        // Vérifier les appels aux méthodes
        verify(dashboardRepository).getIncidentCountByVehicleAndType(debut, fin, vehiculeId);
        verify(vehiculeClient).getAllVehicules();
    }

    @Test
    void getRepairCostByVehicle_Success() {
        // Arrange
        when(reparationRepository.findByDateReparationBetween(debut, fin)).thenReturn(mockReparations);

        List<VehiculeDto> mockVehicules = new ArrayList<>();
        VehiculeDto vehicule1 = new VehiculeDto();
        vehicule1.setIdVehicule(1L);
        vehicule1.setImmatriculation("ABC123");

        VehiculeDto vehicule2 = new VehiculeDto();
        vehicule2.setIdVehicule(2L);
        vehicule2.setImmatriculation("DEF456");

        mockVehicules.add(vehicule1);
        mockVehicules.add(vehicule2);
        when(vehiculeClient.getAllVehicules()).thenReturn(mockVehicules);

        // Act
        RepairCostByVehicleDTO result = dashboardService.getRepairCostByVehicle(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getCostByVehicleAndStatus().size());
        assertTrue(result.getCostByVehicleAndStatus().containsKey(1L));
        assertEquals(BigDecimal.valueOf(500), result.getCostByVehicleAndStatus().get(1L).get("EN_COURS"));
        assertEquals("ABC123", result.getVehicleImmatriculations().get(1L));

        // Vérifier les appels aux méthodes
        verify(reparationRepository).findByDateReparationBetween(debut, fin);
        verify(vehiculeClient).getAllVehicules();
    }

    @Test
    void getInsuranceCoverage_Success() {
        // Arrange
        when(incidentRepository.findByDateBetween(debut, fin)).thenReturn(mockIncidents);
        when(reparationRepository.findByDateReparationBetween(debut, fin)).thenReturn(mockReparations);

        List<VehiculeDto> mockVehicules = new ArrayList<>();
        VehiculeDto vehicule1 = new VehiculeDto();
        vehicule1.setIdVehicule(1L);
        vehicule1.setImmatriculation("ABC123");

        VehiculeDto vehicule2 = new VehiculeDto();
        vehicule2.setIdVehicule(2L);
        vehicule2.setImmatriculation("DEF456");

        mockVehicules.add(vehicule1);
        mockVehicules.add(vehicule2);
        when(vehiculeClient.getAllVehicules()).thenReturn(mockVehicules);

        // Act
        InsuranceCoverageDTO result = dashboardService.getInsuranceCoverage(debut, fin, vehiculeId);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getInsuranceCoverageByVehicle().size());
        assertTrue(result.getInsuranceCoverageByVehicle().containsKey(1L));
        assertEquals(BigDecimal.valueOf(300), result.getInsuranceCoverageByVehicle().get(1L).get("covered"));
        assertEquals(BigDecimal.valueOf(200), result.getInsuranceCoverageByVehicle().get(1L).get("uncovered"));
        assertEquals("ABC123", result.getVehicleImmatriculations().get(1L));

        // Vérifier les appels aux méthodes
        verify(incidentRepository).findByDateBetween(debut, fin);
        verify(reparationRepository).findByDateReparationBetween(debut, fin);
        verify(vehiculeClient).getAllVehicules();
    }
}
