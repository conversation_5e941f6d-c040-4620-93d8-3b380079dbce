package otbs.ms_incident.entity;

import otbs.ms_incident.enums.StatusReparation;
import otbs.ms_incident.enums.TypeIncident;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

class ReparationTest {

    private Reparation reparation;
    private Incident incident;

    @BeforeEach
    void setUp() {
        // Create an incident
        incident = new Incident();
        incident.setId(1L);
        incident.setDate(LocalDate.of(2023, 5, 15));
        incident.setType(TypeIncident.ACCIDENT);
        incident.setLieu("Boulevard Haussmann, Paris");
        incident.setDescription("Collision avec un autre véhicule");
        incident.setCreatedAt(LocalDateTime.now());
        incident.setCreatedBy("test-user");

        // Create a reparation
        reparation = new Reparation();
        reparation.setId(1L);
        reparation.setDateReparation(LocalDate.of(2023, 6, 10));
        reparation.setStatus(StatusReparation.EN_COURS);
        reparation.setDescription("Remplacement pare-choc avant");
        reparation.setCout(new BigDecimal("1200.50"));
        reparation.setGarage("Garage Central");
        reparation.setMontantCouverture(new BigDecimal("800.00"));
        reparation.setRembourse(false);
        reparation.setCreatedAt(LocalDateTime.now());
        reparation.setCreatedBy("test-user");
    }

    @Test
     void testReparationProperties() {
        // Arrange
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        StatusReparation status = StatusReparation.TERMINEE;
        String description = "Remplacement pare-choc avant";
        BigDecimal cout = new BigDecimal("1200.50");
        String garage = "Garage Central";
        Boolean rembourse = false;
        BigDecimal montantCouverture = new BigDecimal("800.00");

        // Act
        reparation.setDateReparation(dateReparation);
        reparation.setStatus(status);
        reparation.setDescription(description);
        reparation.setCout(cout);
        reparation.setGarage(garage);
        reparation.setRembourse(rembourse);
        reparation.setMontantCouverture(montantCouverture);

        // Assert
        assertEquals(dateReparation, reparation.getDateReparation());
        assertEquals(status, reparation.getStatus());
        assertEquals(description, reparation.getDescription());
        assertEquals(cout, reparation.getCout());
        assertEquals(garage, reparation.getGarage());
        assertFalse(reparation.getRembourse());
        assertEquals(montantCouverture, reparation.getMontantCouverture());
    }

    @Test
     void testReparationRelationWithIncident() {
        // Arrange
        Reparation reparation1 = new Reparation();
        Incident incident1 = new Incident();
        incident1.setId(1L);  // Set the ID here before using it in the assertion

        // Act
        reparation1.setIncident(incident1);

        // Assert
        assertEquals(incident1, reparation1.getIncident());
        assertEquals(1L, reparation1.getIncident().getId());
    }

    @Test
     void testCreateReparation() {
        // Arrange
        LocalDate dateReparation = LocalDate.of(2023, 6, 15);
        StatusReparation status = StatusReparation.TERMINEE;
        String description = "Remplacement phare avant";
        BigDecimal cout = new BigDecimal("500.75");
        String garage = "Garage Express";
        Boolean rembourse = true;
        BigDecimal montantCouverture = new BigDecimal("400.00");

        // Act
        Reparation reparation2 = new Reparation();
        reparation2.setDateReparation(dateReparation);
        reparation2.setStatus(status);
        reparation2.setDescription(description);
        reparation2.setCout(cout);
        reparation2.setGarage(garage);
        reparation2.setRembourse(rembourse);
        reparation2.setMontantCouverture(montantCouverture);

        // Assert
        assertEquals(dateReparation, reparation2.getDateReparation());
        assertEquals(status, reparation2.getStatus());
        assertEquals(description, reparation2.getDescription());
        assertEquals(cout, reparation2.getCout());
        assertEquals(garage, reparation2.getGarage());
        assertTrue(reparation2.getRembourse());
        assertEquals(montantCouverture, reparation2.getMontantCouverture());
    }

    @Test
     void testEqualsAndHashCode() {
        // Arrange
        Reparation reparation1 = new Reparation();
        reparation1.setId(1L);

        // Test equals with self
        assertEquals(reparation1, reparation1, "A reparation should equal itself");

        // Test that hashCode is consistent
        int hash1 = reparation1.hashCode();
        int hash2 = reparation1.hashCode();
        assertEquals(hash1, hash2, "Hash code should be consistent");

        // For non-identical objects, we can't guarantee equals or hashCode behavior
        // in the implementation, so we won't test those assertions
    }

    @Test
     void testToString() {
        // Arrange
        Reparation reparation3 = new Reparation();
        reparation3.setId(1L);
        reparation3.setDescription("Test Description");

        // Assert - just verify toString doesn't throw an exception and returns a non-empty string
        String result = reparation3.toString();
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void testHandlingAllStatusReparationEnumValues() {
        // Test for each enum value
        for (StatusReparation statusValue : StatusReparation.values()) {
            // Arrange
            Reparation testReparation = new Reparation();

            // Act
            testReparation.setStatus(statusValue);

            // Assert
            assertEquals(statusValue, testReparation.getStatus());
        }
    }

    @Test
    void testNoArgsConstructorDefaultValues() {
        // Act
        Reparation testReparation = new Reparation();

        // Assert
        assertEquals(StatusReparation.EN_COURS, testReparation.getStatus(), "Default status should be EN_COURS");
    }
}