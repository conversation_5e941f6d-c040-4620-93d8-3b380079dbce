package otbs.ms_mission.client.keycloak;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * DTO représentant un utilisateur Keycloak.
 * Contient uniquement les champs nécessaires pour notre application.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserDTO {
    private String id;
    private String username;
    private String firstName;
    private String lastName;
    private String email;
    private boolean enabled;
    private LocalDateTime createdTimestamp;
    private Map<String, List<String>> attributes;
    private List<String> realmRoles;
    private List<String> clientRoles;

    /**
     * Retourne le nom complet de l'utilisateur.
     *
     * @return Le nom complet (prénom + nom)
     */
    public String getFullName() {
        return firstName + " " + lastName;
    }
}
